﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PaymentAdviceCostAllocations", Schema = "fin")]
    public partial class PaymentAdviceCostAllocation
    {
        [Key]
        public int PaymentAdviceCostAllocationId { get; set; }
        public int? PaymentAdviceLineId { get; set; }
        public int? DepartmentId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ApportionPct { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("PaymentAdviceCostAllocations")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(PaymentAdviceLineId))]
        [InverseProperty("PaymentAdviceCostAllocations")]
        public virtual PaymentAdviceLine PaymentAdviceLine { get; set; }
    }
}
