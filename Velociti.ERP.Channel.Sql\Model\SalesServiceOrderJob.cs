﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesServiceOrderJobs", Schema = "sales")]
    public partial class SalesServiceOrderJob
    {
        public SalesServiceOrderJob()
        {
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SalesServiceOrderJobLines = new HashSet<SalesServiceOrderJobLine>();
        }

        [Key]
        public int SalesServiceOrderJobId { get; set; }
        public int SalesServiceOrderLineId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public int? ProductId { get; set; }
        public int? BillOfMaterialId { get; set; }
        public int? BrandId { get; set; }
        public int? TherapistId { get; set; }
        public int? TherapyRoomId { get; set; }
        public int? Quantity { get; set; }
        public byte? StatusEnum { get; set; }
        public int? TxnWorkFlowId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(SalesServiceOrderLineId))]
        [InverseProperty("SalesServiceOrderJobs")]
        public virtual SalesServiceOrderLine SalesServiceOrderLine { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("SalesServiceOrderJobs")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.SalesServiceOrderJob))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SalesServiceOrderJobLine.SalesServiceOrderJob))]
        public virtual ICollection<SalesServiceOrderJobLine> SalesServiceOrderJobLines { get; set; }
    }
}
