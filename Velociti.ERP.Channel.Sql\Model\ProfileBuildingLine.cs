﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProfileBuildingLines", Schema = "man")]
    public partial class ProfileBuildingLine
    {
        [Key]
        public int ProfileBuildingLineId { get; set; }
        public int? ProfileBuildingId { get; set; }
        public int? ProductionOrderLineId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? PlannedStartDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDate { get; set; }
        public bool? IsBarcodePrinted { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ProductionOrderLineId))]
        [InverseProperty("ProfileBuildingLines")]
        public virtual ProductionOrderLine ProductionOrderLine { get; set; }
        [ForeignKey(nameof(ProfileBuildingId))]
        [InverseProperty("ProfileBuildingLines")]
        public virtual ProfileBuilding ProfileBuilding { get; set; }
    }
}
