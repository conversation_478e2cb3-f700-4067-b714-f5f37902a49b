﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FixedAssetMaintenance", Schema = "fa")]
    public partial class FixedAssetMaintenance
    {
        [Key]
        public int FixedAssetMaintenanceId { get; set; }
        public int? FixedAssetId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date { get; set; }
        public int? EmployeeId { get; set; }
        public int? SupplierId { get; set; }
        public int? ServiceInvoiceId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Cost { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? Duration { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("FixedAssetMaintenances")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(ServiceInvoiceId))]
        [InverseProperty("FixedAssetMaintenances")]
        public virtual ServiceInvoice ServiceInvoice { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("FixedAssetMaintenances")]
        public virtual Supplier Supplier { get; set; }
    }
}
