﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class GeneralLedgerSettlementsController : ControllerBase
    {
        private readonly IGeneralLedgerSettlementService _generalLedgerSettlementService;

        public GeneralLedgerSettlementsController(IGeneralLedgerSettlementService generalLedgerSettlementService)
        {
            _generalLedgerSettlementService = generalLedgerSettlementService;
        }

        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]GeneralLedgerSettlement generalLedgerSettlement)
        {
            await _generalLedgerSettlementService.SaveAsync(generalLedgerSettlement);

            return Ok();
        }
    }
}