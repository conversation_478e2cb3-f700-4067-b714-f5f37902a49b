﻿using System;
using System.Collections.Generic;
using System.Text;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class TokenRepository : ITokenRepository
    {
        private readonly MarangoniERPContext context;

        public TokenRepository(MarangoniERPContext context)
        {
            this.context = context;
        }

        public Token Create(Token token)
        {
            try
            {
                this.context.Tokens.Add(token);
                this.context.SaveChanges();

                return token;
            }
            catch (Exception)
            {
                return null;
            }
        }

        public Token Find(string token)
        {
            return this.context.Tokens.Include(t => t.User).SingleOrDefault(t => t.TokenId == Guid.Parse(token));
        }

        public Token Update(Token token)
        {
            try
            {
                this.context.Tokens.Update(token);
                this.context.SaveChanges();

                return Find(token.TokenId.ToString());
            }
            catch (Exception)
            {
                return null;
            }
        }
    }
}
