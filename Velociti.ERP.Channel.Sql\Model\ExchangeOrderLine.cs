﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ExchangeOrderLines", Schema = "prc")]
    public partial class ExchangeOrderLine
    {
        [Key]
        public int ExchangeOrderLineId { get; set; }
        public int? ExchangeOrderId { get; set; }
        public int? ProductId { get; set; }
        public int? WarehouseId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        public int? ReasonId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ExpectedDeliveryDate { get; set; }
        [StringLength(50)]
        public string WarrantyPeriod { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int? LeadDays { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ExchangeOrderLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ExchangeOrderLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ExchangeOrderId))]
        [InverseProperty("ExchangeOrderLines")]
        public virtual ExchangeOrder ExchangeOrder { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ExchangeOrderLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ReasonId))]
        [InverseProperty(nameof(SupportData.ExchangeOrderLines))]
        public virtual SupportData Reason { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.ExchangeOrderLines))]
        public virtual Warehous Warehouse { get; set; }
    }
}
