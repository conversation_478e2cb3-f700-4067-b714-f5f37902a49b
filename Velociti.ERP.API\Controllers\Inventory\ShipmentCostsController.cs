﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;
using Microsoft.Extensions.Options;
using Velociti.ERP.Domain.Utils;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ShipmentCostsController : ControllerBase
    {
        private readonly AppSettings _appSettings;
        private readonly IShipmentCostService _shipmentCostService;

        public ShipmentCostsController(IOptions<AppSettings> appSettings, IShipmentCostService shipmentCostService)
        {
            _shipmentCostService = shipmentCostService;
        }

        [HttpGet]
        [Route("Single/{shipmentCostId}")]
        public async Task<IActionResult> GetByIdAsync(int shipmentCostId)
        {
            return Ok(await _shipmentCostService.GetByIdAsync(shipmentCostId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAllAsync(int companyId, int userId, DateTime start, DateTime end)
        {
            return Ok(await _shipmentCostService.GetAllAsync(companyId, userId, start, end));
        }

        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]ShipmentCost shipmentCost)
        {
            await _shipmentCostService.SaveAsync(shipmentCost);

            return Ok();
        }

        [HttpPost]
        [Route("ShipmentCostLinesToPaymentAdvice")]
        public async Task<IActionResult> ConvertToPaymentAdviceAsync([FromBody]ShipmentCostLinesToConvert shipmentCostLinesToConvert)
        {
            if (shipmentCostLinesToConvert.ShipmentCostLineIds.Count() == 0)
                return BadRequest("Please select shipment cost lines to convert.");

            int paymentAdviceId = await _shipmentCostService.ConvertShipmentCostLinesToPaymentAdviceAsync(shipmentCostLinesToConvert);

            return Ok(paymentAdviceId);
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAsync([FromBody]ShipmentCost shipmentCost)
        {
            switch (shipmentCost.Action)
            {

                case "submit": await _shipmentCostService.SubmitAsync(shipmentCost); break;
                case "reverse": await _shipmentCostService.ReverseAsync(shipmentCost.ShipmentCostId, shipmentCost.ModifiedUserId.Value); break;
                case "cancel": await _shipmentCostService.CancelAsync(shipmentCost); break;
            }

            return Ok();
        }
    }
}