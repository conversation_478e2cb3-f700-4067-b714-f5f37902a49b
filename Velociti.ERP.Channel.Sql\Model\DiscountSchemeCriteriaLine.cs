﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("DiscountSchemeCriteriaLines", Schema = "sales")]
    public partial class DiscountSchemeCriteriaLine
    {
        [Key]
        public int DiscountSchemeCriteriaLineId { get; set; }
        public int DiscountSchemeId { get; set; }
        public int? ProductCategoryId { get; set; }
        public int? ProductHierarchyId2 { get; set; }
        public int? ProductBrandId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal BreakQuantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DiscountSchemeId))]
        [InverseProperty("DiscountSchemeCriteriaLines")]
        public virtual DiscountScheme DiscountScheme { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("DiscountSchemeCriteriaLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductBrandId))]
        [InverseProperty(nameof(SupportData.DiscountSchemeCriteriaLines))]
        public virtual SupportData ProductBrand { get; set; }
        [ForeignKey(nameof(ProductHierarchyId2))]
        [InverseProperty(nameof(ProductHierarchy.DiscountSchemeCriteriaLines))]
        public virtual ProductHierarchy ProductHierarchyId2Navigation { get; set; }
    }
}
