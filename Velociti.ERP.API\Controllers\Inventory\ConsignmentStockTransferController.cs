﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Entities.Inventory;
using Microsoft.AspNetCore.Authorization;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class ConsignmentStockTransferController : ControllerBase
    {
        private readonly IConsignmentStockTransferService _consignmentStockTranserService;
        public ConsignmentStockTransferController(IConsignmentStockTransferService consignmentStockTranserService)
        {

            _consignmentStockTranserService = consignmentStockTranserService;

        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte typeEnum)
        {
            return Ok(await _consignmentStockTranserService.GetAll(companyId, userId, startDate, endDate, typeEnum));
        }
        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/{productId}")]
        public async Task<IActionResult> GetProducts(int fromWarehouseId, int toWarehouseId, int productId)
        {
            var result = await _consignmentStockTranserService.GetStockByWarehouses(fromWarehouseId, toWarehouseId, productId);
            return Ok(result);
        }

        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/Barcode/{barcode}")]
        public async Task<IActionResult> GetProductByBarcode(int fromWarehouseId, int toWarehouseId, string barcode)
        {
            var result = await _consignmentStockTranserService.GetStockByBarcodeAsync(fromWarehouseId, toWarehouseId, barcode);
            return Ok(result);
        }

        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/Pallet/{palletCode}")]
        public async Task<IActionResult> GetProductsByPallet(int fromWarehouseId, int toWarehouseId, string palletCode)
        {
            var result = await _consignmentStockTranserService.GetStockByPalletAsync(fromWarehouseId, toWarehouseId, palletCode);
            return Ok(result);
        }
        [HttpGet]
        [Route("{transferOrderId}")]
        public async Task<IActionResult> GetById(int transferOrderId)
        {
            return Ok(await _consignmentStockTranserService.FindByIdAsync(transferOrderId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _consignmentStockTranserService.GetShortListAsync(companyId);

            return Ok(list);
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] ConsignmentStockTransfer stockTransfer)
        {
            return Ok(await _consignmentStockTranserService.SaveAsync(stockTransfer));
        }
        [HttpGet]
        [Route("Single/{consignmentStockTransferId}")]
        public async Task<IActionResult> GetStockTransfer(int consignmentStockTransferId)
        {
            return Ok(await _consignmentStockTranserService.GetStockTransfer(consignmentStockTransferId));
        }
        [HttpPut]
        public async Task<IActionResult> Update([FromBody] ConsignmentStockTransfer stockTransfer)
        {
            switch (stockTransfer.Action)
            {
                case "submit":
                    await _consignmentStockTranserService.SubmitStockTransfer(stockTransfer.ConsignmentStockTransferId, stockTransfer.ModifiedUserId.Value);
                    break;
                case "reject":
                    await _consignmentStockTranserService.RejectStockTransfer(stockTransfer.ConsignmentStockTransferId, stockTransfer.ModifiedUserId.Value);
                    break;
                case "approve":
                    await _consignmentStockTranserService.ApproveTransfer(stockTransfer.ConsignmentStockTransferId, stockTransfer.ModifiedUserId.Value);
                    break;
                case "convert":
                    return Ok(await _consignmentStockTranserService.ConvertAsync(stockTransfer));
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{stockTransferId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int stockTransferId, int userId)
        {
            await _consignmentStockTranserService.CancelAsync(stockTransferId, userId);
            return Ok();
        }
    }
}
