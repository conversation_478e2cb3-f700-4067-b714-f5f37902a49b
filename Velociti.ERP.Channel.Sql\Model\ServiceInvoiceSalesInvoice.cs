﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ServiceInvoiceSalesInvoices", Schema = "prc")]
    public partial class ServiceInvoiceSalesInvoice
    {
        [Key]
        public int ServiceInvoiceSalesInvoiceId { get; set; }
        public int ServiceInvoiceId { get; set; }
        public int SalesInvoiceId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(SalesInvoiceId))]
        [InverseProperty("ServiceInvoiceSalesInvoices")]
        public virtual SalesInvoice SalesInvoice { get; set; }
        [ForeignKey(nameof(ServiceInvoiceId))]
        [InverseProperty("ServiceInvoiceSalesInvoices")]
        public virtual ServiceInvoice ServiceInvoice { get; set; }
    }
}
