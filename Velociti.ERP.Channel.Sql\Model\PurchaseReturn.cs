﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseReturns", Schema = "prc")]
    public partial class PurchaseReturn
    {
        public PurchaseReturn()
        {
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            PurchaseReturnLines = new HashSet<PurchaseReturnLine>();
        }

        [Key]
        public int PurchaseReturnId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? SupplierId { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        [Column(TypeName = "money")]
        public decimal? SupplierDiscount { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? AdditionalCharges { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PurchaseReturns")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("PurchaseReturns")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("PurchaseReturns")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("PurchaseReturns")]
        public virtual Supplier Supplier { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.PurchaseReturn))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(PurchaseReturnLine.PurchaseReturn))]
        public virtual ICollection<PurchaseReturnLine> PurchaseReturnLines { get; set; }
        public decimal? TaxValueTotal { get; set; }
        //public string VATNo { get; set; }

    }
}
