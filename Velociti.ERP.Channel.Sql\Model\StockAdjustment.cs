﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockAdjustments", Schema = "inv")]
    public partial class StockAdjustment
    {
        public StockAdjustment()
        {
            StockAdjustmentLines = new HashSet<StockAdjustmentLine>();
        }

        [Key]
        public int StockAdjustmentId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? WarehouseId { get; set; }
        public int? ReasonId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? TxnWorkFlowId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
        public decimal Total { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("StockAdjustments")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("StockAdjustments")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("StockAdjustments")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.StockAdjustments))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(StockAdjustmentLine.StockAdjustment))]
        public virtual ICollection<StockAdjustmentLine> StockAdjustmentLines { get; set; }
    }
}
