﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("HOReturnStockTransferIntegrationLines", Schema = "inv")]
    public partial class HoreturnStockTransferIntegrationLine
    {
        [Key]
        [Column("HOReturnStockTransferIntegrationLineId")]
        public int HoreturnStockTransferIntegrationLineId { get; set; }
        [Column("HOReturnStockTransferId")]
        public int? HoreturnStockTransferId { get; set; }
        public int ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(32)]
        public string ItemBatchCode { get; set; }
        public int UnitQty { get; set; }
        [Column(TypeName = "money")]
        public decimal UnitPrice { get; set; }
        [Column(TypeName = "money")]
        public decimal UnitCost { get; set; }
        [Column(TypeName = "money")]
        public decimal GrossValue { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal LineDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal LineDisc { get; set; }
        [Column(TypeName = "money")]
        public decimal NetValue { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal TaxPct { get; set; }
        [Column(TypeName = "money")]
        public decimal TaxValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.HoreturnStockTransferIntegrationLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.HoreturnStockTransferIntegrationLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(HoreturnStockTransferId))]
        [InverseProperty("HoreturnStockTransferIntegrationLines")]
        public virtual HoreturnStockTransfer HoreturnStockTransfer { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("HoreturnStockTransferIntegrationLines")]
        public virtual Product Product { get; set; }
    }
}
