﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class PurchaseOrdersController : ControllerBase
    {
        private readonly IPurchaseOrderService _purchaseOrderService;

        public PurchaseOrdersController(IPurchaseOrderService purchaseOrderService)
        {
            _purchaseOrderService = purchaseOrderService;
        }

        [HttpGet]
        [Route("Single/{purchaseOrderId}")]
        public async Task<IActionResult> FindById(int purchaseOrderId)
        {
            return Ok(await _purchaseOrderService.FindByIdAsync(purchaseOrderId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte typeEnum)
        {
            return Ok(await _purchaseOrderService.GetAllAsync(companyId, userId, startDate, endDate, typeEnum));
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate, byte typeEnum)
        {
            return Ok(await _purchaseOrderService.GetForApprovalAsync(companyId, userId, startDate, endDate, typeEnum));
        }

        [HttpGet]
        [Route("{companyId}/User/{userId}")]
        public async Task<IActionResult> GetAllByUserId(int companyId, int userId)
        {
            return Ok(await _purchaseOrderService.GetAllByUserId(companyId, userId));
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Supplier/{supplierId}")]
        public async Task<IActionResult> GetShortList(int companyId, int supplierId)
        {
            return Ok(await _purchaseOrderService.GetShortListAsync(companyId, supplierId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]PurchaseOrder purchaseOrder)
        {
            switch(purchaseOrder.Action)
            {
                case "generate": await _purchaseOrderService.GeneratePurchaseOrderAsync(purchaseOrder); break;
                case "update": await _purchaseOrderService.SaveAsync(purchaseOrder); break;
                case "new": await _purchaseOrderService.SaveAsync(purchaseOrder); break;
            }
            
            return Ok();
        }

        [HttpDelete]
        [Route("{purchaseOrderId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int purchaseOrderId, int userId)
        {
            await _purchaseOrderService.CancelAsync(purchaseOrderId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]PurchaseOrder purchaseOrder)
        {
            switch (purchaseOrder.Action)
            {
                case "submit": await _purchaseOrderService.SubmitAsync(purchaseOrder.PurchaseOrderId, purchaseOrder.ModifiedUserId.Value); break;
                case "copy": await _purchaseOrderService.CopyAsync(purchaseOrder.PurchaseOrderId, purchaseOrder.ModifiedUserId.Value); break;
                case "update status": await _purchaseOrderService.UpdateStatusAsync(purchaseOrder.PurchaseOrderId, purchaseOrder.StatusEnum.Value, purchaseOrder.ExpectedDate,  purchaseOrder.ModifiedUserId.Value); break;
                case "reverse": await _purchaseOrderService.ReverseAsync(purchaseOrder.PurchaseOrderId, purchaseOrder.ModifiedUserId); break;
                case "convert to service invoice": return Ok(await _purchaseOrderService.ConvertAsync(purchaseOrder));
                case "convert to grn": return Ok(await _purchaseOrderService.ConvertToGRNAsync(purchaseOrder.PurchaseOrderId, purchaseOrder.ModifiedUserId.Value));
                case "send for approval": await _purchaseOrderService.SendForApprovalAsync(purchaseOrder.PurchaseOrderId, purchaseOrder.CompanyId.Value, purchaseOrder.ModifiedUserId.Value, purchaseOrder.TypeEnum.Value); break;
                case "update work flow": await _purchaseOrderService.UpdateWorkFlowStatusAsync(purchaseOrder); break;
            }

            return Ok();
        }
    }
}