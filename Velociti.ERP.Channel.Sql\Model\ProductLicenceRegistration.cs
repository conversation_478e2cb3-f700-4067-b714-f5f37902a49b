﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductLicenceRegistration", Schema = "inv")]
    public partial class ProductLicenceRegistration
    {
        [Key]
        public int LicenceRegistrationId { get; set; }
        public int? ProductId { get; set; }
        [StringLength(50)]
        public string LicenseRegistrationNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DateOfIssue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [StringLength(15)]
        public string LicenseRegistrationStatus { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DateOfExpiry { get; set; }
        public int? NotificationDateCount { get; set; }
        public byte? LicenseTypeEnum { get; set; }
        public int? CompanyId { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ProductLicenceRegistrations")]
        public virtual Product Product { get; set; }
    }
}
