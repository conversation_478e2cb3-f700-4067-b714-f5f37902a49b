﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class StockCountSheetsController : ControllerBase
    {
        private readonly IStockCountSheetsService _stockCountSheetsService;

        public StockCountSheetsController(IStockCountSheetsService stockCountSheetsService)
        {
            _stockCountSheetsService = stockCountSheetsService;
        }

        [HttpGet]
        [Route("Single/{stockCountSheetId}")]
        public async Task<IActionResult> FindById(int stockCountSheetId)
        {
            return Ok(await _stockCountSheetsService.FindByIdAsync(stockCountSheetId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _stockCountSheetsService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]StockCountSheet stockCountSheet)
        {
            switch (stockCountSheet.Action)
            {
                case "save": await _stockCountSheetsService.SaveAsync(stockCountSheet); break;
            }
            

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]StockCountSheet stockCountSheet)
        {
            switch (stockCountSheet.Action)
            {
                case "submit": await _stockCountSheetsService.SubmitAsync(stockCountSheet.StockCountSheetId, stockCountSheet.ModifiedUserId.Value);break;
                case "convert to sales invoice": await _stockCountSheetsService.ConvertToSalesInvoiceAsync(stockCountSheet.StockCountSheetId, stockCountSheet.ModifiedUserId.Value); break;
                case "reverse": await _stockCountSheetsService.ReverseAsync(stockCountSheet); break;

            }

            return Ok();
        }
    }
}