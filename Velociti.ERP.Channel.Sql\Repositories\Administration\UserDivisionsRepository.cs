﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class UserDivisionsRepository : IUserDivisionsRepository
    {
        private readonly MarangoniERPContext _context;

        public UserDivisionsRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<UserDivision>> GetAssignedDivisionsAsync(int userId)
        {
            return await _context.UserDivisions.Include(p => p.Division).Include(p => p.Division.Department).Include(p => p.Division.Department.Company).Where(p => p.UserId == userId).ToListAsync();
        }

        public async Task SaveAsync(int departmentId, IEnumerable<UserDivision> userDivisions)
        {
            try
            {
                foreach (var row in userDivisions)
                {
                    if (row.IsDefault == true)
                    {
                        var defaultDivision = await _context.UserDivisions.SingleOrDefaultAsync(p => p.UserId == row.UserId && p.Division.DepartmentId == departmentId && p.IsDefault == true);
                        if (defaultDivision != null)
                        {
                            defaultDivision.IsDefault = false;
                            defaultDivision.ModifiedUserId = row.ModifiedUserId;
                        }
                    }

                    row.CreationDate = DateTime.Now;
                    await _context.UserDivisions.AddAsync(row);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        public async Task ToggleDefaultAsync(int userDivisionId)
        {
            var record = await _context.UserDivisions.Include(p=> p.Division).FirstAsync(c => c.UserDivisionId == userDivisionId);
            record.IsDefault = !record.IsDefault;

            if (record.IsDefault == true)
            {
                var defaultDivision = await _context.UserDivisions.SingleOrDefaultAsync(p => p.UserId == record.UserId && p.Division.DepartmentId == record.Division.DepartmentId && p.IsDefault == true);
                if (defaultDivision != null)
                {
                    defaultDivision.IsDefault = false;
                }
            }

            await _context.SaveChangesAsync();
        }

        public async Task ToggleActivationAsync(int userDivisionId)
        {
            var record = await _context.UserDivisions.FirstAsync(c => c.UserDivisionId == userDivisionId);
            if (record.ExpiryDate == null && record.IsDefault == true)
                throw new Exception("Cannot deactivate default record.");

            record.ExpiryDate = record.ExpiryDate == null ? DateTime.Now : (DateTime?)null;

            await _context.SaveChangesAsync();
        }
    }
}
