﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Finance;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class GoodsReceivedNoteLinesController : ControllerBase
    {
        private readonly IGoodsReceivedNoteLineService _goodsReceivedNoteLineService;
        private readonly ITaxGroupService _taxGroupService;

        public GoodsReceivedNoteLinesController(IGoodsReceivedNoteLineService goodsReceivedNoteLineService, ITaxGroupService taxGroupService)
        {
            _goodsReceivedNoteLineService = goodsReceivedNoteLineService;
            _taxGroupService = taxGroupService;
        }

        [HttpGet]
        [Route("{grnId}/company/{companyId}")]
        public async Task<IActionResult> Get(int grnId, int companyId)
        {
            IEnumerable<GoodsReceiveNoteLine> goodsReceiveNoteLines = await _goodsReceivedNoteLineService.GetAsync(grnId, companyId);

            return Ok(goodsReceiveNoteLines);
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]GoodsReceiveNoteLine goodsReceiveNoteLine)
        {
            await _goodsReceivedNoteLineService.UpdateAsync(goodsReceiveNoteLine);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _goodsReceivedNoteLineService.DeleteAsync(id);

            return Ok();
        }
    }
}