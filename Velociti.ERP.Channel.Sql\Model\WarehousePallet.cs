﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("WarehousePallets", Schema = "inv")]
    public partial class WarehousePallet
    {
        [Key]
        public int WarehousePalletId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string WarehousePalletCode { get; set; }
        [StringLength(150)]
        public string WarehousePalletDescription { get; set; }
        public int? SequenceNumber { get; set; }
        public bool? IsBarcodePrinted { get; set; }
        [StringLength(15)]
        public string GeneratedLocation { get; set; }
        [Column(TypeName = "date")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("WarehousePallets")]
        public virtual Company Company { get; set; }
    }
}
