﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class FinancialStatementLineDetailsController : ControllerBase    
    {
        private readonly IFinancialStatementLineDetailService _financialStatementLineDetailService;

        public FinancialStatementLineDetailsController(IFinancialStatementLineDetailService financialStatementLineDetailService)
        {
            _financialStatementLineDetailService = financialStatementLineDetailService;
        }

        [HttpGet]
        [Route("{financialStatementLineId}")]
        public async Task<IActionResult> Get(int financialStatementLineId)    
        {
            return Ok(await _financialStatementLineDetailService.GetAsync(financialStatementLineId));
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _financialStatementLineDetailService.DeleteAsync(id);

            return Ok();
        }
    }
}