﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class PermissionsController : ControllerBase
    {
        private readonly IPermissionService _permissionService;

        public PermissionsController(IPermissionService permissionService)
        {
            _permissionService = permissionService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _permissionService.GetAllAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]List<Permission> permissions)
        {
            if (permissions == null)
                return BadRequest();

            await _permissionService.SaveAsync(permissions);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _permissionService.DeleteAsync(id);

            return Ok();
        }
    }
}