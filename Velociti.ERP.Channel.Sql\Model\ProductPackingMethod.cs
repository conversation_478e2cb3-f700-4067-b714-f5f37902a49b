﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductPackingMethods", Schema = "inv")]
    public partial class ProductPackingMethod
    {
        [Key]
        public int ProductPackingMethodId { get; set; }
        public int? ProductId { get; set; }
        public int? ShipmentPackingMethodId { get; set; }
        public int? UnitQty { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ProductPackingMethods")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ShipmentPackingMethodId))]
        [InverseProperty(nameof(SupportData.ProductPackingMethods))]
        public virtual SupportData ShipmentPackingMethod { get; set; }
    }
}
