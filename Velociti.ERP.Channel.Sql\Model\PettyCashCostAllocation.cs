﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PettyCashCostAllocations", Schema = "fin")]
    public partial class PettyCashCostAllocation
    {
        [Key]
        public int PettyCashCostAllocationId { get; set; }
        public int? PettyCashLineId { get; set; }
        public int? DepartmentId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ApportionPct { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }

        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("PettyCashCostAllocations")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("PettyCashCostAllocations")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
        [ForeignKey(nameof(PettyCashLineId))]
        [InverseProperty("PettyCashCostAllocations")]
        public virtual PettyCashLine PettyCashLine { get; set; }
    }
}
