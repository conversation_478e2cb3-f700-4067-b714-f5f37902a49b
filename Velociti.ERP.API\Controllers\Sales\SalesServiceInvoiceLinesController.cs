﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [ApiController]
    public class SalesServiceInvoiceLinesController : ControllerBase
    {
        private readonly ISalesServiceInvoiceLineService serviceInvoiceLineService;

        public SalesServiceInvoiceLinesController(ISalesServiceInvoiceLineService serviceInvoiceLineService)
        {
            this.serviceInvoiceLineService = serviceInvoiceLineService;
        }

        [HttpGet]
        [Route("{salesInvoiceId}/Company/{companyId}")]
        public async Task<IActionResult> GetAll(int salesInvoiceId, int companyId)
        {
           return Ok(await this.serviceInvoiceLineService.GetByHeaderIdAsync(companyId, salesInvoiceId));
        }
    }
}