﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SubContractOrders", Schema = "prc")]
    public partial class SubContractOrder
    {
        public SubContractOrder()
        {
            SubContractOrderLines = new HashSet<SubContractOrderLine>();
        }

        [Key]
        public int SubContractOrderId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        public int? SupplierId { get; set; }
        public int? CustomerId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? ProductId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? BillOfMaterialId { get; set; }
        public int? Quantity { get; set; }
        [StringLength(255)]
        public string DeliveryAddress { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfMaterialId))]
        [InverseProperty("SubContractOrders")]
        public virtual BillOfMaterial BillOfMaterial { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SubContractOrders")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("SubContractOrders")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("SubContractOrders")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.SubContractOrders))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SubContractOrders")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("SubContractOrders")]
        public virtual Supplier Supplier { get; set; }
        [InverseProperty(nameof(SubContractOrderLine.SubContractOrder))]
        public virtual ICollection<SubContractOrderLine> SubContractOrderLines { get; set; }
    }
}
