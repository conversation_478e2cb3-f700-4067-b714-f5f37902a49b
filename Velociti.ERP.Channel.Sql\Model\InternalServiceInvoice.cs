﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalServiceInvoices", Schema = "prc")]
    public partial class InternalServiceInvoice
    {
        public InternalServiceInvoice()
        {
            InternalServiceInvoiceLines = new HashSet<InternalServiceInvoiceLine>();
            ServiceInvoiceMatarialRequestNotes = new HashSet<ServiceInvoiceMatarialRequestNote>();
        }

        [Key]
        public int InternalServiceInvoiceId { get; set; }
        public int CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        public int? EmployeeId { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? PostDate { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? Cost { get; set; }
        [StringLength(500)]
        public string Reason { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("InternalServiceInvoices")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("InternalServiceInvoices")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("InternalServiceInvoices")]
        public virtual Employee Employee { get; set; }
        [InverseProperty(nameof(InternalServiceInvoiceLine.InternalServiceInvoice))]
        public virtual ICollection<InternalServiceInvoiceLine> InternalServiceInvoiceLines { get; set; }
        [InverseProperty(nameof(ServiceInvoiceMatarialRequestNote.InternalServiceInvoice))]
        public virtual ICollection<ServiceInvoiceMatarialRequestNote> ServiceInvoiceMatarialRequestNotes { get; set; }
    }
}
