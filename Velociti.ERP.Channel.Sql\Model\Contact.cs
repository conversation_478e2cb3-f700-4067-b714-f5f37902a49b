﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Contacts", Schema = "cmn")]
    public partial class Contact
    {
        public Contact()
        {
            CustomerContacts = new HashSet<CustomerContact>();
            EmployeeContacts = new HashSet<EmployeeContact>();
            SupplementaryManufacturerContacts = new HashSet<SupplementaryManufacturerContact>();
            SupplierContacts = new HashSet<SupplierContact>();
        }

        [Key]
        public int ContactId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(255)]
        public string FullName { get; set; }
        [StringLength(255)]
        public string StreetAdress { get; set; }
        [StringLength(150)]
        public string Telephone1 { get; set; }
        [StringLength(50)]
        public string Telephone2 { get; set; }
        [StringLength(50)]
        public string Mobile { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string Notes { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Contacts")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(CustomerContact.Contact))]
        public virtual ICollection<CustomerContact> CustomerContacts { get; set; }
        [InverseProperty(nameof(EmployeeContact.Contact))]
        public virtual ICollection<EmployeeContact> EmployeeContacts { get; set; }
        [InverseProperty(nameof(SupplementaryManufacturerContact.Contact))]
        public virtual ICollection<SupplementaryManufacturerContact> SupplementaryManufacturerContacts { get; set; }
        [InverseProperty(nameof(SupplierContact.Contact))]
        public virtual ICollection<SupplierContact> SupplierContacts { get; set; }
    }
}
