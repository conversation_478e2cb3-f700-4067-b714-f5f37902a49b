﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Tyre_Specification")]
    public partial class ITyreSpecification
    {
        [StringLength(255)]
        public string Code { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        [StringLength(255)]
        public string GrossWeight { get; set; }
        [StringLength(255)]
        public string NetWeight { get; set; }
        [Column("DESIGN")]
        [StringLength(255)]
        public string Design { get; set; }
        [StringLength(255)]
        public string Measure { get; set; }
        [Column("RIMSIZE")]
        [StringLength(255)]
        public string Rimsize { get; set; }
        [Column("STUCTURE")]
        [StringLength(255)]
        public string Stucture { get; set; }
        [StringLength(255)]
        public string Nooftyresperpallet { get; set; }
        [StringLength(255)]
        public string NoOfTyresperbox { get; set; }
    }
}
