﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BundleProductLines", Schema = "inv")]
    public partial class BundleProductLine
    {
        [Key]
        public int BundleProductLineId { get; set; }
        public int? BundleProductId { get; set; }
        public int? ProductId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BundleProductId))]
        [InverseProperty("BundleProductLines")]
        public virtual BundleProduct BundleProduct { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.BundleProductLines))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("BundleProductLines")]
        public virtual Product Product { get; set; }
    }
}
