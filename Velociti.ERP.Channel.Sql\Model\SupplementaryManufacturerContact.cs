﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SupplementaryManufacturerContacts", Schema = "prc")]
    public partial class SupplementaryManufacturerContact
    {
        [Key]
        public int SupplementaryManufacturerId { get; set; }
        [Key]
        public int ContactId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ContactId))]
        [InverseProperty("SupplementaryManufacturerContacts")]
        public virtual Contact Contact { get; set; }
        [ForeignKey(nameof(SupplementaryManufacturerId))]
        [InverseProperty("SupplementaryManufacturerContacts")]
        public virtual SupplementaryManufacturer SupplementaryManufacturer { get; set; }
    }
}
