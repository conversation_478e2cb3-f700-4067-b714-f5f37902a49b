﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class EmailTemplateLinesController : ControllerBase
    {
        private readonly IEmailTemplateLineService _emailTemplateLineService;
        
        public EmailTemplateLinesController(IEmailTemplateLineService emailTemplateLineService)
        {
            _emailTemplateLineService = emailTemplateLineService;
        }

        [HttpGet]
        [Route("{emailTemplateId}")]
        public async Task<IActionResult> GetByHeaderId(int emailTemplateId)
        {
            return Ok(await _emailTemplateLineService.GetByHeaderIdAsync(emailTemplateId));
        }

        [HttpDelete]
        [Route("{id}/loggedInUser/{loggedInUserId}")]
        public async Task<IActionResult> ToggleActivation(int id, int loggedInUserId)
        {
            await _emailTemplateLineService.ToggleActivationAsync(id, loggedInUserId);

            return Ok();
        }
    }
}