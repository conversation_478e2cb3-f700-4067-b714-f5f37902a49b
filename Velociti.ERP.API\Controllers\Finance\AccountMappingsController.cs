﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class AccountMappingsController : ControllerBase
    {
        private readonly IAccountMappingService _accountMappingService;

        public AccountMappingsController(IAccountMappingService accountMappingService)
        {
            _accountMappingService = accountMappingService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            var records = await _accountMappingService.GetAllAsync(companyId);

            return Ok(records);
        }

        [HttpGet]
        [Route("TransactionDocType/{transactionDocTypeEnum}/Company/{companyId}")]
        public async Task<IActionResult> GetByTransactionDocType(byte transactionDocTypeEnum, int companyId)
        {
            var records = await _accountMappingService.GetByTransactionDocTypeAsync(transactionDocTypeEnum, companyId);

            return Ok(records);
        }
    }
}