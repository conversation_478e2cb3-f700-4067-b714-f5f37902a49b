﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class DocCodeConfigController : ControllerBase
    {
        private readonly IDocCodeConfigService _docCodeConfigService;
        private readonly IDocTypeConfigService _docTypeConfigService;

        public DocCodeConfigController(IDocCodeConfigService docCodeConfigService, IDocTypeConfigService docTypeConfigService)
        {
            _docCodeConfigService = docCodeConfigService;
            _docTypeConfigService = docTypeConfigService;
        }

        [HttpGet]
        public async Task<IActionResult> Get()
        {
            return Ok(await _docCodeConfigService.GetAllAsync());
        }

        [HttpGet]
        [Route("ModuleShortList")]
        public async Task<IActionResult> GetModuleShortList()
        {
            return Ok(await _docTypeConfigService.GetModuleShortListAsync());
        }

        [HttpGet]
        [Route("DocumentType/{documentType}/Company/{companyId}")]
        public async Task<IActionResult> GetNextSequenceNumberAsync(Module.DocumentType documentType, int companyId)
        {
            return Ok(await _docCodeConfigService.GetNextSequenceNumberAsync(documentType, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]DocCodeConfig docCodeConfig)
        {
            if (docCodeConfig == null)
                return BadRequest();

            await _docCodeConfigService.SaveAsync(docCodeConfig);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/user/{userId}")]
        public async Task<IActionResult> ToggleActivation(int id, int userId)
        {
            await _docCodeConfigService.ToggleActivationAsync(id, userId);

            return Ok();
        }
    }
}