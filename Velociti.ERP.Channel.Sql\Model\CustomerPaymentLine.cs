﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("CustomerPaymentLines", Schema = "sales")]
    public partial class CustomerPaymentLine
    {
        [Key]
        public int CustomerPaymentLineId { get; set; }
        public int? CustomerPaymentId { get; set; }
        public int? GeneralLedgerLineId { get; set; }
        [Column(TypeName = "money")]
        public decimal? BalanceAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? AmountInBaseCurrency { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocumentAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? SettlementValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? SettledAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? Discount { get; set; }
        [Column(TypeName = "money")]
        public decimal? CrossPaidAmount { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DueDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CustomerPaymentId))]
        [InverseProperty("CustomerPaymentLines")]
        public virtual CustomerPayment CustomerPayment { get; set; }
        [ForeignKey(nameof(GeneralLedgerLineId))]
        [InverseProperty("CustomerPaymentLines")]
        public virtual GeneralLedgerLine GeneralLedgerLine { get; set; }
    }
}
