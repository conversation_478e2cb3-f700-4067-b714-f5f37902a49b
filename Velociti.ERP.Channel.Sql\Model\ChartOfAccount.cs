﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ChartOfAccounts", Schema = "fin")]
    public partial class ChartOfAccount
    {
        public ChartOfAccount()
        {
            AccountMappingLineDetails = new HashSet<AccountMappingLineDetail>();
            AccountMappingLines = new HashSet<AccountMappingLine>();
            BankAdjustmentLines = new HashSet<BankAdjustmentLine>();
            CoamappingChildChartOfAccounts = new HashSet<Coamapping>();
            CoamappingParentChartOfAccounts = new HashSet<Coamapping>();
            FinancialStatementLineDetails = new HashSet<FinancialStatementLineDetail>();
            GeneralLedgerLines = new HashSet<GeneralLedgerLine>();
            ManualJournalLines = new HashSet<ManualJournalLine>();
            PaymentAdviceLines = new HashSet<PaymentAdviceLine>();
            PettyCashLines = new HashSet<PettyCashLine>();
        }

        [Key]
        public int ChartOfAccountId { get; set; }
        [Required]
        [StringLength(50)]
        public string ChartOfAccountCode { get; set; }
        [Required]
        [StringLength(255)]
        public string ChartOfAccountName { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(1)]
        public string PrefixChar { get; set; }
        public byte? TypeEnum { get; set; }
        public bool? IsBudgetAccount { get; set; }
        public bool? IsControlAccount { get; set; }
        public byte? ControlSegmentEnum { get; set; }
        [StringLength(50)]
        public string AnalysisCode { get; set; }
        public int? ParentId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ChartOfAccounts")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(AccountMappingLineDetail.ChartOfAccount))]
        public virtual ICollection<AccountMappingLineDetail> AccountMappingLineDetails { get; set; }
        [InverseProperty(nameof(AccountMappingLine.ChartOfAccount))]
        public virtual ICollection<AccountMappingLine> AccountMappingLines { get; set; }
        [InverseProperty(nameof(BankAdjustmentLine.ChartOfAccount))]
        public virtual ICollection<BankAdjustmentLine> BankAdjustmentLines { get; set; }
        [InverseProperty(nameof(Coamapping.ChildChartOfAccount))]
        public virtual ICollection<Coamapping> CoamappingChildChartOfAccounts { get; set; }
        [InverseProperty(nameof(Coamapping.ParentChartOfAccount))]
        public virtual ICollection<Coamapping> CoamappingParentChartOfAccounts { get; set; }
        [InverseProperty(nameof(FinancialStatementLineDetail.ChartOfAccount))]
        public virtual ICollection<FinancialStatementLineDetail> FinancialStatementLineDetails { get; set; }
        [InverseProperty(nameof(GeneralLedgerLine.ChartOfAccount))]
        public virtual ICollection<GeneralLedgerLine> GeneralLedgerLines { get; set; }
        [InverseProperty(nameof(ManualJournalLine.ChartOfAccount))]
        public virtual ICollection<ManualJournalLine> ManualJournalLines { get; set; }
        [InverseProperty(nameof(PaymentAdviceLine.ChartOfAccount))]
        public virtual ICollection<PaymentAdviceLine> PaymentAdviceLines { get; set; }
        [InverseProperty(nameof(PettyCashLine.ChartOfAccount))]
        public virtual ICollection<PettyCashLine> PettyCashLines { get; set; }
    }
}
