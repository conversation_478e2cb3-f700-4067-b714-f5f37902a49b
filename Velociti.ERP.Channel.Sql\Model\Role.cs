﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Roles", Schema = "adm")]
    public partial class Role
    {
        public Role()
        {
            GroupRoles = new HashSet<GroupRole>();
            Permissions = new HashSet<Permission>();
            UserRoles = new HashSet<UserRole>();
        }

        [Key]
        public int RoleId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(100)]
        public string RoleName { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Roles")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(GroupRole.Role))]
        public virtual ICollection<GroupRole> GroupRoles { get; set; }
        [InverseProperty(nameof(Permission.Role))]
        public virtual ICollection<Permission> Permissions { get; set; }
        [InverseProperty(nameof(UserRole.Role))]
        public virtual ICollection<UserRole> UserRoles { get; set; }
    }
}
