﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("OutboundPaymentLines", Schema = "fin")]
    public partial class OutboundPaymentLine
    {
        public OutboundPaymentLine()
        {
            OutboundPaymentCostAllocations = new HashSet<OutboundPaymentCostAllocation>();
        }

        [Key]
        public int OutboundPaymentLineId { get; set; }
        public int? OutboundPaymentId { get; set; }
        public int? PaymentAdviceId { get; set; }
        public int? GeneralLedgerLineId { get; set; }
        [Column(TypeName = "money")]
        public decimal? BalanceAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? AmountInBaseCurrency { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocumentAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? SettlementValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? Discount { get; set; }
        [Column(TypeName = "money")]
        public decimal? CrossPaidAmount { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DueDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(GeneralLedgerLineId))]
        [InverseProperty("OutboundPaymentLines")]
        public virtual GeneralLedgerLine GeneralLedgerLine { get; set; }
        [ForeignKey(nameof(OutboundPaymentId))]
        [InverseProperty("OutboundPaymentLines")]
        public virtual OutboundPayment OutboundPayment { get; set; }
        [ForeignKey(nameof(PaymentAdviceId))]
        [InverseProperty("OutboundPaymentLines")]
        public virtual PaymentAdvice PaymentAdvice { get; set; }
        [InverseProperty(nameof(OutboundPaymentCostAllocation.OutboundPaymentLine))]
        public virtual ICollection<OutboundPaymentCostAllocation> OutboundPaymentCostAllocations { get; set; }
    }
}
