﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductDies", Schema = "man")]
    public partial class ProductDy
    {
        [Key]
        public int ProductDieId { get; set; }
        public int? CompanyId { get; set; }
        public int? ProductId { get; set; }
        public int? DieId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ProductDies")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DieId))]
        [InverseProperty(nameof(SupportData.ProductDies))]
        public virtual SupportData Die { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ProductDies")]
        public virtual Product Product { get; set; }
    }
}
