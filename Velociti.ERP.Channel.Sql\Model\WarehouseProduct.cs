﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("WarehouseProducts", Schema = "inv")]
    public partial class WarehouseProduct
    {
        public WarehouseProduct()
        {
            StockAllocations = new HashSet<StockAllocation>();
        }

        [Key]
        public int WarehouseProductId { get; set; }
        public int WarehouseId { get; set; }
        public int? CompanyId { get; set; }
        public int ProductId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int UnitOfMeasureId { get; set; }
        public long? AllocatedQuantity { get; set; }
        public decimal? OnHandQuantity { get; set; }
        public long? PhysicalQuantity { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ProductExpiryDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? WarrantyDate { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public byte? Grade { get; set; }
        [StringLength(50)]
        public string PalletCode { get; set; }
        [Column("QCParameters")]
        [StringLength(255)]
        public string Qcparameters { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("WarehouseProducts")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("WarehouseProducts")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(UnitOfMeasureId))]
        [InverseProperty("WarehouseProducts")]
        public virtual UnitOfMeasure UnitOfMeasure { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.WarehouseProducts))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(StockAllocation.WarehouseProduct))]
        public virtual ICollection<StockAllocation> StockAllocations { get; set; }
    }
}
