﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalServiceInvoiceLineDetails", Schema = "prc")]
    public partial class InternalServiceInvoiceLineDetail
    {
        [Key]
        public int InternalServiceInvoiceLineDetailId { get; set; }
        public int? InternalServiceInvoiceLineId { get; set; }
        public int? ProductId { get; set; }
        public int? UnitOfMeasureId { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(InternalServiceInvoiceLineId))]
        [InverseProperty("InternalServiceInvoiceLineDetails")]
        public virtual InternalServiceInvoiceLine InternalServiceInvoiceLine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("InternalServiceInvoiceLineDetails")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(UnitOfMeasureId))]
        [InverseProperty("InternalServiceInvoiceLineDetails")]
        public virtual UnitOfMeasure UnitOfMeasure { get; set; }
    }
}
