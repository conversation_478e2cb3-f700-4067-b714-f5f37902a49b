﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ModuleCavities", Schema = "man")]
    public partial class ModuleCavity
    {
        [Key]
        public int CavityId { get; set; }
        public int MouldId { get; set; }
        [StringLength(50)]
        public string CavityCode { get; set; }
        [StringLength(255)]
        public string CavityDescription { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }

        [ForeignKey(nameof(MouldId))]
        [InverseProperty("ModuleCavities")]
        public virtual Mould Mould { get; set; }
    }
}
