﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BankAdjustments", Schema = "fin")]
    public partial class BankAdjustment
    {
        public BankAdjustment()
        {
            BankAdjustmentLines = new HashSet<BankAdjustmentLine>();
        }

        [Key]
        public int BankAdjustmentId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime DocDate { get; set; }
        public int? CompanyId { get; set; }
        public byte? StatusEnum { get; set; }
        public int? AccountId { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalAmount { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("BankAdjustments")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("BankAdjustments")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("BankAdjustments")]
        public virtual Department Department { get; set; }
        [InverseProperty(nameof(BankAdjustmentLine.BankAdjustment))]
        public virtual ICollection<BankAdjustmentLine> BankAdjustmentLines { get; set; }
    }
}
