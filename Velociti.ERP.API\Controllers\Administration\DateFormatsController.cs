﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class DateFormatsController : ControllerBase
    {
        private readonly IDateFormatService _dateFormatService;

        public DateFormatsController(IDateFormatService dateFormatService)
        {
            _dateFormatService = dateFormatService;
        }

        [HttpGet]
        [Route("ShortList")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetShortList()
        {
            var list = await _dateFormatService.GetShortListAsync();

            return Ok(list);
        }
    }
}