﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class MaterialRequisitionNotesController : ControllerBase
    {
        private readonly IMaterialRequisitionNoteService _materialRequisitionNoteService;

        public MaterialRequisitionNotesController(IMaterialRequisitionNoteService materialRequisitionNoteService)
        {
            _materialRequisitionNoteService = materialRequisitionNoteService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _materialRequisitionNoteService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _materialRequisitionNoteService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortListByStatus/{companyId}/status/{statusEnum}")]
        public async Task<IActionResult> GetShortListByStatus(int companyId, byte statusEnum)
        {
            var list = await _materialRequisitionNoteService.GetShortListByStatusAsync(companyId, statusEnum);

            return Ok(list);
        }

        [HttpGet]
        [Route("Single/{id}")]
        public async Task<IActionResult> FindById(int id)
        {
            return Ok(await _materialRequisitionNoteService.FindByIdAsync(id));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]MaterialRequisitionNote materialRequisitionNote)
        {
            await _materialRequisitionNoteService.SaveAsync(materialRequisitionNote);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]MaterialRequisitionNote materialRequisitionNote)
        {
            switch (materialRequisitionNote.Action)
            {
                case "submit": await _materialRequisitionNoteService.SubmitAsync(materialRequisitionNote.MaterialRequisitionNoteId, materialRequisitionNote.ModifiedUserId.Value); break;
                case "complete": await _materialRequisitionNoteService.CompleteAsync(materialRequisitionNote.MaterialRequisitionNoteId, materialRequisitionNote.ModifiedUserId.Value); break;
                case "convert to PRN": return Ok(await _materialRequisitionNoteService.ConvertToPRNAsync(materialRequisitionNote.MaterialRequisitionNoteId, materialRequisitionNote.ModifiedUserId.Value));
                case "convertToID": return Ok(await _materialRequisitionNoteService.ConvertToIDAsync(materialRequisitionNote.MaterialRequisitionNoteId, materialRequisitionNote.ModifiedUserId.Value));
                case "reverse": await _materialRequisitionNoteService.ReverseAsync(materialRequisitionNote.MaterialRequisitionNoteId, materialRequisitionNote.ModifiedUserId); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/User/{userId}")]
        public async Task<IActionResult> Cancel(int id, int userId)
        {
            await _materialRequisitionNoteService.CancelAsync(id, userId);

            return Ok();
        }
    }
}