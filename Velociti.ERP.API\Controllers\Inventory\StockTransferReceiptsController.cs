﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class StockTransferReceiptsController : ControllerBase  
    {
        private readonly IStockTransferReceiptService _stockTransferReceiptService;    

        public StockTransferReceiptsController(IStockTransferReceiptService stockTransferReceiptService)
        {
            _stockTransferReceiptService = stockTransferReceiptService;
        }

        [HttpGet]
        [Route("Single/{internalReturnId}")]
        public async Task<IActionResult> FindById(int internalReturnId) 
        {
            return Ok(await _stockTransferReceiptService.FindByIdAsync(internalReturnId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _stockTransferReceiptService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]StockTransferReceipt stockTransferReceipt)
        {
            await _stockTransferReceiptService.SaveAsync(stockTransferReceipt);

            return Ok();
        }

        [HttpDelete]
        [Route("{stockTransferReceiptId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int stockTransferReceiptId, int userId)  
        {
            await _stockTransferReceiptService.CancelAsync(stockTransferReceiptId, userId);  

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]StockTransferReceipt stockTransferReceipt)
        {
            switch (stockTransferReceipt.Action)
            {
                case "submit": await _stockTransferReceiptService.SubmitAsync(stockTransferReceipt.StockTransferReceiptId, stockTransferReceipt.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}