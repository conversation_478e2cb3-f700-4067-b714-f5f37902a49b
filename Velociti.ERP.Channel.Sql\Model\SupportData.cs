﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SupportData", Schema = "cmn")]
    public partial class SupportData
    {
        public SupportData()
        {
            BillOfMaterials = new HashSet<BillOfMaterial>();
            BillOfOperationLineOverheads = new HashSet<BillOfOperationLineOverhead>();
            ChargeInfoChargeGroups = new HashSet<ChargeInfoChargeGroup>();
            CustomerCreditPeriods = new HashSet<Customer>();
            CustomerPriceLists = new HashSet<Customer>();
            DiscountSchemeCriteriaLines = new HashSet<DiscountSchemeCriteriaLine>();
            DiscountSchemeCustomerGroups = new HashSet<DiscountScheme>();
            DiscountSchemeCustomerTypes = new HashSet<DiscountScheme>();
            DiscountSchemeLines = new HashSet<DiscountSchemeLine>();
            ExchangeOrderLines = new HashSet<ExchangeOrderLine>();
            ExchangeOrders = new HashSet<ExchangeOrder>();
            FixedAssetAssetGroups = new HashSet<FixedAsset>();
            FixedAssetAssetLocations = new HashSet<FixedAsset>();
            FixedAssetAssetTypes = new HashSet<FixedAsset>();
            FreeIssueCriteriaLines = new HashSet<FreeIssueCriteriaLine>();
            FreeIssueCustomerGroups = new HashSet<FreeIssue>();
            FreeIssueCustomerTypes = new HashSet<FreeIssue>();
            FreeIssueLines = new HashSet<FreeIssueLine>();
            GoodsDispatchNotePaymentTerms = new HashSet<GoodsDispatchNote>();
            GoodsDispatchNoteShipmentPackingMethods = new HashSet<GoodsDispatchNote>();
            InternalDispatches = new HashSet<InternalDispatch>();
            LeaveConfigurationEmployeeCategories = new HashSet<LeaveConfiguration>();
            LeaveConfigurationEmployeeGroups = new HashSet<LeaveConfiguration>();
            LoanOrderLines = new HashSet<LoanOrderLine>();
            MachineLines = new HashSet<MachineLine>();
            MaterialRequisitionNoteFixedAssetGroups = new HashSet<MaterialRequisitionNote>();
            MaterialRequisitionNoteRequestReasons = new HashSet<MaterialRequisitionNote>();
            Moulds = new HashSet<Mould>();
            PriceLists = new HashSet<PriceList>();
            ProductBrandDiscounts = new HashSet<ProductBrandDiscount>();
            ProductBrands = new HashSet<Product>();
            ProductDies = new HashSet<ProductDy>();
            ProductPackingMethods = new HashSet<ProductPackingMethod>();
            ProductProductTypes = new HashSet<Product>();
            ProductionOrderLineDetails = new HashSet<ProductionOrderLineDetail>();
            ProductionOrders = new HashSet<ProductionOrder>();
            ProductionPlanLines = new HashSet<ProductionPlanLine>();
            ProfileBuildings = new HashSet<ProfileBuilding>();
            PurchaseOrderAdditionalChargeLines = new HashSet<PurchaseOrderAdditionalChargeLine>();
            PurchaseOrders = new HashSet<PurchaseOrder>();
            PurchaseReturnLines = new HashSet<PurchaseReturnLine>();
            SalesInvoiceChargeGroups = new HashSet<SalesInvoiceChargeGroup>();
            SalesInvoices = new HashSet<SalesInvoice>();
            SalesOrders = new HashSet<SalesOrder>();
            SalesReturnLines = new HashSet<SalesReturnLine>();
            SalesReturns = new HashSet<SalesReturn>();
            SalesServiceOrderLines = new HashSet<SalesServiceOrderLine>();
            ServiceInquiryLines = new HashSet<ServiceInquiryLine>();
            ServiceInquirySkinConditions = new HashSet<ServiceInquiry>();
            ServiceInquirySkinTypes = new HashSet<ServiceInquiry>();
            ShipmentCostLines = new HashSet<ShipmentCostLine>();
            ShipmentQualityControlLines = new HashSet<ShipmentQualityControlLine>();
            StockCountSheets = new HashSet<StockCountSheet>();
            SupplementaryManufacturers = new HashSet<SupplementaryManufacturer>();
            SupplierCreditPeriods = new HashSet<Supplier>();
            SupplierPaymentTerms = new HashSet<Supplier>();
            SupplierPriceLists = new HashSet<Supplier>();
            SupplierShippingTerms = new HashSet<Supplier>();
            Users = new HashSet<User>();
        }

        [Key]
        public int SupportDataId { get; set; }
        public int CompanyId { get; set; }
        public int? TypeEnum { get; set; }
        [StringLength(50)]
        public string SupportDataCode { get; set; }
        [StringLength(255)]
        public string SupportDataDescription { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? IntegerData { get; set; }
        [StringLength(255)]
        public string StringData { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SupportDatas")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(BillOfMaterial.Brand))]
        public virtual ICollection<BillOfMaterial> BillOfMaterials { get; set; }
        [InverseProperty(nameof(BillOfOperationLineOverhead.SupportData))]
        public virtual ICollection<BillOfOperationLineOverhead> BillOfOperationLineOverheads { get; set; }
        [InverseProperty(nameof(ChargeInfoChargeGroup.ChargeGroup))]
        public virtual ICollection<ChargeInfoChargeGroup> ChargeInfoChargeGroups { get; set; }
        [InverseProperty(nameof(Customer.CreditPeriod))]
        public virtual ICollection<Customer> CustomerCreditPeriods { get; set; }
        [InverseProperty(nameof(Customer.PriceList))]
        public virtual ICollection<Customer> CustomerPriceLists { get; set; }
        [InverseProperty(nameof(DiscountSchemeCriteriaLine.ProductBrand))]
        public virtual ICollection<DiscountSchemeCriteriaLine> DiscountSchemeCriteriaLines { get; set; }
        [InverseProperty(nameof(DiscountScheme.CustomerGroup))]
        public virtual ICollection<DiscountScheme> DiscountSchemeCustomerGroups { get; set; }
        [InverseProperty(nameof(DiscountScheme.CustomerType))]
        public virtual ICollection<DiscountScheme> DiscountSchemeCustomerTypes { get; set; }
        [InverseProperty(nameof(DiscountSchemeLine.ProductBrand))]
        public virtual ICollection<DiscountSchemeLine> DiscountSchemeLines { get; set; }
        [InverseProperty(nameof(ExchangeOrderLine.Reason))]
        public virtual ICollection<ExchangeOrderLine> ExchangeOrderLines { get; set; }
        [InverseProperty(nameof(ExchangeOrder.ShipmentPackingMethod))]
        public virtual ICollection<ExchangeOrder> ExchangeOrders { get; set; }
        [InverseProperty(nameof(FixedAsset.AssetGroup))]
        public virtual ICollection<FixedAsset> FixedAssetAssetGroups { get; set; }
        [InverseProperty(nameof(FixedAsset.AssetLocation))]
        public virtual ICollection<FixedAsset> FixedAssetAssetLocations { get; set; }
        [InverseProperty(nameof(FixedAsset.AssetType))]
        public virtual ICollection<FixedAsset> FixedAssetAssetTypes { get; set; }
        [InverseProperty(nameof(FreeIssueCriteriaLine.ProductBrand))]
        public virtual ICollection<FreeIssueCriteriaLine> FreeIssueCriteriaLines { get; set; }
        [InverseProperty(nameof(FreeIssue.CustomerGroup))]
        public virtual ICollection<FreeIssue> FreeIssueCustomerGroups { get; set; }
        [InverseProperty(nameof(FreeIssue.CustomerType))]
        public virtual ICollection<FreeIssue> FreeIssueCustomerTypes { get; set; }
        [InverseProperty(nameof(FreeIssueLine.ProductBrand))]
        public virtual ICollection<FreeIssueLine> FreeIssueLines { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.PaymentTerm))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotePaymentTerms { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.ShipmentPackingMethod))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNoteShipmentPackingMethods { get; set; }
        [InverseProperty(nameof(InternalDispatch.RequestReason))]
        public virtual ICollection<InternalDispatch> InternalDispatches { get; set; }
        [InverseProperty(nameof(LeaveConfiguration.EmployeeCategory))]
        public virtual ICollection<LeaveConfiguration> LeaveConfigurationEmployeeCategories { get; set; }
        [InverseProperty(nameof(LeaveConfiguration.EmployeeGroup))]
        public virtual ICollection<LeaveConfiguration> LeaveConfigurationEmployeeGroups { get; set; }
        [InverseProperty(nameof(LoanOrderLine.Reason))]
        public virtual ICollection<LoanOrderLine> LoanOrderLines { get; set; }
        [InverseProperty(nameof(MachineLine.OverheadInformation))]
        public virtual ICollection<MachineLine> MachineLines { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNote.FixedAssetGroup))]
        public virtual ICollection<MaterialRequisitionNote> MaterialRequisitionNoteFixedAssetGroups { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNote.RequestReason))]
        public virtual ICollection<MaterialRequisitionNote> MaterialRequisitionNoteRequestReasons { get; set; }
        [InverseProperty(nameof(Mould.Category))]
        public virtual ICollection<Mould> Moulds { get; set; }
        [InverseProperty(nameof(PriceList.PriceListNavigation))]
        public virtual ICollection<PriceList> PriceLists { get; set; }
        [InverseProperty(nameof(ProductBrandDiscount.ProductBrand))]
        public virtual ICollection<ProductBrandDiscount> ProductBrandDiscounts { get; set; }
        [InverseProperty(nameof(Product.Brand))]
        public virtual ICollection<Product> ProductBrands { get; set; }
        [InverseProperty(nameof(ProductDy.Die))]
        public virtual ICollection<ProductDy> ProductDies { get; set; }
        [InverseProperty(nameof(ProductPackingMethod.ShipmentPackingMethod))]
        public virtual ICollection<ProductPackingMethod> ProductPackingMethods { get; set; }
        [InverseProperty(nameof(Product.ProductType))]
        public virtual ICollection<Product> ProductProductTypes { get; set; }
        [InverseProperty(nameof(ProductionOrderLineDetail.OverheadInformation))]
        public virtual ICollection<ProductionOrderLineDetail> ProductionOrderLineDetails { get; set; }
        [InverseProperty(nameof(ProductionOrder.CancelledActivity))]
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; }
        [InverseProperty(nameof(ProductionPlanLine.Activity))]
        public virtual ICollection<ProductionPlanLine> ProductionPlanLines { get; set; }
        [InverseProperty(nameof(ProfileBuilding.Die))]
        public virtual ICollection<ProfileBuilding> ProfileBuildings { get; set; }
        [InverseProperty(nameof(PurchaseOrderAdditionalChargeLine.ChargeGroup))]
        public virtual ICollection<PurchaseOrderAdditionalChargeLine> PurchaseOrderAdditionalChargeLines { get; set; }
        [InverseProperty(nameof(PurchaseOrder.PriceList))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        [InverseProperty(nameof(PurchaseReturnLine.ReturnReason))]
        public virtual ICollection<PurchaseReturnLine> PurchaseReturnLines { get; set; }
        [InverseProperty(nameof(SalesInvoiceChargeGroup.ChargeGroup))]
        public virtual ICollection<SalesInvoiceChargeGroup> SalesInvoiceChargeGroups { get; set; }
        [InverseProperty(nameof(SalesInvoice.PriceList))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
        [InverseProperty(nameof(SalesOrder.PriceList))]
        public virtual ICollection<SalesOrder> SalesOrders { get; set; }
        [InverseProperty(nameof(SalesReturnLine.ReturnReason))]
        public virtual ICollection<SalesReturnLine> SalesReturnLines { get; set; }
        [InverseProperty(nameof(SalesReturn.PriceList))]
        public virtual ICollection<SalesReturn> SalesReturns { get; set; }
        [InverseProperty(nameof(SalesServiceOrderLine.Brand))]
        public virtual ICollection<SalesServiceOrderLine> SalesServiceOrderLines { get; set; }
        [InverseProperty(nameof(ServiceInquiryLine.MethodOfUse))]
        public virtual ICollection<ServiceInquiryLine> ServiceInquiryLines { get; set; }
        [InverseProperty(nameof(ServiceInquiry.SkinConditions))]
        public virtual ICollection<ServiceInquiry> ServiceInquirySkinConditions { get; set; }
        [InverseProperty(nameof(ServiceInquiry.SkinType))]
        public virtual ICollection<ServiceInquiry> ServiceInquirySkinTypes { get; set; }
        [InverseProperty(nameof(ShipmentCostLine.ChargeGroup))]
        public virtual ICollection<ShipmentCostLine> ShipmentCostLines { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlLine.RejectReason))]
        public virtual ICollection<ShipmentQualityControlLine> ShipmentQualityControlLines { get; set; }
        [InverseProperty(nameof(StockCountSheet.PriceList))]
        public virtual ICollection<StockCountSheet> StockCountSheets { get; set; }
        [InverseProperty(nameof(SupplementaryManufacturer.Category))]
        public virtual ICollection<SupplementaryManufacturer> SupplementaryManufacturers { get; set; }
        [InverseProperty(nameof(Supplier.CreditPeriod))]
        public virtual ICollection<Supplier> SupplierCreditPeriods { get; set; }
        [InverseProperty(nameof(Supplier.PaymentTerm))]
        public virtual ICollection<Supplier> SupplierPaymentTerms { get; set; }
        [InverseProperty(nameof(Supplier.PriceList))]
        public virtual ICollection<Supplier> SupplierPriceLists { get; set; }
        [InverseProperty(nameof(Supplier.ShippingTerm))]
        public virtual ICollection<Supplier> SupplierShippingTerms { get; set; }
        [InverseProperty(nameof(User.Title))]
        public virtual ICollection<User> Users { get; set; }
    }
}
