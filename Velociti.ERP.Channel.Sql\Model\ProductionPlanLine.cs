﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductionPlanLines", Schema = "sales")]
    public partial class ProductionPlanLine
    {
        public ProductionPlanLine()
        {
            MachineVisualPlanLines = new HashSet<MachineVisualPlanLine>();
        }

        [Key]
        public int ProductionPlanLineId { get; set; }
        public int? ProductionPlanId { get; set; }
        public int? CompanyId { get; set; }
        public int? BillOfOperationId { get; set; }
        [StringLength(50)]
        public string ProductionPlanCode { get; set; }
        [StringLength(50)]
        public string Barcode { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        public int? RefDocLineId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BarcodeScannedStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CompletedDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualEndDateTime { get; set; }
        public int? MachineId { get; set; }
        public int? ActivityId { get; set; }
        public int? CavityId { get; set; }
        public byte? DayLightNo { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(100)]
        public string DayLightNumbers { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? CancelledActivityId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BarcodePrintedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SentDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ActivityId))]
        [InverseProperty(nameof(SupportData.ProductionPlanLines))]
        public virtual SupportData Activity { get; set; }
        [ForeignKey(nameof(BillOfOperationId))]
        [InverseProperty("ProductionPlanLines")]
        public virtual BillOfOperation BillOfOperation { get; set; }
        [ForeignKey(nameof(CavityId))]
        [InverseProperty(nameof(MouldCavity.ProductionPlanLines))]
        public virtual MouldCavity Cavity { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ProductionPlanLines")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("ProductionPlanLines")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ProductionPlanLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductionPlanId))]
        [InverseProperty("ProductionPlanLines")]
        public virtual ProductionPlan ProductionPlan { get; set; }
        [InverseProperty(nameof(MachineVisualPlanLine.ProductionPlanLine))]
        public virtual ICollection<MachineVisualPlanLine> MachineVisualPlanLines { get; set; }
    }
}
