﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class SupplementaryManufacturersController : ControllerBase
    {
        private readonly ISupplementaryManufacturerService _supplementaryManufactureService;

        public SupplementaryManufacturersController(ISupplementaryManufacturerService supplementaryManufactureService)
        {
            _supplementaryManufactureService = supplementaryManufactureService;
        }

        [HttpGet]
        [Route("Active/{companyId}")]
        public async Task<IActionResult> GetActiveAll(int companyId)
        {
            return Ok(await _supplementaryManufactureService.GetActiveAllAsync(companyId));
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> Get(int companyId)
        {
            return Ok(await _supplementaryManufactureService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            return Ok(await _supplementaryManufactureService.GetShorListAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]SupplementaryManufacturer supplementaryManufacturer)
        {
            if (supplementaryManufacturer == null)
                return BadRequest();

            await _supplementaryManufactureService.SaveAsync(supplementaryManufacturer);

            return Ok();
        }

        [HttpDelete]
        [Route("{supplementaryManufacturerId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int supplementaryManufacturerId, int userId)
        {
            await _supplementaryManufactureService.ToggleActivationAsync(supplementaryManufacturerId, userId);

            return Ok();
        }
    }
}