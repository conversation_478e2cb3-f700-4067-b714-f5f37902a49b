﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("DiscountSchemes", Schema = "sales")]
    public partial class DiscountScheme
    {
        public DiscountScheme()
        {
            DiscountSchemeCriteriaLines = new HashSet<DiscountSchemeCriteriaLine>();
            DiscountSchemeCustomers = new HashSet<DiscountSchemeCustomer>();
            DiscountSchemeLines = new HashSet<DiscountSchemeLine>();
            PromotionLines = new HashSet<PromotionLine>();
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SalesInvoices = new HashSet<SalesInvoice>();
        }

        [Key]
        public int DiscountSchemeId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DiscountCode { get; set; }
        [StringLength(150)]
        public string Description { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime StartDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDate { get; set; }
        public int? CustomerTypeId { get; set; }
        public int? CustomerGroupId { get; set; }
        public int? CustomerCategoryId { get; set; }
        public byte TypeEnum { get; set; }
        public int? ApplicableTypeEnum { get; set; }
        public byte CalculationTypeEnum { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal BreakQuantity { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal DiscountValue { get; set; }
        public byte? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("DiscountSchemes")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerGroupId))]
        [InverseProperty(nameof(SupportData.DiscountSchemeCustomerGroups))]
        public virtual SupportData CustomerGroup { get; set; }
        [ForeignKey(nameof(CustomerTypeId))]
        [InverseProperty(nameof(SupportData.DiscountSchemeCustomerTypes))]
        public virtual SupportData CustomerType { get; set; }
        [InverseProperty(nameof(DiscountSchemeCriteriaLine.DiscountScheme))]
        public virtual ICollection<DiscountSchemeCriteriaLine> DiscountSchemeCriteriaLines { get; set; }
        [InverseProperty(nameof(DiscountSchemeCustomer.DiscountScheme))]
        public virtual ICollection<DiscountSchemeCustomer> DiscountSchemeCustomers { get; set; }
        [InverseProperty(nameof(DiscountSchemeLine.DiscountScheme))]
        public virtual ICollection<DiscountSchemeLine> DiscountSchemeLines { get; set; }
        [InverseProperty(nameof(PromotionLine.DiscountScheme))]
        public virtual ICollection<PromotionLine> PromotionLines { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.DiscountScheme))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SalesInvoice.DiscountScheme))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
    }
}
