﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using System.Linq;
using Microsoft.EntityFrameworkCore;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class OrganizationRepository : IOrganizationRepository
    {
        private readonly MarangoniERPContext _context;

        public OrganizationRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<HierarchyTemplate>> GetHierarchyAsync()
        {
            var records = await _context.HierarchyTemplates.FromSqlRaw("[dbo].[GetOrganizationStructure]").ToListAsync();

            return records;
        }
    }
}
