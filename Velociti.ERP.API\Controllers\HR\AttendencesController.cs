﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.HR;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Entities.Sales;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class AttendencesController : ControllerBase
    {
        private readonly IAttendenceService _attendenceService;

        public AttendencesController(IAttendenceService attendenceService)
        {
            _attendenceService = attendenceService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _attendenceService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("UploadedLines")]
        public async Task<IActionResult> GetUploadedLines(int userId)
        {
            return Ok(await _attendenceService.GetAllUploadedLinesAsync(userId));
        }

        [HttpPost]
        public async Task<IActionResult> Save(int companyId, int modifiedUserId)
        {
            await _attendenceService.SaveAsync(companyId, modifiedUserId);

            return Ok();
        }

        [HttpPost]
        [Route("Individual")]
        public async Task<IActionResult> Save([FromBody]Attendence attendence)
        {
            if (attendence == null)
                return BadRequest();

            await _attendenceService.SaveAsync(attendence);

            return Ok();
        }

        [HttpPost]
        [Route("Upload")]
        public IActionResult UploadSave([FromBody]List<Attendence> attendencesList)
        {
            if (attendencesList == null)
                return BadRequest();

            _attendenceService.UploadSaveAsync(attendencesList);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> FreezeAttendence(int companyId, int userId, DateTime startDate, DateTime endDate)    
        {
            await _attendenceService.FreezeAttendenceAsync(companyId, userId, startDate, endDate);

            return Ok();
        }
    }
}