﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PasswordPolicies", Schema = "adm")]
    public partial class PasswordPolicy
    {
        [Key]
        public int PasswordPolicyId { get; set; }
        public int? CompanyId { get; set; }
        public int? RequiredLength { get; set; }
        public int? ExpiryPeriod { get; set; }
        public int? NotificationPeriod { get; set; }
        public bool? RequireDigit { get; set; }
        public bool? RequireLowercase { get; set; }
        public bool? RequireUppercase { get; set; }
        public bool? RequireNonLetterOrDigit { get; set; }
        public int? MaxFailedAccessAttempts { get; set; }
        public int? LockoutPeriod { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PasswordPolicies")]
        public virtual Company Company { get; set; }
    }
}
