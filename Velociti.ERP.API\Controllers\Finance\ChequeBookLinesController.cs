﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class ChequeBookLinesController : ControllerBase
    {
        private readonly IChequeBookLineService _chequeBookLineService;

        public ChequeBookLinesController(IChequeBookLineService chequeBookLineService)  
        {
            _chequeBookLineService = chequeBookLineService;
        }

        [HttpGet]
        [Route("{chequeBookId}")]
        public async Task<IActionResult> GetNextAvailableChequeNo(int chequeBookId)  
        {
            return Ok(await _chequeBookLineService.GetNextAvailableChequeNoAsync(chequeBookId));
        }

        [HttpGet]
        [Route("Single/{chequeBookLineId}")]
        public async Task<IActionResult> FindById(int chequeBookLineId)
        {
            return Ok(await _chequeBookLineService.FindByIdAsync(chequeBookLineId));  
        }

        [HttpGet]
        [Route("ShortList/{chequeBookId}")]
        public async Task<IActionResult> GetShortList(int chequeBookId)
        {
            return Ok(await _chequeBookLineService.GetShortListByChequeBookIdAsync(chequeBookId));
        }
    }
}