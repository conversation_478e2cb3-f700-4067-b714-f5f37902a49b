﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Common;

namespace Velociti.ERP.API.Controllers.Common
{
    [Route("api/Common/[controller]")]
    [Authorize]
    [ApiController]
    public class TxnWorkFlowLinesController : ControllerBase
    {
        private readonly ITxnWorkFlowLineService _txnWorkFlowLineService;

        public TxnWorkFlowLinesController(ITxnWorkFlowLineService txnWorkFlowLineService)
        {
            _txnWorkFlowLineService = txnWorkFlowLineService;
        }

        [HttpGet]
        [Route("WithPendingLines/{txnWorkFlowId}")]
        public async Task<IActionResult> GetWithPendingLines(int txnWorkFlowId)
        {
            return Ok(await _txnWorkFlowLineService.GetWithPendingLinesAsync(txnWorkFlowId));
        }
    }
}