﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [ApiController]
    public class ProductionOrderLineDetailsController : ControllerBase
    {
        private readonly IProductionOrderLineDetailService _productionOrderLineDetailService;

        public ProductionOrderLineDetailsController(IProductionOrderLineDetailService productionOrderLineDetailService)
        {
            _productionOrderLineDetailService = productionOrderLineDetailService;
        }

        [HttpGet]
        [Route("Products/{productionOrderLineId}")]
        public async Task<IActionResult> GetProductLinesAsync(int productionOrderLineId)
        {
            return Ok(await _productionOrderLineDetailService.GetProductLinesAsync(productionOrderLineId));
        }

        [HttpGet]
        [Route("MachineOverheads/{productionOrderLineId}")]
        public async Task<IActionResult> GetMachineOverheadsAsync(int productionOrderLineId)
        {
            return Ok(await _productionOrderLineDetailService.GetMachineOverheadsAsync(productionOrderLineId));
        }

        [HttpGet]
        [Route("MachineOverheadsForSemiFinished/{productionOrderLineId}")]
        public async Task<IActionResult> GetMachineOverheadsForSemiFinishedAsync(int productionOrderLineId)
        {
            return Ok(await _productionOrderLineDetailService.GetMachineOverheadsForSemiFinishedAsync(productionOrderLineId));
        }

        [HttpGet]
        [Route("AllocatedEmployees/{productionOrderLineId}")]
        public async Task<IActionResult> GetAllocatedEmployeesAsync(int productionOrderLineId)
        {
            return Ok(await _productionOrderLineDetailService.GetAllocatedEmployeesAsync(productionOrderLineId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("ByProductionOrderAndMachine/productionOrder/{productionOrderId}/machine/{machineId}/productCategory/{productCategoryEnum}")]
        public async Task<IActionResult> GetByProductionOrderAndMachine(int productionOrderId, int machineId, byte productCategoryEnum)
        {
            return Ok(await _productionOrderLineDetailService.GetByProductionOrderAndMachineAsync(productionOrderId, machineId, productCategoryEnum));
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("UpdateFromTC")]
        public async Task<IActionResult> UpdateFromTC([FromBody]List<ProductionOrderLineDetail> productionOrderLineDetails)
        {
            try
            {
                await _productionOrderLineDetailService.UpdateBarcodeAndQtyAsync(productionOrderLineDetails);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }
    }
}