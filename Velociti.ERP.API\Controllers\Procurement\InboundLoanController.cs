﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text.Json;
using System.Text.Json.Serialization;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class InboundLoanController : ControllerBase
    {
        private readonly IInboundLoanService _inboundLoanService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public InboundLoanController(IInboundLoanService inboundLoanService, IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _inboundLoanService = inboundLoanService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }
        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]LoanOrder loanOrder)
        {
            await _inboundLoanService.SaveAsync(loanOrder);

            return Ok();
        }
        [HttpGet]
        [Route("Single/{loanOrderId}")]
        public async Task<IActionResult> GetById(int loanOrderId)
        {
            
            var result = await _inboundLoanService.GetByIdAsync(loanOrderId);
            return Ok(result);
           
        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte orderTypeEnum)
        {
            return Ok(await _inboundLoanService.GetAllAsync(companyId, userId, startDate, endDate, (int)Module.DocumentType.InboundLoanRequest));
        }
        [HttpPut]
        public async Task<IActionResult> Update([FromBody]LoanOrder loanOrder)
        {
            int id = 0;
            switch (loanOrder.Action)
            {
                case "submit":
                     await _inboundLoanService.SubmitAsync(loanOrder);
                    break;
                case "cancel":
                    await _inboundLoanService.CancelAsync(loanOrder);
                    break;
                case "convertToGRN":
                   id = await _inboundLoanService.ConvertToGRNAsync(loanOrder);
                    break;
                case "reverse":
                    await _inboundLoanService.ReverseAsync(loanOrder);
                    break;
                case "complete":
                    await _inboundLoanService.CompleteAsync(loanOrder);
                    break;
                case "convertToGDN":
                    return Ok(await _goodsDispatchNoteService.ConvertAsync(loanOrder.LoanOrderId, Module.DocumentType.InboundLoanRequest, loanOrder.ModifiedUserId));
            }

            return Ok(id);
        }
    }
}