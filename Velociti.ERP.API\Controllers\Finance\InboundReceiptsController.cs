﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;
using Velociti.ERP.Domain.Utils;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class InboundReceiptsController : ControllerBase
    {
        private readonly IInboundReceiptService _inboundReceiptService;
        private readonly AppSettings _appSettings;

        public InboundReceiptsController(IOptions<AppSettings> appSettings, IInboundReceiptService inboundReceiptService)
        {
            _appSettings = appSettings.Value;
            _inboundReceiptService = inboundReceiptService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _inboundReceiptService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("Single/{inboundReceiptId}")]
        public async Task<IActionResult> GetById(int inboundReceiptId)
        {
            return Ok(await _inboundReceiptService.GetByIdAsync(inboundReceiptId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]InboundReceipt inboundReceipt)
        {
            await _inboundReceiptService.SaveAsync(inboundReceipt);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]InboundReceipt inboundReceipt)
        {
            switch (inboundReceipt.Action)
            {
                case "submit":
                    await _inboundReceiptService.SubmitAsync(inboundReceipt);
                    break;
                case "cancel":
                    await _inboundReceiptService.CancelAsync(inboundReceipt);
                    break;
                case "chequeReturn":
                    await _inboundReceiptService.ChequeReturnAsync(inboundReceipt);
                    break;
                case "reverse":
                    await _inboundReceiptService.ReverseAsync(inboundReceipt.InboundReceiptId, inboundReceipt.ModifiedUserId.Value);
                    break;
            }

            return Ok();
        }
        [HttpPut]
        [Route("BulkSubmit")]
        public async Task<IActionResult> BulkSubmit([FromBody]ViewForm viewForm)
        {
            await _inboundReceiptService.BulkSubmitAsync(viewForm.IntegerList, viewForm.ParameterOne);
            return Ok();
        }

        [HttpGet]
        [Route("AvailableAdvances/{customerId}")]
        public async Task<ActionResult> GetAvailableAdvancesBySupplier(int customerId)
        {
            return Ok(await _inboundReceiptService.GetAvailableAdvancesByCustomerAsync(customerId));
        }
    }
}