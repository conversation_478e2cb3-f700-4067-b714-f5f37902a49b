﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class HoldingsRepository : IHoldingsRepository
    {
        private readonly MarangoniERPContext _context;

        public HoldingsRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<Holding> FindByCodeAsync(string code)
        {
            return await _context.Holdings.FirstOrDefaultAsync(c => c.HoldingCode == code);
        }

        public async Task<Holding> FindByIdAsync(int holdingId)
        {
            return await _context.Holdings.FirstOrDefaultAsync(c => c.HoldingId == holdingId);
        }

        public async Task<IEnumerable<Holding>> GetActiveAllAsync()
        {
            return await _context.Holdings.Where(p => p.ExpiryDate == null && p.HoldingName != "Sandbox").ToListAsync();
        }

        public async Task SaveAsync(Holding holding)
        {
            if (holding.HoldingId == default)
            {
                holding.CreationDate = DateTime.Now;
                await _context.Holdings.AddAsync(holding);
            }
            else
            {
                _context.Holdings.Attach(holding);
                _context.Entry(holding).State = EntityState.Modified;
            }

            await _context.SaveChangesAsync();
        }
    }
}
