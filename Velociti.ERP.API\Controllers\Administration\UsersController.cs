﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    //[Authorize]
    [ApiController]
    public class UsersController : ControllerBase
    {
        private readonly IUserService _userService;

        public UsersController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet]
        //[Route("All")]
        public async Task<ActionResult<Response<User>>> GetAll()
        {
            return Ok(await _userService.GetAllAsync());
        }

        [HttpGet]
        [Route("Single/{userId}")]
        public async Task<ActionResult<Response<User>>> FindById(int userId)
        {
            return Ok(await _userService.FindByIdAsync(userId));
        }

        [HttpGet]
        [Route("{userId}/designation/{designationId}/company/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<User>>>> GetAllByDesignation(int designationId, int userId, int companyId)
        {
            return Ok(await _userService.GetAllByDesignationAsync(designationId, userId, companyId));
        }

        [HttpGet]
        [Route("ParentShortList/designation/{designationId}")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetParentShortList(int designationId)
        {
            var list = await _userService.GetParentUsersShortListAsync(designationId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetShortList(int companyId)
        {
            var list = await _userService.GetShortListAsync(companyId);

            return Ok(list);
        }

        //[HttpPost]
        ////public async Task<IActionResult> Save([FromForm]User user)
        //public async Task<IActionResult> Save([FromBody]User user)
        //{
        //    if (user == null)
        //        return BadRequest();

        //    //if (user.SignatureFile != null && user.SignatureFile.Length > 0)
        //    //{
        //    //    using (var ms = new MemoryStream())
        //    //    {
        //    //        user.SignatureFile.CopyTo(ms);
        //    //        var fileBytes = ms.ToArray();
        //    //        user.Signature = fileBytes;
        //    //        //string s = Convert.ToBase64String(fileBytes);
        //    //        // act on the Base64 data
        //    //    }
        //    //}

        //    //user.Login = new Login
        //    //{
        //    //    LoginId = user.LoginId,
        //    //    Username = user.Username,
        //    //    PasswordHash = user.PasswordHash
        //    //};

        //    //try
        //    //{
        //        user = await _userService.SaveAsync(user);

        //        return Ok(user);
        //    //}
        //    //catch (Exception ex)
        //    //{
        //    //    return StatusCode(500, ex.Message);
        //    //}
        //}

        [HttpPost]
        public async Task<IActionResult> Post([FromForm]User user)
        {
            if (user.SignatureFile != null && user.SignatureFile.Length > 0)
            {
                using var ms = new MemoryStream();

                user.SignatureFile.CopyTo(ms);
                byte[] fileBytes = ms.ToArray();
                user.Signature = fileBytes;
            }

            user = await _userService.SaveAsync(user);
            return Ok(user);
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivation(int id)
        {
            await _userService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}