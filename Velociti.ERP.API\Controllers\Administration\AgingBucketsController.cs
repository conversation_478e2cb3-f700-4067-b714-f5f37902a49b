﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class AgingBucketsController : ControllerBase  
    {
        private readonly IAgingBucketService _agingBucketService;

        public AgingBucketsController(IAgingBucketService agingBucketService)
        {
            _agingBucketService = agingBucketService;
        }

        [HttpGet]
        [Route("company/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<AgingBucket>>>> Get(int companyId)
        {
            return Ok(await _agingBucketService.GetAllAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]AgingBucket agingBucket)  
        {
            if (agingBucket == null)
                return BadRequest();

            await _agingBucketService.SaveAsync(agingBucket);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]AgingBucket agingBucket)
        {
            await _agingBucketService.SubmitAsync(agingBucket.AgingBucketId, agingBucket.ModifiedUserId.Value);
            return Ok();
        }

        [HttpDelete]
        [Route("{id}/loggedInUser/{loggedInUserId}")]
        public async Task<IActionResult> ToggleActivation(int id, int loggedInUserId)
        {
            await _agingBucketService.ToggleActivationAsync(id, loggedInUserId);

            return Ok();
        }
    }
}