﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]  
    public class StockTransferReceiptLinesController : ControllerBase
    {
        private readonly IStockTransferReceiptLineService _stockTransferReceiptLineService;  

        public StockTransferReceiptLinesController(IStockTransferReceiptLineService stockTransferReceiptLineService)      
        {
            _stockTransferReceiptLineService = stockTransferReceiptLineService;
        }

        [HttpGet]
        [Route("{stockTransferReceiptId}/company/{companyId}")]
        public async Task<IActionResult> Get(int stockTransferReceiptId, int companyId)
        {
            return Ok(await _stockTransferReceiptLineService.GetAsync(stockTransferReceiptId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> GetInternalReturnLine(int companyId, [FromBody]StockTransferReceiptLine stockTransferReceiptLine)
        {
            return Ok(await _stockTransferReceiptLineService.AddStockTransferReceiptLineAsync(companyId, stockTransferReceiptLine));
        }
    }
}