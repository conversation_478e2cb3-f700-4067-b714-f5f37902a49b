﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductionNoteLines", Schema = "inv")]
    public partial class ProductionNoteLine
    {
        [Key]
        public int ProductionNoteLineId { get; set; }

        public int ProductionNoteId { get; set; }
        public int ProductId { get; set; }
        public int UnitOfMeasureId { get; set; }
        public int ReceipeId { get; set; }

        [Column(TypeName = "decimal(18, 3)")]
        public decimal Quantity { get; set; }

        [Column(TypeName = "decimal(18, 2)")]
        public decimal Price { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }

        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ProductionNoteId))]
        [InverseProperty("ProductionNoteLines")]
        public virtual ProductionNote ProductionNote { get; set; }

        [ForeignKey(nameof(ProductId))]
        public virtual Product Product { get; set; }

        [ForeignKey(nameof(UnitOfMeasureId))]
        public virtual UnitOfMeasure UnitOfMeasure { get; set; }

        //[ForeignKey(nameof(CreatedUserId))]
        //[InverseProperty("ProductionNoteLinesCreatedBy")]
        //public virtual User CreatedUser { get; set; }

        //[ForeignKey(nameof(ModifiedUserId))]
        //[InverseProperty("ProductionNoteLinesModifiedBy")]
        //public virtual User ModifiedUser { get; set; }
    }
}