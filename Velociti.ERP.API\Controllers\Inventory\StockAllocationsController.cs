﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class StockAllocationsController : ControllerBase
    {
        private readonly IStockAllocationService _stockAllocationService;

        public StockAllocationsController(IStockAllocationService stockAllocationService)
        {
            _stockAllocationService = stockAllocationService;
        }

        [HttpGet]
        [Route("TxnLines/{companyId}")]
        public async Task<IActionResult> GetTxnLines(int companyId)
        {
            return Ok(await _stockAllocationService.GetTxnLinesForAllocationManager(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> AllocateStock([FromBody]StockAllocation stockAllocation)
        {
            await _stockAllocationService.AllocateStockAsync(stockAllocation);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> DeallocateStock([FromBody]List<StockAllocation> stockAllocations)
        {
            await _stockAllocationService.DeallocateStockAsync(stockAllocations);

            return Ok();
        }
    }
}