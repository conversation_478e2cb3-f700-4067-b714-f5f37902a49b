﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("TxnWorkFlows", Schema = "cmn")]
    public partial class TxnWorkFlow
    {
        public TxnWorkFlow()
        {
            ExchangeOrders = new HashSet<ExchangeOrder>();
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            GoodsReceiveNotes = new HashSet<GoodsReceiveNote>();
            InternalDispatches = new HashSet<InternalDispatch>();
            PurchaseOrders = new HashSet<PurchaseOrder>();
            Quotations = new HashSet<Quotation>();
            SalesOrders = new HashSet<SalesOrder>();
            SalesServiceOrderJobs = new HashSet<SalesServiceOrderJob>();
            StockAdjustments = new HashSet<StockAdjustment>();
            StockTakes = new HashSet<StockTake>();
            TxnWorkFlowLines = new HashSet<TxnWorkFlowLine>();
        }

        [Key]
        public int TxnWorkFlowId { get; set; }
        public int CompanyId { get; set; }
        public int? WorkFlowId { get; set; }
        public byte? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("TxnWorkFlows")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(WorkFlowId))]
        [InverseProperty("TxnWorkFlows")]
        public virtual WorkFlow WorkFlow { get; set; }
        [InverseProperty(nameof(ExchangeOrder.TxnWorkFlow))]
        public virtual ICollection<ExchangeOrder> ExchangeOrders { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.TxnWorkFlow))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(GoodsReceiveNote.TxnWorkFlow))]
        public virtual ICollection<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        [InverseProperty(nameof(InternalDispatch.TxnWorkFlow))]
        public virtual ICollection<InternalDispatch> InternalDispatches { get; set; }
        [InverseProperty(nameof(PurchaseOrder.TxnWorkFlow))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        [InverseProperty(nameof(Quotation.TxnWorkFlow))]
        public virtual ICollection<Quotation> Quotations { get; set; }
        [InverseProperty(nameof(SalesOrder.TxnWorkFlow))]
        public virtual ICollection<SalesOrder> SalesOrders { get; set; }
        [InverseProperty(nameof(SalesServiceOrderJob.TxnWorkFlow))]
        public virtual ICollection<SalesServiceOrderJob> SalesServiceOrderJobs { get; set; }
        [InverseProperty(nameof(StockAdjustment.TxnWorkFlow))]
        public virtual ICollection<StockAdjustment> StockAdjustments { get; set; }
        [InverseProperty(nameof(StockTake.TxnWorkFlow))]
        public virtual ICollection<StockTake> StockTakes { get; set; }
        [InverseProperty(nameof(TxnWorkFlowLine.TxnWorkFlow))]
        public virtual ICollection<TxnWorkFlowLine> TxnWorkFlowLines { get; set; }
    }
}
