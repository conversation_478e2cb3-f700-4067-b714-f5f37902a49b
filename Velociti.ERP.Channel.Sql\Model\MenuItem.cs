﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MenuHeads", Schema = "inv")]

    public partial class MenuItem
    {
        [Key]
        public int MenuItemId { get; set; }
        public string MCode { get; set; }
        public int? CompanyId { get; set; }
        public string MName { get; set; }
        public string NameOnBill { get; set; }
        public decimal? Price { get; set; }
        public string CategoryCode { get; set; }
        public string PCode { get; set; }
        public int? StatusEnum { get; set; }
        public int ServiceCharge { get; set; }
        public int PLocation { get; set; }
        public int CatId { get; set; }

        public DateTime CreatedDate { get; set; }

        public List<MenuHeadLines> MenuHeadLinem { get; set; }
    }
}
