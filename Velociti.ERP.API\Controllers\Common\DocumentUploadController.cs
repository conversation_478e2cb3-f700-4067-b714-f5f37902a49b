﻿using System.Collections.Generic;
using System.IO;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Common;
using System.Linq;
using System;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Common
{
    [Route("api/Common/[controller]")]
    [Authorize]
    [ApiController]
    public class DocumentUploadController : ControllerBase
    {
        private IDocumentUploadService _documentUploadService;
        private readonly ICompanyService _companyService;
        private readonly IConfiguration _config;
        private readonly IWebHostEnvironment _hostingEnvironment;

        public DocumentUploadController(IConfiguration config, IDocumentUploadService documentUploadService, ICompanyService companyService
            , IWebHostEnvironment hostingEnvironment)
        {
            _documentUploadService = documentUploadService;
            _companyService = companyService;
            _config = config;
            _hostingEnvironment = hostingEnvironment;
        }

        [HttpGet]
        [Route("TransactionDocs/{typeEnum}/company/{companyId}/{transactionId}")]
        public async Task<IActionResult> GetAll(int typeEnum, int companyId, int transactionId)
        {
            var result = await _documentUploadService.GetAsync(transactionId, companyId, typeEnum);

            return Ok(result);
        }

        //[HttpGet]
        //[Route("TransactionImages/{typeEnum}/company/{companyId}/{transactionId}")]
        //public async Task<TransactionDocumentVM> GetTransactionImages(int typeEnum,int companyId,int transactionId)
        //{
        //    string rootPath = _hostingEnvironment.ContentRootPath;

        //    TransactionDocumentVM result = await _documentUploadService.FindByTransactionIdAsync(transactionId, companyId, typeEnum);
        //    List<string> newList = new List<string>();
        //    newList = result.FileNames;

        //    if (result != null && result.FileNames != null)
        //    {
        //        result.FilePath = new List<string>();
        //        foreach (var file in newList)
        //        {
        //            //var folder = _config.GetValue<string>("AppSettings:TransImagesViewFolderPath");
        //            //var filepath = Path.Combine(rootPath, folder, file);

        //            var filepath = _config.GetValue<string>("AppSettings:TransImagesViewFolderPath") + $@"\{file}";
        //            result.FilePath.Add(filepath);
                    
        //        }              
        //    }
        //    return result;
        //}


        [HttpPost]
        public async Task<IActionResult> UploadAsync(TransactionDocuments documents)
        {
            await this._documentUploadService.SaveAsync(documents, documents.CreatedUserId.Value, documents.CompanyId);

            return Ok();
        }

        //[HttpPost]
        //[Route("Upload")]
        //public async Task<IActionResult> UploadOneFile([FromForm]TransactionDocuments documents)
        //{
        //    //if (documents != null && documents.Files != null)
        //    //{
        //    //    for (int i = 0; documents.Files.Count() > i; i++)
        //    //    {
        //    //        var filename = documents.TransactionId.ToString() + "_" + documents.DocTypeEnum + "_" + ContentDispositionHeaderValue.Parse(documents.Files[i].ContentDisposition).FileName.Trim('"');
        //    //        filename = _config.GetValue<string>("AppSettings:TransImagesFolderPath") + $@"\{filename}";
        //    //        var timestamp = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds();
        //    //        var newFile = documents.TransactionId.ToString() + "ARCH_" + documents.DocTypeEnum + "_" + timestamp.ToString() + "_" + ContentDispositionHeaderValue.Parse(documents.Files[i].ContentDisposition).FileName.Trim('"');
        //    //        newFile = _config.GetValue<string>("AppSettings:TransImagesArchiveFolderPath") + $@"\{newFile}";


        //    //        FileInfo fi = new FileInfo(filename);
        //    //        {
        //    //            if (System.IO.File.Exists(filename))
        //    //            {
        //    //                fi.MoveTo(newFile);

        //    //                using (FileStream fs = System.IO.File.Create(filename))
        //    //                {
        //    //                    documents.Files[i].CopyTo(fs);
        //    //                    fs.Flush();
        //    //                }
        //    //            }
        //    //            else
        //    //            {
        //    //                using (FileStream fs = System.IO.File.Create(filename))
        //    //                {
        //    //                    documents.Files[i].CopyTo(fs);
        //    //                    fs.Flush();
        //    //                }
        //    //            }
        //    //        }
        //    //    }
        //    //    await this._documentUploadService.SaveAsync(documents, documents.CreatedUserId.Value, documents.CompanyId);
        //    //}
        //    return Ok();
        //}

        //[HttpDelete]
        //[Route("TransactionImages/{typeEnum}/company/{companyId}/{transactionId}/{fileName}/{userId}")]
        //public async Task<IActionResult> DeleteFile(int typeEnum, int companyId, int transactionId,string fileName,int userId)
        //{
        //    if (!string.IsNullOrEmpty(fileName))
        //    {
        //        var timestamp = new DateTimeOffset(DateTime.UtcNow).ToUnixTimeSeconds().ToString();
        //        var newFile = "ARCH_" + timestamp + fileName;
        //        string filename = null;

        //        filename = _config.GetValue<string>("AppSettings:TransImagesFolderPath") + $@"\{fileName}";
        //        newFile = _config.GetValue<string>("AppSettings:TransImagesArchiveFolderPath") + $@"\{newFile}";

        //        FileInfo fileInfo = new FileInfo(filename);
        //        {
        //            if (System.IO.File.Exists(filename))
        //            {
        //                fileInfo.CopyTo(newFile);
        //            }
        //        }

        //        System.IO.File.Delete(filename);
            

        //        await this._documentUploadService.DeleteAsync(typeEnum, companyId, transactionId, fileName, userId, timestamp);
        //    }
        //    return Ok();
        //}

        [HttpDelete]
        [Route("DeleteFile/{fileName}/company/{companyId}")]
        public async Task<IActionResult> DeleteFile(string fileName, int companyId)
        {
            if (!string.IsNullOrEmpty(fileName))
            {
                await this._companyService.DeleteFileNamesAsync(fileName, companyId);
            }
            return Ok();
        }

        [HttpPost]
        [Route("CompanyLogoUploadFiles")]
        public async Task<IActionResult> CompanyLogoUploadFilesAsync(TransactionDocuments documents)
        {
            await this._companyService.UpdateFileNamesAsync(documents.FileNames, documents.CompanyId);

            return Ok();
        }

        [HttpGet]
        [Route("CompanyLogoImages/{companyId}")]
        public async Task<IActionResult> GetCompanyLogoImages(int companyId)
        {
            var result = await _companyService.GetByIdAsync(companyId);

            var list = new List<TransactionDocuments>();

            if (result != null)
            {
                if (result.FilePath1 != null)
                {
                    var td = new TransactionDocuments
                    {
                        TransactionDocumentId = 1,
                        FileName = result.FilePath1
                    };

                    list.Add(td);
                }
                if (result.FilePath2 != null)
                {
                    var td = new TransactionDocuments
                    {
                        TransactionDocumentId = 2,
                        FileName = result.FilePath2
                    };

                    list.Add(td);
                }
                if (result.FilePath3 != null)
                {
                    var td = new TransactionDocuments
                    {
                        TransactionDocumentId = 3,
                        FileName = result.FilePath3
                    };

                    list.Add(td);
                }
            }

            return Ok(list);
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _documentUploadService.DeleteAsync(id);

            return Ok();
        }
    }
}