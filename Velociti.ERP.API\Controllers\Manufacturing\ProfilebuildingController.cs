﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [ApiController]
    public class ProfilebuildingController : ControllerBase
    {
        private readonly IProfileBuildingService _profileBuildingService;

        public ProfilebuildingController(IProfileBuildingService profileBuildingService)
        {
            _profileBuildingService = profileBuildingService;
        }

        [HttpGet]
        [Route("company/{companyId}/machine/{machineId}/startDate/{startDate}/endDate/{endDate}")]
        public async Task<IActionResult> GetAll(int companyId, int machineId, DateTime startDate, DateTime endDate)
        {
            var list = await _profileBuildingService.GetAllAsync(companyId, machineId, startDate, endDate);

            return Ok(list);
        }

        [HttpGet]
        [Route("ForBarcodePrint/profileBuildingId/{profileBuildingId}/count/{count}")]
        public async Task<IActionResult> GetForBarcodePrint(int profileBuildingId, int count)
        {
            var list = await _profileBuildingService.GetForBarcodePrintAsync(profileBuildingId, count);

            return Ok(list);
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody] ProfileBuilding profileBuilding)
        {
            try
            {
                switch (profileBuilding.Action)
                {
                    case "validate SFG stock":
                        await _profileBuildingService.StockValidationForProfileBuildingAsync(profileBuilding.ProfileBuildingId, profileBuilding.ProductionQty.Value);
                        break;
                    case "update end date":
                        await _profileBuildingService.UpdateEndDateAsync(profileBuilding.ProfileBuildingId, profileBuilding.ProductionQty.Value); break;
                    case "update barcode printed":
                        await _profileBuildingService.UpdateBarcodePrintedAsync(profileBuilding.ProfileBuildingLineIds); break;
                }

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }
    }
}