﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Authorize]
    [Route("api/Sales/[controller]")]
    [ApiController]
    public class CustomerPaymentLinesController : ControllerBase
    {
        private readonly ICustomerPaymentLineService _customerPaymentLineService;

        public CustomerPaymentLinesController(ICustomerPaymentLineService customerPaymentLineService)
        {
            _customerPaymentLineService = customerPaymentLineService;
        }

        [HttpGet]
        [Route("{customerPaymentId}")]
        public async Task<IActionResult> GetByHCustomerPaymentIdAsync(int customerPaymentId)
        {
            return Ok(await _customerPaymentLineService.GetByCustomerPaymentIdAsync(customerPaymentId));
        }

    }
}