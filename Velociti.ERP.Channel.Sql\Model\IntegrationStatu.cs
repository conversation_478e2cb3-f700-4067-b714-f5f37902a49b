﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("IntegrationStatus", Schema = "adm")]
    public partial class IntegrationStatu
    {
        [Key]
        public int IntegrationStatusId { get; set; }
        public int? CompanyId { get; set; }
        public byte? TypeEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastSyncDate { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("IntegrationStatus")]
        public virtual Company Company { get; set; }
    }
}
