﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class LoadingPlanLineDetailsController : ControllerBase
    {
        private readonly ILoadingPlanLineDetailService _loadingPlanLineDetailService;  

        public LoadingPlanLineDetailsController(ILoadingPlanLineDetailService loadingPlanLineDetailService)    
        {
            _loadingPlanLineDetailService = loadingPlanLineDetailService;
        }

        [HttpGet]
        [Route("{loadingPlanId}")]
        public async Task<IActionResult> Get(int loadingPlanId)  
        {
            return Ok(await _loadingPlanLineDetailService.GetAsync(loadingPlanId));
        }

        [HttpPost]
        [Route("{loadingPlanId}/warehouse/{warehouseId}/Product/{productId}/Barcode/{barcode}/User/{modifiedUserId}")]
        public async Task<IActionResult> Save(int loadingPlanId, int warehouseId, int productId, string barcode, int modifiedUserId)  
        {
            await _loadingPlanLineDetailService.SaveAsync(loadingPlanId, warehouseId, productId, barcode, modifiedUserId);
            
            return Ok();
        }

        [HttpPost]
        [Route("{loadingPlanId}/Company/{companyId}/Warehouse/{warehouseId}/Palletcode/{palletcode}/User/{modifiedUserId}")]
        public async Task<IActionResult> SavePallet(int loadingPlanId, int companyId, int warehouseId, string palletcode, int modifiedUserId)    
        {
            await _loadingPlanLineDetailService.SavePalletAsync(loadingPlanId, companyId, warehouseId, palletcode, modifiedUserId);

            return Ok();
        }

        [HttpDelete]
        [Route("{loadingPlanLineDetailId}")]
        public async Task<IActionResult> Delete(int loadingPlanLineDetailId)  
        {
            await _loadingPlanLineDetailService.DeleteAsync(loadingPlanLineDetailId);

            return Ok();
        }

        [HttpDelete]
        [Route("All/{loadingPlanId}")]
        public async Task<IActionResult> DeleteAll(int loadingPlanId)
        {
            await _loadingPlanLineDetailService.DeleteAllAsync(loadingPlanId);

            return Ok();
        }

        [HttpDelete]
        [Route("Selected/{loadingPlanLineDetailIds}")]
        public async Task<IActionResult> DeleteSelected(string loadingPlanLineDetailIds)    
        {
            List<int> ids = loadingPlanLineDetailIds.Split(',').Select(int.Parse).ToList();
            await _loadingPlanLineDetailService.DeleteSelectedAsync(ids);

            return Ok();
        }
    }
}