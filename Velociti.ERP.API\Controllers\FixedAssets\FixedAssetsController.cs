﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.FixedAssets;
using Velociti.ERP.Domain.Services.FixedAssets;

namespace Velociti.ERP.API.Controllers.FixedAssets
{
    [Route("api/FixedAssets/[controller]")]
    [Authorize]
    [ApiController]
    public class FixedAssetsController : ControllerBase
    {
        private readonly IFixedAssetService _fixedAssetService;

        public FixedAssetsController(IFixedAssetService fixedAssetService)
        {
            _fixedAssetService = fixedAssetService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _fixedAssetService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("{fixedAssetId}")]
        public async Task<IActionResult> GetById(int fixedAssetId)
        {
            return Ok(await _fixedAssetService.FindByIdAsync(fixedAssetId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _fixedAssetService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]FixedAsset fixedAsset)
        {
            return Ok(await _fixedAssetService.SaveAsync(fixedAsset));
        }

        [HttpDelete]
        [Route("{fixedAssetId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int fixedAssetId, int userId)
        {
            await _fixedAssetService.ToggleActivationAsync(fixedAssetId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]FixedAsset fixedAsset)
        {
            await _fixedAssetService.SubmitAsync(fixedAsset.FixedAssetId, fixedAsset.ModifiedUserId.Value);
            return Ok();
        }
    }
}