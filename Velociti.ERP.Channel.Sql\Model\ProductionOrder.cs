﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductionOrders", Schema = "man")]
    public partial class ProductionOrder
    {
        public ProductionOrder()
        {
            InternalOrderLines = new HashSet<InternalOrderLine>();
            ProductionOrderLines = new HashSet<ProductionOrderLine>();
            SemiFinishedGoodsProductionPlans = new HashSet<SemiFinishedGoodsProductionPlan>();
        }

        [Key]
        public int ProductionOrderId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        public int? RefDocId { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        public byte? TypeEnum { get; set; }
        public int? ProductId { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? PlannedWeight { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ActualWeight { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BarcodeScannedStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CompletedDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualEndDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BarcodePrintedDate { get; set; }
        public int? BillOfMaterialId { get; set; }
        public int? BillOfOperationId { get; set; }
        [StringLength(50)]
        public string Barcode { get; set; }
        [StringLength(100)]
        public string Scrap { get; set; }
        public byte? StatusEnum { get; set; }
        public int? CancelledActivityId { get; set; }
        public int? ParentId { get; set; }
        [Column("BOMLevel")]
        public byte? Bomlevel { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfMaterialId))]
        [InverseProperty("ProductionOrders")]
        public virtual BillOfMaterial BillOfMaterial { get; set; }
        [ForeignKey(nameof(BillOfOperationId))]
        [InverseProperty("ProductionOrders")]
        public virtual BillOfOperation BillOfOperation { get; set; }
        [ForeignKey(nameof(CancelledActivityId))]
        [InverseProperty(nameof(SupportData.ProductionOrders))]
        public virtual SupportData CancelledActivity { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ProductionOrders")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ProductionOrders")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(InternalOrderLine.ProductionOrderIdLinkedToReworkNavigation))]
        public virtual ICollection<InternalOrderLine> InternalOrderLines { get; set; }
        [InverseProperty(nameof(ProductionOrderLine.ProductionOrder))]
        public virtual ICollection<ProductionOrderLine> ProductionOrderLines { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsProductionPlan.ProductionOrderIdLinkedToReworkNavigation))]
        public virtual ICollection<SemiFinishedGoodsProductionPlan> SemiFinishedGoodsProductionPlans { get; set; }
    }
}
