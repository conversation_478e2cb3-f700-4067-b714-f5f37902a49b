﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class CompaniesController : ControllerBase
    {
        private readonly ICompanyService _companyService;

        public CompaniesController(ICompanyService companyService)
        {
            _companyService = companyService;
        }

        [HttpGet]
        [Route("ShortList/User/{userId}")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetShortListForAssignUser(int userId)
        {
            var list = await _companyService.GetShortListForAssignUserAsync(userId);

            return Ok(list);
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetByIdAsync(int companyId)
        {
            var list = await _companyService.GetByIdAsync(companyId);

            return Ok(list);
        }
    }
}