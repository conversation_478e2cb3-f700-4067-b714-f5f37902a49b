﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GoodsReceiveNoteLines", Schema = "inv")]
    public partial class GoodsReceiveNoteLine
    {
        public GoodsReceiveNoteLine()
        {
            CostingWarehous = new HashSet<CostingWarehous>();
            GoodsReceiveNoteLineDetails = new HashSet<GoodsReceiveNoteLineDetail>();
        }

        [Key]
        public int GoodsReceiveNoteLineId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column("QCPassedQuantity", TypeName = "decimal(18, 5)")]
        public decimal? QcpassedQuantity { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? ReceivedQuantity { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Tolerance { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? ApportionedValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ProductExpiryDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? WarrantyDate { get; set; }
        [StringLength(32)]
        public string LotNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.GoodsReceiveNoteLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.GoodsReceiveNoteLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("GoodsReceiveNoteLines")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("GoodsReceiveNoteLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("GoodsReceiveNoteLines")]
        public virtual TaxGroup TaxGroup { get; set; }
        [InverseProperty("GoodsReceiveNoteLine")]
        public virtual ICollection<CostingWarehous> CostingWarehous { get; set; }
        [InverseProperty(nameof(GoodsReceiveNoteLineDetail.GoodsReceiveNoteLine))]
        public virtual ICollection<GoodsReceiveNoteLineDetail> GoodsReceiveNoteLineDetails { get; set; }
    }
}
