﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class ExchangeOrderLinesController : ControllerBase
    {
        private readonly IExchangeOrderLineService _exchangeOrderLineService;

        public ExchangeOrderLinesController(IExchangeOrderLineService exchangeOrderLineService)
        {
            _exchangeOrderLineService = exchangeOrderLineService;
        }

        [HttpGet]
        [Route("{exchangeOrderId}/Company/{companyId}")]
        public async Task<IActionResult> GetAll(int exchangeOrderId, int companyId)
        {
            return Ok(await _exchangeOrderLineService.GetByHeaderIdAsync(companyId, exchangeOrderId));
        }
    }
}