using System;
using System.IO.Compression;
using System.Net;
using System.Text;
using AutoMapper;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Diagnostics;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.ResponseCompression;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using Velociti.ERP.API.Utils;
using Velociti.ERP.Channel.Mock.Services;
using Velociti.ERP.Channel.Sql;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Administration;
using Velociti.ERP.Channel.Sql.Repositories.Common;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Common;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Finance;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.FixedAssets;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.HR;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Inventory;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Manufacturing;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Procurement;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Sales;
using Velociti.ERP.Channel.Sql.Repositories.Finance;
using Velociti.ERP.Channel.Sql.Repositories.FixedAssets;
using Velociti.ERP.Channel.Sql.Repositories.HR;
using Velociti.ERP.Channel.Sql.Repositories.Inventory;
using Velociti.ERP.Channel.Sql.Repositories.Manufacturing;
using Velociti.ERP.Channel.Sql.Repositories.Procurement;
using Velociti.ERP.Channel.Sql.Repositories.Sales;
using Velociti.ERP.Channel.Sql.Services.Administration;
using Velociti.ERP.Channel.Sql.Services.Common;
using Velociti.ERP.Channel.Sql.Services.Finance;
using Velociti.ERP.Channel.Sql.Services.FixedAssets;
using Velociti.ERP.Channel.Sql.Services.HR;
using Velociti.ERP.Channel.Sql.Services.Inventory;
using Velociti.ERP.Channel.Sql.Services.Manufacturing;
using Velociti.ERP.Channel.Sql.Services.Procurement;
using Velociti.ERP.Channel.Sql.Services.Sales;
using Velociti.ERP.Channel.Sql.Utils;
using Velociti.ERP.Domain.Services.Administration;
using Velociti.ERP.Domain.Services.Common;
using Velociti.ERP.Domain.Services.Finance;
using Velociti.ERP.Domain.Services.FixedAssets;
using Velociti.ERP.Domain.Services.HR;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Manufacturing;
using Velociti.ERP.Domain.Services.Procurement;
using Velociti.ERP.Domain.Services.Production;
using Velociti.ERP.Domain.Services.Sales;
using Velociti.ERP.Domain.Utils;

namespace Velociti.ERP.API
{
    public class Startup
    {
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }

        // This method gets called by the runtime. Use this method to add services to the container.
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddCors(options =>
            {
                options.AddPolicy("AllowFrontend", builder =>
                {
                    builder.WithOrigins("https://localhost:44355")
                           .AllowAnyHeader()
                           .AllowAnyMethod();
                });
            });




            services.AddCors();
            services.AddControllers();

            // configure strongly typed settings objects
            var appSettingsSection = Configuration.GetSection("AppSettings");
            services.Configure<AppSettings>(appSettingsSection);

            #region Repository DI
            services.AddScoped<ICurrencyRepository, CurrencyRepository>();
            services.AddScoped<ISupportDataRepository, SupportDataRepository>();
            services.AddScoped<IOrganizationRepository, OrganizationRepository>();
            services.AddScoped<ILoginRepository, LoginRepository>();
            services.AddScoped<ITokenRepository, TokenRepository>();
            services.AddScoped<IDateFormatRepository, DateFormatRepository>();
            services.AddScoped<IHoldingsRepository, HoldingsRepository>();
            services.AddScoped<ISubGroupsRepository, SubGroupsRepository>();
            services.AddScoped<ICompanyRepository, CompanyRepository>();
            services.AddScoped<IDepartmentsRepository, DepartmentRepository>();
            services.AddScoped<IDivisionsRepository, DivisionsRepository>();
            services.AddScoped<IDesignationsRepository, DesignationsRepository>();
            services.AddScoped<IUsersRepository, UsersRepository>();
            services.AddScoped<IUserCompanyRepository, UserCompanyRepository>();
            services.AddScoped<IUserSupervisorsRepository, UserSupervisorsRepository>();
            services.AddScoped<IUserDepartmentsRepository, UserDepartmentsRepository>();
            services.AddScoped<IDepartmentsRepository, DepartmentRepository>();
            services.AddScoped<IDocCodeConfigRepository, DocCodeConfigRepository>();
            services.AddScoped<IUserDivisionsRepository, UserDivisionsRepository>();
            services.AddScoped<IDocTypeConfigRepository, DocTypeConfigRepository>();
            services.AddScoped<IWarehouseRepository, WarehouseRepository>();
            services.AddScoped<IProductHierarchyRepository, ProductHierarchyRepository>();
            services.AddScoped<IEmployeeRepository, EmployeeRepository>();
            services.AddScoped<IProductRepository, ProductRepository>();
            services.AddScoped<ISupplierProductRepository, SupplierProductRepository>();
            services.AddScoped<IUnitOfMeasuresRepository, UnitOfMeasuresRepository>();
            services.AddScoped<IProductUnitOfMeasuresRepository, ProductUnitOfMeasuresRepository>();
            services.AddScoped<IPurchaseRequisitionNotesRepository, PurchaseRequisitionNotesRepository>();
            services.AddScoped<IPurchaseRequisitionNoteLinesRepository, PurchaseRequisitionNoteLinesRepository>();
            services.AddScoped<ISupplementaryManufacturerRepository, SupplementaryManufacturerRepository>();
            services.AddScoped<ISupplementaryManufacturerContactRepository, SupplementaryManufacturerContactRepository>();
            services.AddScoped<IContactRepository, ContactRepository>();
            services.AddScoped<ISuppliersRepository, SuppliersRepository>();
            services.AddScoped<IQuotationRepository, QuotationRepository>();
            services.AddScoped<IPurchaseOrderRepository, PurchaseOrderRepository>();
            services.AddScoped<IQuotationLineRepository, QuotationLineRepository>();
            services.AddScoped<IPurchaseOrderLinesRepository, PurchaseOrderLinesRepository>();
            services.AddScoped<IRolesRepository, RolesRepository>();
            services.AddScoped<IGroupsRepository, GroupsRepository>();
            services.AddScoped<IGroupRoleRepository, GroupRoleRepository>();
            services.AddScoped<IUserGroupsRepository, UserGroupsRepository>();
            services.AddScoped<IUserRoleRepository, UserRoleRepository>();
            services.AddScoped<IResourceRepository, ResourceRepository>();
            services.AddScoped<IModuleRepository, ModuleRepository>();
            services.AddScoped<IPermissionRepository, PermissionRepository>();
            services.AddScoped<IAccountsRepository, AccountsRepository>();
            services.AddScoped<ISupplierContactRepository, SupplierContactRepository>();
            //services.AddScoped<ITaxGroupRepository, TaxGroupRepository>();
            services.AddScoped<IFixedAssetRepository, FixedAssetRepository>();
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<IPriceListRepository, PriceListRepository>();
            services.AddScoped<ISalesOrdersRepository, SalesOrdersRepository>();
            services.AddScoped<IEmployeeRepository, EmployeeRepository>();
            services.AddScoped<ISalesOrderLinesRepository, SalesOrderLinesRepository>();
            services.AddScoped<IWarehouseProductsRepository, WarehouseProductsRepository>();
            services.AddScoped<IGoodsReceivedNotesRepository, GoodsReceivedNotesRepository>();
            services.AddScoped<IServiceInvoiceRepository, ServiceInvoiceRepository>();
            services.AddScoped<IServiceInvoiceLineRepository, ServiceInvoiceLineRepository>();
            services.AddScoped<IAuditLogRepository, AuditLogRepository>();
            services.AddScoped<IStockTransferRepository, StockTransferRepository>();
            services.AddScoped<IStockTransferLineRepository, StockTransferLineRepository>();
            services.AddScoped<ISubContractOrderRepository, SubContractOrderRepository>();
            services.AddScoped<ISubContractOrderLineRepository, SubContractOrderLineRepository>();
            services.AddScoped<IMachinesRepository, MachinesRepository>();
            services.AddScoped<IStockTakeRepository, StockTakeRepository>();
            services.AddScoped<IProductionPlanLinesRepository, ProductionPlanLinesRepository>();
            services.AddScoped<IExchangeOrderRepository, ExchangeOrderRepository>();
            services.AddScoped<IExchangeOrderLineRepository, ExchangeOrderLineRepository>();
            services.AddScoped<ITyreSpecificationsRepository,TyreSpecificationsRepository>();
            services.AddScoped<IStockTakeLinesRepository, StockTakeLinesRepository>();
            services.AddScoped<IGoodsReceivedNoteLinesRepository, GoodsReceivedNoteLinesRepository>();
            services.AddScoped<IGoodsReceiveNoteLineDetailsRepository, GoodsReceiveNoteLineDetailsRepository>();
            services.AddScoped<IShipmentQualityControlsRepository, ShipmentQualityControlsRepository>();
            services.AddScoped<IShipmentQualityControlLinesRepository, ShipmentQualityControlLinesRepository>();
            services.AddScoped<IShipmentQualityControlReturnsRepository, ShipmentQualityControlReturnsRepository>();
            services.AddScoped<IShipmentQualityControlReturnLinesRepository, ShipmentQualityControlReturnLinesRepository>();
            services.AddScoped<IProductPackingMethodRepository, ProductPackingMethodRepository>();
            services.AddScoped<IInboundLoanRepository, InboundLoanRepository>();
            services.AddScoped<IInboundLoanLineRepository, InboundLoanLineRepository>();
            services.AddScoped<ICustomerRepository, CustomerRepository>();
            services.AddScoped<ICustomerContactRepository, CustomerContactRepository>();
            services.AddScoped<IMaterialRequisitionNotesRepository, MaterialRequisitionNotesRepository>();
            services.AddScoped<IMaterialRequisitionNoteLinesRepository, MaterialRequisitionNoteLinesRepository>();
            services.AddScoped<IInternalServiceInvoicesRepository, InternalServiceInvoicesRepository>();
            services.AddScoped<IInternalServiceInvoiceLinesRepository, InternalServiceInvoiceLinesRepository>();
            services.AddScoped<IInternalServiceInvoiceLineDetailsRepository, InternalServiceInvoiceLineDetailsRepository>();
            services.AddScoped<IOutboundLoanRepository, OutboundLoanRepository>();
            services.AddScoped<IOutboundLoanLineRepository, OutboundLoanLineRepository>();
            services.AddScoped<IPurchaseReturnRepository, PurchaseReturnRepository>();
            services.AddScoped<IPurchaseReturnLineRepository, PurchaseReturnLineRepository>();
            services.AddScoped<IPackingListsRepository, PackingListsRepository>();
            services.AddScoped<IPackingListLinesRepository, PackingListLinesRepository>();
            services.AddScoped<IGoodsDispatchNoteRepository, GoodsDispatchNoteRepository>();
            services.AddScoped<IGoodsDispatchNoteLineRepository, GoodsDispatchNoteLineRepository>();
            services.AddScoped<IInternalOrderRepository, InternalOrderRepository>();
            services.AddScoped<IInternalOrderLinesRepository, InternalOrderLinesRepository>();
            services.AddScoped<IPerformaInvoiceRepository, PerformaInvoiceRepository>();
            services.AddScoped<IPerformaInvoiceLineRepository, PerformaInvoiceLineRepository>();
            services.AddScoped<IInternalReturnRepository, InternalReturnRepository>();
            services.AddScoped<IInternalReturnLinesRepository, InternalReturnLinesRepository>();
            services.AddScoped<ILoadingPlansRepository, LoadingPlansRepository>();
            services.AddScoped<ILoadingPlanLinesRepository, LoadingPlanLinesRepository>();
            services.AddScoped<IStockAdjustmentsRepository, StockAdjustmentsRepository>();
            services.AddScoped<IStockAdjustmentLinesRepository, StockAdjustmentLinesRepository>();
            services.AddScoped<IInternalDispatchRepository, InternalDispatchRepository>();
            services.AddScoped<IInternalDispatchLineRepository, InternalDispatchLineRepository>();
            services.AddScoped<ICommercialInvoicesRepository, CommercialInvoicesRepository>();
            services.AddScoped<ISalesInvoiceLinesRepository, SalesInvoiceLinesRepository>();
            services.AddScoped<ISalesInvoicesRepository, SalesInvoicesRepository>();
            services.AddScoped<IInternalDispatchLineDetailRepository, InternalDispatchLineDetailRepository>();
            services.AddScoped<ISalesReturnsRepository, SalesReturnsRepository>();
            services.AddScoped<ISalesReturnLinesRepository, SalesReturnLinesRepository>();
            services.AddScoped<IStockTransferReceiptsRepository, StockTransferReceiptsRepository>();
            services.AddScoped<IStockTransferReceiptLinesRepository, StockTransferReceiptLinesRepository>();
            services.AddScoped<IChartOfAccountRepository, ChartOfAccountRepository>();
            services.AddScoped<IStockAllocationsRepository, StockAllocationsRepository>();
            services.AddScoped<ITaxTypeRepository, TaxTypeRepository>();
            services.AddScoped<ICurrencyExchangeRatesRepository, CurrencyExchangeRatesRepository>();
            services.AddScoped<ITaxGroupTaxTypeRepository, TaxGroupTaxTypeRepository>();
            services.AddScoped<ITaxGroupRepository, TaxGroupRepository>();
            services.AddScoped<ISalesServiceInvoiceRepository, SalesServiceInvoiceRepository>();
            services.AddScoped<ISalesServiceInvoiceLineRepository, SalesServiceInvoiceLineRepository>();
            services.AddScoped<IAccountsRepository, AccountsRepository>();
            services.AddScoped<IBankAdjustmentRepository, BankAdjustmentRepository>();
            services.AddScoped<IBankAdjustmentLineRepository, BankAdjustmentLineRepository>();
            services.AddScoped<IManualJournalRepository, ManualJournalRepository>();
            services.AddScoped<IManualJournalLineRepository, ManualJournalLineRepository>();
            services.AddScoped<IManualJournalCostAllocationRepository, ManualJournalCostAllocationRepository>();
            services.AddScoped<IServiceInvoiceMatarialRequestNotesRepository, ServiceInvoiceMatarialRequestNotesRepository>();
            services.AddScoped<IPayBookRepository, PayBookRepository>();
            services.AddScoped<IPaymentAdvicesRepository, PaymentAdvicesRepository>();
            services.AddScoped<IPaymentAdviceLinesRepository, PaymentAdviceLinesRepository>();
            services.AddScoped<IPaymentAdviceCostAllocationsRepository, PaymentAdviceCostAllocationsRepository>();
            services.AddScoped<IOutboundPaymentRepository, OutboundPaymentRepository>();
            services.AddScoped<IOutboundPaymentLineRepository, OutboundPaymentLineRepository>();
            services.AddScoped<IGoodsDispatchNoteLineDetailRepository, GoodsDispatchNoteLineDetailRepository>();
            services.AddScoped<IChargeInfoChargeGroupsRepository, ChargeInfoChargeGroupsRepository>();
            services.AddScoped<IChequeBookRepository, ChequeBookRepository>();
            services.AddScoped<IFinancialStatementsRepository, FinancialStatementsRepository>();
            services.AddScoped<IFinancialStatementLinesRepository, FinancialStatementLinesRepository>();
            services.AddScoped<IFinancialStatementLineDetailsRepository, FinancialStatementLineDetailsRepository>();
            services.AddScoped<IPasswordPolicyRepository, PasswordPolicyRepository>();
            services.AddScoped<IMouldRepository, MouldRepository>();
            services.AddScoped<IStockTransferDetailRepository, StockTransferDetailRepository>();
            services.AddScoped<IMachineLinesRepository, MachineLinesRepository>();
            services.AddScoped<IChargeInformationsRepository, ChargeInformationsRepository>();
            services.AddScoped<IAgingBucketsRepository, AgingBucketsRepository>();
            services.AddScoped<IInboundReceiptRepository, InboundReceiptRepository>();
            services.AddScoped<IInboundReceiptLineRepository, InboundReceiptLineRepository>();
            services.AddScoped<IEmployeeContactRepository, EmployeeContactRepository>();
            services.AddScoped<IEmployeeAccountsRepository, EmployeeAccountsRepository>();
            services.AddScoped<IEmployeeIncrementsRepository, EmployeeIncrementsRepository>();
            services.AddScoped<IPettyCashRepository, PettyCashRepository>();
            services.AddScoped<IPettyCashLineRepository, PettyCashLineRepository>();
            services.AddScoped<IEmailTemplatesRepository, EmailTemplatesRepository>();
            services.AddScoped<IEmailTemplateLinesRepository, EmailTemplateLinesRepository>();
            services.AddScoped<IWorkstationCadresRepository, WorkstationCadresRepository>();
            services.AddScoped<IMouldProductsRepository, MouldProductsRepository>();
            services.AddScoped<IMachineMouldsRepository, MachineMouldsRepository>();
            services.AddScoped<IWorkFlowRepository, WorkFlowRepository>();
            services.AddScoped<IWorkFlowLineRepository, WorkFlowLineRepository>();
            services.AddScoped<IShipmentCostRepository, ShipmentCostRepository>();
            services.AddScoped<IShipmentCostLineRepository, ShipmentCostLineRepository>();
            services.AddScoped<ICostingWarehouseRepository, CostingWarehouseRepository>();
            services.AddScoped<IBillOfMaterialsRepository, BillOfMaterialsRepository>();
            services.AddScoped<IBillOfMaterialLinesRepository, BillOfMaterialLinesRepository>();
            services.AddScoped<IBillOfOperationsRepository, BillOfOperationsRepository>();
            services.AddScoped<IBillOfOperationLinesRepository, BillOfOperationLinesRepository>();
            services.AddScoped<IBillOfOperationLineDetailsRepository, BillOfOperationLineDetailsRepository>();
            services.AddScoped<IDocumentUploadRepository, DocumentUploadRepository>();
            services.AddScoped<IBillOfOperationLineOverheadsRepository, BillOfOperationLineOverheadsRepository>();
            services.AddScoped<ITxnWorkFlowLineRepository, TxnWorkFlowLineRepository>();
            services.AddScoped<IMouldCavitiesRepository, MouldCavitiesRepository>();
            services.AddScoped<IMachineVisualPlanLinesRepository, MachineVisualPlanLinesRepository>();
            services.AddScoped<ICommercialInvoicesLinesRepository, CommercialInvoicesLinesRepository>();
            services.AddScoped<IQCParametersRepository, QCParametersRepository>();
            services.AddScoped<IQCParameterMappingsRepository, QCParameterMappingsRepository>();
            services.AddScoped<ISalesReturnLineDetailsRepository, SalesReturnLineDetailsRepository>();
            services.AddScoped<IPressLinesRepository, PressLinesRepository>();
            services.AddScoped<IWarehousePalletsRepository, WarehousePalletsRepository>();
            services.AddScoped<IAttendenceAllowanceRepository, AttendenceAllowanceRepository>();
            services.AddScoped<IHRSettingsRepository, HRSettingsRepository>();
            services.AddScoped<IProductionPlanBarcodesRepository, ProductionPlanBarcodesRepository>();
            services.AddScoped<ISemiFinishedGoodsProductionPlansRepository, SemiFinishedGoodsProductionPlansRepository>();
            services.AddScoped<IProductionPlansRepository, ProductionPlansRepository>();
            services.AddScoped<IChequeBookLinesRepository, ChequeBookLinesRepository>();
            services.AddScoped<IFixedAssetMaintenanceRepository, FixedAssetMaintenanceRepository>();
            services.AddScoped<IFixedAssetRegistryRepository, FixedAssetRegistryRepository>();
            services.AddScoped<IProductionOrdersRepository, ProductionOrdersRepository>();
            services.AddScoped<IPurchaseOrderLineDetailsRepository, PurchaseOrderLineDetailsRepository>();
            services.AddScoped<IProductionOrderLinesRepository, ProductionOrderLinesRepository>();
            services.AddScoped<IProductionOrderLineDetailsRepository, ProductionOrderLineDetailsRepository>();
            services.AddScoped<IMachineProductComponentRepository, MachineProductComponentRepository>();
            services.AddScoped<IAccountMappingsRepository, AccountMappingsRepository>();
            services.AddScoped<IAccountMappingLinesRepository, AccountMappingLinesRepository>();
            services.AddScoped<IAccountMappingLineDetailsRepository, AccountMappingLineDetailsRepository>();
            services.AddScoped<IBaseIncentiveRateRepository, BaseIncentiveRateRepository>();
            services.AddScoped<IScrapPenaltyDeductionRatesRepository, ScrapPenaltyDeductionRatesRepository>();
            services.AddScoped<IIncentiveApportionmentRatesRepository, IncentiveApportionmentRatesRepository>();
            services.AddScoped<IProductLicenceRegistrationRepository, ProductLicenceRegistrationRepository>();
            services.AddScoped<ISemiFinishedGoodsQCScanningsRepository, SemiFinishedGoodsQCScanningsRepository>();
            services.AddScoped<ISemiFinishedGoodsQCScanningLinesRepository, SemiFinishedGoodsQCScanningLinesRepository>();
            services.AddScoped<IMaterialRequisitionPlansRepository, MaterialRequisitionPlansRepository>();
            services.AddScoped<ISalesServiceOrderRepository, SalesServiceOrderRepository>();
            services.AddScoped<ILoadingPlanLineDetailsRepository, LoadingPlanLineDetailsRepository>();
            services.AddScoped<IGeneralLedgerLineRepository, GeneralLedgerLineRepository>();
            services.AddScoped<ISalesServiceOrderLinesRepository, SalesServiceOrderLinesRepository>();
            services.AddScoped<IServiceInquiryRepository, ServiceInquiryRepository>();
            services.AddScoped<IServiceInquiryLinesRepository, ServiceInquiryLinesRepository>();
            services.AddScoped<ISalesServiceOrderJobRepository, SalesServiceOrderJobRepository>();
            services.AddScoped<ISalesServiceOrderJobLineRepository, SalesServiceOrderJobLineRepository>();
            services.AddScoped<ITxnLineHistoryRepository, TxnLineHistoryRepository>();
            services.AddScoped<IAttendencesRepository, AttendencesRepository>();
            services.AddScoped<ITouchScreenHistoryRepository, TouchScreenHistoryRepository>();
            services.AddScoped<IFreeIssuesRepository, FreeIssuesRepository>();
            services.AddScoped<IFreeIssueLinesRepository, FreeIssueLinesRepository>();
            services.AddScoped<IFreeIssueCriteriaLinesRepository, FreeIssueCriteriaLinesRepository>();
            services.AddScoped<IDiscountSchemesRepository, DiscountSchemesRepository>();
            services.AddScoped<IDiscountSchemeLinesRepository, DiscountSchemeLinesRepository>();
            services.AddScoped<IDiscountSchemeCriteriaLinesRepository, DiscountSchemeCriteriaLinesRepository>();
            services.AddScoped<IAppSettingsRepository, AppSettingsRepository>();
            services.AddScoped<IStockCountSheetLinesRepository, StockCountSheetLinesRepository>();
            services.AddScoped<IStockCountSheetsRepository, StockCountSheetsRepository>();
            services.AddScoped<IProductDiesRepository, ProductDiesRepository>();
            services.AddScoped<IProfileBuildingRepository, ProfileBuildingRepository>();
            services.AddScoped<IGeneralLedgerSettlementRepository, GeneralLedgerSettlementRepository>();
            services.AddScoped<IPurchaseOrderSalesInvoicesRepository, PurchaseOrderSalesInvoicesRepository>();
            services.AddScoped<IServiceInvoiceSalesInvoicesRepository, ServiceInvoiceSalesInvoicesRepository>();
            services.AddScoped<IBundleProductRepository, BundleProductRepository>();
            services.AddScoped<IBundleProductLineRepository, BundleProductLineRepository>();
            services.AddScoped<IProductBrandDiscountRepository, ProductBrandDiscountRepository>();
            services.AddScoped<ISalesInvoiceChargeGroupsRepository, SalesInvoiceChargeGroupsRepository>();
            services.AddScoped<IPurchaseOrderAdditionalChargeRepository, PurchaseOrderAdditionalChargeRepository>();
            services.AddScoped<IPurchaseOrderAdditionalChargeLineRepository, PurchaseOrderAdditionalChargeLineRepository>();
            services.AddScoped<ICustomerPaymentsRepository, CustomerPaymentsRepository>();
            services.AddScoped<ICustomerPaymentLineRepository, CustomerPaymentLineRepository>();
            services.AddScoped<IPromotionRepository, PromotionRepository>();
            services.AddScoped<IProductionNotesRepository, ProductionNotesRepository>();
            services.AddScoped<IPromotionLinesRepository, PromotionLinesRepository>();
            services.AddScoped<IBankReconciliationsRepository, BankReconciliationsRepository>();
            services.AddScoped<IBankReconciliationLinesRepository, BankReconciliationLinesRepository>();
            services.AddScoped<IOutboundPaymentCostAllocationRepository, OutboundPaymentCostAllocationRepository>();
            services.AddScoped<IInboundReceiptCostAllocationRepository, InboundReceiptCostAllocationRepository>();
            services.AddScoped<IPettyCashCostAllocationRepository, PettyCashCostAllocationRepository>();
            services.AddScoped<IGeneralLedgerSettlementLineRepository, GeneralLedgerSettlementLineRepository>();
            services.AddScoped<ITransactionTypesRepository, TransactionTypesRepository>();
            services.AddScoped<IRawMaterialPlanningsRepository, RawMaterialPlanningsRepository>();
            services.AddScoped<IConsignmentStockTransferLineRepository, ConsignmentStockTransferLineRepository>();
            services.AddScoped<IConsignmentStockTransferRepository, ConsignmentStockTransferRepository>();
            services.AddScoped<IHOReturnStockTransferRepository, HOReturnStockTransferRepository>();
            services.AddScoped<IHOReturnStockTransferLineRepository, HOReturnStockTransferLineRepository>();
            services.AddScoped<INotificationRepository, NotificationRepository>();
            services.AddScoped<IMenuHeadRepository, MenuHeadRepository>();
            services.AddScoped<IMenuHeadLineRepository, MenuHeadLinesRepository>();
            services.AddScoped<IMenuCategoryRepository, MenuCategoryRepository>();
            services.AddScoped<IPrintersRepository, PrintersRepository>();
            services.AddScoped<IBankPromotionRepository, BankPromotionRepository>();
            #endregion

            #region Services DI
            services.AddScoped<ICurrencyService, CurrencyService>();
            services.AddScoped<ISupportDataService, SupportDataService>();
            services.AddScoped<IOrganizationService, OrganizationService>();
            services.AddScoped<IAuthService, AuthService>();
            services.AddScoped<IJwtService, JwtService>();
            services.AddScoped<IDateFormatService, DateFormatService>();
            services.AddScoped<IDesignationService, DesignationService>();
            services.AddScoped<IUserService, UserService>();
            services.AddScoped<ICompanyService, CompanyService>();
            services.AddScoped<IUserCompanyService, UserCompanyService>();
            services.AddScoped<IUserSupervisorService, UserSupervisorService>();
            services.AddScoped<IUserDepartmentService, UserDepartmentService>();
            services.AddScoped<IDepartmentService, DepartmentService>();
            services.AddScoped<IDocCodeConfigService, DocCodeConfigService>();
            services.AddScoped<IUserDivisionService, UserDivisionService>();
            services.AddScoped<IDocTypeConfigService, DocTypeConfigService>();
            services.AddScoped<IDivisionService, DivisionService>();
            services.AddScoped<IWarehouseService, WarehouseService>();
            services.AddScoped<IProductHierarchyService, ProductHierarchyService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<IProductService, ProductService>();
            services.AddScoped<IUnitOfMeasureService, UnitOfMeasureService>();
            services.AddScoped<IProductUnitOfMeasureService, ProductUnitOfMeasureService>();
            services.AddScoped<IPurchaseRequisitionNoteService, PurchaseRequisitionNoteService>();
            services.AddScoped<IPurchaseRequisitionNoteLineService, PurchaseRequisitionNoteLineService>();
            services.AddScoped<ISupplementaryManufacturerService, SupplementaryManufacturerService>();
            services.AddScoped<ISupplementaryManufacturerContactService, SupplementaryManufacturerContactService>();
            services.AddScoped<IContactService, ContactService>();
            services.AddScoped<ISupplierService, SupplierService>();
            services.AddScoped<IQuotationService, QuotationService>();
            services.AddScoped<IPurchaseOrderService, PurchaseOrderService>();
            services.AddScoped<IQuotationLineService, QuotationLineService>();
            services.AddScoped<IPurchaseOrderLineService, PurchaseOrderLineService>();
            services.AddScoped<IRoleService, RoleService>();
            services.AddScoped<IGroupService, GroupService>();
            services.AddScoped<IGroupRoleService, GroupRoleService>();
            services.AddScoped<IUserGroupService, UserGroupService>();
            services.AddScoped<IUserRoleService, UserRoleService>();
            services.AddScoped<IResourceService, ResourceService>();
            services.AddScoped<IModuleService, ModuleService>();
            services.AddScoped<IPermissionService, PermissionService>();
            services.AddScoped<IAccountService, AccountService>();
            services.AddScoped<ISupplierContactService, SupplierContactService>();
            services.AddScoped<IFixedAssetService, FixedAssetService>();
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<IPriceListService, PriceListService>();
            services.AddScoped<ISalesOrderLineService, SalesOrderLineService>();
            services.AddScoped<ISalesOrderService, SalesOrderService>();
            services.AddScoped<IEmployeeService, EmployeeService>();
            services.AddScoped<ISalesOrderLineService, SalesOrderLineService>();
            services.AddScoped<IGoodsReceivedNoteService, GoodsReceivedNoteService>();
            services.AddScoped<IAuditLogService, AuditLogService>();
            services.AddScoped<IServiceInvoiceService,ServiceInvoiceService>();
            services.AddScoped<IServiceInvoiceLineService, ServiceInvoiceLineService>();
            services.AddScoped<IWarehouseProductService, WarehouseProductService>();
            services.AddScoped<IStockTranserService, StockTransferService>();
            services.AddScoped<IStockTransferLineService, StockTransferLineService>();
            services.AddScoped<ISubContractOrderService, SubContractOrderService>();
            services.AddScoped<ISubContractOrderLineService, SubContractOrderLineService>();
            services.AddScoped<IStockTakeService, StockTakeService>();
            services.AddScoped<IMachineService, MachineService>();
            services.AddScoped<IProductionPlanLineService, ProductionPlanLineService>();
            services.AddScoped<IDashboardService, MockDashboadService>();
            services.AddScoped<IExchangeOrderService, ExchangeOrderService>();
            services.AddScoped<IExchangeOrderLineService, ExchangeOrderLineService>();
            services.AddScoped<IDashboardService, MockDashboadService>();
            services.AddScoped<ITyreSpecificationsService, TyreSpecificationsService>();
            services.AddScoped<IStockTakeLineService, StockTakeLineService>();
            services.AddScoped<IGoodsReceivedNoteLineService, GoodsReceivedNoteLineService>();
            services.AddScoped<IGoodsReceiveNoteLineDetailService, GoodsReceiveNoteLineDetailService>();
            services.AddScoped<IShipmentQualityControlService, ShipmentQualityControlService>();
            services.AddScoped<IShipmentQualityControlLineService, ShipmentQualityControlLineService>();
            services.AddScoped<IShipmentQualityControlReturnService, ShipmentQualityControlReturnService>();
            services.AddScoped<IShipmentQualityControlReturnLineService, ShipmentQualityControlReturnLineService>();
            services.AddScoped<IProductPackingMethodService, ProductPackingMethodService>();
            services.AddScoped<IInboundLoanService, InboundLoanService>();
            services.AddScoped<IInboundLoanLineService, InboundLoanLineService>();
            services.AddScoped<ICustomerService, CustomerService>();
            services.AddScoped<ICustomerContactService, CustomerContactService>();
            services.AddScoped<IMaterialRequisitionNoteService, MaterialRequisitionNoteService>();
            services.AddScoped<IMaterialRequisitionNoteLineService, MaterialRequisitionNoteLineService>();
            services.AddScoped<IInternalServiceInvoiceService, InternalServiceInvoiceService>();
            services.AddScoped<IInternalServiceInvoiceLineService, InternalServiceInvoiceLineService>();
            services.AddScoped<IInternalServiceInvoiceLineDetailService, InternalServiceInvoiceLineDetailService>();
            services.AddScoped<IOutboundLoanService, OutboundLoanService>();
            services.AddScoped<IOutboundLoanLineService, OutboundLoanLineService>();
            services.AddScoped<IPurchaseReturnService, PurchaseReturnService>();
            services.AddScoped<IPurchaseReturnLineService, PurchaseReturnLineService>();
            services.AddScoped<IPackingListService, PackingListService>();
            services.AddScoped<IPackingListLineService, PackingListLineService>();
            services.AddScoped<IGoodsDispatchNoteService, GoodsDispatchNoteService>();
            services.AddScoped<IGoodsDispatchNoteLineService, GoodsDispatchNoteLineService>();
            services.AddScoped<IInternalOrderService, InternalOrderService>();
            services.AddScoped<IInternalOrderLineService, InternalOrderLineService>();
            services.AddScoped<IPerformaInvoiceService, PerformaInvoiceService>();
            services.AddScoped<IPerformaInvoiceLineService, PerformaInvoiceLineService>();
            services.AddScoped<IInternalReturnService, InternalReturnService>();
            services.AddScoped<IInternalReturnLineService, InternalReturnLineService>();
            services.AddScoped<ILoadingPlanService, LoadingPlanService>();
            services.AddScoped<ILoadingPlanLineService, LoadingPlanLineService>();
            services.AddScoped<IStockAdjustmentService, StockAdjustmentService>();
            services.AddScoped<IStockAdjustmentLineService, StockAdjustmentLineService>();
            services.AddScoped<IInternalDispatchService, InternalDispatchService>();
            services.AddScoped<IInternalDispatchLineService, InternalDispatchLineService>();
            services.AddScoped<ICommercialInvoiceService, CommercialInvoiceService>();
            services.AddScoped<ISalesInvoiceLineService, SalesInvoiceLineService>();
            services.AddScoped<ISalesInvoiceService, SalesInvoiceService>();
            services.AddScoped<IInternalDispatchLineDetailService, InternalDispatchLineDetailService>();
            services.AddScoped<ISalesReturnsService, SalesReturnsService>();
            services.AddScoped<ISalesReturnLineService, SalesReturnLineService>();
            services.AddScoped<IStockTransferReceiptService, StockTransferReceiptService>();
            services.AddScoped<IStockTransferReceiptLineService, StockTransferReceiptLineService>();
            services.AddScoped<IChartOfAccountService, ChartOfAccountService>();
            services.AddScoped<IStockAllocationService, StockAllocationService>();
            services.AddScoped<ITaxTypeService, TaxTypeService>();
            services.AddScoped<ICurrencyExchangeRateService, CurrencyExchangeRateService>();
            services.AddScoped<ITaxGroupService, TaxGroupService>();
            services.AddScoped<ISalesServiceInvoiceService, SalesServiceInvoiceService>();
            services.AddScoped<ISalesServiceInvoiceLineService, SalesServiceInvoiceLineService>();
            services.AddScoped<IAccountService, AccountService>();
            services.AddScoped<IBankAdjustmentService, BankAdjustmentService>();
            services.AddScoped<IBankAdjustmentLineService, BankAdjustmentLineService>();
            services.AddScoped<IManualJournalService, ManualJournalService>();
            services.AddScoped<IManualJournalLineService, ManualJournalLineService>();
            services.AddScoped<IServiceInvoiceMatarialRequestNotesService, ServiceInvoiceMatarialRequestNotesService>();
            services.AddScoped<IPayBookService, PayBookService>();
            services.AddScoped<IPaymentAdviceService, PaymentAdviceService>();
            services.AddScoped<IPaymentAdviceLineService, PaymentAdviceLineService>();
            services.AddScoped<IPaymentAdviceCostAllocationService, PaymentAdviceCostAllocationService>();
            services.AddScoped<IOutboundPaymentService, OutboundPaymentService>();
            services.AddScoped<IOutboundPaymentLineService, OutboundPaymentLineService>();
            services.AddScoped<IGoodsDispatchNoteLineDetailService, GoodsDispatchNoteLineDetailService>();
            services.AddScoped<IChequeBookService, ChequeBookService>();
            services.AddScoped<IFinancialStatementService, FinancialStatementService>();
            services.AddScoped<IFinancialStatementLineService, FinancialStatementLineService>();
            services.AddScoped<IFinancialStatementLineDetailService, FinancialStatementLineDetailService>();
            services.AddScoped<IPasswordPolicyService, PasswordPolicyService>();
            services.AddScoped<IMouldService, MouldService>();
            services.AddScoped<IMachineLineService, MachineLineService>();
            services.AddScoped<IChargeInformationService, ChargeInformationService>();
            services.AddScoped<IAgingBucketService, AgingBucketService>();
            services.AddScoped<IInboundReceiptService, InboundReceiptService>();
            services.AddScoped<IInboundReceiptLineService, InboundReceiptLineService>();
            services.AddScoped<IEmployeeContactService, EmployeeContactService>();
            services.AddScoped<IEmployeeAccountService, EmployeeAccountService>();
            services.AddScoped<IEmployeeIncrementService, EmployeeIncrementService>();
            services.AddScoped<IPettyCashService, PettyCashService>();
            services.AddScoped<IPettyCashLineService, PettyCashLineService>();
            services.AddScoped<IEmailTemplateService, EmailTemplateService>();
            services.AddScoped<IEmailTemplateLineService, EmailTemplateLineService>();
            services.AddScoped<IWorkstationCadreService, WorkstationCadreService>();
            services.AddScoped<IMouldProductService, MouldProductService>();
            services.AddScoped<IMachineMouldService, MachineMouldService>();
            services.AddScoped<IWorkFlowService, WorkFlowService>();
            services.AddScoped<IWorkFlowLineService, WorkFlowLineService>();
            services.AddScoped<IBillOfMaterialService, BillOfMaterialService>();
            services.AddScoped<IBillOfMaterialLineService, BillOfMaterialLineService>();
            services.AddScoped<IShipmentCostService, ShipmentCostService>();
            services.AddScoped<IShipmentCostLineService, ShipmentCostLineService>();
            services.AddScoped<IBillOfOperationService, BillOfOperationService>();
            services.AddScoped<IBillOfOperationLineService, BillOfOperationLineService>();
            services.AddScoped<IBillOfOperationLineDetailService, BillOfOperationLineDetailService>();
            services.AddScoped<IBillOfOperationLineOverheadService, BillOfOperationLineOverheadService>();
            services.AddScoped<IDocumentUploadService, DocumentUploadService>();
            services.AddScoped<ITxnWorkFlowLineService, TxnWorkFlowLineService>();
            services.AddScoped<IMouldCavityService, MouldCavityService>();
            services.AddScoped<IMachineVisualPlanLineService, MachineVisualPlanLineService>();
            services.AddScoped<IQCParameterService, QCParameterService>();
            services.AddScoped<IQCParameterMappingService, QCParameterMappingService>();
            services.AddScoped<ISalesReturnLineDetailService, SalesReturnLineDetailService>();
            services.AddScoped<IAttendenceAllowanceService, AttendenceAllowanceService>();
            services.AddScoped<IHRSettingsService, HRSettingsService>();
            services.AddScoped<IPressLineService, PressLineService>();
            services.AddScoped<IWarehousePalletService, WarehousePalletService>();
            services.AddScoped<ISemiFinishedGoodsProductionPlanService, SemiFinishedGoodsProductionPlanService>();
            services.AddScoped<IProductionPlanService, ProductionPlanService>();
            services.AddScoped<IChequeBookLineService, ChequeBookLineService>();
            services.AddScoped<IFixedAssetMaintenanceService, FixedAssetMaintenanceService>();
            services.AddScoped<IFixedAssetRegistryService, FixedAssetRegistryService>();
            services.AddScoped<IProductionOrderService, ProductionOrderService>();
            services.AddScoped<IPurchaseOrderLineDetailService, PurchaseOrderLineDetailService>();
            services.AddScoped<IProductionOrderLineService, ProductionOrderLineService>();
            services.AddScoped<IProductionOrderLineDetailService, ProductionOrderLineDetailService>();
            services.AddScoped<IMachineProductComponentService, MachineProductComponentService>();
            services.AddScoped<IAccountMappingService, AccountMappingService>();
            services.AddScoped<IAccountMappingLineService, AccountMappingLineService>();
            services.AddScoped<IAccountMappingLineDetailService, AccountMappingLineDetailService>();
            services.AddScoped<IBaseIncentiveRateService, BaseIncentiveRateService>();
            services.AddScoped<IScrapPenaltyDeductionRatesService, ScrapPenaltyDeductionRatesService>();
            services.AddScoped<IIncentiveApportionmentRatesService, IncentiveApportionmentRatesService>();
            services.AddScoped<ISalesDeskService, SalesDeskService>();
            services.AddScoped<IProductLicenceRegistrationService, ProductLicenceRegistrationService>();
            services.AddScoped<ISemiFinishedGoodsQCScanningService, SemiFinishedGoodsQCScanningService>();
            services.AddScoped<ISemiFinishedGoodsQCScanningLineService, SemiFinishedGoodsQCScanningLineService>();
            services.AddScoped<IMaterialRequisitionPlanService, MaterialRequisitionPlanService>();
            services.AddScoped<ISalesServiceOrderService, SalesServiceOrderService>();
            services.AddScoped<ISalesServiceOrderLineService, SalesServiceOrderLineService>();
            services.AddScoped<ILoadingPlanLineDetailService, LoadingPlanLineDetailService>();
            services.AddScoped<IServiceInquiryService, ServiceInquiryService>();
            services.AddScoped<IServiceInquiryLineService, ServiceInquiryLineService>();
            services.AddScoped<IServiceOrderDeskService, ServiceOrderDeskService>();
            services.AddScoped<ISalesServiceOrderJobService, SalesServiceOrderJobService>();
            services.AddScoped<ISalesServiceOrderJobLineService, SalesServiceOrderJobLineService>();
            services.AddScoped<IGeneralLedgerLineService, GeneralLedgerLineService>();
            services.AddScoped<IAttendenceService, AttendenceService>();
            services.AddScoped<ITouchScreenHistoryService, TouchScreenHistoryService>();
            services.AddScoped<IFreeIssuesService, FreeIssuesService>();
            services.AddScoped<IFreeIssueLinesService, FreeIssueLinesService>();
            services.AddScoped<IFreeIssueCriteriaLinesService, FreeIssueCriteriaLinesService>();
            services.AddScoped<IDiscountSchemesService, DiscountSchemesService>();
            services.AddScoped<IDiscountSchemeLinesService, DiscountSchemeLinesService>();
            services.AddScoped<IDiscountSchemeCriteriaLinesService, DiscountSchemeCriteriaLinesService>();
            services.AddScoped<IAppSettingsService, AppSettingsService>();
            services.AddScoped<IStockCountSheetLineService, StockCountSheetLineService>();
            services.AddScoped<IStockCountSheetsService, StockCountSheetsService>();
            services.AddScoped<IProductDieService, ProductDiesService>();
            services.AddScoped<IProfileBuildingService, ProfileBuildingService>();
            services.AddScoped<IGeneralLedgerSettlementService, GeneralLedgerSettlementService>();
            services.AddScoped<IPurchaseOrderSalesInvoiceService, PurchaseOrderSalesInvoiceService>();
            services.AddScoped<IServiceInvoiceSalesInvoiceService, ServiceInvoiceSalesInvoiceService>();
            services.AddScoped<IBundleProductService, BundleProductService>();
            services.AddScoped<IBundleProductLineService, BundleProductLineService>();
            services.AddScoped<IProductBrandDiscountService, ProductBrandDiscountService>();
            services.AddScoped<ISalesInvoiceChargeGroupService, SalesInvoiceChargeGroupService>();
            services.AddScoped<IPurchaseOrderAdditionalChargeService, PurchaseOrderAdditionalChargeService>();
            services.AddScoped<ICustomerPaymentsService, CustomerPaymentsService>();
            services.AddScoped<ICustomerPaymentLineService, CustomerPaymentLineService>();
            services.AddScoped<IPromotionService, PromotionService>();
            services.AddScoped<IPromotionLinesService, PromotionLinesService>();
            services.AddScoped<IProductionNotesService, ProductionNotesService>();
            services.AddScoped<IBankReconciliationService, BankReconciliationService>();
            services.AddScoped<IBankReconciliationLineService, BankReconciliationLineService>();
            services.AddScoped<IOutboundPaymentCostAllocationService, OutboundPaymentCostAllocationService>();
            services.AddScoped<IInboundReceiptCostAllocationService, InboundReceiptCostAllocationService>();
            services.AddScoped<ITransactionTypeService, TransactionTypeService>();
            services.AddScoped<IRawMaterialPlanningService, RawMaterialPlanningService>();
            services.AddScoped<IConsignmentStockTransferLineService, ConsignmentStockTransferLineService>();
            services.AddScoped<IConsignmentStockTransferService, ConsignmentStockTransferService>();
            services.AddScoped<IHOReturnStockTransferService, HOReturnStockTransferService>();
            services.AddScoped<IHOReturnStockTransferLineService, HOReturnStockTransferLineService>();
            services.AddScoped<INotificationService, NotificationService>();
            services.AddScoped<IMenuHeadService, MenuHeadService>();
            services.AddScoped<IMenuHeadLineService, MenuHeadLineService>();
            services.AddScoped<IMenuCategoryService, MenuCategoryService>();
            services.AddScoped<IPrintersService, PrintersService>();
            services.AddScoped<IBankPromotionService, BankPromotionService>();
            #endregion

            var appSettings = appSettingsSection.Get<AppSettings>();
            var key = Encoding.ASCII.GetBytes(appSettings.Secret);
            var tokenValidatonParams = new TokenValidationParameters
            {
                ValidateIssuerSigningKey = true,
                IssuerSigningKey = new SymmetricSecurityKey(key),
                ValidateIssuer = false,
                ValidateAudience = false,
                ValidateLifetime = true
            };

            // Auto Mapper Configurations
            var mappingConfig = new MapperConfiguration(mc =>
            {
                mc.AddProfile(new MappingProfile());
            });

            IMapper mapper = mappingConfig.CreateMapper();

            services.AddSingleton(mapper);

            services.AddDbContextPool<MarangoniERPContext>(options => options.UseSqlServer(Configuration.GetConnectionString("DefaultConnection")));
          

            services.AddSingleton(tokenValidatonParams);
            services.AddSingleton<JWT>();
            services.AddSingleton<IPasswordHasher<Domain.Entities.Administration.User>, CustomPasswordHasher>();

            services.AddAuthentication(x =>
            {
                x.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultScheme = JwtBearerDefaults.AuthenticationScheme;
                x.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(x =>
            {
                x.RequireHttpsMetadata = false;
                x.SaveToken = true;
                x.TokenValidationParameters = tokenValidatonParams;
            });

            services.AddResponseCompression(options =>
            {
                options.Providers.Add<BrotliCompressionProvider>();
                options.Providers.Add<GzipCompressionProvider>();
            });

            services.Configure<BrotliCompressionProviderOptions>(options =>
            {
                options.Level = CompressionLevel.Optimal;
            });
            services.Configure<GzipCompressionProviderOptions>(options => options.Level = CompressionLevel.Optimal);

        }


        public void Configure(IApplicationBuilder app, IWebHostEnvironment env, ILogger<Startup> logger)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage(); // Enable detailed errors in development
            }
            else
            {
                app.UseExceptionHandler(builder =>
                {
                    builder.Run(async context =>
                    {
                        context.Response.StatusCode = (int)HttpStatusCode.InternalServerError;
                        context.Response.Headers.Add("Access-Control-Allow-Origin", "*"); // Ensure CORS headers in error responses
                        var error = context.Features.Get<IExceptionHandlerFeature>();
                        if (error != null)
                        {
                            context.Response.AddApplicationError(error.Error.Message);
                            await context.Response.WriteAsync(error.Error.Message);
                        }
                    });
                });
                // app.UseHsts(); // Uncomment for production if using HTTPS
            }

            app.UseCors(opt => opt.AllowAnyOrigin().AllowAnyMethod().AllowAnyHeader()); // Move CORS before other middleware
            app.UseResponseCompression();
            app.UseHttpsRedirection();
            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();
            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });

            logger.LogInformation("CORS and middleware configured."); // Add logging for debugging
        }
    }
}
