﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PromotionLines", Schema = "sales")]
    public partial class PromotionLine
    {
        [Key]
        public int PromotionLineId { get; set; }
        public int? PromotionId { get; set; }
        public int? FreeIssueId { get; set; }
        public int? DiscountSchemeId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DiscountSchemeId))]
        [InverseProperty("PromotionLines")]
        public virtual DiscountScheme DiscountScheme { get; set; }
        [ForeignKey(nameof(FreeIssueId))]
        [InverseProperty("PromotionLines")]
        public virtual FreeIssue FreeIssue { get; set; }
        [ForeignKey(nameof(PromotionId))]
        [InverseProperty("PromotionLines")]
        public virtual Promotion Promotion { get; set; }
    }
}
