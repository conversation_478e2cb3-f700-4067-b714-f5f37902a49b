﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseRequisitionNotes", Schema = "prc")]
    public partial class PurchaseRequisitionNote
    {
        public PurchaseRequisitionNote()
        {
            PurchaseRequisitionNoteLines = new HashSet<PurchaseRequisitionNoteLine>();
            Quotations = new HashSet<Quotation>();
        }

        [Key]
        public int PurchaseRequisitionNoteId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? CompanyId { get; set; }
        public int? MaterialRequisitionNoteId { get; set; }
        public int? DepartmentId { get; set; }
        public int? EmployeeId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ExpectedDeliveryDate { get; set; }
        public int? RequestReasonId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PurchaseRequisitionNotes")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("PurchaseRequisitionNotes")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("PurchaseRequisitionNotes")]
        public virtual Employee Employee { get; set; }
        [InverseProperty(nameof(PurchaseRequisitionNoteLine.PurchaseRequisitionNote))]
        public virtual ICollection<PurchaseRequisitionNoteLine> PurchaseRequisitionNoteLines { get; set; }
        [InverseProperty(nameof(Quotation.PurchaseRequisitionNote))]
        public virtual ICollection<Quotation> Quotations { get; set; }
    }
}
