﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class PerformaInvoiceController : ControllerBase
    {
        private readonly IPerformaInvoiceService performaInvoiceService;

        public PerformaInvoiceController(IPerformaInvoiceService performaInvoiceService)
        {
            this.performaInvoiceService = performaInvoiceService;
        }

        [HttpGet]
        [Route("Single/{salesInvoiceId}")]
        public async Task<IActionResult> FindById(int salesInvoiceId)
        {
            return Ok(await this.performaInvoiceService.FindByIdAsync(salesInvoiceId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]SalesInvoice salesInvoice)
        {
            switch (salesInvoice.Action)
            {
                case "generate": return Ok(await this.performaInvoiceService.GeneratePerformaInvoiceAsync(salesInvoice));
                case "save": await this.performaInvoiceService.SaveAsync(salesInvoice); break;
              
            }

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesInvoice salesInvoice)
        {
            switch (salesInvoice.Action)
            {
                case "convert to LP": return Ok(await performaInvoiceService.ConvertToLoadingPlanAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value));
                case "convert to COI": return Ok(await performaInvoiceService.ConvertToCommercialInvoiceAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value));
                case "submit": await this.performaInvoiceService.SubmitAsync(salesInvoice); break;
                case "cancel": await this.performaInvoiceService.CancelAsync(salesInvoice); break;
                case "reverse": await this.performaInvoiceService.ReverseAsync(salesInvoice); break;

            }

            return Ok();
        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await this.performaInvoiceService.GetAllAsync(companyId, userId, startDate, endDate));
        }

    }
}