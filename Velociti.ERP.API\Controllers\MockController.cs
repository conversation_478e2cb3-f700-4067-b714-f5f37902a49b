﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Mock.Services;

namespace Velociti.ERP.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MockController : ControllerBase
    {
        [HttpGet]
        [Route("Barcode/{barcode}")]
        public IActionResult GetProductByBarcode(string barcode)
        {
            return Ok(new MockService().GetProductByBarcode(barcode));
        }

        [HttpGet]
        [Route("Presslines")]
        public IActionResult GetPressLines()
        {
            return Ok(new MockService().GetPressLines());
        }

        [HttpGet]
        [Route("PressMachines")]
        public IActionResult GetPressMachines()
        {
            return Ok(new MockService().GetPressMachines());
        }

        [HttpGet]
        [Route("QCReasons")]
        public IActionResult GetQCReasons()
        {
            return Ok(new MockService().GetQCReasons());
        }

        [HttpPost]
        [Route("QCStatus/Save")]
        public IActionResult SaveQCStatus(QCResponse response)
        {
            new MockService().SaveQCStatus(response);
            return Ok();
        }

        [HttpGet]
        [Route("Pallet/Barcode/{barcode}")]
        public IActionResult GetPalletByBarcode(string barcode)
        {
            return Ok(new MockService().GetPalletByBarcode(barcode));
        }

        [HttpGet]
        [Route("CompoundMachines")]
        public IActionResult GetCompoundMachines()
        {
            return Ok(new MockService().GetCompoundMachines());
        }
    }
}