﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalOrders", Schema = "inv")]
    public partial class InternalOrder
    {
        public InternalOrder()
        {
            InternalOrderLines = new HashSet<InternalOrderLine>();
        }

        [Key]
        public int InternalOrderId { get; set; }
        public int? CompanyId { get; set; }
        public int EmployeeId { get; set; }
        public int DepartmentId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ExpectedDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public int? RequestReasonId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("InternalOrders")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("InternalOrders")]
        public virtual Employee Employee { get; set; }
        [InverseProperty(nameof(InternalOrderLine.InternalOrder))]
        public virtual ICollection<InternalOrderLine> InternalOrderLines { get; set; }
    }
}
