﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Runtime.CompilerServices;
using System.Text;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BankCards", Schema = "dbo")]
    public class CardType
    {
        [Key]
        [StringLength(8)]
        public string Code { get; set; }
        [StringLength(25)]
        public string CardName { get; set; }
        public int OrderNo { get; set; }
        [StringLength(3)]
        public string MainLoc { get; set; }
        [Column(TypeName = "CurType")]
        [StringLength(4)]
        public string CurType { get; set; }
        public decimal ComPrec { get; set; }

    }
}
