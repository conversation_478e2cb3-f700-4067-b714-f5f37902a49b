﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Common;

namespace Velociti.ERP.API.Controllers.Common
{
    [Route("api/Common/[controller]")]
    [Authorize]
    [ApiController]
    public class PrintersController : ControllerBase
    {
        private readonly IPrintersService _printersService;

        public PrintersController(IPrintersService printersService)
        {
            _printersService = printersService;
        }

        [HttpGet]
        [Route("Printers")]
        public async Task<ActionResult<IEnumerable<Printers>>> GetAll()
        {
            var data = await _printersService.GetAll();
            return Ok(data);
        }
    }
}
