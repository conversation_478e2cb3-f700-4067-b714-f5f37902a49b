﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Authorize]
    [Route("api/Finance/[controller]")]
    [ApiController]
    public class InboundReceiptCostAllocationsController : ControllerBase
    {
        private readonly IInboundReceiptCostAllocationService _inboundReceiptCostAllocationService;

        public InboundReceiptCostAllocationsController(IInboundReceiptCostAllocationService inboundReceiptCostAllocationService)
        {
            _inboundReceiptCostAllocationService = inboundReceiptCostAllocationService;
        }

        [HttpGet]
        [Route("{inboundReceiptLineId}")]
        public async Task<IActionResult> GetByInboundReceiptLineIdAsync(int inboundReceiptLineId)
        {
            return Ok(await _inboundReceiptCostAllocationService.GetByInboundReceiptLineIdAsync(inboundReceiptLineId));
        }
    }
}