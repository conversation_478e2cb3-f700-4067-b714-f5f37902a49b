﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("LoanOrders", Schema = "prc")]
    public partial class LoanOrder
    {
        public LoanOrder()
        {
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            LoanOrderLines = new HashSet<LoanOrderLine>();
        }

        [Key]
        public int LoanOrderId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        public int? SupplementaryManufacturerId { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public int? PurchaseOrderId { get; set; }
        [StringLength(1000)]
        public string Description { get; set; }
        [StringLength(255)]
        public string DeliveryAddress { get; set; }
        [StringLength(255)]
        public string SupplierAddress { get; set; }
        public int? ShipmentPackingMethodId { get; set; }
        [StringLength(255)]
        public string LoanTerms { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }
        public int? DeliveryWeek { get; set; }
        [Column(TypeName = "date")]
        public DateTime? RepaymentDueDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("LoanOrders")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("LoanOrders")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(SupplementaryManufacturerId))]
        [InverseProperty("LoanOrders")]
        public virtual SupplementaryManufacturer SupplementaryManufacturer { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.LoanOrder))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(LoanOrderLine.LoanOrder))]
        public virtual ICollection<LoanOrderLine> LoanOrderLines { get; set; }
    }
}
