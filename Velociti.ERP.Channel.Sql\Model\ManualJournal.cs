﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ManualJournals", Schema = "fin")]
    public partial class ManualJournal
    {
        public ManualJournal()
        {
            ManualJournalLines = new HashSet<ManualJournalLine>();
        }

        [Key]
        public int ManualJournalId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime DocDate { get; set; }
        public int? CompanyId { get; set; }
        public byte? StatusEnum { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalAmount { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ManualJournals")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("ManualJournals")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("ManualJournals")]
        public virtual Department Department { get; set; }
        [InverseProperty(nameof(ManualJournalLine.ManualJournal))]
        public virtual ICollection<ManualJournalLine> ManualJournalLines { get; set; }
    }
}
