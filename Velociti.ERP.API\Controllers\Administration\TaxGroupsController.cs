﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class TaxGroupsController : ControllerBase
    {
        private readonly ITaxGroupService _taxGroupService;

        public TaxGroupsController(ITaxGroupService taxGroupService)
        {
            _taxGroupService = taxGroupService;
        }

        [HttpGet]
        [Route("ShortList")]
        public async Task<IActionResult> GetShortList()
        {
            return Ok(await _taxGroupService.GetShortListAsync());
        }
    }
}