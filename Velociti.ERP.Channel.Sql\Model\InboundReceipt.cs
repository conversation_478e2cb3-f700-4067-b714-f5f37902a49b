﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InboundReceipts", Schema = "fin")]
    public partial class InboundReceipt
    {
        public InboundReceipt()
        {
            InboundReceiptLines = new HashSet<InboundReceiptLine>();
        }

        [Key]
        public int InboundReceiptId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(50)]
        public string RefNumber { get; set; }
        public int? SupplierId { get; set; }
        public int? CustomerId { get; set; }
        public int? EmployeeId { get; set; }
        public int? TaxGroupId { get; set; }
        public byte? ReceivingMethodEnum { get; set; }
        public int? AccountId { get; set; }
        public int? ChequeBookId { get; set; }
        [StringLength(50)]
        public string ChequeNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ChequeDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? BankingDate { get; set; }
        public bool? IsPostDatedCheque { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [StringLength(255)]
        public string PayeeName { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
        [Column(TypeName = "money")]
        public decimal? AvailableAdvances { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("InboundReceipts")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("InboundReceipts")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("InboundReceipts")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("InboundReceipts")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("InboundReceipts")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("InboundReceipts")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("InboundReceipts")]
        public virtual Supplier Supplier { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("InboundReceipts")]
        public virtual TaxGroup TaxGroup { get; set; }
        [InverseProperty(nameof(InboundReceiptLine.InboundReceipt))]
        public virtual ICollection<InboundReceiptLine> InboundReceiptLines { get; set; }
    }
}
