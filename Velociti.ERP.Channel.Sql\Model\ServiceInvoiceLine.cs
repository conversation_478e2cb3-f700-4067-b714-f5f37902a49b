﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ServiceInvoiceLines", Schema = "prc")]
    public partial class ServiceInvoiceLine
    {
        [Key]
        public int ServiceInvoiceLineId { get; set; }
        public int? ServiceInvoiceId { get; set; }
        public int? ServiceOrderLineId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ServiceDate { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [StringLength(255)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }

        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ServiceInvoiceLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ServiceInvoiceId))]
        [InverseProperty("ServiceInvoiceLines")]
        public virtual ServiceInvoice ServiceInvoice { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("ServiceInvoiceLines")]
        public virtual TaxGroup TaxGroup { get; set; }
    }
}
