﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class PaymentAdvicesController : ControllerBase
    {
        private readonly IPaymentAdviceService _paymentAdviceService;

        public PaymentAdvicesController(IPaymentAdviceService paymentAdviceService)
        {
            _paymentAdviceService = paymentAdviceService;
        }

        [HttpGet]
        [Route("Company/{companyId}/userId/{userId}/Type/{typeEnum}")]
        public async Task<IActionResult> GetAll(int companyId, int userId, byte typeEnum)
        {
            var list = await _paymentAdviceService.GetAllAsync(companyId, userId, typeEnum);

            return Ok(list);
        }

        [HttpGet]
        [Route("{paymentAdviceId}")]
        public async Task<IActionResult> GetById(int paymentAdviceId)
        {
            return Ok(await _paymentAdviceService.GetByPaymentAdviceIdAsync(paymentAdviceId));
        }

        [HttpGet]
        [Route("SettlementDocuments/{paymentAdviceId}")]
        public async Task<IActionResult> GetSettlemetDocumentsAsync(int paymentAdviceId)
        {
            return Ok(await _paymentAdviceService.GetSettlementDocumentsAsync(paymentAdviceId));
        }

        [HttpGet]
        [Route("ReceiptAdviceSettlementDocuments/{receiptAdviceId}")]
        public async Task<IActionResult> GetReceiptAdviceSettlemetDocumentsAsync(int receiptAdviceId)
        {
            return Ok(await _paymentAdviceService.GetReceiptAdviceSettlementDocumentsAsync(receiptAdviceId));
        }

        [HttpGet]
        [Route("LedgerLines/{paymentAdviceId}/TypeEnum/{typeEnum}/Company/{companyId}/Supplier/{supplierId}/Currency/{currencyId}")]
        public async Task<IActionResult> GetLedgerLinesAsync(int paymentAdviceId, byte typeEnum, int companyId, int supplierId, int currencyId)
        {
            return Ok(await _paymentAdviceService.GetLedgerLinesAsync(paymentAdviceId, typeEnum, companyId, supplierId, currencyId));
        }

        [HttpGet]
        [Route("LedgerLines/{paymentAdviceId}/Company/{companyId}/Customer/{customerId}/Currency/{currencyId}")]
        public async Task<IActionResult> GetLedgerLinesForCustomerRefundAsync(int paymentAdviceId, int companyId, int customerId, int currencyId)
        {
            return Ok(await _paymentAdviceService.GetLedgerLinesForCustomerRefundAsync(paymentAdviceId, companyId, customerId, currencyId));
        }

        [HttpGet]
        [Route("SettlementLines/{paymentAdviceId}/Company/{companyId}/Customer/{customerId}/Currency/{currencyId}")]
        public async Task<IActionResult> GetLedgerLinesForCustomerAdvanceSettlementAsync(int paymentAdviceId, int companyId, int customerId, int currencyId)
        {
            return Ok(await _paymentAdviceService.GetLedgerLinesForCustomerAdvanceSettlementAsync(paymentAdviceId, companyId, customerId, currencyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]PaymentAdvice paymentAdvice)
        {
            if (paymentAdvice == null)
                return BadRequest();

            await _paymentAdviceService.SaveAsync(paymentAdvice);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]PaymentAdvice paymentAdvice)
        {
            switch (paymentAdvice.Action)
            {
                case "submit": await _paymentAdviceService.SubmitAsync(paymentAdvice.PaymentAdviceId, paymentAdvice.ModifiedUserId.Value); break;
                case "reverse": await _paymentAdviceService.ReverseAsync(paymentAdvice.PaymentAdviceId, paymentAdvice.ModifiedUserId.Value); break;
                case "convertToOP": return Ok(await _paymentAdviceService.ConvertToOutboundPayment(paymentAdvice.PaymentAdviceId, paymentAdvice.ModifiedUserId.Value)); break;
                case "convertToIR": return Ok(await _paymentAdviceService.ConvertToInboundReceipt(paymentAdvice.PaymentAdviceId, paymentAdvice.ModifiedUserId.Value)); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivation(int id)
        {
            await _paymentAdviceService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}