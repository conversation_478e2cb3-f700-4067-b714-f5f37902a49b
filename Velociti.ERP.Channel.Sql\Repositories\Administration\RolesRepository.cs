﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class RolesRepository : IRolesRepository
    {
        private readonly MarangoniERPContext _context;

        public RolesRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<Role> FindByIdAsync(int roleId)
        {
            return await _context.Roles.FirstOrDefaultAsync(c => c.RoleId == roleId);
        }

        public async Task<IEnumerable<Role>> GetAllAsync(int companyId)
        {
            return await _context.Roles.Where(p => p.CompanyId == companyId).ToListAsync();
        }

        public async Task SaveAsync(Role role)
        {
            if (role.RoleId == default)
            {
                await _context.Roles.AddAsync(role);
            }
            else
            {
                _context.Roles.Attach(role);
                _context.Entry(role).State = EntityState.Modified;
            }

            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var record = await _context.Roles.FirstAsync(c => c.RoleId == id);

            _context.Roles.Remove(record);
            await _context.SaveChangesAsync();
        }
    }
}
