﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GoodsDispatchNoteLines", Schema = "inv")]
    public partial class GoodsDispatchNoteLine
    {
        public GoodsDispatchNoteLine()
        {
            GoodsDispatchNoteLineDetails = new HashSet<GoodsDispatchNoteLineDetail>();
        }

        [Key]
        public int GoodsDispatchNoteLineId { get; set; }
        public int? GoodsDispatchNoteId { get; set; }
        public int? WarehouseId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? WarrantyDate { get; set; }
        [StringLength(32)]
        public string LotNumber { get; set; }
        public int? SalesOrderLineId { get; set; }
        [StringLength(500)]
        public string CustomerReferenceNo { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.GoodsDispatchNoteLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.GoodsDispatchNoteLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(GoodsDispatchNoteId))]
        [InverseProperty("GoodsDispatchNoteLines")]
        public virtual GoodsDispatchNote GoodsDispatchNote { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("GoodsDispatchNoteLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SalesOrderLineId))]
        [InverseProperty("GoodsDispatchNoteLines")]
        public virtual SalesOrderLine SalesOrderLine { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("GoodsDispatchNoteLines")]
        public virtual TaxGroup TaxGroup { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.GoodsDispatchNoteLines))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLineDetail.GoodsDispatchNoteLine))]
        public virtual ICollection<GoodsDispatchNoteLineDetail> GoodsDispatchNoteLineDetails { get; set; }
    }
}
