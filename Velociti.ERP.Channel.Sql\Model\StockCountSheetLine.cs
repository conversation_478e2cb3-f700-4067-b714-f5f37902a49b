﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockCountSheetLines", Schema = "inv")]
    public partial class StockCountSheetLine
    {
        [Key]
        public int StockCountSheetLineId { get; set; }
        public int StockCountSheetId { get; set; }
        public int ProductId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? OpeningStockQuantity { get; set; }
        public int? TransferredQuantity { get; set; }
        public int? TotalStockQuantity { get; set; }
        public int? RemainingQuantity { get; set; }
        public int? VarianceQuantity { get; set; }
        public int? FreeIssuesQuantity { get; set; }
        public int? SoldQuantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPercentage { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.StockCountSheetLines))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(StockCountSheetId))]
        [InverseProperty("StockCountSheetLines")]
        public virtual StockCountSheet StockCountSheet { get; set; }
    }
}
