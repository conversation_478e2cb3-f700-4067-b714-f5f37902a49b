﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class AccountsController : ControllerBase
    {
        private readonly IAccountService _accountService;

        public AccountsController(IAccountService accountService)
        {
            _accountService = accountService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            var list = await _accountService.GetAllAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("Type/{typeEnum}/Company/{companyId}")]
        public async Task<IActionResult> GetAll(int companyId, byte typeEnum)
        {
            var list = await _accountService.GetAllByTypeAsync(companyId, typeEnum);

            return Ok(list);
        }


        [HttpGet]
        [Route("ShortList/{companyId}/Type/{typeEnum}")]
        public async Task<IActionResult> GetShortList(int companyId, byte typeEnum)
        {
            var list = await _accountService.GetShortListAsync(companyId, typeEnum);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/{companyId}/Types/{types}")]
        public async Task<IActionResult> GetShortListByTypes(int companyId, string types)
        {
            var list = await _accountService.GetShortListByTypesAsync(companyId, Array.ConvertAll(types.Split(','), int.Parse));

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/General/{companyId}/Type/{typeEnum}")]
        public async Task<IActionResult> GetGeneralShortList(int companyId, byte typeEnum)
        {
            var list = await _accountService.GetGeneralShortListAsync(companyId, typeEnum);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _accountService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ReceivingShortList/{companyId}")]
        public async Task<IActionResult> GetReceivingAccountShortList(int companyId)
        {
            var list = await _accountService.GetReceivingAccountShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("{accountId}")]
        public async Task<IActionResult> GetById(int accountId)
        {
            return Ok(await _accountService.GetByAccountIdAsync(accountId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Account account)
        {
            if (account == null)
                return BadRequest();

            await _accountService.SaveAsync(account);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/user/{userId}")]
        public async Task<IActionResult> ToggleActivation(int id, int userId)
        {
            await _accountService.ToggleActivationAsync(id, userId);

            return Ok();
        }
    }
}