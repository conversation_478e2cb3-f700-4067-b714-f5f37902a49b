﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SemiFinishedGoodsProductionPlans", Schema = "man")]
    public partial class SemiFinishedGoodsProductionPlan
    {
        [Key]
        public int SemiFinishedGoodsProductionPlanId { get; set; }
        public int? SemiFinishedGoodsProductionPlanSummaryId { get; set; }
        [StringLength(50)]
        public string SemiFinishedGoodsProductionPlanCode { get; set; }
        public int? CompanyId { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        public int? RefDocLineId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualEndDateTime { get; set; }
        public byte? StatusEnum { get; set; }
        public int? ProductId { get; set; }
        public int? RequiredQuantity { get; set; }
        public int? ProductionQuantity { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? ProductionOrderIdLinkedToRework { get; set; }
        [Column("BOMLevel")]
        public byte? Bomlevel { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SemiFinishedGoodsProductionPlans")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SemiFinishedGoodsProductionPlans")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductionOrderIdLinkedToRework))]
        [InverseProperty(nameof(ProductionOrder.SemiFinishedGoodsProductionPlans))]
        public virtual ProductionOrder ProductionOrderIdLinkedToReworkNavigation { get; set; }
        [ForeignKey(nameof(SemiFinishedGoodsProductionPlanSummaryId))]
        [InverseProperty("SemiFinishedGoodsProductionPlans")]
        public virtual SemiFinishedGoodsProductionPlanSummary SemiFinishedGoodsProductionPlanSummary { get; set; }
    }
}
