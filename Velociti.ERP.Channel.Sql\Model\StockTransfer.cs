﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockTransfers", Schema = "inv")]
    public partial class StockTransfer
    {
        public StockTransfer()
        {
            StockTransferLines = new HashSet<StockTransferLine>();
            StockTransferReceipts = new HashSet<StockTransferReceipt>();
        }

        [Key]
        public int StockTransferId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column("AODNumber")]
        [StringLength(50)]
        public string Aodnumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? FromWarehouseId { get; set; }
        public int? ToWarehouseId { get; set; }
        [StringLength(255)]
        public string VehicleDetails { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("StockTransfers")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("StockTransfers")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(FromWarehouseId))]
        [InverseProperty(nameof(Warehous.StockTransferFromWarehouses))]
        public virtual Warehous FromWarehouse { get; set; }
        [ForeignKey(nameof(ToWarehouseId))]
        [InverseProperty(nameof(Warehous.StockTransferToWarehouses))]
        public virtual Warehous ToWarehouse { get; set; }
        [InverseProperty(nameof(StockTransferLine.StockTransfer))]
        public virtual ICollection<StockTransferLine> StockTransferLines { get; set; }
        [InverseProperty(nameof(StockTransferReceipt.StockTransfer))]
        public virtual ICollection<StockTransferReceipt> StockTransferReceipts { get; set; }
    }
}
