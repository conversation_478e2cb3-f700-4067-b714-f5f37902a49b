﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_BOM")]
    public partial class IBom
    {
        [StringLength(255)]
        public string Header { get; set; }
        [Column("BOM Description")]
        [StringLength(255)]
        public string BomDescription { get; set; }
        [Column("TMT  - Master")]
        [StringLength(255)]
        public string TmtMaster { get; set; }
        [Column("TEV - Master")]
        [StringLength(255)]
        public string TevMaster { get; set; }
        [Column("GPT-3 - Master")]
        [StringLength(255)]
        public string Gpt3Master { get; set; }
        [Column("GPS - Master")]
        [StringLength(255)]
        public string GpsMaster { get; set; }
        [Column("BEV - Master")]
        [StringLength(255)]
        public string BevMaster { get; set; }
        [Column("MBT - Master")]
        [StringLength(255)]
        public string MbtMaster { get; set; }
        [Column("BAP - Master")]
        [StringLength(255)]
        public string BapMaster { get; set; }
        [Column("J2 - Master")]
        [StringLength(255)]
        public string J2Master { get; set; }
        [Column("CSM - Master")]
        [StringLength(255)]
        public string CsmMaster { get; set; }
        [Column("ECO - DSI - Master")]
        [StringLength(255)]
        public string EcoDsiMaster { get; set; }
        [Column("ECO - TWW - Master")]
        [StringLength(255)]
        public string EcoTwwMaster { get; set; }
        [Column("ECO - ARP - Master")]
        [StringLength(255)]
        public string EcoArpMaster { get; set; }
        [Column("ECO Gray NM - Master")]
        [StringLength(255)]
        public string EcoGrayNmMaster { get; set; }
        [Column("BFP - Master")]
        [StringLength(255)]
        public string BfpMaster { get; set; }
        [Column("GPT 2 - Final")]
        [StringLength(255)]
        public string Gpt2Final { get; set; }
        [Column("GPT 1 - Final")]
        [StringLength(255)]
        public string Gpt1Final { get; set; }
        [Column("BFP - Final")]
        [StringLength(255)]
        public string BfpFinal { get; set; }
        [Column("ECO Gray NM - Final")]
        [StringLength(255)]
        public string EcoGrayNmFinal { get; set; }
        [Column("TMT - Final")]
        [StringLength(255)]
        public string TmtFinal { get; set; }
        [Column("TEV - Final")]
        [StringLength(255)]
        public string TevFinal { get; set; }
        [Column("GPT-3 - Final")]
        [StringLength(255)]
        public string Gpt3Final { get; set; }
        [Column("GPS - Final")]
        [StringLength(255)]
        public string GpsFinal { get; set; }
        [Column("BEV - Final")]
        [StringLength(255)]
        public string BevFinal { get; set; }
        [Column("MBT - Final")]
        [StringLength(255)]
        public string MbtFinal { get; set; }
        [Column("BAP - Final")]
        [StringLength(255)]
        public string BapFinal { get; set; }
        [Column("J2 - Final")]
        [StringLength(255)]
        public string J2Final { get; set; }
        [Column("CSM - Final")]
        [StringLength(255)]
        public string CsmFinal { get; set; }
        [Column("ECO - DSI - Final")]
        [StringLength(255)]
        public string EcoDsiFinal { get; set; }
        [Column("ECO - TWW - Final")]
        [StringLength(255)]
        public string EcoTwwFinal { get; set; }
        [Column("ECO - ARP - Final")]
        [StringLength(255)]
        public string EcoArpFinal { get; set; }
        [Column("Yellow - Master Batch")]
        [StringLength(255)]
        public string YellowMasterBatch { get; set; }
    }
}
