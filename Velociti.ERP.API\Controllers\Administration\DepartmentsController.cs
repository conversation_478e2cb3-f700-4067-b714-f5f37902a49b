﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class DepartmentsController : ControllerBase
    {
        private readonly IDepartmentService _departmentService;

        public DepartmentsController(IDepartmentService departmentService)
        {
            _departmentService = departmentService;
        }

        [HttpGet]
        [Route("{departmentId}")]
        public async Task<IActionResult> GetById(int departmentId)
        {
            return Ok(await _departmentService.FindByIdAsync(departmentId));
        }

        [HttpGet]
        [Route("ShortList/User/{userId}/company/{companyId}")]
        public async Task<IActionResult> GetShortListForAssignUser(int userId, int companyId)
        {
            var list = await _departmentService.GetShortListForAssignUserAsync(userId, companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetShortList(int companyId)
        {
            return Ok(await _departmentService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("DefaultDepartment/company/{companyId}")]
        public async Task<IActionResult> GetDefaultDepartment( int companyId)
        {
            return Ok(await _departmentService.GetDefaultDepartmentAsync( companyId));
        }
    }
}