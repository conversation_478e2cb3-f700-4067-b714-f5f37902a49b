﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Users")]
    public partial class IUser
    {
        [StringLength(255)]
        public string Title { get; set; }
        [StringLength(255)]
        public string FullName { get; set; }
        [StringLength(255)]
        public string DisplayName { get; set; }
        [StringLength(255)]
        public string UserName { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string AllowBackDate { get; set; }
        [StringLength(255)]
        public string Signature { get; set; }
        [StringLength(255)]
        public string AccessibleCompanies { get; set; }
        [StringLength(255)]
        public string DepartmentAccess { get; set; }
        [StringLength(255)]
        public string Designation { get; set; }
        [StringLength(255)]
        public string Division { get; set; }
        [StringLength(255)]
        public string EmployeeName { get; set; }
    }
}
