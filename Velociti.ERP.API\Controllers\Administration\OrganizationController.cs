﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;
using static Velociti.ERP.Domain.Entities.Administration.OrganizationUnit;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class OrganizationController : ControllerBase
    {
        private readonly IOrganizationService _organizationService;

        public OrganizationController(IOrganizationService organizationService)
        {
            _organizationService = organizationService;
        }

        [HttpGet]
        public async Task<ActionResult<Response<IEnumerable<HierarchyTemplate>>>> GetHierarchy()
        {
            return Ok(await _organizationService.GetHierarchyAsync());
        }

        [HttpGet]
        [Route("ShortList/Type/{typeEnum}")]
        public async Task<ActionResult<Response<IEnumerable<HierarchyTemplate>>>> GetOperationalUnitShortList(TypeEnum typeEnum)
        {
            return Ok(await _organizationService.GetShortListByTypeAsync(typeEnum));
        }

        [HttpGet]
        [Route("OperationalUnit/{key}")]
        public async Task<ActionResult<Response<OrganizationUnit>>> FindById(string key)
        {
            return Ok(await _organizationService.FindByIdAsync(key));
        }

        [HttpPost]
        public async Task<IActionResult> Save(OrganizationUnit organizationUnit)
        {
            if (organizationUnit == null)
                return BadRequest();

            await _organizationService.SaveAsync(organizationUnit);
            
            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> ToggleActivation([FromBody]HierarchyTemplate hierarchyTemplate)
        {
            if (hierarchyTemplate == null)
                return BadRequest();

            await _organizationService.ToggleActivationAsync(hierarchyTemplate);
            
            return Ok();
        }
    }
}