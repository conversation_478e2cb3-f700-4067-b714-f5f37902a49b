﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using OfficeOpenXml;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class StockTakesController : ControllerBase
    {
        private readonly IStockTakeService _stockTakeService;
        private readonly IWarehouseProductService _warehouseProductService;

        public StockTakesController(IStockTakeService stockTakeService, IWarehouseProductService warehouseProductService)
        {
            _stockTakeService = stockTakeService;
            _warehouseProductService = warehouseProductService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _stockTakeService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _stockTakeService.GetForApprovalAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("UploadedLines")]
        public async Task<IActionResult> GetUploadedLines(int userId)  
        {
            return Ok(await _warehouseProductService.GetAllUploadedProductsAsync(userId));  
        }

        [HttpPost]
        [Route("Upload")]
        public IActionResult UploadSave([FromBody]List<WarehouseProduct> warehouseProductsList)          
        {
            if (warehouseProductsList == null)
                return BadRequest();

            _warehouseProductService.UploadSaveAsync(warehouseProductsList);

            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Save(int companyId, int warehouseId, int modifiedUserId)
        {
            await _stockTakeService.SaveAsync(companyId, warehouseId, modifiedUserId);

            return Ok();
        }

        [HttpDelete]
        [Route("{stockTakeId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int stockTakeId, int userId)
        {
            await _stockTakeService.CancelAsync(stockTakeId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]StockTake stockTake)
        {
            switch (stockTake.Action)
            {
                case "submit": await _stockTakeService.SubmitAsync(stockTake.StockTakeId, stockTake.ModifiedUserId.Value); break;
                case "send for approval": await _stockTakeService.SendForApprovalAsync(stockTake.StockTakeId, stockTake.CompanyId.Value, stockTake.ModifiedUserId.Value); break;
                case "update work flow": await _stockTakeService.UpdateWorkFlowStatusAsync(stockTake); break;
            }

            return Ok();
        }

    }
}