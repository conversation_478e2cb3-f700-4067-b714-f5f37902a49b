﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class ExchangeOrdersController : ControllerBase
    {
        private readonly IExchangeOrderService _exchangeOrderService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public ExchangeOrdersController(IExchangeOrderService exchangeOrderService, IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _exchangeOrderService = exchangeOrderService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _exchangeOrderService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _exchangeOrderService.GetForApprovalAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("Single/{exchangeOrderId}")]
        public async Task<IActionResult> GetById(int exchangeOrderId)
        {
            return Ok(await _exchangeOrderService.GetByIdAsync(exchangeOrderId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]ExchangeOrder exchangeOrder)
        {
            await _exchangeOrderService.SaveAsync(exchangeOrder);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]ExchangeOrder exchangeOrder)
        {
            switch (exchangeOrder.Action)
            {
                case "submit":
                    await _exchangeOrderService.SubmitAsync(exchangeOrder);
                    break;
                case "reverse":
                    await _exchangeOrderService.ReverseAsync(exchangeOrder);
                    break;
                case "cancel":
                    await _exchangeOrderService.CancelAsync(exchangeOrder);
                    break;
                case "convert":
                    return Ok(await _goodsDispatchNoteService.ConvertAsync(exchangeOrder.ExchangeOrderId, Module.DocumentType.ExchangeOrder, exchangeOrder.ModifiedUserId));
                case "send for approval": 
                    await _exchangeOrderService.SendForApprovalAsync(exchangeOrder.ExchangeOrderId, exchangeOrder.CompanyId.Value, exchangeOrder.ModifiedUserId.Value); 
                    break;
                case "update work flow": 
                    await _exchangeOrderService.UpdateWorkFlowStatusAsync(exchangeOrder); 
                    break;
                case "copy": 
                    await _exchangeOrderService.CopyAsync(exchangeOrder.ExchangeOrderId, exchangeOrder.ModifiedUserId.Value); 
                    break;
            }

            return Ok();
        }
    }
}