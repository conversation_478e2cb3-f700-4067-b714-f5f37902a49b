﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SupplementaryManufacturers", Schema = "prc")]
    public partial class SupplementaryManufacturer
    {
        public SupplementaryManufacturer()
        {
            GoodsReceiveNotes = new HashSet<GoodsReceiveNote>();
            LoanOrders = new HashSet<LoanOrder>();
            ShipmentQualityControlReturns = new HashSet<ShipmentQualityControlReturn>();
            ShipmentQualityControls = new HashSet<ShipmentQualityControl>();
            SupplementaryManufacturerContacts = new HashSet<SupplementaryManufacturerContact>();
        }

        [Key]
        public int SupplementaryManufacturerId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string SupplementaryManufacturerCode { get; set; }
        [StringLength(255)]
        public string SupplementaryManufacturerDescription { get; set; }
        public int? CategoryId { get; set; }
        [StringLength(255)]
        public string Address { get; set; }
        [StringLength(50)]
        public string TaxRegistrationNo { get; set; }
        [StringLength(255)]
        public string Notes { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CategoryId))]
        [InverseProperty(nameof(SupportData.SupplementaryManufacturers))]
        public virtual SupportData Category { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SupplementaryManufacturers")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(GoodsReceiveNote.SupplementaryManufacturer))]
        public virtual ICollection<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        [InverseProperty(nameof(LoanOrder.SupplementaryManufacturer))]
        public virtual ICollection<LoanOrder> LoanOrders { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturn.SupplementaryManufacturer))]
        public virtual ICollection<ShipmentQualityControlReturn> ShipmentQualityControlReturns { get; set; }
        [InverseProperty(nameof(ShipmentQualityControl.SupplementaryManufacturer))]
        public virtual ICollection<ShipmentQualityControl> ShipmentQualityControls { get; set; }
        [InverseProperty(nameof(SupplementaryManufacturerContact.SupplementaryManufacturer))]
        public virtual ICollection<SupplementaryManufacturerContact> SupplementaryManufacturerContacts { get; set; }
    }
}
