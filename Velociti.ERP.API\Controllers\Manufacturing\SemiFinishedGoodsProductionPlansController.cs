﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class SemiFinishedGoodsProductionPlansController : ControllerBase    
    {
        private readonly ISemiFinishedGoodsProductionPlanService _semiFinishedGoodsProductionPlanService;  

        public SemiFinishedGoodsProductionPlansController(ISemiFinishedGoodsProductionPlanService semiFinishedGoodsProductionPlanService)  
        {
            _semiFinishedGoodsProductionPlanService = semiFinishedGoodsProductionPlanService;
        }

        [HttpGet]
        [Route("startDate/{startDate}/endDate/{endDate}/company/{companyId}")]
        public async Task<IActionResult> Get(DateTime startDate, DateTime endDate, int companyId)
        {
            return Ok(await _semiFinishedGoodsProductionPlanService.GetActiveAllSummaryAsync(startDate, endDate, companyId));
        }

        [HttpGet]
        [Route("MRP/startDate/{startDate}/endDate/{endDate}/company/{companyId}")]
        public async Task<IActionResult> GetMRP(DateTime startDate, DateTime endDate, int companyId)
        {
            return Ok(await _semiFinishedGoodsProductionPlanService.GetActiveAllMRPAsync(startDate, endDate, companyId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Process/startDate/{startDate}/endDate/{endDate}/company/{companyId}")]
        public async Task<IActionResult> GetForProduction(DateTime startDate, DateTime endDate, int companyId)
        {
            return Ok(await _semiFinishedGoodsProductionPlanService.GetForProductionAsync(startDate, endDate, companyId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("GetByTypeForProcess/type/{typeEnum}/startDate/{startDate}/endDate/{endDate}/company/{companyId}")]
        public async Task<IActionResult> GetByTypeForProduction(byte typeEnum, DateTime startDate, DateTime endDate, int companyId)
        {
            return Ok(await _semiFinishedGoodsProductionPlanService.GetByTypeForProductionAsync((ProductionOrder.Type)typeEnum, startDate, endDate, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save(SemiFinishedGoodsProductionPlan semiFinishedGoodsProductionPlan)
        {
            await _semiFinishedGoodsProductionPlanService.SaveAsync(semiFinishedGoodsProductionPlan);

            return Ok();
        }

        [HttpPut]
        [Route("{id}/Date/{date}/Quantity/{qty}")]
        public async Task<IActionResult> UpdateProductionQuantity(int id, string date, int qty)        
        {
            await _semiFinishedGoodsProductionPlanService.UpdateProductionQuantity(id, date, qty);

            return Ok();
        }
    }
}