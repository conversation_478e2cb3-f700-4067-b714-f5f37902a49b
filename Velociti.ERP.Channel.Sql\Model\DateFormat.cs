﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("DateFormats", Schema = "adm")]
    public partial class DateFormat
    {
        [Key]
        public int DateFormatId { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        [StringLength(50)]
        public string Format { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }
    }
}
