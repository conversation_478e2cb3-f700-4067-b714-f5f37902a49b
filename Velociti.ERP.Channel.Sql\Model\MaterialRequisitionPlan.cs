﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MaterialRequisitionPlans", Schema = "man")]
    public partial class MaterialRequisitionPlan
    {
        public MaterialRequisitionPlan()
        {
            MaterialRequisitionPlanLines = new HashSet<MaterialRequisitionPlanLine>();
        }

        [Key]
        public int MaterialRequisitionPlanId { get; set; }
        public int? CompanyId { get; set; }
        public byte? TypeEnum { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public int? ProductId { get; set; }
        public long? RequiredQuantity { get; set; }
        public long? ProductionQuantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("MaterialRequisitionPlans")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("MaterialRequisitionPlans")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(MaterialRequisitionPlanLine.MaterialRequisitionPlan))]
        public virtual ICollection<MaterialRequisitionPlanLine> MaterialRequisitionPlanLines { get; set; }
    }
}
