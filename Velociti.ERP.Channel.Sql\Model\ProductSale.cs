﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    public partial class ProductSale
    {
        [Key]
        public int Id { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date { get; set; }
        public int ProductListId { get; set; }
        [StringLength(50)]
        public string Barcode { get; set; }
        [StringLength(50)]
        public string PalletBarcode { get; set; }
        public int? UnitQty { get; set; }
        public bool? IsSecondGrade { get; set; }
        public int? TempId { get; set; }
    }
}
