﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BundleProducts", Schema = "inv")]
    public partial class BundleProduct
    {
        public BundleProduct()
        {
            BundleProductLines = new HashSet<BundleProductLine>();
        }

        [Key]
        public int BundleProductId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? StatusEnum { get; set; }
        public int CompanyId { get; set; }
        public int? ProductId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? Quantity { get; set; }
        public int? WarehouseId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("BundleProducts")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.BundleProducts))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("BundleProducts")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.BundleProducts))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(BundleProductLine.BundleProduct))]
        public virtual ICollection<BundleProductLine> BundleProductLines { get; set; }
    }
}
