﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Employees")]
    public partial class IEmployee
    {
        [StringLength(255)]
        public string EmployeeCode { get; set; }
        [Column("EPFNo")]
        [StringLength(255)]
        public string Epfno { get; set; }
        [StringLength(255)]
        public string FullName { get; set; }
        [StringLength(255)]
        public string NamewithInitials { get; set; }
        [Column("NICNo")]
        [StringLength(255)]
        public string Nicno { get; set; }
        [StringLength(255)]
        public string PassportNo { get; set; }
        [StringLength(255)]
        public string DateofBirth { get; set; }
        [StringLength(255)]
        public string Gender { get; set; }
        [StringLength(255)]
        public string DateOfEmployment { get; set; }
        [StringLength(255)]
        public string EmployeeGroup { get; set; }
        [StringLength(255)]
        public string EmployeeCategory { get; set; }
        [StringLength(255)]
        public string RelationtoProduction { get; set; }
        [StringLength(255)]
        public string Designation { get; set; }
        [StringLength(255)]
        public string Department { get; set; }
        [StringLength(255)]
        public string DateOfPermanency { get; set; }
        [StringLength(255)]
        public string DateofResignation { get; set; }
        [StringLength(255)]
        public string ApplicableShift { get; set; }
        [StringLength(255)]
        public string GrossSalary { get; set; }
        [StringLength(255)]
        public string AvgHourlyRate { get; set; }
        [StringLength(255)]
        public string Address { get; set; }
        [StringLength(255)]
        public string TelephoneNo { get; set; }
        [StringLength(255)]
        public string MobileNo { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string AccountType { get; set; }
        [StringLength(255)]
        public string SalaryAccountNo { get; set; }
        [StringLength(255)]
        public string SalaryAccountName { get; set; }
        [StringLength(255)]
        public string AccountType1 { get; set; }
        [StringLength(255)]
        public string LoanAccountNo { get; set; }
        [StringLength(255)]
        public string LoanAccountName { get; set; }
    }
}
