﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class PurchaseReqNoteLinesController : ControllerBase
    {
        private readonly IPurchaseRequisitionNoteLineService _purchaseRequisitionNoteLineService;

        public PurchaseReqNoteLinesController(IPurchaseRequisitionNoteLineService purchaseRequisitionNoteLineService)
        {
            _purchaseRequisitionNoteLineService = purchaseRequisitionNoteLineService;
        }

        [HttpGet]
        [Route("{purchaseRequisitionNoteId}")]
        public async Task<IActionResult> Get(int purchaseRequisitionNoteId)
        {
            return Ok(await _purchaseRequisitionNoteLineService.GetAsync(purchaseRequisitionNoteId));
        }

        [HttpGet]
        [Route("Submitted/{companyId}")]
        public async Task<IActionResult> GetSubmitted(int companyId)
        {
            return Ok(await _purchaseRequisitionNoteLineService.GetSubmittedLinesAsync(companyId));
        }

        [HttpPut]
        [Route("Update")]
        public async Task<IActionResult> Generate([FromBody]ViewForm viewForm)
        {
            switch(viewForm.Action)
            {
                case "update closed date": await _purchaseRequisitionNoteLineService.UpdateClosedDateAsync(viewForm.IntegerList, viewForm.ParameterOne); break;
            }

            return Ok();
        }
    }
}