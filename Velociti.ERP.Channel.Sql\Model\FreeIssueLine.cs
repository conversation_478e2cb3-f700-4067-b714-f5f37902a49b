﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FreeIssueLines", Schema = "sales")]
    public partial class FreeIssueLine
    {
        [Key]
        public int FreeIssueLineId { get; set; }
        public int FreeIssueId { get; set; }
        public int? ProductCategoryId { get; set; }
        public int? ProductBrandId { get; set; }
        public int? ProductId { get; set; }
        public int Quantity { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.FreeIssueLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.FreeIssueLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(FreeIssueId))]
        [InverseProperty("FreeIssueLines")]
        public virtual FreeIssue FreeIssue { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("FreeIssueLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductBrandId))]
        [InverseProperty(nameof(SupportData.FreeIssueLines))]
        public virtual SupportData ProductBrand { get; set; }
    }
}
