﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.FixedAssets;
using Velociti.ERP.Domain.Services.FixedAssets;

namespace Velociti.ERP.API.Controllers.FixedAssets
{
    [Route("api/FixedAssets/[controller]")]
    [Authorize]
    [ApiController]
    public class FixedAssetRegistryController : ControllerBase  
    {
        private readonly IFixedAssetRegistryService _fixedAssetRegistryService;  

        public FixedAssetRegistryController(IFixedAssetRegistryService fixedAssetRegistryService)
        {
            _fixedAssetRegistryService = fixedAssetRegistryService;
        }

        [HttpGet]
        [Route("FixedAsset/{fixedAssetId}")]
        public async Task<IActionResult> GetAll(int fixedAssetId)
        {
            return Ok(await _fixedAssetRegistryService.GetAllAsync(fixedAssetId));
        }

        [HttpGet]
        [Route("{fixedAssetRegistryId}")]
        public async Task<IActionResult> GetById(int fixedAssetRegistryId)
        {
            return Ok(await _fixedAssetRegistryService.FindByIdAsync(fixedAssetRegistryId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]FixedAssetRegistry fixedAssetRegistry)
        {
            return Ok(await _fixedAssetRegistryService.SaveAsync(fixedAssetRegistry));
        }

        [HttpDelete]
        [Route("{fixedAssetRegistryId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int fixedAssetRegistryId, int userId)
        {
            await _fixedAssetRegistryService.ToggleActivationAsync(fixedAssetRegistryId, userId);

            return Ok();
        }
    }
}