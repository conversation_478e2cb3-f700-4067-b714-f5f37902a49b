﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class OutboundLoanController : ControllerBase
    {
        private readonly IOutboundLoanService _outBoundLoanService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public OutboundLoanController(IOutboundLoanService outBoundLoanService, IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _outBoundLoanService = outBoundLoanService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }
        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]LoanOrder loanOrder)
        {
            await _outBoundLoanService.SaveAsync(loanOrder);

            return Ok();
        }
        [HttpGet]
        [Route("Single/{loanOrderId}")]
        public async Task<IActionResult> GetById(int loanOrderId)
        {

            var result = await _outBoundLoanService.GetByIdAsync(loanOrderId);
            return Ok(result);

        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte orderTypeEnum)
        {
            return Ok(await _outBoundLoanService.GetAllAsync(companyId, userId, startDate, endDate,(int)Module.DocumentType.OutboundLoanRequest));
        }
        [HttpPut]
        public async Task<IActionResult> Update([FromBody]LoanOrder loanOrder)
        {
            switch (loanOrder.Action)
            {
                case "submit":
                       await _outBoundLoanService.SubmitAsync(loanOrder);
                    break;
                case "cancel":
                    await _outBoundLoanService.CancelAsync(loanOrder);
                    break;
                case "convertToShipmentDispatch":
                    return Ok(await _outBoundLoanService.ConvertToShipmentDispatchAsync(loanOrder));
                case "complete":
                    await _outBoundLoanService.CompleteAsync(loanOrder);
                    break;
                case "reverse":
                    await _outBoundLoanService.ReverseAsync(loanOrder);
                    break;
                case "convertToGDN":
                    return Ok(await _goodsDispatchNoteService.ConvertAsync(loanOrder.LoanOrderId, Module.DocumentType.OutboundLoanRequest, loanOrder.ModifiedUserId));
            }

            return Ok();
        }
    }
}