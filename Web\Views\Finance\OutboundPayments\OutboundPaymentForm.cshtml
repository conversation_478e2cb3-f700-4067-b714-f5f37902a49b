﻿﻿
<div class="kt-content  kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor" id="kt_content">
    <!-- begin:: Subheader -->
    <div class="kt-subheader   kt-grid__item" id="kt_subheader">
        <div class="kt-container  kt-container--fluid ">
            <div class="kt-subheader__main">
                <h3 class="kt-subheader__title">
                </h3>
                <span class="kt-subheader__separator kt-hidden"></span>
                <div class="kt-subheader__breadcrumbs">
                    <a href='@Url.Action("Index", "Home")' class="kt-subheader__breadcrumbs-home"><i class="flaticon2-shelter"></i></a>
                    <span class="kt-subheader__breadcrumbs-separator"></span>
                    <a href='@Url.Action("Index", "Home")' class="kt-subheader__breadcrumbs-link">
                        Home
                    </a>
                    <span class="kt-subheader__breadcrumbs-separator"></span>
                    <a class="kt-subheader__breadcrumbs-link">
                        Finance
                    </a>
                    <span class="kt-subheader__breadcrumbs-separator"></span>
                    <a href='@Url.Action("Index", "OutboundPayments")' class="kt-subheader__breadcrumbs-link">
                        Payment Vouchers
                    </a>
                    <span class="kt-subheader__breadcrumbs-separator"></span>
                    <span class="kt-subheader__breadcrumbs-link kt-subheader__breadcrumbs-link--active">@ViewBag.Title</span>
                </div>
            </div>
        </div>
    </div>
    <!-- end:: Subheader -->
    <!-- begin:: Content -->
    <div class="kt-container  kt-container--fluid  kt-grid__item kt-grid__item--fluid">
        <!--begin::Accordion-->
        <div class="accordion accordion-solid accordion-toggle-svg" id="accordionExample8">
            <div class="card">
                <div class="card-header" id="headingOne8">
                    <div class="card-title" data-toggle="collapse" data-target="#collapseOne8" aria-expanded="true" aria-controls="collapseOne8">
                        Payment Voucher Details <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <polygon points="0 0 24 0 24 24 0 24" />
                                <path d="M12.2928955,6.70710318 C11.9023712,6.31657888 11.9023712,5.68341391 12.2928955,5.29288961 C12.6834198,4.90236532 13.3165848,4.90236532 13.7071091,5.29288961 L19.7071091,11.2928896 C20.085688,11.6714686 20.0989336,12.281055 19.7371564,12.675721 L14.2371564,18.675721 C13.863964,19.08284 13.2313966,19.1103429 12.8242777,18.7371505 C12.4171587,18.3639581 12.3896557,17.7313908 12.7628481,17.3242718 L17.6158645,12.0300721 L12.2928955,6.70710318 Z" fill="#000000" fill-rule="nonzero" />
                                <path d="M3.70710678,15.7071068 C3.31658249,16.0976311 2.68341751,16.0976311 2.29289322,15.7071068 C1.90236893,15.3165825 1.90236893,14.6834175 2.29289322,14.2928932 L8.29289322,8.29289322 C8.67147216,7.91431428 9.28105859,7.90106866 9.67572463,8.26284586 L15.6757246,13.7628459 C16.0828436,14.1360383 16.1103465,14.7686056 15.7371541,15.1757246 C15.3639617,15.5828436 14.7313944,15.6103465 14.3242754,15.2371541 L9.03007575,10.3841378 L3.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" transform="translate(9.000003, 11.999999) rotate(-270.000000) translate(-9.000003, -11.999999) " />
                            </g>
                        </svg>
                    </div>
                </div>
                <div id="collapseOne8" class="collapse show" aria-labelledby="headingOne8" data-parent="#accordionExample8">
                <div id="collapseOne8" class="collapse show" aria-labelledby="headingOne8" data-parent="#accordionExample8">
                    <div class="card-body">
                        <!--begin::Form-->
                        <form class="kt-form" id="form">
                            <div class="kt-portlet__body">
                                <div class="form-group row">
                                    <div class="col-lg-4" id="dvCode" style="display:none">
                                        <label class="form-control-label">Doc Number </label> <span class="require">*</span>
                                        <input type="text" class="form-control form-control-sm" id="txtCode" disabled />
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Type </label> <span class="require">*</span>
                                        <select class="form-control form-control-sm kt-select2" id="cmbType">
                                         <option value="">Select an option</option>
                                         <option value="1">Supplier Payment Voucher</option>
                                         <option value="2">Fund Transfer</option>
                                         <option value="3">Customer Payment Voucher</option>
                                         <option value="4">Payment Voucher</option>
                                         <option value="5">Employee Payment Voucher</option>
                                         <option value="6">Custom Voucher</option>
                                         <option value="7">IOU Settlement</option>
                                         </select>
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Document Date </label> <span class="require">*</span>
                                        <input type="text" class="form-control form-control-sm js-datepicker" id="deDocDate" readonly />
                                    </div>
                                    <div class="col-lg-4" style="display:none" id="dvSupplier">
                                        <label class="form-control-label">Supplier </label> <span class="require">*</span>
                                        <select class="form-control form-control-sm kt-select2" id="cmbSupplier">
                                            <option value="">Select an option</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4" style="display:none" id="dvCustomer">
                                        <label class="form-control-label">Customer </label> <span class="require">*</span>
                                        <select class="form-control form-control-sm kt-select2" style="width:100%" id="cmbCustomer">
                                            <option value="">Select an option</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4" style="display:none" id="dvEmployee">
                                        <label class="form-control-label">Employee </label> <span class="require">*</span>
                                        <select class="form-control form-control-sm kt-select2" id="cmbEmployee">
                                            <option value="">Select an option</option>
                                        </select>
                                    </div>
                                </div>

                                <div class="form-group row">
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Pay Book </label> <span class="require">*</span>
                                        <select class="form-control form-control-sm kt-select2" id="cmbPayBook">
                                            <option value="">Select an option</option>
                                        </select>
                                        <button type="button" id="btnTestPayBooks" class="btn btn-sm btn-warning mt-1">🔧 Test PayBooks API</button>
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Bank Code</label>
                                        <input type="text" id="txtBankCode" class="form-control form-control-sm" readonly />
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Cash AC</label>
                                        <input type="text" id="txtBank" class="form-control form-control-sm" readonly />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Bank Account No</label>
                                        <input type="text" id="txtAccountNo" class="form-control form-control-sm" readonly />
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Bank Branch</label>
                                        <input type="text" id="txtBranch" class="form-control form-control-sm" readonly />
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Cheque Book</label>
                                        <select class="form-control form-control-sm kt-select2" id="cmbChequeBook">
                                            <option value="">Select an option</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Payee Name</label> <span id="payeeNameRequired" class="require">*</span>
                                        <input type="text" id="txtPayeeName" class="form-control form-control-sm" />
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Cheque Date</label> <span id="chequeDateRequired" class="require">*</span>
                                        <input type="text" class="form-control form-control-sm js-datepicker" id="deChequeDate" readonly />
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Banking Date</label> <span id="bankingDateRequired" class="require">*</span>
                                        <input type="text" class="form-control form-control-sm js-datepicker" id="deBankingDate" readonly />
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Description </label>
                                        <textarea id="txtRemarks" class="form-control form-control-sm"></textarea>
                                    </div>
                                    <div class="col-lg-4">
                                        <!--<label class="form-control-label">Currency </label> <span class="require">*</span>
        <select class="form-control form-control-sm kt-select2" id="cmbCurrency">
            <option value="">Select an option</option>
        </select>-->
                                    </div>
                                    <div class="col-lg-4">
                                        <!--<label class="form-control-label">Exchange Rate </label> <span class="require">*</span>
        <input type="number" id="txtExchangeRate" class="form-control form-control-sm" />-->
                                    </div>

                                </div>
                                <div class="form-group row">
                                    <div class="col-lg-4" style="display:none" id="dvAccount">
                                        <label class="form-control-label">Disbursement Account </label> <span class="require">*</span>
                                        <select class="form-control form-control-sm kt-select2" id="cmbAccount">
                                            <option value="">Select an option</option>
                                        </select>
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Ref Number</label>
                                        <input type="text" id="txtRefNumber" class="form-control form-control-sm" disabled />
                                    </div>
                                    <div class="col-lg-4">
                                        <!--<label for="cmbDepartment" class="form-control-label">Department</label>
                                        <select class="form-control form-control-sm kt-select2" id="cmbDepartment">
                                            <option value="">Select an option</option>
                                        </select>-->
                                    </div>
                                    <div class="col-lg-4" style="display:none" id="dvIOU">
                                        <label class="form-control-label">IOU </label>
                                        <select class="form-control form-control-sm kt-select2" id="cmbIOU">
                                            <option value="">Select an option</option>
                                        </select>
                                    </div>
                                    <div class="col-lg-4">
                                        <label class="form-control-label">Available Advances </label>
                                        <input type="number" id="txtAvailableAdvances" class="form-control form-control-sm" disabled="disabled" />
                                    </div>
                                </div>
                                <div id="dvPDCheque">
                                    <div class="form-group row">
                                        <div class="col-lg-4">
                                            <label class="kt-checkbox kt-checkbox--solid kt-checkbox--brand">
                                                <input type="checkbox" id="chkPDCheque"> PD Cheque
                                                <span></span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!--end::Accordion-->
        <div class="row mt-4">
            <div class="col-md-12">
                <!--begin::Portlet-->
                <div class="kt-portlet">
                    <div class="kt-portlet__head">
                        <div class="kt-portlet__head-label">
                            <h3 class="kt-portlet__head-title">
                               Payment Voucher Line Details
                            </h3>
                        </div>
                        <div class="kt-portlet__head-toolbar">
                            <div class="kt-portlet__head-wrapper">
                                <div class="kt-portlet__head-actions">
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--begin::Form-->
                    <form class="kt-form">
                        <div class="kt-portlet__body">
                            <div class="form-group">
                                <label class="kt-checkbox kt-checkbox--solid kt-checkbox--brand">
                                    <input type="checkbox" id="chkAllCurrencies"> Show All Currencies
                                    <span></span>
                                </label>
                            </div>
                            <div id="linesContainer"></div>
                            <div class="form-group row mt-5">
                                <label class="col-lg-2 col-form-label">Total Amount</label>
                                <div class="col-lg-2">
                                    <input type="text" id="txtSettlementValue" class="form-control form-control-sm" disabled>
                                </div>
                            </div>
                        </div>
                        <div class="kt-portlet__foot">
                            <div class="kt-form__actions">
                                <button type="button" id="btnSave" class="btn btn-primary">Save</button>
                                <button type="button" id="btnCancel" class="btn btn-secondary">Cancel</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <!-- end:: Content -->
</div>

<!--begin::LineDetailModal-->
<div class="modal fade" id="lineDetailModal" tabindex="-1" role="dialog" aria-labelledby="exampleModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Cost Allocation</h5>
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                </button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="form-group">
                        <div class="form-group row">
                            <div class="kt-radio-inline">
                                <label class="kt-radio">
                                    <input type="radio" name="radioCostAllocation" id="radioCostAllocation1"> Business Unit
                                    <span></span>
                                </label>
                                <label class="kt-radio">
                                    <input type="radio" name="radioCostAllocation" id="radioCostAllocation2"> GRN Shipment
                                    <span></span>
                                </label>
                            </div>
                        </div>
                        <div class="form-group row" id="dvBusinessUnit">
                            <label for="cmbBusinessUnit" class="form-control-label">Business Unit</label>
                            <select class="form-control form-control-sm kt-select2" id="cmbBusinessUnit">
                                <option value="">Select an option</option>
                            </select>
                        </div>
                        <div class="form-group row" id="dvGRNShipment" style="display:none">
                            <label for="cmbGRNShipment" class="form-control-label">GRN Shipment</label>
                            <select class="form-control form-control-sm kt-select2" id="cmbGRNShipment">
                                <option value="">Select an option</option>
                            </select>
                        </div>
                        <div class="form-group row">
                            <label>Apportion Percentage</label>
                            <input type="text" id="txtApportionPercentage" min="1" value="1" class="form-control form-control-sm bootstrap-touchspin-vertical-btn">
                        </div>
                    </div>
                    <input id="hiddenLineId" value="0" type="hidden" />
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-dismiss="modal">Cancel</button>
                <button type="button" id="btnAddDetailLine" class="btn btn-primary">Add Cost Allocation</button>
            </div>
            <div class="modal-body">
                <form>
                    <div id="lineDetailsContainer" style="width:100%"></div>
                </form>
            </div>
        </div>
    </div>
</div>
<!--end::Modal-->


@section scripts{
    <script>
        $(function () {
            const defaultCompanyId = parseInt($("#hiddenDefaultCompanyId").val());
            const loggedInUserId = parseInt($("#hiddenUserId").val());
            const outboundPaymentId = @ViewBag.OutboundPaymentId;
            const baseCurrencyId = 79;
            const client = "@System.Configuration.ConfigurationManager.AppSettings["Client"]";

            // Debug logging
            console.log("=== OutboundPayment Form Debug Info ===");
            console.log("defaultCompanyId:", defaultCompanyId);
            console.log("loggedInUserId:", loggedInUserId);
            console.log("outboundPaymentId:", outboundPaymentId);
            console.log("apiUrl:", apiUrl);
            console.log("hiddenDefaultCompanyId element:", $("#hiddenDefaultCompanyId"));
            console.log("hiddenDefaultCompanyId value:", $("#hiddenDefaultCompanyId").val());

            let backDateEnabledUserIds = [@ViewBag.BackDateEnabledUserIds];
            let payBookList = [];
            let readOnly = true;
            let linesGrid = null;
            let isCheque = false;
            let isDiscountDisabled = false;
            let isEmployeePaymentVoucher = false;
            let isOldCurrencyRateDate = false;
            let outboundPaymentCostAllocations = [];

            $("#chequeDateRequired").hide();
            $("#bankingDateRequired").hide();
            $("#payeeNameRequired").hide();
            $("#deChequeDate").attr("disabled", true);
            $("#deBankingDate").attr("disabled", true);

            let vm = {
                outboundPaymentId: 0,
                outboundPaymentLines: []
            };

            // Test API connectivity first
            console.log("Testing API connectivity...");
            $.ajax({
                url: apiUrl + "Finance/PayBooks/ShortList/1", // Test with company ID 1
                method: "GET",
                timeout: 5000,
                success: function(result) {
                    console.log("API test successful:", result);
                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.error("API test failed:", jqXHR.status, textStatus, errorThrown);
                }
            });

            if (outboundPaymentId > 0)
                initModel();
            else {
                $("#deDocDate").val(getFormattedDate(new Date()));
                bindSupplierList();

                bindPayBookList();
                bindCurrencyList();
                bindCustomerList();
                bindEmployeeList();
                bindAccountList();
                bindUserDepartment();
                bindIOUList();
            }

            $("#btnCancel").click(function () {
                window.location = "@Url.Action("Index", "OutboundPayments")";
            });

            $("#btnSaveLine").click(function () {
                saveLine();
            });

            $("#btnSave").click(function () {
                saveOutboundPaymentData();
            });

            $("#cmbPayBook").on("change", function () {
                let payBookId = parseInt($("#cmbPayBook").val());
                if (payBookId) {
                    var paybook = payBookList.find(p => p.id == payBookId);

                    if (paybook.typeEnum == 2) {
                        isCheque = true;
                        $("#chequeDateRequired").show();
                        $("#bankingDateRequired").show();
                        $("#payeeNameRequired").show();
                        $("#deChequeDate").attr("disabled", false);
                        $("#deBankingDate").attr("disabled", false);

                        if (outboundPaymentId == 0) {
                            $('#deChequeDate').datepicker('update', getFormattedDate(vm.docDate));
                            $('#deBankingDate').datepicker('update', getFormattedDate(vm.docDate));
                        }
                        
                    }
                    else {
                        isCheque = false;
                        $("#chequeDateRequired").hide();
                        $("#bankingDateRequired").hide();
                        $("#payeeNameRequired").hide();
                        $("#deChequeDate").attr("disabled", true);
                        $("#deBankingDate").attr("disabled", true);

                        if (outboundPaymentId == 0) {
                            $('#deChequeDate').datepicker('update', getFormattedDate(vm.chequeDate));
                            $('#deBankingDate').datepicker('update', getFormattedDate(vm.bankingDate));
                        }
                    }
                }

                bindBankDetails();
                bindChequeBookList();
            });

            $("#cmbSupplier").on("change", function () {
                bindSupplierDetails();
                bindAvailableAdvances();
                bindLines();
            });

            $("#cmbCustomer").on("change", function () {
                bindLines();
                bindCustomerDetails();
            });

            $("#cmbEmployee").on("change", function () {
                bindLines();
            });

            $("#cmbEmployee").on("change", function () {
                bindLines();
            });

            $("#cmbCurrency").on("change", function () {
                bindLines();
                bindCurrencyDetails();
            });

            $("#cmbAccount").on("change", function () {
                bindLines();
            });

            $("#btnAddDetailLine").click(function () {
                saveLineDetailsData();
            });

            $("#btnTestPayBooks").click(function () {
                console.log("=== Manual PayBooks API Test ===");
                console.log("defaultCompanyId:", defaultCompanyId);
                console.log("apiUrl:", apiUrl);

                if (!defaultCompanyId || isNaN(defaultCompanyId)) {
                    alert("Error: Invalid defaultCompanyId = " + defaultCompanyId);
                    return;
                }

                const testUrl = apiUrl + `Finance/PayBooks/ShortList/${defaultCompanyId}`;
                console.log("Testing URL:", testUrl);

                $.ajax({
                    url: testUrl,
                    method: "GET",
                    timeout: 30000,
                    success: function (result) {
                        console.log("✅ API Test Success:", result);
                        alert(`✅ API Test Success!\nFound ${result ? result.length : 0} paybooks\nCheck console for details`);
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.error("❌ API Test Failed:", {
                            status: jqXHR.status,
                            statusText: jqXHR.statusText,
                            responseText: jqXHR.responseText,
                            errorThrown: errorThrown
                        });
                        alert(`❌ API Test Failed!\nStatus: ${jqXHR.status}\nError: ${errorThrown}\nCheck console for details`);
                    }
                });
            });

            $("#cmbType").on("change", function () {
                let typeEnum = $(this).val();
                //$("#cmbCurrency").attr("disabled", false);
                isEmployeePaymentVoucher = false;

                if (typeEnum == 1) {
                    $("#dvSupplier").show();
                    $("#dvCustomer").hide();
                    $("#dvEmployee").hide();
                    $("#dvAccount").hide();
                }

                if (typeEnum == 2) {
                    $("#dvSupplier").hide();
                    $("#dvCustomer").hide();
                    $("#dvEmployee").hide();
                    $("#dvAccount").show();

                    //$("#cmbCurrency").attr("disabled", true);
                    //$("#cmbCurrency").val(baseCurrencyId);
                    //$("#cmbCurrency").trigger("change");

                    isDiscountDisabled = true;
                }
                if (typeEnum == 3) {
                    $("#dvSupplier").hide();
                    $("#dvCustomer").show();
                    $("#dvEmployee").hide();
                    $("#dvAccount").hide();
                }

                if (typeEnum == 4 || !typeEnum) {
                    $("#dvSupplier").hide();
                    $("#dvCustomer").hide();
                    $("#dvEmployee").hide();
                    $("#dvAccount").hide();
                }

                if (typeEnum == 5) {
                    $("#dvSupplier").hide();
                    $("#dvCustomer").hide();
                    $("#dvEmployee").show();
                    $("#dvAccount").hide();
                    //$("#cmbCurrency").val(baseCurrencyId).change();
                    //$("#cmbCurrency").attr("disabled", true);
                    //$("#cmbCurrency").val(baseCurrencyId);
                    //$("#cmbCurrency").trigger("change");
                    isEmployeePaymentVoucher = true;
                }
                if (typeEnum == 7) {
                    $("#dvSupplier").show();
                    $("#dvCustomer").hide();
                    $("#dvEmployee").hide();
                    $("#dvAccount").hide();
                    $("#dvIOU").show();
                }

                bindLines();
            });

            $("#chkAllCurrencies").on("change", function () {
                bindLines();
            });

            $("#radioCostAllocation1").on("change", function () {
                radioCheckChange();
            });

            $("#radioCostAllocation2").on("change", function () {
                radioCheckChange();
            });

            function radioCheckChange() {
                let radioCheckEnum = $('#radioCostAllocation1').is(':checked') ? 1 : $('#radioCostAllocation2').is(':checked') ? 2 : null;

                if (radioCheckEnum == 1) {
                    $("#cmbBusinessUnit").val("").trigger("change");
                    $('#dvBusinessUnit').show();
                    $('#dvGRNShipment').hide();
                    bindBusinessUnitList();
                }
                else if (radioCheckEnum == 2) {
                    $("#cmbGRNShipment").val("").trigger("change");
                    $('#dvBusinessUnit').hide();
                    $('#dvGRNShipment').show();
                    bindGRNShipmentList();
                }
            }

            function initModel() {
                $("#dvCode").show();

                $.ajax({
                    url: apiUrl + "Finance/OutboundPayments/Single/" + outboundPaymentId,
                    method: "GET",
                    success: function (result) {
                        vm = result;
                        readOnly = vm.statusEnum != 1;
                        bindControls();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindLines() {
                let supplierId = $("#cmbSupplier").val();
                let customerId = $("#cmbCustomer").val();
                let employeeId = $("#cmbEmployee").val();
                let currencyId = $("#cmbCurrency").val();
                let typeEnum = parseInt($("#cmbType").val());
                let showAll = $("#chkAllCurrencies").is(":checked");
                let accountId = 0;

                if (showAll)
                    currencyId = -1;

                if (!currencyId || (!typeEnum && typeEnum != 3)) {
                    vm.outboundPaymentLines = [];
                    bindLinesGrid();
                    return;
                }

                if (typeEnum == 1 && (!supplierId || !currencyId)) {
                    vm.outboundPaymentLines = [];
                    bindLinesGrid();
                    return;
                }

                if (typeEnum == 2) {
                    accountId = $("#cmbAccount").val();

                    if (!accountId) {
                        vm.outboundPaymentLines = [];
                        bindLinesGrid();
                        return;
                    }
                }

                if (typeEnum == 3 && (!customerId || !currencyId)) {
                    vm.outboundPaymentLines = [];
                    bindLinesGrid();
                    return;
                }

                if (typeEnum == 5 && (!employeeId || !currencyId)) {
                    vm.outboundPaymentLines = [];
                    bindLinesGrid();
                    return;
                }
                if (typeEnum == 7 && (!supplierId || !currencyId)) {
                    vm.outboundPaymentLines = [];
                    bindLinesGrid();
                    return;
                }

                let endPoint = function () {
                    switch (typeEnum) {
                        case 1: return `Finance/OutboundPaymentLines/${outboundPaymentId}/Supplier/${supplierId}/Currency/${currencyId}/Company/${defaultCompanyId}`;
                        case 2: return `Finance/OutboundPaymentLines/${outboundPaymentId}/Type/${3}/Account/${accountId}/Currency/${currencyId}/Company/${defaultCompanyId}`; // 3 is fund transfer payment advices
                        case 3: return `Finance/OutboundPaymentLines/${outboundPaymentId}/Customer/${customerId}/Currency/${currencyId}/Company/${defaultCompanyId}`;
                        case 4: return `Finance/OutboundPaymentLines/${outboundPaymentId}/Type/${5}/Account/${0}/Currency/${currencyId}/Company/${defaultCompanyId}`; // 5 is payment voucher payment advices
                        case 5: return `Finance/OutboundPaymentLines/${outboundPaymentId}/Employee/${employeeId}/Currency/${currencyId}/Company/${defaultCompanyId}`;
                        case 6: return `Finance/OutboundPaymentLines/${outboundPaymentId}/Type/${6}/Account/${0}/Currency/${currencyId}/Company/${defaultCompanyId}`;
                        case 7: return `Finance/OutboundPaymentLines/${outboundPaymentId}/Supplier/${supplierId}/Currency/${currencyId}/Company/${defaultCompanyId}`;

                    }
                }

                $.ajax({
                    url: apiUrl + endPoint(),
                    method: "GET",
                    success: function (result) {
                        vm.outboundPaymentLines = result;

                        vm.outboundPaymentLines.forEach(li => {
                            if (!li.outboundPaymentCostAllocations)
                                li.outboundPaymentCostAllocations = [];
                        });

                        bindLinesGrid();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindControls() {
                $("#txtCode").val(vm.docNumber);
                $('#deDocDate').datepicker('update', getFormattedDate(vm.docDate));
                $('#deChequeDate').datepicker('update', getFormattedDate(vm.chequeDate));
                $('#deBankingDate').datepicker('update', getFormattedDate(vm.bankingDate));
                $("#txtRemarks").val(vm.remarks);
                $("#txtSettlementValue").val(vm.settlementValue ? vm.settlementValue.toFixed(2) : '0.00');
                $("#txtPayeeName").val(vm.payeeName);
                $("#txtRefNumber").val(vm.refNumber);
                $("#chkPDCheque").prop('checked', vm.isPostDatedCheque);
                $("#txtAvailableAdvances").val(vm.availableAdvances ? vm.availableAdvances.toFixed(2) : '0.00');

                if (!backDateEnabledUserIds.includes(loggedInUserId))
                    $("#deDocDate").attr("disabled", true);
                else
                    $("#deDocDate").removeAttr("disabled", true);

                if (vm.statusEnum > 1) {
                    $("#btnNewLine").hide();
                    $("#btnSave").hide();
                }

                if (outboundPaymentId > 0) {
                    $("#cmbType").val(vm.typeEnum).change();
                    $("#cmbType").attr("disabled", true);
                }

                if (readOnly)
                    $("#btnSave").hide();

                bindSupplierList();
                bindPayBookList();
                bindCurrencyList();
                bindCustomerList();
                bindEmployeeList();
                bindAccountList();
                bindUserDepartment();
            }

            function bindLinesGrid() {
                linesGrid = $("#linesContainer").dxDataGrid({
                    dataSource: vm.outboundPaymentLines,
                    allowColumnReordering: true,
                    allowColumnResizing: true,
                    columnAutoWidth: true,
                    wordWrapEnabled: true,
                    onContentReady: function () {
                        $(".dx-datagrid-table").addClass("table");
                        $(".dx-header-row").addClass("table-active");
                    },
                    columns: [
                        {
                            dataField: "docNumber",
                            caption: "Payable Invoice No"
                        },
                        {
                            dataField: "typeName",
                            caption: "Type",
                            width: 200
                        },
                        {
                            dataField: "currencyName",
                            caption: "Currency",
                            width: 100
                        },
                        {
                            dataField: "reference",
                            width: 150
                        },
                        {
                            dataField: "isSettled",
                            cellTemplate: function (container, options) {
                                $("<div class='ml-2' />").dxCheckBox({
                                    value: options.value,
                                    onOptionChanged: function (data) {
                                        saveLine(options, data.value);
                                    }
                                }).appendTo(container);
                            }
                        },
                        {
                            dataField: "settlementValue",
                            format: "#,##0.00",
                            cellTemplate: function (container, options) {
                                let type = $("#cmbType").val();
                                let value = options.value;
                                let isDisabled = options.data.subTypeEnum == 1 || options.data.subTypeEnum == 3 || options.data.subTypeEnum == 5;

                                if (type == 2) { //Fund Transfer
                                    value = options.data.balanceAmount - (options.data.discount ?? 0);
                                    isDisabled = true;
                                }
                                $("<div class='ml-2' />").dxNumberBox({
                                    value: value,
                                    format: "#,##0.00",
                                    disabled: isDisabled,
                                    onFocusOut: function (e) { saveLine(options, e.component.option("value")); },
                                    step: 0
                                }).appendTo(container);
                            }
                        },
                        {
                            dataField: "documentAmount",
                            format: "#,##0.00",
                            width: 150
                        },
                        {
                            dataField: "amountInBaseCurrency",
                            caption: "Amount In Base Currency",
                            format: "#,##0.00",
                            width: 150
                        },
                        {
                            dataField: "dueDate",
                            dataType: "date",
                            width: 100,
                            format: "yyyy-MM-dd"
                        },
                        {
                            dataField: "balanceAmount",
                            format: "#,##0.00",
                        },
                        {
                            dataField: "discount",
                            format: "#,##0.00",
                            cellTemplate: function (container, options) {
                                $("<div class='ml-2' />").dxNumberBox({
                                    value: options.value,
                                    format: "#,##0.00",
                                    disabled: isDiscountDisabled,
                                    onFocusOut: function (e) { saveLine(options, e.component.option("value")); },
                                    step: 0
                                }).appendTo(container);
                            }
                        },
                        {
                            dataField: "settlementDue",
                            format: "#,##0.00",
                            cellTemplate: function (container, options) {
                                $("<div class='ml-2' />").dxNumberBox({
                                    value: options.data.balanceAmount - (options.data.discount ?? 0),
                                    format: "#,##0.00",
                                    disabled: true,
                                    onFocusOut: function (e) { saveLine(options, e.component.option("value")); },
                                    step: 0
                                }).appendTo(container);
                            }
                        },
                        {
                            dataField: "crossPaidAmount",
                            format: "#,##0.00",
                            width: 150,
                            cellTemplate: function (container, options) {
                                let currencyId = $("#cmbCurrency").val();
                                let isDisable = currencyId == options.data.currencyId;
                                //baseCurrencyId;
                                //let isDisabled = isEmployeePaymentVoucher && options.data.currencyId == currencyId;
                                $("<div class='ml-2' />").dxNumberBox({
                                    value: options.value,
                                    format: "#,##0.00",
                                    disabled: isDisable,
                                    onFocusOut: function (e) { saveLine(options, e.component.option("value")); },
                                    step: 0
                                }).appendTo(container);
                            }
                        },
                        {
                            dataField: "generalLedgerLineId",
                            caption: "Action",
                            width: 100,
                            allowSorting: false,
                            allowFiltering: false,
                            cellTemplate: function (container, options) {
                                $("<div class='ml-2'/>").dxButton({
                                    icon: "la la-plus",
                                    type: "normal",
                                    hint: "Add Cost Allocation",
                                    elementAttr: {
                                        class: "btn btn-xs"
                                    },
                                    text: "",
                                    onClick: function (e) {
                                        showLineDetailsModal(options.data);
                                    }
                                }).appendTo(container);
                            }
                        }
                    ],
                    paging: {
                        pageSize: 10
                    },
                    pager: {
                        showPageSizeSelector: true,
                        allowedPageSizes: [10, 20],
                        showInfo: true
                    },
                    groupPanel: {
                        visible: true
                    },
                    searchPanel: {
                        visible: false
                    },
                    filterRow: {
                        visible: true
                    },
                    showBorders: true,
                    rowAlternationEnabled: true,
                    showRowLines: true
                }).dxDataGrid("instance");
            }

            function showLineDetailsModal(lineData) {
                $("#hiddenLineId").val(lineData.generalLedgerLineId);
                $("#txtApportionPercentage").val("0");
                $('#lineDetailModal').modal('show');
                $("#cmbBusinessUnit").val("").trigger("change");
                $("#cmbGRNShipment").val("").trigger("change");

                if (lineData.outboundPaymentLineId > 0 && lineData.outboundPaymentCostAllocations.length == 0) {

                    $.ajax({
                        url: apiUrl + "Finance/OutboundPaymentCostAllocations/" + lineData.outboundPaymentLineId,
                        method: "GET",
                        success: function (result) {
                            result.forEach((ca) => {
                                ca.generatedId = generateUUID();
                                lineData.outboundPaymentCostAllocations.push(ca);
                            });

                            outboundPaymentCostAllocations = lineData.outboundPaymentCostAllocations;
                            bindLineDetailsGrid();
                        },
                        error: function (jqXHR, textStatus, errorThrown) {
                            toastr.error(jqXHR.responseText);
                        }
                    });
                }
                else {
                    outboundPaymentCostAllocations = lineData.outboundPaymentCostAllocations;
                    bindLineDetailsGrid();
                }
            }

            function bindLineDetailsGrid() {
                $("#lineDetailsContainer").dxDataGrid({
                    dataSource: outboundPaymentCostAllocations,
                    key: "generatedId",
                    allowColumnReordering: true,
                    allowColumnResizing: true,
                    columnAutoWidth: true,
                    onContentReady: function () {
                        $(".dx-datagrid-table").addClass("table");
                        $(".dx-header-row").addClass("table-active");
                    },
                    columns: [
                        {
                            dataField: "departmentName",
                            caption: "Business Unit",
                            width: 200
                        },
                        {
                            dataField: "goodsReceiveNoteDocNumber",
                            caption: "GRN Shipment",
                            width: 100
                        },
                        {
                            dataField: "apportionPct",
                            format: "#,##0.00",
                            width: 100
                        },
                        {
                            dataField: "generatedId",
                            caption: "Remove",
                            width: 100,
                            allowSorting: false,
                            allowFiltering: false,
                            cellTemplate: function (container, options) {
                                $("<div class='ml-2'/>").dxButton({
                                    icon: "la la-remove",
                                    type: "normal",
                                    hint: "Remove",
                                    elementAttr: {
                                        class: "btn btn-xs"
                                    },
                                    onClick: function (e) {
                                        removeLineDetail(options.data);
                                    }
                                }).appendTo(container);
                            }
                        }
                    ],
                    wordWrapEnabled: true,
                    paging: {
                        pageSize: 10
                    },
                    pager: {
                        showPageSizeSelector: true,
                        allowedPageSizes: [10, 20],
                        showInfo: true
                    },
                    groupPanel: {
                        visible: false
                    },
                    searchPanel: {
                        visible: false
                    },
                    filterRow: {
                        visible: true
                    },
                    showBorders: true,
                    rowAlternationEnabled: true,
                    showRowLines: true
                }).dxDataGrid("instance");
            }

            function removeLineDetail(lineDetail) {
                const swalWithBootstrapButtons = Swal.mixin({
                    customClass: {
                        confirmButton: 'btn btn-success',
                        cancelButton: 'btn btn-danger'
                    },
                    buttonsStyling: false
                })

                swalWithBootstrapButtons.fire({
                    title: 'Are you sure?',
                    text: "The line will be removed!",
                    type: 'warning',
                    showCancelButton: true,
                    confirmButtonText: 'Yes, remove it!',
                    cancelButtonText: 'No, cancel!',
                    reverseButtons: true
                }).then((result) => {
                    if (result.value) {
                        let generalLedgerLineId = $("#hiddenLineId").val();
                        let outboundPaymentLine = vm.outboundPaymentLines.find(p => p.generalLedgerLineId == generalLedgerLineId);
                        let indexToRemove = outboundPaymentLine.outboundPaymentCostAllocations.findIndex(p => p.generatedId == lineDetail.generatedId);

                        if (indexToRemove != -1) {
                            outboundPaymentLine.outboundPaymentCostAllocations.splice(indexToRemove, 1);
                            outboundPaymentCostAllocations = outboundPaymentLine.outboundPaymentCostAllocations;
                            bindLineDetailsGrid();
                        }
                    } else if (result.dismiss === Swal.DismissReason.cancel) {
                    }
                })
            }

            function bindUserDepartment() {

                $("#cmbDepartment").empty();
                $("#cmbDepartment").append(
                    $("<option></option>").val("").html("Select an option")
                );

                $.ajax({
                    url: apiUrl + "Administration/UserDepartments/Assigned/user/" + loggedInUserId,
                    method: "GET",
                    success: function (result) {
                        $.each(result.data, function (index, item) {
                            if (item.isDefault) {
                                userDept = item.departmentId;
                            }
                        });
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    },
                    complete: function () {
                        bindDepartmentList();
                    }
                });
            }

           function bindDepartmentList() {

                $("#cmbDepartment").empty();
                $("#cmbDepartment").append(
                    $("<option></option>").val("").html("Select an option")
                );

                $.ajax({
                    url: apiUrl + "Administration/Departments/ShortList/" + defaultCompanyId,
                    method: "GET",
                    success: function (result) {
                        $.each(result.data, function (index, item) {
                            $("#cmbDepartment").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    },
                    complete: function () {
                        if (userDept > 0) {
                            $("#cmbDepartment").val(userDept).trigger("change");
                        }

                        if (vm.departmentId > 0) {
                            $("#cmbDepartment").val(vm.departmentId).trigger("change");
                        }
                    }
                });
            }

            function bindSupplierList() {
                $.ajax({
                    url: apiUrl + "Procurement/Supplier/ShortList/Company/" + defaultCompanyId,
                    method: "GET",
                    success: function (result) {
                        $.each(result, function (index, item) {
                            $("#cmbSupplier").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });

                        $("#cmbSupplier").val(vm.supplierId || "").trigger("change");
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindPayBookList() {
                // Clear existing options first
                $("#cmbPayBook").empty();
                $("#cmbPayBook").append(
                    $("<option></option>").val("").html("Select an option")
                );

                console.log("=== bindPayBookList Debug ===");
                console.log("defaultCompanyId:", defaultCompanyId);
                console.log("apiUrl:", apiUrl);

                // Check if defaultCompanyId is valid
                if (!defaultCompanyId || isNaN(defaultCompanyId) || defaultCompanyId <= 0) {
                    console.error("Invalid defaultCompanyId:", defaultCompanyId);
                    toastr.error("Invalid company ID. Please refresh the page.");
                    return;
                }

                const fullUrl = apiUrl + `Finance/PayBooks/ShortList/${defaultCompanyId}`;
                console.log("Full API URL:", fullUrl);

                $.ajax({
                    url: fullUrl,
                    method: "GET",
                    timeout: 30000, // 30 second timeout
                    success: function (result) {
                        console.log("Paybook API response:", result);
                        console.log("Response type:", typeof result);
                        console.log("Response length:", result ? result.length : 'null/undefined');

                        payBookList = result || [];

                        if (result && Array.isArray(result) && result.length > 0) {
                            $.each(result, function (index, item) {
                                console.log(`Adding paybook ${index}:`, item);
                                $("#cmbPayBook").append(
                                    $("<option></option>").val(item.id).html(item.description)
                                );
                            });
                            console.log("Successfully loaded", result.length, "paybooks");
                            toastr.success(`Loaded ${result.length} paybooks`);
                        } else {
                            console.warn("No paybook data found or invalid response format");
                            toastr.warning("No paybooks found for this company");
                        }

                        $("#cmbPayBook").val(vm.payBookId || "").change();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        console.error("=== Paybook API Error ===");
                        console.error("Status:", jqXHR.status);
                        console.error("Status Text:", jqXHR.statusText);
                        console.error("Response Text:", jqXHR.responseText);
                        console.error("Error Thrown:", errorThrown);
                        console.error("Ready State:", jqXHR.readyState);

                        let errorMessage = "Failed to load paybooks";
                        if (jqXHR.status === 0) {
                            errorMessage += " - Network error or API not accessible";
                        } else if (jqXHR.status === 404) {
                            errorMessage += " - API endpoint not found";
                        } else if (jqXHR.status === 500) {
                            errorMessage += " - Server error";
                        } else {
                            errorMessage += ` - HTTP ${jqXHR.status}: ${jqXHR.statusText}`;
                        }

                        toastr.error(errorMessage);
                    }
                });
            }

            function bindChequeBookList() {
                $("#cmbChequeBook").empty();
                $("#cmbChequeBook").append(
                    $("<option></option>").val("").html("Select an option")
                );

                let payBookId = parseInt($("#cmbPayBook").val()) || 0;

                if (!payBookId || payBookList.length == 0)
                    return;

                let currentIndex = payBookList.findIndex(pb => pb.id == payBookId);

                if (currentIndex == -1)
                    return;

                let typeEnum = payBookList[currentIndex].typeEnum;

                // cheque book list only bind if pay book type is cheque
                if (typeEnum != 2) {
                    $('#deChequeDate').datepicker('update', null);
                    return;
                }

                $.ajax({
                    url: apiUrl + `Finance/ChequeBooks/ShortList/${payBookId}`,
                    method: "GET",
                    success: function (result) {
                        $.each(result, function (index, item) {
                            $("#cmbChequeBook").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });

                        $("#cmbChequeBook").val(vm.chequeBookId || "").change();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindBankDetails() {
                $("#txtBank").val("");
                $("#txtAccountNo").val("");
                $("#txtBranch").val("");
                $("#txtBankCode").val("");

                let payBookId = $("#cmbPayBook").val();

                if (!payBookId)
                    return;

                $.ajax({
                    url: apiUrl + `Finance/PayBooks/Single/${payBookId}`,
                    method: "GET",
                    success: function (result) {
                        $("#txtBank").val(result.accountName);
                        $("#txtAccountNo").val(result.accountNumber);
                        $("#txtBranch").val(result.bankBranch);
                        $("#txtBankCode").val(result.bankCode);
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindSupplierDetails() {
                // $("#cmbCurrency").val("").trigger("change");

                let supplierId = $("#cmbSupplier").val();

                if (!supplierId)
                    return;
                if (vm.payeeName) {
                    $("#txtPayeeName").val(vm.payeeName);
                    return;
                }

                $.ajax({
                    url: apiUrl + `Procurement/Supplier/${supplierId}`,
                    method: "GET",
                    success: function (result) {
                        $("#txtPayeeName").val(result.data.payeeName);
                        //$("#cmbCurrency").val(result.data.billingCurrencyId || "").trigger("change");
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindCustomerDetails() {
                let customerId = $("#cmbCustomer").val();

                if (!customerId)
                    return;

                if (client != clientsEnum.Sothys)
                    $("#cmbCurrency").val("").change();

                $.ajax({
                    url: apiUrl + `Sales/Customers/Single/${customerId}`,
                    method: "GET",
                    success: function (result) {
                        //$("#cmbCurrency").val(result.billingCurrencyId || "").change();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindCurrencyList() {
                let typeEnum = parseInt($("#cmbType").val());
                $.ajax({
                    url: apiUrl + "Administration/Currency/ShortList",
                    method: "GET",
                    success: function (result) {
                        $.each(result.data, function (index, item) {
                            $("#cmbCurrency").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });

                        $("#cmbCurrency").val(vm.currencyId || "").change();

                        if (!vm.currencyId) {
                            $("#cmbCurrency").attr("disabled", false);
                            if (client == clientsEnum.Sothys) {
                                $("#cmbCurrency").val(baseCurrencyId).trigger("change");
                            }
                            else {
                                let typeEnum = parseInt($("#cmbType").val());

                                if (typeEnum == 2) {
                                    $("#cmbCurrency").attr("disabled", true);
                                    $("#cmbCurrency").val(baseCurrencyId);
                                    $("#cmbCurrency").trigger("change");
                                }
                                else if (typeEnum == 5) {
                                    $("#cmbCurrency").val(baseCurrencyId).change();
                                    $("#cmbCurrency").attr("disabled", true);
                                    $("#cmbCurrency").val(baseCurrencyId);
                                    $("#cmbCurrency").trigger("change");
                                }
                            }
                        }
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindCurrencyDetails() {
                $("#txtExchangeRate").val("");

                let currencyId = $("#cmbCurrency").val();
                let currencyRateDate = getCurrentDate();

                if (!currencyId)
                    return;

                $.ajax({
                    url: apiUrl + `Administration/Currency/Single/${currencyId}`,
                    method: "GET",
                    success: function (result) {
                        if (vm.outboundPaymentId > 0) {
                            $("#txtExchangeRate").val(vm.exchangeRate);
                        }
                        else {
                            if (currencyId == baseCurrencyId) {
                                $("#txtExchangeRate").val("1.00");
                                return;
                            }
                            else {
                                $("#txtExchangeRate").val(result.sellingRate ? result.sellingRate : "");

                                if (result.currencyRateDate < currencyRateDate)
                                    isOldCurrencyRateDate = true;
                            }
                        }


                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindCustomerList() {
                $('#cmbCustomer').select2({
                    ajax: {
                        url: apiUrl + "Sales/Customers/ShortListWithPaging",
                        dataType: 'json',
                        delay: 250, // wait 250 milliseconds before triggering the request
                        data: function (params) {
                            var query = {
                                searchBy: params.term || "",
                                page: params.page || 1,
                                take: 100,
                                companyId: defaultCompanyId
                            }

                            // Query parameters will be ?search=[term]&page=[page]
                            return query;
                        },
                        processResults: function (data) {
                            console.log(data);

                            var d = $.map(data.results, function (obj) {
                                obj.text = obj.text || obj.description; // replace name with the property used for the text

                                return obj;
                            });

                            return {
                                results: data.results,
                                pagination: {
                                    more: data.pagination
                                }
                            };
                        }

                    }
                });

                //Initialize customer Id value
                var customerSelect = $('#cmbCustomer');
                var defaultOption = new Option("Select an option", "", true, true);
                customerSelect.append(defaultOption).trigger('change');

                let customerId = vm.customerId;
                if (customerId) {
                    // Fetch the preselected item, and add to the control

                    $.ajax({
                        type: 'GET',
                        url: apiUrl + "Sales/Customers/Single/" + customerId,
                    }).then(function (data) {
                        // create the option and append to Select2
                        var option = new Option(data.customerCode + " - " + data.customerName, data.customerId, true, true);
                        customerSelect.append(option).trigger('change');

                        // manually trigger the `select2:select` event
                        customerSelect.trigger({
                            type: 'select2:select',
                            params: {
                                data: data
                            }
                        });

                        customerSelect.prop("disabled", true).change();
                    });
                }
            }

            function bindEmployeeList() {
                $.ajax({
                    url: apiUrl + "HR/Employees/ShortList/" + defaultCompanyId,
                    method: "GET",
                    success: function (result) {
                        $.each(result.data, function (index, item) {
                            $("#cmbEmployee").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });

                        $("#cmbEmployee").val(vm.employeeId || "").change();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindAccountList() {
                $.ajax({
                    url: apiUrl + "Finance/Accounts/ReceivingShortList/" + defaultCompanyId,
                    method: "GET",
                    success: function (result) {
                        $.each(result, function (index, item) {
                            $("#cmbAccount").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });

                        $("#cmbAccount").val(vm.accountId || "").change();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindIOUList() {
                $.ajax({
                    url: apiUrl + "Finance/PettyCashes/IOUShortList/" + defaultCompanyId,
                    method: "GET",
                    success: function (result) {
                        $.each(result, function (index, item) {
                            $("#cmbIOU").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });

                        $("#cmbIOU").val(vm.accountId || "").change();
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindAvailableAdvances() {
                // $("#cmbCurrency").val("").trigger("change");

                let supplierId = $("#cmbSupplier").val();

                if (!supplierId)
                    return;

                $.ajax({
                    url: apiUrl + `Finance/OutboundPayments/AvailableAdvances/${supplierId}`,
                    method: "GET",
                    success: function (result) {
                        $("#txtAvailableAdvances").val(result.toFixed(2));
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindBusinessUnitList() {
                $("#cmbBusinessUnit").empty();
                $("#cmbBusinessUnit").append(
                    $("<option></option>").val("").html("Select an option")
                );

                $.ajax({
                    url: apiUrl + "Administration/Departments/ShortList/" + defaultCompanyId,
                    method: "GET",
                    success: function (result) {
                        $.each(result.data, function (index, item) {
                            $("#cmbBusinessUnit").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function bindGRNShipmentList() {
                $("#cmbGRNShipment").empty();
                $("#cmbGRNShipment").append(
                    $("<option></option>").val("").html("Select an option")
                );

                $.ajax({
                    url: apiUrl + "Inventory/GoodsReceivedNotes/ShortList/" + defaultCompanyId,
                    method: "GET",
                    success: function (result) {
                        $.each(result, function (index, item) {
                            $("#cmbGRNShipment").append(
                                $("<option></option>").val(item.id).html(item.description)
                            );
                        });
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        toastr.error(jqXHR.responseText);
                    }
                });
            }

            function saveLine(lineData, fieldValue) {
                lineData.data.settlementDue = lineData.data.balanceAmount.toFixed(4) - (lineData.data.discount ?? 0).toFixed(4);

                if (lineData.column.dataField == "isSettled") {
                    lineData.data.isSettled = fieldValue;
                    if (fieldValue) {
                        if (lineData.data.discount) {
                            lineData.data.settlementDue = lineData.data.balanceAmount.toFixed(4) - lineData.data.discount.toFixed(4);
                        }
                        else {
                            lineData.data.settlementDue = lineData.data.balanceAmount.toFixed(4);
                        }

                        //if (lineData.data.settlementValue > lineData.data.settlementDue) {
                        //    toastr.error("Settlement Value Cannot be greater than the Settlement Due");
                        //    linesGrid.refresh();
                        //    return;
                        //}

                        lineData.data.settlementValue = lineData.data.settlementDue;
                    }
                    else {
                        lineData.data.settlementValue = null;
                    }
                }

                if (lineData.column.dataField == "crossPaidAmount") {
                    if (fieldValue < 0) {
                        toastr.error("Invalid cross paid amount.");
                        linesGrid.refresh();
                        return;
                    }

                    lineData.data.crossPaidAmount = fieldValue;
                }

                if (lineData.column.dataField == "discount") {
                    if (fieldValue < 0) {
                        toastr.error("Invalid discount value.");
                        linesGrid.refresh();
                        return;
                    }

                    if (fieldValue > lineData.data.balanceAmount.toFixed(4)) {
                        toastr.error("Invalid discount value.");
                        linesGrid.refresh();
                        return;
                    }

                    var isSettledValue = lineData.data.isSettled;
                    if (isSettledValue) {
                        lineData.data.settlementDue = lineData.data.balanceAmount.toFixed(4) - fieldValue.toFixed(4);
                        lineData.data.settlementValue = lineData.data.settlementDue.toFixed(4);
                    }
                    //else {
                    //    if (lineData.data.settlementValue > lineData.data.balanceAmount.toFixed(4) - fieldValue.toFixed(4)) {
                    //        linesGrid.refresh();
                    //            toastr.error("Settlement Value Cannot be greater than the Settlement Due");
                    //            return;
                    //    }
                    //}

                    lineData.data.discount = fieldValue;
                }

                if (lineData.column.dataField == "settlementValue") {
                    if (fieldValue < 0) {
                        toastr.error("Invalid settlement value.");
                        linesGrid.refresh();
                        return;
                    }

                    //if (fieldValue > lineData.data.settlementDue) {
                    //    toastr.error("Settlement Value Cannot be greater than the Settlement Due");
                    //    linesGrid.refresh();
                    //    return;
                    //}

                    lineData.data.settlementValue = fieldValue;
                }

                linesGrid.refresh();
                CalculateHeaderValues();
            }

            function saveOutboundPaymentData() {
                let docDate = $("#deDocDate").val();
                let remarks = $("#txtRemarks").val();
                let supplierId = parseInt($("#cmbSupplier").val()) || null;
                let customerId = parseInt($("#cmbCustomer").val()) || null;
                let employeeId = parseInt($("#cmbEmployee").val()) || null;
                let accountId = parseInt($("#cmbAccount").val()) || null;
                let payBookId = parseInt($("#cmbPayBook").val()) || null;
                let chequeBookId = parseInt($("#cmbChequeBook").val()) || null;
                let payeeName = $("#txtPayeeName").val();
                let chequeDate = $("#deChequeDate").val() || null;
                let bankingDate = $("#deBankingDate").val() || null;
                let currencyId = parseInt($("#cmbCurrency").val());
                let exchangeRate = parseFloat($("#txtExchangeRate").val());
                let refNumber = $("#txtRefNumber").val();
                let departmentId = parseInt($("#cmbDepartment").val()) || null;
                let availableAdvances = parseFloat($("#txtAvailableAdvances").val());
                let typeEnum = parseInt($("#cmbType").val());
                let modifiedUserId = loggedInUserId;
                let companyId = defaultCompanyId;

                if (!typeEnum) {
                    toastr.error("Please select a type.");
                    return;
                }
                if (typeEnum == 1 && !supplierId) {
                    toastr.error("Please select a supplier.");
                    return;
                }
                if (typeEnum == 3 && !customerId) {
                    toastr.error("Please select a customer.");
                    return;
                }
                if (typeEnum == 2 && !accountId) {
                    toastr.error("Please select an account.");
                    return;
                }
                if (typeEnum == 5 && !employeeId) {
                    toastr.error("Please select a employee.");
                    return;
                }
                if (typeEnum == 7 && !supplierId) {
                    toastr.error("Please select a supplier.");
                    return;
                }
                if (!docDate) {
                    toastr.error("Please select a document date.");
                    return;
                }
                if (!payBookId) {
                    toastr.error("Please select a pay book.");
                    return;
                }
                let payBookType = payBookList.filter(pb => pb.id == payBookId)[0].typeEnum;
                // if paybook type is cheque
                if (payBookType == 2 && !chequeBookId) {
                    toastr.error("Please select a cheque book.");
                    return;
                }
                /**if (!currencyId) {
                    toastr.error("Please select a currency.");
                    return;
                }
                if (!exchangeRate) {
                    toastr.error("Invalid exchange rate.");
                    return;
                }**/

                let isPostDatedCheque = false;
                isPostDatedCheque = $("#chkPDCheque").is(':checked');

                if (isCheque) {
                    if (payeeName == "") {
                        toastr.error("Please Enter Payee Name");
                        return;
                    }

                    if (bankingDate == null || bankingDate == "") {
                        toastr.error("Please Select a Banking Date");
                        return;
                    }

                    if (chequeDate == null || chequeDate == "") {
                        toastr.error("Please Select a Cheque Date");
                        return;
                    }

                    if (bankingDate < docDate) {
                        toastr.error("Banking Date cannot be less than the Document Date");
                        return;
                    }

                    if (bankingDate < chequeDate) {
                        toastr.error("Banking Date cannot be less than the Cheque Date");
                        return;
                    }
                }

                if (isOldCurrencyRateDate)
                    toastr.error("Exchange rate is not up-to date.");

                let outboundPaymentLines = [];
                let settlementValue = 0;

                vm.outboundPaymentLines.forEach(function (outboundPaymentLine) {
                    let outboundPaymentLineObj = {};

                    if (outboundPaymentLine.settlementValue > 0) {
                        outboundPaymentLineObj.outboundPaymentId = outboundPaymentId;
                        outboundPaymentLineObj.outboundPaymentLineId = outboundPaymentLine.outboundPaymentLineId;
                        outboundPaymentLineObj.paymentAdviceId = outboundPaymentLine.paymentAdviceId == 0 ? null : outboundPaymentLine.paymentAdviceId;
                        outboundPaymentLineObj.generalLedgerLineId = outboundPaymentLine.generalLedgerLineId;
                        outboundPaymentLineObj.balanceAmount = outboundPaymentLine.balanceAmount;
                        outboundPaymentLineObj.settlementValue = parseFloat(outboundPaymentLine.settlementValue);
                        outboundPaymentLineObj.discount = parseFloat(outboundPaymentLine.discount) || 0;
                        outboundPaymentLineObj.documentAmount = outboundPaymentLine.documentAmount;
                        outboundPaymentLineObj.crossPaidAmount = parseFloat(outboundPaymentLine.crossPaidAmount) || null;
                        outboundPaymentLineObj.dueDate = outboundPaymentLine.dueDate || null;
                        outboundPaymentLineObj.amountInBaseCurrency = outboundPaymentLine.amountInBaseCurrency;
                        outboundPaymentLineObj.modifiedUserId = loggedInUserId;
                        outboundPaymentLineObj.outboundPaymentCostAllocations = outboundPaymentLine.outboundPaymentCostAllocations;

                        settlementValue += outboundPaymentLineObj.settlementValue;
                        outboundPaymentLines.push(outboundPaymentLineObj);
                    }
                });

                if (outboundPaymentLines.length == 0) {
                    toastr.error("Please settle at least one payment.");
                    return;
                }

                $("#btnSave").attr("disabled", "disabled");

                let paymentOutboundObj = {
                    outboundPaymentId,
                    docNumber: vm.docNumber || null,
                    companyId,
                    docDate,
                    remarks,
                    //taxGroupId,
                    isPostDatedCheque,
                    typeEnum,
                    supplierId,
                    customerId,
                    employeeId,
                    accountId,
                    payBookId,
                    chequeBookId,
                    exchangeRate,
                    payeeName,
                    settlementValue,
                    chequeDate,
                    bankingDate,
                    currencyId,
                    refNumber,
                    modifiedUserId,
                    createdUserId: modifiedUserId,
                    outboundPaymentLines,
                    departmentId,
                    availableAdvances
                };

                $.ajax({
                    url: apiUrl + "Finance/OutboundPayments",
                    method: "POST",
                    data: JSON.stringify(paymentOutboundObj),
                    contentType: "application/json; charset=utf-8",
                    success: function (result) {
                        swal.fire({
                            "title": "Success",
                            "text": "Record saved!",
                            "type": "success",
                            "confirmButtonClass": "btn btn-secondary",
                            "onClose": function (e) {
                                window.location = "@Url.Action("Index", "OutboundPayments")";
                            }
                        });
                    },
                    error: function (jqXHR, textStatus, errorThrown) {
                        swal.fire({
                            "title": "Error",
                            "text": jqXHR.responseText,
                            "type": "error",
                            "confirmButtonClass": "btn btn-secondary",
                            "onClose": function (e) {
                            }
                        });

                        $("#btnSave").removeAttr("disabled");
                    }
                });
            }

            function saveLineDetailsData() {
                let departmentId = null;
                let goodsReceiveNoteId = null;
                let departmentName = "";
                let goodsReceiveNoteDocNumber = "";

                let generalLedgerLineId = $("#hiddenLineId").val();
                let apportionPct = parseFloat($("#txtApportionPercentage").val());
                let radioCheckEnum = $('#radioCostAllocation1').is(':checked') ? 1 : $('#radioCostAllocation2').is(':checked') ? 2 : null;

                /**if (radioCheckEnum == 1) {
                    departmentId = parseInt($("#cmbBusinessUnit").val());

                    if (isNaN(departmentId)) {
                        toastr.error("Please select a business unit.");
                        return;
                    }

                    departmentName = $("#cmbBusinessUnit option:selected").text();
                }**/
                else if (radioCheckEnum == 2) {
                    goodsReceiveNoteId = parseInt($("#cmbGRNShipment").val());

                    if (isNaN(goodsReceiveNoteId)) {
                        toastr.error("Please select a GRN Shipment.");
                        return;
                    }

                    goodsReceiveNoteDocNumber = $("#cmbGRNShipment option:selected").text();
                }

                if (!apportionPct || apportionPct < 0) {
                    toastr.error("Please enter a valid apportion percentage.");
                    return;
                }

                let outboundPaymentLine = vm.outboundPaymentLines.find(p => p.generalLedgerLineId == generalLedgerLineId);

                //check for duplicate item
                let duplicateItem = false;
                let isDepartment = false;
                let isMultipleTypes = false;

                outboundPaymentLine.outboundPaymentCostAllocations.forEach(function (s) {
                   /** if (s.departmentId > 0 && s.departmentId == departmentId) {
                        duplicateItem = true;
                        isDepartment = true;
                    }**/
                    if (s.goodsReceiveNoteId > 0 && s.goodsReceiveNoteId == goodsReceiveNoteId)
                        duplicateItem = true;

                    /**if ((s.departmentId > 0 && goodsReceiveNoteId > 0) || (s.goodsReceiveNoteId > 0 && departmentId > 0))
                        isMultipleTypes = true;
                });**/

                if (isMultipleTypes) {
                    toastr.error("Allocation can be done only for one type for a line. either Invoice or Business unit");
                    return;
                }

                /**if (duplicateItem && isDepartment) {
                    toastr.error("selected business unit is already added.");
                    return;
                }**/

                if (duplicateItem && !isDepartment) {
                    toastr.error("selected sales invoice is already added.");
                    return;
                }

                //check for apportion percentage
                let apportionPt = 0;

                outboundPaymentLine.outboundPaymentCostAllocations.forEach(function (s) {
                    apportionPt += s.apportionPct;
                });

                if (apportionPt + apportionPct > 100) {
                    toastr.error("apportion percentage can not exceed 100.");
                    return;
                }

                let lineDetail = {
                    generatedId: generateUUID(),
                    departmentId,
                    goodsReceiveNoteId,
                    apportionPct,
                    modifiedUserId: loggedInUserId,
                    goodsReceiveNoteDocNumber,
                    departmentName,
                    outboundPaymentLineId: outboundPaymentLine.outboundPaymentLineId || null
                };

                outboundPaymentLine.outboundPaymentCostAllocations.push(lineDetail);
                outboundPaymentCostAllocations = outboundPaymentLine.outboundPaymentCostAllocations;
                bindLineDetailsGrid();
            }

            function CalculateHeaderValues() {
                let settlementValueTotal = 0;

                $.each(vm.outboundPaymentLines, function (index, item) {

                    if (!isNaN(item.settlementValue) && item.settlementValue > 0) {
                        settlementValueTotal += parseFloat(item.settlementValue);
                    }
                });

                var formattedValue = settlementValueTotal.toFixed(2).replace(/(\d)(?=(\d\d\d)+(?!\d))/g, "$1,");
                $("#txtSettlementValue").val(formattedValue);
            }
        });
    </script>
}
