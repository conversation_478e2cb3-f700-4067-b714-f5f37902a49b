﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesInvoicesController : ControllerBase
    {
        private readonly ISalesInvoiceService _salesInvoiceService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public SalesInvoicesController(ISalesInvoiceService  salesInvoiceService, IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _salesInvoiceService = salesInvoiceService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }

        [HttpGet]
        [Route("Single/{salesInvoiceId}")]
        public async Task<IActionResult> FindById(int salesInvoiceId)
        {
            return Ok(await _salesInvoiceService.FindByIdAsync(salesInvoiceId));
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Type/{typeEnum}")]
        public async Task<IActionResult> GetShortList(int companyId, byte typeEnum)  
        {
            return Ok(await _salesInvoiceService.GetShortListAsync(companyId, typeEnum));
        }

        [HttpGet]
        [Route("SubmittedInvoices/Company/{companyId}/Customer/{customerId}")]
        public async Task<IActionResult> GetSubmittedList(int companyId, int customerId)
        {
            return Ok(await _salesInvoiceService.GetSubmittedList(companyId, customerId));
        }

        [HttpGet]
        [Route("LinkedInvoices/Company/{companyId}/SalesOrder/{salesOrderId}")]
        public async Task<IActionResult> GetLinkedInvoicesList(int companyId, int salesOrderId) 
        {
            return Ok(await _salesInvoiceService.GetLinkedInvoicesListAsync(companyId, salesOrderId));
        }

        [HttpGet]
        [Route("ConfirmedShortList/Company/{companyId}/Type/{typeEnum}")]
        public async Task<IActionResult> GetConfirmedShortList(int companyId, byte typeEnum)  
        {
            return Ok(await _salesInvoiceService.GetConfirmedShortListAsync(companyId, typeEnum));  
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesInvoiceService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("GetAll/Company/{companyId}/StartDate/{startDate}/EndDate/{endDate}")]
        public async Task<IActionResult> GetAllInvoiceLines(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesInvoiceService.GetAllInvoiceLines(companyId, startDate, endDate));
        }

        [HttpGet]
        [Route("GetAllWalkIn/Company/{companyId}/User/{userId}/Department/{departmentId}/StartDate/{startDate}/EndDate/{endDate}")]
        public async Task<IActionResult> GetAllWalkIn(int companyId, int userId, int departmentId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesInvoiceService.GetAllWalkInAsync(companyId, userId, departmentId, startDate, endDate));
        }

        [HttpGet]
        [Route("GetLatestWalkIn/Company/{companyId}/Customer/{customerId}")]
        public async Task<IActionResult> GetLatestWalkIn(int companyId, int customerId)
        {
            return Ok(await _salesInvoiceService.GetLatestWalkIn(companyId, customerId));
        }

        [HttpGet]
        [Route("GetSubmittedWalkIn/Company/{companyId}")]
        public async Task<IActionResult> GetSubmittedWalkInLines(int companyId)
        {
            return Ok(await _salesInvoiceService.GetSubmittedWalkInLines(companyId));
        }

        [HttpGet]
        [Route("GetLatestServiceInvoices/Company/{companyId}/Customer/{customerId}")]
        public async Task<IActionResult> GetLatestServiceInvoicess(int companyId, int customerId)
        {
            return Ok(await _salesInvoiceService.GetLatestServiceInvoices(companyId, customerId));
        }

        [HttpGet]
        [Route("GetAllConsignmentInvoices/Company/{companyId}/User/{userId}/StartDate/{startDate}/EndDate/{endDate}")]
        public async Task<IActionResult> GetAllConsignmentInvoices(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesInvoiceService.GetAllConsignmentInvoices(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("SettlementDocuments/{serviceInvoiceId}/DocumentType/{txnDocumentType}")]
        public async Task<IActionResult> GetSettlemetDocumentsAsync(int serviceInvoiceId, byte txnDocumentType)
        {
            return Ok(await _salesInvoiceService.GetSettlementDocumentsAsync(serviceInvoiceId, txnDocumentType));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]SalesInvoice salesInvoice)
        {
            switch (salesInvoice.Action)
            {
                case "new": return Ok(await _salesInvoiceService.AddNewSalesInvoiceAsync(salesInvoice, false));
                case "generate": return Ok(await _salesInvoiceService.GenerateSalesInvoiceAsync(salesInvoice));
                case "save": await _salesInvoiceService.SaveAsync(salesInvoice);break;
                case "save walk In": await _salesInvoiceService.SaveWalkInAsync(salesInvoice, false); break;
                case "save consignment": await _salesInvoiceService.SaveConsignmentAsync(salesInvoice); break;
                case "save walk In - free issue": await _salesInvoiceService.SaveWalkInAsync(salesInvoice, true); break;
                case "new - free issue": return Ok(await _salesInvoiceService.AddNewSalesInvoiceAsync(salesInvoice, true));
            }

            return Ok();
        }

        [HttpPost]
        [Route("CalculateFreeIssues")]
        public async Task<IActionResult> CalculateFreeIssues([FromBody]SalesInvoice salesInvoice)
        {
            var freeIssues = await _salesInvoiceService.CalculateFreeIssuesAsync(salesInvoice);

            return Ok(freeIssues);
        }

        [HttpPost]
        [Route("CalculateDiscounts")]
        public async Task<IActionResult> CalculateDiscounts([FromBody] SalesInvoice salesInvoice)
        {
            var record = await _salesInvoiceService.CalculateDiscountsAsync(salesInvoice);

            return Ok(record);
        }

        [HttpPost]
        [Route("CalculateDocumentDiscount")]
        public async Task<IActionResult> CalculateDocumentDiscount([FromBody]SalesInvoice salesInvoice)
        {
            var record = await _salesInvoiceService.CalculateDocumentDiscountAsync(salesInvoice);

            return Ok(record);
        }

        [HttpDelete]
        [Route("{salesInvoiceId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int salesInvoiceId, int userId)
        {
            await _salesInvoiceService.CancelAsync(salesInvoiceId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesInvoice salesInvoice)
        {
            switch (salesInvoice.Action)
            {
                case "submit": await _salesInvoiceService.SubmitAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value); break;
                case "submit walk in": await _salesInvoiceService.SubmitWalkInInvoiceAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value); break;
                case "submit consignment": await _salesInvoiceService.SubmitConsignmentInvoiceAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value); break;
                case "reverse": await _salesInvoiceService.ReverseAsync(salesInvoice); break;
                case "reverse walk in": await _salesInvoiceService.ReverseWalkInInvoiceAsync(salesInvoice); break;
                case "reverse consignment": await _salesInvoiceService.ReverseConsignmentAsync(salesInvoice); break;
                case "savedetails": await _salesInvoiceService.SaveCourier(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value,salesInvoice.CourierName,salesInvoice.CourierReferenceNo,salesInvoice.DestinationReceivedDate, salesInvoice.VesselName); break;
                case "convertToGDN": return Ok(await _goodsDispatchNoteService.ConvertAsync(salesInvoice.SalesInvoiceId, Domain.Entities.Administration.Module.DocumentType.SalesInvoice, salesInvoice.ModifiedUserId));
            }

            return Ok();
        }
    }
}