﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class ServiceInquiriesController : ControllerBase
    {
        private readonly IServiceInquiryService _serviceInquiryService;

        public ServiceInquiriesController(IServiceInquiryService  serviceInquiryService)
        {
            _serviceInquiryService = serviceInquiryService;
        }

        [HttpGet]
        [Route("Single/{serviceInquiryId}")]
        public async Task<IActionResult> FindById(int serviceInquiryId)
        {
            return Ok(await _serviceInquiryService.FindByIdAsync(serviceInquiryId));
        }

        [HttpGet]
        [Route("ShortList/Customer/{customerId}/Company/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId, int customerId)
        {
            return Ok(await _serviceInquiryService.GetShortListAsync(companyId, customerId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _serviceInquiryService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]ServiceInquiry serviceInquiry)
        {
            switch (serviceInquiry.Action)
            {
                case "save": await _serviceInquiryService.SaveAsync(serviceInquiry); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{serviceInquiryId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int serviceInquiryId, int userId)
        {
            await _serviceInquiryService.CancelAsync(serviceInquiryId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]ServiceInquiry serviceInquiry)
        {
            switch (serviceInquiry.Action)
            {
                case "submit": await _serviceInquiryService.SubmitAsync(serviceInquiry.ServiceInquiryId, serviceInquiry.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}