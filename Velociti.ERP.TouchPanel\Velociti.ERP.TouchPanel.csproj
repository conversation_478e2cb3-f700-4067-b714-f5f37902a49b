﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{1319936C-C53D-4163-B483-67D219EC6042}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <RootNamespace>Velociti.ERP.TouchPanel.FinishedGoodsQC</RootNamespace>
    <AssemblyName>Velociti_ERP_TS</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <Deterministic>true</Deterministic>
    <TargetFrameworkProfile />
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>\\172.20.2.5\sandbox\TouchScreen\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Unc</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>true</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <MinimumRequiredVersion>1.1.1.1</MinimumRequiredVersion>
    <CreateWebPageOnPublish>true</CreateWebPageOnPublish>
    <WebPage>publish.htm</WebPage>
    <ApplicationRevision>53</ApplicationRevision>
    <ApplicationVersion>1.1.1.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>7004E16B8BEBDB4DAC2C4058968B7021D75444AC</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>Velociti.ERP.TouchPanel_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="ClosedXML, Version=0.102.2.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b, processorArchitecture=MSIL">
      <HintPath>..\packages\ClosedXML.0.102.2\lib\netstandard2.0\ClosedXML.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Charts.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.CodeParser.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Drawing.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.DataAccess.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Office.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.PivotGrid.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL">
    </Reference>
    <Reference Include="DevExpress.RichEdit.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Sparkline.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.Desktop.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Utils.v25.1.UI, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraBars.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraEditors.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraGauges.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraPrinting.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v25.1.Extensions, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DocumentFormat.OpenXml, Version=2.16.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.16.0\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=4.5.3.2, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.4.5.3.2\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="ExcelNumberFormat, Version=1.1.0.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelNumberFormat.1.1.0\lib\net20\ExcelNumberFormat.dll</HintPath>
    </Reference>
    <Reference Include="Irony, Version=1.0.11.0, Culture=neutral, PublicKeyToken=ca48ace7223ead47, processorArchitecture=MSIL">
      <HintPath>..\packages\Irony.NetCore.1.0.11\lib\net461\Irony.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=3.0.4.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.3.0.4\lib\net462\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.4\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.4\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=4.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.2.2\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=4.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.2.2\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=4.2.2.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.4.2.2\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=3.0.1.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.0.1\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="SixLabors.Fonts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.Fonts.1.0.0\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.configuration" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data.SqlClient, Version=4.6.1.6, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SqlClient.4.8.6\lib\net461\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Packaging.6.0.0\lib\net461\System.IO.Packaging.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.4\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=4.0.6.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.4.7.0\lib\netstandard2.0\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=4.1.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=4.1.0.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.3.1\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
    <Reference Include="XLParser, Version=1.5.2.0, Culture=neutral, PublicKeyToken=63397e1e46bb91b4, processorArchitecture=MSIL">
      <HintPath>..\packages\XLParser.1.5.2\lib\net461\XLParser.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BarcodePrintForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="BarcodePrintForm.Designer.cs">
      <DependentUpon>BarcodePrintForm.cs</DependentUpon>
    </Compile>
    <Compile Include="CompoundBatchStickerPrintTouchScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CompoundBatchStickerPrintTouchScreen.Designer.cs">
      <DependentUpon>CompoundBatchStickerPrintTouchScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="CompoundMachine.cs" />
    <Compile Include="CompoundProductionProcessTouchScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CompoundProductionProcessTouchScreen.Designer.cs">
      <DependentUpon>CompoundProductionProcessTouchScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="CompoundProductionProcessTouchScreenII.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CompoundProductionProcessTouchScreenII.Designer.cs">
      <DependentUpon>CompoundProductionProcessTouchScreenII.cs</DependentUpon>
    </Compile>
    <Compile Include="DTOs\IntegerListDTO.cs" />
    <Compile Include="DTOs\MachineDTO.cs" />
    <Compile Include="DTOs\MachineProductComponentDTO.cs" />
    <Compile Include="DTOs\MachineWarehouseStockTransferDTO.cs" />
    <Compile Include="DTOs\ProductionOrderLineActualStartDTO.cs" />
    <Compile Include="DTOs\ProductionOrderLineDetailDTO.cs" />
    <Compile Include="DTOs\ProductionOrderLineQtyDTO.cs" />
    <Compile Include="DTOs\ProductionOrderUpdateDTO.cs" />
    <Compile Include="DTOs\ProfileBuildingDTO.cs" />
    <Compile Include="DTOs\ProfileBuildingProductionOrderDTO.cs" />
    <Compile Include="DTOs\ProfileBuildingValidateSFGStockDTO.cs" />
    <Compile Include="DTOs\ProfileMachineDTO.cs" />
    <Compile Include="DTOs\ScrapProductionOrderDTO.cs" />
    <Compile Include="DTOs\WarehouseProductDTO.cs" />
    <Compile Include="DTOs\WarehouseProductSequenceDTO.cs" />
    <Compile Include="DTOs\WarehouseToMachineDTO.cs" />
    <Compile Include="LogService.cs" />
    <Compile Include="MainForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MainForm.Designer.cs">
      <DependentUpon>MainForm.cs</DependentUpon>
    </Compile>
    <Compile Include="NumPad.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="NumPad.Designer.cs">
      <DependentUpon>NumPad.cs</DependentUpon>
    </Compile>
    <Compile Include="DailyPlan.cs" />
    <Compile Include="DataRepository.cs" />
    <Compile Include="DTOs\CompoundProductionConfirmDTO.cs" />
    <Compile Include="DTOs\ProductionOrderDTO.cs" />
    <Compile Include="DTOs\QualityControlStatus02DTO.cs" />
    <Compile Include="DTOs\SemiFinishedGoodsProductionPlanDTO.cs" />
    <Compile Include="DTOs\WarehouseDTO.cs" />
    <Compile Include="DTOs\WarehousePalletDTO.cs" />
    <Compile Include="FinishedGoodsQCTouchScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FinishedGoodsQCTouchScreen.Designer.cs">
      <DependentUpon>FinishedGoodsQCTouchScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="CompoundIssuingTouchScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="CompoundIssuingTouchScreen.Designer.cs">
      <DependentUpon>CompoundIssuingTouchScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="FinishedGoodsQCTouchScreen2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FinishedGoodsQCTouchScreen2.Designer.cs">
      <DependentUpon>FinishedGoodsQCTouchScreen2.cs</DependentUpon>
    </Compile>
    <Compile Include="FinishedTyreBarcodePrintingTouchScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FinishedTyreBarcodePrintingTouchScreen.Designer.cs">
      <DependentUpon>FinishedTyreBarcodePrintingTouchScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="OtherMachineTouchScreenI.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OtherMachineTouchScreenI.Designer.cs">
      <DependentUpon>OtherMachineTouchScreenI.cs</DependentUpon>
    </Compile>
    <Compile Include="OtherMachineTouchScreenII.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OtherMachineTouchScreenII.Designer.cs">
      <DependentUpon>OtherMachineTouchScreenII.cs</DependentUpon>
    </Compile>
    <Compile Include="OtherMachineTouchScreenII_S2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OtherMachineTouchScreenII_S2.Designer.cs">
      <DependentUpon>OtherMachineTouchScreenII_S2.cs</DependentUpon>
    </Compile>
    <Compile Include="OtherMachineTouchScreenI_BreakTyre.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OtherMachineTouchScreenI_BreakTyre.Designer.cs">
      <DependentUpon>OtherMachineTouchScreenI_BreakTyre.cs</DependentUpon>
    </Compile>
    <Compile Include="OtherMachineTouchScreenI_S2.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="OtherMachineTouchScreenI_S2.Designer.cs">
      <DependentUpon>OtherMachineTouchScreenI_S2.cs</DependentUpon>
    </Compile>
    <Compile Include="PasswordBox.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PasswordBox.Designer.cs">
      <DependentUpon>PasswordBox.cs</DependentUpon>
    </Compile>
    <Compile Include="PressLine.cs" />
    <Compile Include="PressMachine.cs" />
    <Compile Include="PressMachineBarcodeScan.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PressMachineBarcodeScan.Designer.cs">
      <DependentUpon>PressMachineBarcodeScan.cs</DependentUpon>
    </Compile>
    <Compile Include="Product.cs" />
    <Compile Include="ProfileBuildingTouchScreen.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProfileBuildingTouchScreen.Designer.cs">
      <DependentUpon>ProfileBuildingTouchScreen.cs</DependentUpon>
    </Compile>
    <Compile Include="ProfileBuildingTouchScreenII.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProfileBuildingTouchScreenII.Designer.cs">
      <DependentUpon>ProfileBuildingTouchScreenII.cs</DependentUpon>
    </Compile>
    <Compile Include="ProfileBuildingTouchScreenIII.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProfileBuildingTouchScreenIII.Designer.cs">
      <DependentUpon>ProfileBuildingTouchScreenIII.cs</DependentUpon>
    </Compile>
    <Compile Include="ProfileBuildingTouchScreenIV.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProfileBuildingTouchScreenIV.Designer.cs">
      <DependentUpon>ProfileBuildingTouchScreenIV.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="PleaseWaitForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="PleaseWaitForm.Designer.cs">
      <DependentUpon>PleaseWaitForm.cs</DependentUpon>
    </Compile>
    <Compile Include="ProgressBarForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="ProgressBarForm.Designer.cs">
      <DependentUpon>ProgressBarForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QCReason.cs" />
    <Compile Include="QCReasonForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="QCReasonForm.Designer.cs">
      <DependentUpon>QCReasonForm.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\BarcodeLabelMultipleBasicReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\BarcodeLabelMultipleReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\BarcodeLabelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\PalletBarcodeLabelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="CompoundControl.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="CompoundControl.Designer.cs">
      <DependentUpon>CompoundControl.cs</DependentUpon>
    </Compile>
    <Compile Include="Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="TestForm.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="TestForm.Designer.cs">
      <DependentUpon>TestForm.cs</DependentUpon>
    </Compile>
    <Compile Include="DTOs\QualityControlStatusDTO.cs" />
    <Compile Include="WebService.cs" />
    <EmbeddedResource Include="BarcodePrintForm.resx">
      <DependentUpon>BarcodePrintForm.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="CompoundBatchStickerPrintTouchScreen.resx">
      <DependentUpon>CompoundBatchStickerPrintTouchScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CompoundProductionProcessTouchScreen.resx">
      <DependentUpon>CompoundProductionProcessTouchScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CompoundProductionProcessTouchScreenII.resx">
      <DependentUpon>CompoundProductionProcessTouchScreenII.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="MainForm.resx">
      <DependentUpon>MainForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="NumPad.resx">
      <DependentUpon>NumPad.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FinishedGoodsQCTouchScreen.resx">
      <DependentUpon>FinishedGoodsQCTouchScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CompoundIssuingTouchScreen.resx">
      <DependentUpon>CompoundIssuingTouchScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FinishedGoodsQCTouchScreen2.resx">
      <DependentUpon>FinishedGoodsQCTouchScreen2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="FinishedTyreBarcodePrintingTouchScreen.resx">
      <DependentUpon>FinishedTyreBarcodePrintingTouchScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="OtherMachineTouchScreenI.resx">
      <DependentUpon>OtherMachineTouchScreenI.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="OtherMachineTouchScreenII.resx">
      <DependentUpon>OtherMachineTouchScreenII.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="OtherMachineTouchScreenII_S2.resx">
      <DependentUpon>OtherMachineTouchScreenII_S2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="OtherMachineTouchScreenI_BreakTyre.resx">
      <DependentUpon>OtherMachineTouchScreenI_BreakTyre.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="OtherMachineTouchScreenI_S2.resx">
      <DependentUpon>OtherMachineTouchScreenI_S2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PasswordBox.resx">
      <DependentUpon>PasswordBox.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PleaseWaitForm.resx">
      <DependentUpon>PleaseWaitForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="PressMachineBarcodeScan.resx">
      <DependentUpon>PressMachineBarcodeScan.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProfileBuildingTouchScreen.resx">
      <DependentUpon>ProfileBuildingTouchScreen.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProfileBuildingTouchScreenII.resx">
      <DependentUpon>ProfileBuildingTouchScreenII.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProfileBuildingTouchScreenIII.resx">
      <DependentUpon>ProfileBuildingTouchScreenIII.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProfileBuildingTouchScreenIV.resx">
      <DependentUpon>ProfileBuildingTouchScreenIV.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="ProgressBarForm.resx">
      <DependentUpon>ProgressBarForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="QCReasonForm.resx">
      <DependentUpon>QCReasonForm.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="CompoundControl.resx">
      <DependentUpon>CompoundControl.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BarcodeLabelMultipleBasicReport.resx">
      <DependentUpon>BarcodeLabelMultipleBasicReport.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BarcodeLabelMultipleReport.resx">
      <DependentUpon>BarcodeLabelMultipleReport.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\BarcodeLabelReport.resx">
      <DependentUpon>BarcodeLabelReport.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\PalletBarcodeLabelReport.resx">
      <DependentUpon>PalletBarcodeLabelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="TestForm.resx">
      <DependentUpon>TestForm.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="packages.config" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
    <None Include="Scripts\jquery-3.7.1.min.map" />
    <None Include="Scripts\jquery-3.7.1.slim.min.map" />
    <None Include="Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <None Include="Velociti.ERP.TouchPanel_TemporaryKey.pfx" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.6.1">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.6.1 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <Content Include="Scripts\jquery-3.7.1.intellisense.js" />
    <Content Include="Scripts\jquery-3.7.1.js" />
    <Content Include="Scripts\jquery-3.7.1.min.js" />
    <Content Include="Scripts\jquery-3.7.1.slim.js" />
    <Content Include="Scripts\jquery-3.7.1.slim.min.js" />
    <Content Include="Scripts\jquery.validate-vsdoc.js" />
    <Content Include="Scripts\jquery.validate.js" />
    <Content Include="Scripts\jquery.validate.min.js" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>