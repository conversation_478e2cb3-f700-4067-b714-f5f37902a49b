﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("LoadingPlans", Schema = "sales")]
    public partial class LoadingPlan
    {
        public LoadingPlan()
        {
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            LoadingPlanLines = new HashSet<LoadingPlanLine>();
        }

        [Key]
        public int LoadingPlanId { get; set; }
        public int CompanyId { get; set; }
        public int? SalesInvoiceId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public int? CustomerId { get; set; }
        public int? VersionNo { get; set; }
        public byte? StatusEnum { get; set; }
        public int? AccountId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? PlannedShipmentDate { get; set; }
        public byte? ShippingModeEnum { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? GrossWeight { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? ParentId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("LoadingPlans")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("LoadingPlans")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("LoadingPlans")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(SalesInvoiceId))]
        [InverseProperty("LoadingPlans")]
        public virtual SalesInvoice SalesInvoice { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.LoadingPlan))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(LoadingPlanLine.LoadingPlan))]
        public virtual ICollection<LoadingPlanLine> LoadingPlanLines { get; set; }
    }
}
