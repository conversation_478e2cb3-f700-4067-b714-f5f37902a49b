﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class UserDivisionsController : ControllerBase
    {
        private readonly IUserDivisionService _userDivisionService;

        public UserDivisionsController(IUserDivisionService userDivisionService)
        {
            _userDivisionService = userDivisionService;
        }

        [HttpGet]
        [Route("Assigned/user/{userId}")]
        public async Task<IActionResult> GetAssignedDivisions(int userId)
        {
            return Ok(await _userDivisionService.GetAssignedDivisionsAsync(userId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]IEnumerable<UserDivision> userDivisions)
        {
            if (userDivisions == null)
                return BadRequest();

            await _userDivisionService.SaveAsync(userDivisions);

            return Ok();
        }

        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> ToggleDefault(int id)
        {
            await _userDivisionService.ToggleDefaultAsync(id);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivationAsync(int id)
        {
            await _userDivisionService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}