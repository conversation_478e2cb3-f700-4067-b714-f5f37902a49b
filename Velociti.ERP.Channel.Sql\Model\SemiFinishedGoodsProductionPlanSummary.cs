﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SemiFinishedGoodsProductionPlanSummary", Schema = "man")]
    public partial class SemiFinishedGoodsProductionPlanSummary
    {
        public SemiFinishedGoodsProductionPlanSummary()
        {
            SemiFinishedGoodsProductionPlans = new HashSet<SemiFinishedGoodsProductionPlan>();
        }

        [Key]
        public int SemiFinishedGoodsProductionPlanSummaryId { get; set; }
        public int? CompanyId { get; set; }
        public byte? TypeEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDateTime { get; set; }
        public byte? StatusEnum { get; set; }
        public int? ProductId { get; set; }
        public int? RequiredQuantity { get; set; }
        public int? ProductionQuantity { get; set; }
        public int? ProductionBatchCount { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column("BOMLevel")]
        public byte? Bomlevel { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SemiFinishedGoodsProductionPlanSummaries")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SemiFinishedGoodsProductionPlanSummaries")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsProductionPlan.SemiFinishedGoodsProductionPlanSummary))]
        public virtual ICollection<SemiFinishedGoodsProductionPlan> SemiFinishedGoodsProductionPlans { get; set; }
    }
}
