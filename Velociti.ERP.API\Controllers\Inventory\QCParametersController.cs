﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class QCParametersController : ControllerBase
    {
        private readonly IQCParameterService _qCParameterService;

        public QCParametersController(IQCParameterService qCParameterService)
        {
            _qCParameterService = qCParameterService;
        }

        [HttpGet]
        [Route("Parameter/{parameterId}")]
        public async Task<IActionResult> GetById(int parameterId)
        {
            return Ok(await _qCParameterService.FindByIdAsync(parameterId));
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> Get(int companyId)
        {
            return Ok(await _qCParameterService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _qCParameterService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpDelete]
        [Route("{parameterId}/User/{modifiedUserId}")]
        public async Task<IActionResult> ToggleActivation(int parameterId, int modifiedUserId)
        {
            await _qCParameterService.ToggleActivationAsync(parameterId, modifiedUserId);

            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Qcparameter qcparameter)
        {
            if (qcparameter == null)
                return BadRequest();

            await _qCParameterService.SaveAsync(qcparameter);

            return Ok();
        }
    }
}