﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PettyCashLines", Schema = "fin")]
    public partial class PettyCashLine
    {
        public PettyCashLine()
        {
            PettyCashCostAllocations = new HashSet<PettyCashCostAllocation>();
        }

        [Key]
        public int PettyCashLineId { get; set; }
        public int? PettyCashId { get; set; }
        public int? ChartOfAccountId { get; set; }
        [StringLength(255)]
        public string Narration { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }
        [Column(TypeName = "money")]
        public decimal? BalanceAmount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChartOfAccountId))]
        [InverseProperty("PettyCashLines")]
        public virtual ChartOfAccount ChartOfAccount { get; set; }
        [ForeignKey(nameof(PettyCashId))]
        [InverseProperty("PettyCashLines")]
        public virtual PettyCash PettyCash { get; set; }
        [InverseProperty(nameof(PettyCashCostAllocation.PettyCashLine))]
        public virtual ICollection<PettyCashCostAllocation> PettyCashCostAllocations { get; set; }
    }
}
