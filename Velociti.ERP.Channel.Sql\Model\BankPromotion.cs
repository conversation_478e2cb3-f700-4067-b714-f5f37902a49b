﻿using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Promotions", Schema = "dbo")]
    public class BankPromotion
    {
        [Key]
        public int PromotionId { get; set; }
        public bool Active { get; set; }
        [StringLength(255)]
        public string CardBank { get; set; }
        [StringLength(255)]
        public string CardType { get; set; }
        public decimal Discount { get; set; }
        
        public DateTime FromDate { get; set; }
        
        public DateTime FromTime { get; set; }
        [StringLength(255)]
        public string PromotionCode { get; set; }
        public DateTime ToDate { get; set; }
        public DateTime ToTime { get; set; }
        public decimal MaxDiscValue{ get; set; }
    }
}