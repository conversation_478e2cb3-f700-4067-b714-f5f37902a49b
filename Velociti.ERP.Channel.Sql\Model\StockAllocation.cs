﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockAllocations", Schema = "inv")]
    public partial class StockAllocation
    {
        [Key]
        public int StockAllocationId { get; set; }
        public int? CompanyId { get; set; }
        public int? WarehouseId { get; set; }
        public int? WarehouseProductId { get; set; }
        public int? TxnLineId { get; set; }
        public int? ProductId { get; set; }
        public byte? DocTypeEnum { get; set; }
        public long? AllocatedQuantity { get; set; }
        public long? OnHandQuantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("StockAllocations")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(WarehouseProductId))]
        [InverseProperty("StockAllocations")]
        public virtual WarehouseProduct WarehouseProduct { get; set; }
    }
}
