﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("CustomerContacts", Schema = "sales")]
    public partial class CustomerContact
    {
        [Key]
        public int CustomerId { get; set; }
        [Key]
        public int ContactId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ContactId))]
        [InverseProperty("CustomerContacts")]
        public virtual Contact Contact { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("CustomerContacts")]
        public virtual Customer Customer { get; set; }
    }
}
