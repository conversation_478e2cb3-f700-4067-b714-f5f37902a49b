﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ShipmentQualityControlLineReturnsController : ControllerBase
    {  
        private readonly IShipmentQualityControlReturnLineService _shipmentQualityControlReturnLineService;

        public ShipmentQualityControlLineReturnsController(IShipmentQualityControlReturnLineService shipmentQualityControlReturnLineService)
        {
            _shipmentQualityControlReturnLineService = shipmentQualityControlReturnLineService;
        }

        [HttpGet]
        [Route("{shipmentQualityControlReturnId}/company/{companyId}")]
        public async Task<IActionResult> Get(int shipmentQualityControlReturnId, int companyId)
        {
            return Ok(await _shipmentQualityControlReturnLineService.GetAsync(shipmentQualityControlReturnId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ShipmentQualityControlReturnLine shipmentQualityControlReturnLine)
        {
            await _shipmentQualityControlReturnLineService.SaveAsync(shipmentQualityControlReturnLine);  

            return Ok();
        }

        [HttpPost]
        [Route("List")]
        public async Task<IActionResult> SaveList([FromBody]List<ShipmentQualityControlReturnLine> shipmentQualityControlReturnLines)
        {
            await _shipmentQualityControlReturnLineService.SaveListAsync(shipmentQualityControlReturnLines);

            return Ok();
        }
    }
}