﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance  
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class CurrencyExchangeRatesController : ControllerBase  
    {
        private readonly ICurrencyExchangeRateService _currencyExchangeRateService;  

        public CurrencyExchangeRatesController(ICurrencyExchangeRateService currencyExchangeRateService)    
        {
            _currencyExchangeRateService = currencyExchangeRateService;
        }

        [HttpGet]
        public async Task<ActionResult<Response<IEnumerable<CurrencyExchangeRate>>>> Get()
        {
            return Ok(await _currencyExchangeRateService.GetAllAsync());
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]CurrencyExchangeRate currencyExchangeRate)  
        {
            if (currencyExchangeRate == null)
                return BadRequest();

            await _currencyExchangeRateService.SaveAsync(currencyExchangeRate);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivation(int id)
        {
            await _currencyExchangeRateService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}