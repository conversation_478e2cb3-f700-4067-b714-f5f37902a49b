﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class GroupRoleRepository : IGroupRoleRepository
    {
        private readonly MarangoniERPContext _context;

        public GroupRoleRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<GroupRole>> GetAllAsync()
        {
            return await _context.GroupRoles.Include(p=> p.Group).Include(p=> p.Role).ToListAsync();
        }

        public async Task SaveAsync(GroupRole groupRole)
        {
            var duplciateRecord = await _context.GroupRoles.FirstOrDefaultAsync(p => p.GroupId == groupRole.GroupId && p.RoleId == groupRole.RoleId);
            if (duplciateRecord == null)
            {
                await _context.GroupRoles.AddAsync(groupRole);
                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteAsync(GroupRole groupRole)
        {
            var record = await _context.GroupRoles.FirstAsync(c => c.GroupId == groupRole.GroupId && c.RoleId == groupRole.GroupId);

            _context.GroupRoles.Remove(record);
            await _context.SaveChangesAsync();
        }
    }
}
