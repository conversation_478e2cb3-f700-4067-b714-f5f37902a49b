﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class MouldProductsController : ControllerBase  
    {
        private readonly IMouldProductService _mouldProductService;

        public MouldProductsController(IMouldProductService mouldProductService)
        {
            _mouldProductService = mouldProductService;
        }

        [HttpGet]
        public async Task<ActionResult<Response<IEnumerable<MouldProduct>>>> Get(int companyId, int mouldId)
        {
            return Ok(await _mouldProductService.GetAllAsync(companyId, mouldId));
        }

        [HttpPost]
        [Route("mould/{mouldId}/product/{productId}/loggedInUser/{loggedInUserId}")]
        public async Task<IActionResult> ToggleActivation(int mouldId, int productId, int loggedInUserId)
        {
            await _mouldProductService.ToggleActivationAsync(mouldId, productId, loggedInUserId);

            return Ok();
        }
    }
}