﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using Velociti.ERP.Channel.Sql.Model;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class NotificationRepository : INotificationRepository
    {
        private readonly MarangoniERPContext _context;

        public NotificationRepository(MarangoniERPContext context)
        {
            _context = context;
        }
        public async Task<IEnumerable<Notification>> GetNotificationByUserIdAsync(int userId)
        {
            return await _context.Notifications.Where(p => p.AssignedUserId == userId && p.ViewedDateTime == null).ToListAsync();
        }

        public async Task UpdateNotificationAsync(int notificationId, int modifiedUserId, DateTime viewedDatetime)
        {
            var record = await _context.Notifications.FirstAsync(c => c.NotificationId == notificationId);

            record.ViewedDateTime = viewedDatetime;
            record.ModifiedUserId = modifiedUserId;

            await _context.SaveChangesAsync();

        }
        public async Task UpdateAllNotificatonAsync(int modifiedUserId, DateTime viewedDatetime)
        {
            var records = await _context.Notifications.Where(c => c.AssignedUserId == modifiedUserId).ToListAsync();

            foreach (var item in records)
            {
                item.ViewedDateTime = viewedDatetime;
                item.ModifiedUserId = modifiedUserId;
            }
            await _context.SaveChangesAsync();

        }
    }
}
