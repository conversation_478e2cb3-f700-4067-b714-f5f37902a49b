﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>netcoreapp3.1</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<ProjectReference Include="..\Velociti.ERP.Channel.Sql\Velociti.ERP.Channel.Sql.csproj" />
		<ProjectReference Include="..\Velociti.ERP.Domain\Velociti.ERP.Domain.csproj" />
	</ItemGroup>

	<ItemGroup>
		<Folder Include="Repositories\" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="ClosedXML" Version="0.102.2" />
		<PackageReference Include="DevExpress.Xpo" Version="25.1.3" />
		<PackageReference Include="EPPlus" Version="4.5.3.2" />
		<PackageReference Include="log4net" Version="3.0.4" />
		<PackageReference Include="Microsoft.AspNet.Identity.Owin" Version="2.2.4" />
		<PackageReference Include="Microsoft.Owin" Version="4.2.2" />
		<PackageReference Include="Microsoft.Owin.Security" Version="4.2.2" />
		<PackageReference Include="Microsoft.Owin.Security.Cookies" Version="4.2.2" />
		<PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
		<PackageReference Include="SixLabors.Fonts" Version="1.0.0" />
		<PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
		<PackageReference Include="System.Runtime" Version="4.3.1" />
	</ItemGroup>

</Project>