﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Common;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration  
{
    public class EmailTemplatesRepository : IEmailTemplatesRepository 
    {
        private readonly MarangoniERPContext _context;
        private readonly IAuditLogRepository _auditLogRepository;

        public EmailTemplatesRepository(MarangoniERPContext context, IAuditLogRepository auditLogRepository)  
        {
            _context = context;
            _auditLogRepository = auditLogRepository;
        }
        public async Task<EmailTemplate> FindByIdAsync(int id)
        {
            return await _context.EmailTemplates.FirstOrDefaultAsync(c => c.EmailTemplateId == id);

        }

        public async Task<IEnumerable<Domain.Entities.Administration.EmailTemplate>> GetAllAsync(int companyId)  
        {
            var result = from a in _context.EmailTemplates
                         where a.CompanyId == companyId
                         select new Domain.Entities.Administration.EmailTemplate
                         {
                             EmailTemplateId = a.EmailTemplateId,
                             CompanyId = a.CompanyId,
                             TemplateCode = a.TemplateCode,
                             TemplateName = a.TemplateName,
                             Subject = a.Subject,
                             Body = a.Body,
                             CreationDate = a.CreationDate,
                             ExpiryDate = a.ExpiryDate
                         };
            return await result.ToListAsync();
        }

        public async Task<EmailTemplate> FindByCodeAsync(int companyId, string code)
        {
            return await _context.EmailTemplates.FirstOrDefaultAsync(c => c.CompanyId == companyId && c.TemplateCode == code);
        }

        public async Task SaveAsync(EmailTemplate emailTemplate)  
        {
            try
            {
                if (emailTemplate.EmailTemplateId == default)
                {
                    emailTemplate.CreationDate = DateTime.Now;
                    await _context.EmailTemplates.AddAsync(emailTemplate);


                }
                else
                {
                    emailTemplate.CreationDate = default;
                    _context.EmailTemplates.Attach(emailTemplate);
                    _context.Entry(emailTemplate).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {

                throw ex;
            }

        }

        public async Task ToggleActivationAsync(int id, int loggedInUserId)
        {
            EmailTemplate emailTemplate = await _context.EmailTemplates.FirstAsync(c => c.EmailTemplateId == id);

            emailTemplate.ExpiryDate = emailTemplate.ExpiryDate == null ? DateTime.Now : (DateTime?)null;
            emailTemplate.ModifiedUserId = loggedInUserId;

            await _context.SaveChangesAsync();
        }

    }
}
