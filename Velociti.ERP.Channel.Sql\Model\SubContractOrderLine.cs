﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SubContractOrderLines", Schema = "prc")]
    public partial class SubContractOrderLine
    {
        [Key]
        public int SubContractOrderLineId { get; set; }
        public int? SubContractOrderId { get; set; }
        public int? SalesOrderLineId { get; set; }
        public int? ProductId { get; set; }
        public int? BillOfMaterialId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public byte? TypeEnum { get; set; }
        public int? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ExpectedDeliveryDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DueDate { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.SubContractOrderLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(BillOfMaterialId))]
        [InverseProperty("SubContractOrderLines")]
        public virtual BillOfMaterial BillOfMaterial { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.SubContractOrderLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SubContractOrderLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SalesOrderLineId))]
        [InverseProperty("SubContractOrderLines")]
        public virtual SalesOrderLine SalesOrderLine { get; set; }
        [ForeignKey(nameof(SubContractOrderId))]
        [InverseProperty("SubContractOrderLines")]
        public virtual SubContractOrder SubContractOrder { get; set; }
    }
}
