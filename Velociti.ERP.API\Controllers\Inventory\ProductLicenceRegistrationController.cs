﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductLicenceRegistrationController : ControllerBase
    {
        private readonly IProductLicenceRegistrationService _productLicenceRegistrationService;

        public ProductLicenceRegistrationController(IProductLicenceRegistrationService productLicenceRegistrationService)
        {
            _productLicenceRegistrationService = productLicenceRegistrationService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> Get(int companyId)
        {
            return Ok(await _productLicenceRegistrationService.GetAllAsync(companyId));
        }
        
        [HttpGet]
        [Route("ProductLicence/{productId}")]
        public async Task<IActionResult> GetProductLicenceByProductId(int productId)
        {
            return Ok(await _productLicenceRegistrationService.GetProductLicenceByProductIdAsync(productId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ProductLicenceRegistration productLicenceRegistration)
        {
            if (productLicenceRegistration == null)
                return BadRequest();

            await _productLicenceRegistrationService.SaveAsync(productLicenceRegistration);

            return Ok();
        }
    }
}