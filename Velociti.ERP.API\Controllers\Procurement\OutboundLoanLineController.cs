﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class OutboundLoanLineController : ControllerBase
    {
        private readonly IOutboundLoanLineService outBoundLoanLineService;
        public OutboundLoanLineController(IOutboundLoanLineService outBoundLoanLineService)
        {
            this.outBoundLoanLineService = outBoundLoanLineService;
        }
        [HttpGet]
        [Route("{loanOrderId}/Company/{companyId}")]
        public async Task<IActionResult> GetAll(int loanOrderId, int companyId)
        {
            return Ok(await this.outBoundLoanLineService.GetByHeaderIdAsync(companyId, loanOrderId));
        }
    }
}