﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Moulds", Schema = "man")]
    public partial class Mould
    {
        public Mould()
        {
            MachineMoulds = new HashSet<MachineMould>();
            MouldCavities = new HashSet<MouldCavity>();
            MouldProducts = new HashSet<MouldProduct>();
        }

        [Key]
        public int MouldId { get; set; }
        [StringLength(50)]
        public string MouldCode { get; set; }
        public int? CompanyId { get; set; }
        public int? CategoryId { get; set; }
        [StringLength(150)]
        public string MouldMeasure { get; set; }
        [StringLength(50)]
        public string Measurement { get; set; }
        [StringLength(50)]
        public string RimSize { get; set; }
        [StringLength(255)]
        public string Brand { get; set; }
        [StringLength(255)]
        public string Fix { get; set; }
        public byte? CavityCount { get; set; }
        public int? YearOfConstruction { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CategoryId))]
        [InverseProperty(nameof(SupportData.Moulds))]
        public virtual SupportData Category { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Moulds")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(MachineMould.Mould))]
        public virtual ICollection<MachineMould> MachineMoulds { get; set; }
        [InverseProperty(nameof(MouldCavity.Mould))]
        public virtual ICollection<MouldCavity> MouldCavities { get; set; }
        [InverseProperty(nameof(MouldProduct.Mould))]
        public virtual ICollection<MouldProduct> MouldProducts { get; set; }
    }
}
