﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]  
    public class InternalOrderLinesController : ControllerBase
    {
        private readonly IInternalOrderLineService _internalOrderLineService;  

        public InternalOrderLinesController(IInternalOrderLineService internalOrderLineService)      
        {
            _internalOrderLineService = internalOrderLineService;
        }

        [HttpGet]
        [Route("{internalOrderId}/company/{companyId}")]
        public async Task<IActionResult> Get(int internalOrderId, int companyId)
        {
            return Ok(await _internalOrderLineService.GetAsync(internalOrderId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> GetInternalOrderLine([FromBody]InternalOrderLine internalOrderLine)
        {
            return Ok(await _internalOrderLineService.AddInternalOrderLineAsync(internalOrderLine));
        }

        [HttpPut("Update")]
        public async Task<IActionResult> UpdateInternalOrderLine([FromBody] InternalOrderLine internalOrderLine)
        {
            if (internalOrderLine == null || internalOrderLine.InternalOrderLineId <= 0)
                return BadRequest("Invalid internal order line data.");

            var updatedLine = await _internalOrderLineService.UpdateInternalOrderLineAsync(internalOrderLine);

            if (updatedLine == null)
                return NotFound("InternalOrderLine not found.");

            return Ok(updatedLine);
        }
    }
}