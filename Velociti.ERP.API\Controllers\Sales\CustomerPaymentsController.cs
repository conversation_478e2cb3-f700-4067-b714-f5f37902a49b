﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class CustomerPaymentsController : ControllerBase
    {
        private readonly ICustomerPaymentsService _customerPaymentsService;

        public CustomerPaymentsController(ICustomerPaymentsService customerPaymentsService)
        {
            _customerPaymentsService = customerPaymentsService;
        }

        [HttpGet]
        [Route("Single/{salesOrderId}")]
        public async Task<IActionResult> FindById(int salesOrderId)
        {
            return Ok(await _customerPaymentsService.FindByIdAsync(salesOrderId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _customerPaymentsService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("{customerPaymentId}/Customer/{customerId}/Currency/{currencyId}/Company/{companyId}/Type/{type}")]
        public async Task<IActionResult> GetCustomerPaymentVoucherLines(int customerPaymentId, int customerId, int currencyId, int companyId, int type)
        {
            return Ok(await _customerPaymentsService.GetCustomerPaymentVoucherLinesAsync(customerPaymentId, customerId, currencyId, companyId, type));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]CustomerPayment customerPayment)
        {
            switch (customerPayment.Action)
            {
                case "save": await _customerPaymentsService.SaveAsync(customerPayment); break;
            }

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]CustomerPayment customerPayment)
        {
            switch (customerPayment.Action)
            {
                case "submit": await _customerPaymentsService.SubmitAsync(customerPayment); break;
                case "reverse": await _customerPaymentsService.ReverseAsync(customerPayment); break;
            }


            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> CancelAsync([FromBody]CustomerPayment customerPayment)
        {
            await _customerPaymentsService.CancelAsync(customerPayment);

            return Ok();
        }

        [HttpGet]
        [Route("Vouchers/{customerPaymentId}")]
        public async Task<IActionResult> GetVouchersByCustomerPaymentIdAsync(int customerPaymentId)
        {
            return Ok(await _customerPaymentsService.GetVouchersByCustomerPaymentIdAsync(customerPaymentId));
        }
    }
}