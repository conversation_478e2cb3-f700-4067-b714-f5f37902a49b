﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MachineMoulds", Schema = "man")]
    public partial class MachineMould
    {
        [Key]
        public int MachineMouldId { get; set; }
        public int MachineId { get; set; }
        public int? MouldId { get; set; }
        public byte? DayLightNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(MachineId))]
        [InverseProperty("MachineMoulds")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(MouldId))]
        [InverseProperty("MachineMoulds")]
        public virtual Mould Mould { get; set; }
    }
}
