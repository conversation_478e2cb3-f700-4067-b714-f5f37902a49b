﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ScrapPenaltyDeductionRates", Schema = "hr")]
    public partial class ScrapPenaltyDeductionRate
    {
        [Key]
        public int ScrapPenaltyDeductionRatesId { get; set; }
        public int? LowerLimit { get; set; }
        public int? UpperLimit { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? DeductionRate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
    }
}
