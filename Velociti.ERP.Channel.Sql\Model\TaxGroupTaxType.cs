﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("TaxGroupTaxTypes", Schema = "fin")]
    public partial class TaxGroupTaxType
    {
        [Key]
        public int TaxGroupTaxTypeId { get; set; }
        public int? TaxGroupId { get; set; }
        public int? TaxTypeId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("TaxGroupTaxTypes")]
        public virtual TaxGroup TaxGroup { get; set; }
        [ForeignKey(nameof(TaxTypeId))]
        [InverseProperty("TaxGroupTaxTypes")]
        public virtual TaxType TaxType { get; set; }
    }
}
