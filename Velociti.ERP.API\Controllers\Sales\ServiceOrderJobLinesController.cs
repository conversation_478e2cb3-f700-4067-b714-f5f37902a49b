﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class ServiceOrderJobLinesController : ControllerBase
    {
        private readonly ISalesServiceOrderJobLineService _salesServiceOrderJobLineService;

        public ServiceOrderJobLinesController(ISalesServiceOrderJobLineService salesServiceOrderJobLineService)
        {
            _salesServiceOrderJobLineService = salesServiceOrderJobLineService;
        }

        [HttpGet]
        [Route("{salesServiceOrderJobId}")]
        public async Task<IActionResult> Get(int salesServiceOrderJobId)
        {
            return Ok(await _salesServiceOrderJobLineService.GetAsync(salesServiceOrderJobId));
        }

        [HttpGet]
        [Route("SalesServiceOrderJobId/{salesServiceOrderJobId}/Division/{divisionId}/CompanyId/{companyId}")]
        public async Task<IActionResult> Get(int salesServiceOrderJobId, int divisionId, int companyId)
        {
            return Ok(await _salesServiceOrderJobLineService.GetAsync(salesServiceOrderJobId, divisionId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> GetSalesServiceOrderJobLines([FromBody]SalesServiceOrderJobLine salesServiceOrderJobLine)
        {
            return Ok(await _salesServiceOrderJobLineService.AddSalesServiceOrderJobLineAsync(salesServiceOrderJobLine));
        }
    }
}