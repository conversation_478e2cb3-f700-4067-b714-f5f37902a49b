﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("AccountMappings", Schema = "fin")]
    public partial class AccountMapping
    {
        public AccountMapping()
        {
            AccountMappingLines = new HashSet<AccountMappingLine>();
        }

        [Key]
        public int AccountMappingId { get; set; }
        public int? CompanyId { get; set; }
        public byte? TransactionDocTypeEnum { get; set; }
        [StringLength(255)]
        public string TransactionDocTypeName { get; set; }
        public byte? SourceDocTypeEnum { get; set; }
        public byte? SourceDocSubTypeEnum { get; set; }
        [Required]
        [StringLength(255)]
        public string SourceDocName { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("AccountMappings")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(AccountMappingLine.AccountMapping))]
        public virtual ICollection<AccountMappingLine> AccountMappingLines { get; set; }
    }
}
