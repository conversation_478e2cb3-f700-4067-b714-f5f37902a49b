{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "ConnectionStrings": {
    "DefaultConnection": "Server=DESKTOP-NRUK56G\\SQLEXPRESS01;Database=NemsuERPNew;User Id=***;Password=***;MultipleActiveResultSets=true;TrustServerCertificate=true;Encrypt=false"
  },
  "AppSettings": {
    "Secret": "a-very-long-random-string-at-least-32-characters-long",
    "TokenExpiresIn": "24:00:00",
    "RefreshTokenExpiresIn": "24:00:00",
    "Client": "Nemsui"
    //"TransImagesFolderPath": "D:\\Personal\\SandBoxZone\\velociti***\\Web\\assets\\transRefDocs",
    //"TransImagesViewFolderPath": "/assets/transRefDocs",
    //"TransImagesArchiveFolderPath": "D:\\Personal\\SandBoxZone\\velociti***\\Web\\assets\\archiveDocs",
    //"CompanyLogoImagesFolderPath": "D:\\Personal\\SandBoxZone\\velociti***\\Web\\assets\\companyLogo",
    //"CompanyLogoImagesViewFolderPath": "/assets/companyLogo",
    //"CompanyLogoArchiveFolderPath": "D:\\Personal\\SandBoxZone\\velociti***\\Web\\assets\\archiveDocs\\companyLogo"
  },
  "AllowedHosts": "*",
  "NlogConnection": {
    "DbProvider": "NAVITSA",
    "DbHost": "localhost",
    "Database": "newNemsuErp",
    "User": "root",
    "Password": "root"
  }
}
