﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class EmailTemplatesController : ControllerBase  
    {
        private readonly IEmailTemplateService _emailTemplateService;

        public EmailTemplatesController(IEmailTemplateService emailTemplateService)
        {
            _emailTemplateService = emailTemplateService;
        }

        [HttpGet]
        [Route("company/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<EmailTemplate>>>> Get(int companyId)
        {
            return Ok(await _emailTemplateService.GetAllAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]EmailTemplate emailTemplate)  
        {
            if (emailTemplate == null)
                return BadRequest();

            await _emailTemplateService.SaveAsync(emailTemplate);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/loggedInUser/{loggedInUserId}")]
        public async Task<IActionResult> ToggleActivation(int id, int loggedInUserId)
        {
            await _emailTemplateService.ToggleActivationAsync(id, loggedInUserId);

            return Ok();
        }
    }
}