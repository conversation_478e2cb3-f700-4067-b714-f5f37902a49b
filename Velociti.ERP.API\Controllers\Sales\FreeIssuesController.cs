﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class FreeIssuesController : ControllerBase
    {
        private readonly IFreeIssuesService _freeIssuesService;

        public FreeIssuesController(IFreeIssuesService freeIssuesService)
        {
            _freeIssuesService = freeIssuesService;
        }

        [HttpGet]
        [Route("Single/{freeIssueId}")]
        public async Task<IActionResult> FindById(int freeIssueId)
        {
            return Ok(await _freeIssuesService.FindByIdAsync(freeIssueId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _freeIssuesService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("AllActive")]
        public async Task<IActionResult> GetAllActive(int companyId, int userId)
        {
            return Ok(await _freeIssuesService.GetAllActiveAsync(companyId, userId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]FreeIssue freeIssue)
        {
            switch (freeIssue.Action)
            {
                case "save": await _freeIssuesService.SaveAsync(freeIssue); break;
            }

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]FreeIssue freeIssue)
        {
            switch (freeIssue.Action)
            {
                case "submit": await _freeIssuesService.SubmitAsync(freeIssue.FreeIssueId, freeIssue.ModifiedUserId.Value); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{freeIssueId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int freeIssueId, int userId)
        {
            await _freeIssuesService.CancelAsync(freeIssueId, userId);

            return Ok();
        }
    }
}