﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FreeIssueCriteriaLines", Schema = "sales")]
    public partial class FreeIssueCriteriaLine
    {
        [Key]
        public int FreeIssueCriteriaLineId { get; set; }
        public int FreeIssueId { get; set; }
        public int? ProductCategoryId { get; set; }
        public int? ProductHierarchyId2 { get; set; }
        public int? ProductBrandId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal BreakQuantity { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.FreeIssueCriteriaLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.FreeIssueCriteriaLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(FreeIssueId))]
        [InverseProperty("FreeIssueCriteriaLines")]
        public virtual FreeIssue FreeIssue { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("FreeIssueCriteriaLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductBrandId))]
        [InverseProperty(nameof(SupportData.FreeIssueCriteriaLines))]
        public virtual SupportData ProductBrand { get; set; }
        [ForeignKey(nameof(ProductHierarchyId2))]
        [InverseProperty(nameof(ProductHierarchy.FreeIssueCriteriaLines))]
        public virtual ProductHierarchy ProductHierarchyId2Navigation { get; set; }
    }
}
