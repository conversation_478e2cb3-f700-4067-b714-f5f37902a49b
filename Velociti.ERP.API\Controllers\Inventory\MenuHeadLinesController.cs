﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Threading.Tasks;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Channel.Sql.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
 
    public class MenuHeadLinesController : ControllerBase
    {
        private readonly IMenuHeadLineService _menuHeadLineService;

        public MenuHeadLinesController(IMenuHeadLineService menuHeadLineService)
        {
            _menuHeadLineService = menuHeadLineService;
        }

        [HttpGet]
        [Route("{menuHeadLineId}")]
        public async Task<IActionResult> Get(int menuHeadLineId)
        {
            return Ok(await _menuHeadLineService.GetAsync(menuHeadLineId));
        }

        [HttpGet]
        [Route("FindById/{menuItemId}")]
        public async Task<IActionResult> FindById(int menuItemId)
        {
            return Ok(await _menuHeadLineService.FindById(menuItemId));
        }

        [HttpPost]
        public async Task<IActionResult> GetBillOfMaterialLine([FromBody] MenuHeadLines menuHeadLines)
        {
            return Ok(await _menuHeadLineService.AddMenuHeadLinesAsync(menuHeadLines));
        }
    }
}
