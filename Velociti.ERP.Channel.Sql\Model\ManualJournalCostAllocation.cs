﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ManualJournalCostAllocations", Schema = "fin")]
    public partial class ManualJournalCostAllocation
    {
        [Key]
        public int ManualJournalCostAllocationId { get; set; }
        public int? ManualJournalLineId { get; set; }
        public int? DepartmentId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ApportionPct { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("ManualJournalCostAllocations")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("ManualJournalCostAllocations")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
        [ForeignKey(nameof(ManualJournalLineId))]
        [InverseProperty("ManualJournalCostAllocations")]
        public virtual ManualJournalLine ManualJournalLine { get; set; }
    }
}
