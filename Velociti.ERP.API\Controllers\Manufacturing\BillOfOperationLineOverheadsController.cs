﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class BillOfOperationLineOverheadsController : ControllerBase    
    {
        private readonly IBillOfOperationLineOverheadService _billOfOperationLineOverheadService;    

        public BillOfOperationLineOverheadsController(IBillOfOperationLineOverheadService billOfOperationLineOverheadService)    
        {
            _billOfOperationLineOverheadService = billOfOperationLineOverheadService;
        }

        [HttpGet]
        [Route("{billOfOperationLineId}")]
        public async Task<IActionResult> Get(int billOfOperationLineId)  
        {
            return Ok(await _billOfOperationLineOverheadService.GetByBillOfOperationLineIdAsync(billOfOperationLineId));      
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]BillOfOperationLineOverhead billOfOperationLineOverhead)    
        {
            if (billOfOperationLineOverhead == null)
                return BadRequest();

            await _billOfOperationLineOverheadService.SaveAsync(billOfOperationLineOverhead);

            return Ok();
        }

        [HttpDelete]
        [Route("{billOfOperationLineId}/Overhead/{overheadId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int billOfOperationLineId, int overheadId, int userId)
        {
            await _billOfOperationLineOverheadService.ToggleActivationAsync(billOfOperationLineId, overheadId, userId);

            return Ok();
        }
    }
}