﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SupplierContacts", Schema = "prc")]
    public partial class SupplierContact
    {
        [Key]
        public int SupplierId { get; set; }
        [Key]
        public int ContactId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ContactId))]
        [InverseProperty("SupplierContacts")]
        public virtual Contact Contact { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("SupplierContacts")]
        public virtual Supplier Supplier { get; set; }
    }
}
