﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using System.Linq;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class UserCompanyRepository : IUserCompanyRepository
    {
        private readonly MarangoniERPContext _context;

        public UserCompanyRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<UserCompany> GetAsync(int userId, int companyId)
        {
            return await _context.UserCompanies.Where(p => p.UserId == userId && p.CompanyId == companyId).FirstOrDefaultAsync();
        }

        public async Task<UserCompany> GetDefaultCompanyAsync(int userId)
        {
            return await _context.UserCompanies.Include(p=> p.Company)
                                               .Include(p => p.Designation)
                                               .Where(p => p.UserId == userId && p.IsDefault == true).FirstAsync();
        }

        public async Task<IEnumerable<UserCompany>> GetAssignedCompaniesAsync(int userId)
        {
            return await _context.UserCompanies.Include(p => p.Company).Include(p => p.Designation).Where(p => p.UserId == userId).ToListAsync();
        }

        public async Task<int> SaveAsync(UserCompany userCompany)
        {
            try
            {
                //Check if default company changed
                if(userCompany.IsDefault.HasValue && userCompany.IsDefault.Value)
                {
                    var defaultCompany = await _context.UserCompanies.Where(p => p.UserCompanyId != userCompany.UserCompanyId && p.UserId == userCompany.UserId && p.IsDefault == true).SingleOrDefaultAsync();
                    if(defaultCompany != null)
                    {
                        defaultCompany.IsDefault = false;
                        defaultCompany.ModifiedUserId = userCompany.ModifiedUserId;
                    }

                    await _context.SaveChangesAsync();
                }

                if (userCompany.UserCompanyId == default)
                {
                    userCompany.CreationDate = DateTime.Now;
                    await _context.UserCompanies.AddAsync(userCompany);
                }
                else
                {
                    _context.UserCompanies.Attach(userCompany);
                    _context.Entry(userCompany).State = EntityState.Modified;
                }

                await _context.SaveChangesAsync();

                return userCompany.UserCompanyId;
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        public async Task ToggleDefaultAsync(int userCompanyId)
        {
            var record = await _context.UserCompanies.FirstAsync(c => c.UserCompanyId == userCompanyId);
            record.IsDefault = !record.IsDefault;

            if (record.IsDefault == true)
            {
                var defaultCompany = await _context.UserCompanies.Where(p => p.UserId == record.UserId && p.IsDefault == true).SingleOrDefaultAsync();
                if (defaultCompany != null)
                {
                    defaultCompany.IsDefault = false;
                }
            }

            await _context.SaveChangesAsync();
        }

        public async Task ToggleActivationAsync(int userCompanyId)
        {
            var record = await _context.UserCompanies.FirstAsync(c => c.UserCompanyId == userCompanyId);
            if (record.ExpiryDate == null && record.IsDefault == true)
                throw new Exception("Cannot deactivate default record.");

            record.ExpiryDate = record.ExpiryDate == null ? DateTime.Now : (DateTime?)null;

            await _context.SaveChangesAsync();
        }
    }
}
