﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;


namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class UserCompaniesController : ControllerBase
    {
        private readonly IUserCompanyService _userCompanyService;

        public UserCompaniesController(IUserCompanyService userCompanyService)
        {
            _userCompanyService = userCompanyService;
        }

        [HttpGet]
        [Route("user/{userId}/company/{companyId}")]
        public async Task<IActionResult> Get(int userId, int companyId)
        {
            return Ok(await _userCompanyService.GetAsync(userId, companyId));
        }

        [HttpGet]
        [Route("assigned/user/{userId}")]
        public async Task<ActionResult<Response<IEnumerable<UserCompany>>>> GetAssignedCompanies(int userId)
        {
            return Ok(await _userCompanyService.GetAssignedCompaniesAsync(userId));
        }

        [HttpGet]
        [Route("shortList/user/{userId}")]
        public async Task<IActionResult> GetAssignedCompanyShortList(int userId)
        {
            return Ok(await _userCompanyService.GetAssignedCompaniesShortListAsync(userId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]UserCompany userCompany)
        {
            if (userCompany == null)
                return BadRequest();

            var userCompanyId = await _userCompanyService.SaveAsync(userCompany);

            return Ok(userCompanyId);
        }

        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> ToggleDefault(int id)
        {
            await _userCompanyService.ToggleDefaultAsync(id);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivationAsync(int id)
        {
            await _userCompanyService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}