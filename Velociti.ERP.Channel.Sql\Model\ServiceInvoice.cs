﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ServiceInvoices", Schema = "prc")]
    public partial class ServiceInvoice
    {
        public ServiceInvoice()
        {
            FixedAssetMaintenances = new HashSet<FixedAssetMaintenance>();
            ServiceInvoiceLines = new HashSet<ServiceInvoiceLine>();
            ServiceInvoiceSalesInvoices = new HashSet<ServiceInvoiceSalesInvoice>();
        }

        [Key]
        public int ServiceInvoiceId { get; set; }
        public int CompanyId { get; set; }
        public int? ServiceOrderId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        public int? EmployeeId { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public int? SupplierId { get; set; }
        [StringLength(50)]
        public string SupplierInvoiceNumber { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        public int? PaymentTermId { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ServiceInvoices")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("ServiceInvoices")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("ServiceInvoices")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("ServiceInvoices")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("ServiceInvoices")]
        public virtual Supplier Supplier { get; set; }
        [InverseProperty(nameof(FixedAssetMaintenance.ServiceInvoice))]
        public virtual ICollection<FixedAssetMaintenance> FixedAssetMaintenances { get; set; }
        [InverseProperty(nameof(ServiceInvoiceLine.ServiceInvoice))]
        public virtual ICollection<ServiceInvoiceLine> ServiceInvoiceLines { get; set; }
        [InverseProperty(nameof(ServiceInvoiceSalesInvoice.ServiceInvoice))]
        public virtual ICollection<ServiceInvoiceSalesInvoice> ServiceInvoiceSalesInvoices { get; set; }
    }
}
