﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("EmployeeContacts", Schema = "hr")]
    public partial class EmployeeContact
    {
        [Key]
        public int EmployeeId { get; set; }
        [Key]
        public int ContactId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ContactId))]
        [InverseProperty("EmployeeContacts")]
        public virtual Contact Contact { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("EmployeeContacts")]
        public virtual Employee Employee { get; set; }
    }
}
