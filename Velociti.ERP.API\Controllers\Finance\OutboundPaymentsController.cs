﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Options;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Administration;
using Velociti.ERP.Domain.Services.Finance;
using Velociti.ERP.Domain.Utils;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class OutboundPaymentsController : ControllerBase
    {
        private readonly IAppSettingsService _appSettingsService;
        private readonly IOutboundPaymentService _outboundPaymentService;
        private readonly AppSettings _appSettings;

        public OutboundPaymentsController(IOptions<AppSettings> appSettings, IOutboundPaymentService outboundPaymentService, IAppSettingsService appSettingsService)
        {
            _appSettings = appSettings.Value;
            _appSettingsService = appSettingsService;
            _outboundPaymentService = outboundPaymentService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            var appSettings = await _appSettingsService.GetAllAsync(companyId);
            if (appSettings.EffectiveDate.HasValue && appSettings.EffectiveDate < DateTime.Now)
                return BadRequest();

            return Ok(await _outboundPaymentService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("Single/{outboundPaymentId}")]
        public async Task<IActionResult> GetById(int outboundPaymentId)
        {
            return Ok(await _outboundPaymentService.GetByIdAsync(outboundPaymentId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]OutboundPayment outboundPayment)
        {
            await _outboundPaymentService.SaveAsync(outboundPayment);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]OutboundPayment outboundPayment)
        {
            switch (outboundPayment.Action)
            {
                case "submit":
                    await _outboundPaymentService.SubmitAsync(outboundPayment);
                    break;
                case "cancel":
                    await _outboundPaymentService.CancelAsync(outboundPayment);
                    break;
                case "chequeReturn":
                    await _outboundPaymentService.ChequeReturnAsync(outboundPayment);
                    break;
                case "reverse":
                    await _outboundPaymentService.ReverseAsync(outboundPayment.OutboundPaymentId, outboundPayment.ModifiedUserId.Value);
                    break;
            }

            return Ok();
        }

        [HttpPut]
        [Route("{outboundPaymentId}/ChequeNo/{chequeNumber}/User/{modifiedUserId}")]
        public async Task<IActionResult> UpdateChequeNo(int outboundPaymentId, int chequeNumber, int modifiedUserId)
        {
            await _outboundPaymentService.UpdateChequeNoAsync(outboundPaymentId, chequeNumber, modifiedUserId);

            return Ok();
        }

        [HttpPut]
        [Route("BulkSubmit")]
        public async Task<IActionResult> BulkSubmit([FromBody]ViewForm viewForm)
        {
            await _outboundPaymentService.BulkSubmitAsync(viewForm.IntegerList, viewForm.ParameterOne, viewForm.ParameterTwo);

            return Ok();
        }

        [HttpGet]
        [Route("AvailableAdvances/{supplierId}")]
        public async Task<ActionResult> GetAvailableAdvancesBySupplier(int supplierId)  
        {
            return Ok(await _outboundPaymentService.GetAvailableAdvancesBySupplierAsync(supplierId));
        }
    }
}