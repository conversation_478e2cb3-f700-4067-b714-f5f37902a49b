﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("TaxGroups", Schema = "fin")]
    public partial class TaxGroup
    {
        public TaxGroup()
        {
            BankAdjustmentLines = new HashSet<BankAdjustmentLine>();
            CostingWarehous = new HashSet<CostingWarehous>();
            CustomerPayments = new HashSet<CustomerPayment>();
            GoodsDispatchNoteLines = new HashSet<GoodsDispatchNoteLine>();
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            GoodsReceiveNoteLines = new HashSet<GoodsReceiveNoteLine>();
            InboundReceipts = new HashSet<InboundReceipt>();
            OutboundPayments = new HashSet<OutboundPayment>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            PurchaseOrderAdditionalChargeLines = new HashSet<PurchaseOrderAdditionalChargeLine>();
            PurchaseOrderLines = new HashSet<PurchaseOrderLine>();
            PurchaseReturnLines = new HashSet<PurchaseReturnLine>();
            QuotationLines = new HashSet<QuotationLine>();
            SalesOrderLines = new HashSet<SalesOrderLine>();
            SalesReturnLines = new HashSet<SalesReturnLine>();
            SalesServiceOrderLines = new HashSet<SalesServiceOrderLine>();
            ServiceInvoiceLines = new HashSet<ServiceInvoiceLine>();
            ShipmentQualityControlReturnLines = new HashSet<ShipmentQualityControlReturnLine>();
            TaxGroupTaxTypes = new HashSet<TaxGroupTaxType>();
        }

        [Key]
        public int TaxGroupId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string TaxGroupCode { get; set; }
        [StringLength(255)]
        public string TaxGroupName { get; set; }
        public int? TaxTypeId { get; set; }
        public byte? CategoryEnum { get; set; }
        [StringLength(255)]
        public string Formula { get; set; }
        [Column("IsSVAT")]
        public bool? IsSvat { get; set; }
        public bool? IsDefault { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("TaxGroups")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(TaxTypeId))]
        [InverseProperty("TaxGroups")]
        public virtual TaxType TaxType { get; set; }
        [InverseProperty(nameof(BankAdjustmentLine.TaxGroup))]
        public virtual ICollection<BankAdjustmentLine> BankAdjustmentLines { get; set; }
        [InverseProperty("TaxGroup")]
        public virtual ICollection<CostingWarehous> CostingWarehous { get; set; }
        [InverseProperty(nameof(CustomerPayment.TaxGroup))]
        public virtual ICollection<CustomerPayment> CustomerPayments { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLine.TaxGroup))]
        public virtual ICollection<GoodsDispatchNoteLine> GoodsDispatchNoteLines { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.TaxGroup))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(GoodsReceiveNoteLine.TaxGroup))]
        public virtual ICollection<GoodsReceiveNoteLine> GoodsReceiveNoteLines { get; set; }
        [InverseProperty(nameof(InboundReceipt.TaxGroup))]
        public virtual ICollection<InboundReceipt> InboundReceipts { get; set; }
        [InverseProperty(nameof(OutboundPayment.TaxGroup))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
        [InverseProperty(nameof(PaymentAdvice.TaxGroup))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(PurchaseOrderAdditionalChargeLine.TaxGroup))]
        public virtual ICollection<PurchaseOrderAdditionalChargeLine> PurchaseOrderAdditionalChargeLines { get; set; }
        [InverseProperty(nameof(PurchaseOrderLine.TaxGroup))]
        public virtual ICollection<PurchaseOrderLine> PurchaseOrderLines { get; set; }
        [InverseProperty(nameof(PurchaseReturnLine.TaxGroup))]
        public virtual ICollection<PurchaseReturnLine> PurchaseReturnLines { get; set; }
        [InverseProperty(nameof(QuotationLine.TaxGroup))]
        public virtual ICollection<QuotationLine> QuotationLines { get; set; }
        [InverseProperty(nameof(SalesOrderLine.TaxGroup))]
        public virtual ICollection<SalesOrderLine> SalesOrderLines { get; set; }
        [InverseProperty(nameof(SalesReturnLine.TaxGroup))]
        public virtual ICollection<SalesReturnLine> SalesReturnLines { get; set; }
        [InverseProperty(nameof(SalesServiceOrderLine.TaxGroup))]
        public virtual ICollection<SalesServiceOrderLine> SalesServiceOrderLines { get; set; }
        [InverseProperty(nameof(ServiceInvoiceLine.TaxGroup))]
        public virtual ICollection<ServiceInvoiceLine> ServiceInvoiceLines { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturnLine.TaxGroup))]
        public virtual ICollection<ShipmentQualityControlReturnLine> ShipmentQualityControlReturnLines { get; set; }
        [InverseProperty(nameof(TaxGroupTaxType.TaxGroup))]
        public virtual ICollection<TaxGroupTaxType> TaxGroupTaxTypes { get; set; }
    }
}
