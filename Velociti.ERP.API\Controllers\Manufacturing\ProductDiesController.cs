﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductDiesController : ControllerBase
    {
        private readonly IProductDieService _productDieService;

        public ProductDiesController(IProductDieService productDieService)
        {
            _productDieService = productDieService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            var list = await _productDieService.GetAllAsync(companyId);

            return Ok(list);
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]ProductDy productDy)
        {
            await _productDieService.SaveAsync(productDy);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _productDieService.DeleteAsync(id);

            return Ok();
        }
    }
}