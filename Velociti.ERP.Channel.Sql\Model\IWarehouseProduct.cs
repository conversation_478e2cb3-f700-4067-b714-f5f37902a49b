﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_WarehouseProducts", Schema = "inv")]
    public partial class IWarehouseProduct
    {
        [StringLength(50)]
        public string ProductCode { get; set; }
        [StringLength(100)]
        public string ProductName { get; set; }
        [StringLength(50)]
        public string WarehouseName { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int UnitOfMeasureId { get; set; }
        public int? AllocatedQuantity { get; set; }
        public int? OnHandQuantity { get; set; }
        public int? PhysicalQuantity { get; set; }
        [StringLength(200)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [StringLength(250)]
        public string Error { get; set; }
    }
}
