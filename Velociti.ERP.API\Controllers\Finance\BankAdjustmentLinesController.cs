﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class BankAdjustmentLinesController : ControllerBase
    {
        private readonly IBankAdjustmentLineService _bankAdjustmentLineService;
        
        public BankAdjustmentLinesController(IBankAdjustmentLineService bankAdjustmentLineService)
        {
            _bankAdjustmentLineService = bankAdjustmentLineService;
        }

        [HttpGet]
        [Route("{bankAdjustmentId}")]
        public async Task<IActionResult> GetByHeaderId(int bankAdjustmentId)
        {
            return Ok(await _bankAdjustmentLineService.GetByHeaderIdAsync(bankAdjustmentId));
        }

        [HttpGet]
        [Route("CostAllocations/{bankAdjustmentLineId}")]
        public async Task<IActionResult> GetCostAllocationByBankAdjustmentLineIdAsync(int bankAdjustmentLineId)
        {
            return Ok(await _bankAdjustmentLineService.GetCostAllocationByBankAdjustmentLineIdAsync(bankAdjustmentLineId));
        }
    }
}