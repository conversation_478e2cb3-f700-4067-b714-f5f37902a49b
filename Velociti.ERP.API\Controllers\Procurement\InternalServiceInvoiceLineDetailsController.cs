﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class InternalServiceInvoiceLineDetailsController : ControllerBase  
    {
        private readonly IInternalServiceInvoiceLineDetailService _internalServiceInvoiceLineDetailService;

        public InternalServiceInvoiceLineDetailsController(IInternalServiceInvoiceLineDetailService internalServiceInvoiceLineDetailService)
        {
            _internalServiceInvoiceLineDetailService = internalServiceInvoiceLineDetailService;
        }

        [HttpGet]
        [Route("{invoiceLineId}")]
        public async Task<IActionResult> Get(int invoiceLineId)  
        {
            return Ok(await _internalServiceInvoiceLineDetailService.GetAsync(invoiceLineId));
        }

        [HttpPost]
        public async Task<IActionResult> GetInternalServiceInvoiceLineDetails([FromBody]InternalServiceInvoiceLineDetail internalServiceInvoiceLineDetail)
        {
            await _internalServiceInvoiceLineDetailService.SaveAsync(internalServiceInvoiceLineDetail);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _internalServiceInvoiceLineDetailService.DeleteAsync(id);

            return Ok();
        }
    }
}