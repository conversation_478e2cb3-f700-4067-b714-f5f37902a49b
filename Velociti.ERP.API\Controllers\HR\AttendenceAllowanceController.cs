﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.HR;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Entities.Sales;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class AttendenceAllowanceController : ControllerBase
    {
        private readonly IAttendenceAllowanceService _attendenceAllowanceService;

        public AttendenceAllowanceController(IAttendenceAllowanceService attendenceAllowanceService)
        {
            _attendenceAllowanceService = attendenceAllowanceService;
        }

        [HttpGet]
        [Route("Single/{attendenceAllowanceId}")]
        public async Task<IActionResult> FindById(int attendenceAllowanceId)
        {
            return Ok(await _attendenceAllowanceService.FindByIdAsync(attendenceAllowanceId));
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _attendenceAllowanceService.GetAllAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]AttendenceAllowance attendenceAllowance)
        {
            switch (attendenceAllowance.Action)
            {
                case "save": await _attendenceAllowanceService.SaveAsync(attendenceAllowance); break;
                case "submit": await _attendenceAllowanceService.UpdateAsync(attendenceAllowance); break;
            }

            return Ok();
        }
    }
}