﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductionNotes", Schema = "inv")]
    public partial class ProductionNote
    {
        public ProductionNote()
        {
            ProductionNoteLines = new HashSet<ProductionNoteLine>();
        }

        [Key]
        public int ProductionNoteId { get; set; }

        public int? CompanyId { get; set; }
        public int? WarehouseId { get; set; }
        public byte? StatusEnum { get; set; }

        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime ProductionDate { get; set; }

        [StringLength(1000)]
        public string Remarks { get; set; }

        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }

        [Column(TypeName = "datetime")]
        public DateTime? CanceledDate { get; set; }

        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        public virtual Company Company { get; set; }

        [ForeignKey(nameof(WarehouseId))]
        public virtual Warehous Warehouse { get; set; }

        //[ForeignKey(nameof(CreatedUserId))]
        //[InverseProperty("ProductionNotesCreatedBy")]
        //public virtual User CreatedUser { get; set; }

        //[ForeignKey(nameof(ModifiedUserId))]
        //[InverseProperty("ProductionNotesModifiedBy")]
        //public virtual User ModifiedUser { get; set; }

        [InverseProperty(nameof(ProductionNoteLine.ProductionNote))]
        public virtual ICollection<ProductionNoteLine> ProductionNoteLines { get; set; }
    }
}