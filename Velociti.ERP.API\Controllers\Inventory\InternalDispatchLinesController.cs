﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class InternalDispatchLinesController : ControllerBase
    {
        private readonly IInternalDispatchLineService _internalDispatchLineService;

        public InternalDispatchLinesController(IInternalDispatchLineService internalDispatchLineService)
        {
            _internalDispatchLineService = internalDispatchLineService;
        }

        [HttpGet]
        [Route("{internalDispatchId}")]
        public async Task<IActionResult> GetByHeaderId(int internalDispatchId)
        {
            return Ok(await _internalDispatchLineService.GetByHeaderIdAsync(internalDispatchId));
        }
        [HttpGet]
        [Route("Lines/{internalDispatchId}")]
        public async Task<IActionResult> GetByLineId(int internalDispatchId)
        {
            return Ok(await _internalDispatchLineService.GetByLineIdAsync(internalDispatchId));
        }
        [HttpPut]
        public async Task<IActionResult> Update([FromBody]InternalDispatchLine internalDispatchLine)
        {
            await _internalDispatchLineService.SaveAsync(internalDispatchLine);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Delete([FromBody]InternalDispatchLine internalDispatchLine)
        {
            await _internalDispatchLineService.DeleteAsync(internalDispatchLine);

            return Ok();
        }
    }
}