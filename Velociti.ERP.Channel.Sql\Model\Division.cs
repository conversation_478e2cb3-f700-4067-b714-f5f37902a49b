﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Divisions", Schema = "adm")]
    public partial class Division
    {
        public Division()
        {
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SalesServiceOrderLines = new HashSet<SalesServiceOrderLine>();
            UserDivisions = new HashSet<UserDivision>();
        }

        [Key]
        public int DivisionId { get; set; }
        public int DepartmentId { get; set; }
        [StringLength(50)]
        public string DivisionCode { get; set; }
        [StringLength(255)]
        public string DivisionName { get; set; }
        [StringLength(20)]
        public string Telephone { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string Website { get; set; }
        public int? WarehouseId { get; set; }
        public int? CurrencyId { get; set; }
        public byte? OperationType { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("Divisions")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("Divisions")]
        public virtual Department Department { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.TherapyRoom))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SalesServiceOrderLine.TherapyRoom))]
        public virtual ICollection<SalesServiceOrderLine> SalesServiceOrderLines { get; set; }
        [InverseProperty(nameof(UserDivision.Division))]
        public virtual ICollection<UserDivision> UserDivisions { get; set; }
    }
}
