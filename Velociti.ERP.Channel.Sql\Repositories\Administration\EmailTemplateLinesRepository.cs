﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class EmailTemplateLinesRepository : IEmailTemplateLinesRepository
    {
        private readonly MarangoniERPContext _context;

        public EmailTemplateLinesRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Domain.Entities.Administration.EmailTemplateLine>> GetByHeaderIdAsync(int emailTemplateId)
        {
            var result = from a in _context.EmailTemplateLines
                         where a.EmailTemplateId == emailTemplateId
                         select new Domain.Entities.Administration.EmailTemplateLine
                         {
                             EmailTemplateId = a.EmailTemplateId,
                             EmailTemplateLineId = a.EmailTemplateLineId,
                             TypeEnum = a.TypeEnum,
                             ExpiryDate = a.ExpiryDate,
                             TypeName = a.TypeEnum == (byte)Domain.Entities.Administration.Module.DocumentType.PurchaseOrder ? "Purchase Orders"
                                : a.TypeEnum == (byte)Domain.Entities.Administration.Module.DocumentType.GRN ? "GRNs"
                                : a.TypeEnum == (byte)Domain.Entities.Administration.Module.DocumentType.PRN ? "PRNs"
                                : a.TypeEnum == (byte)Domain.Entities.Administration.Module.DocumentType.ServiceInvoice ? "Service Invoices"
                                : a.TypeEnum == (byte)Domain.Entities.Administration.Module.DocumentType.PurchaseReturn ? "Purchase Returns"
                                : a.TypeEnum == (byte)Domain.Entities.Administration.Module.DocumentType.SalesInvoice ? "Sales Invoices"
                                : a.TypeEnum == (byte)Domain.Entities.Administration.Module.DocumentType.SalesReturn ? "Sales Returns"
                                : a.TypeEnum == (byte)Domain.Entities.Administration.Module.DocumentType.SalesServiceInvoice ? "Sales Service Invoices" : "-"
                         };
            return await result.ToListAsync();
        }

        public async Task AddRangeAsync(IEnumerable<EmailTemplateLine> emailTemplateLines)
        {
            await _context.AddRangeAsync(emailTemplateLines);

            await _context.SaveChangesAsync();
        }

        public async Task RemoveRangeAsync(IEnumerable<EmailTemplateLine> emailTemplateLines)
        {
            _context.RemoveRange(emailTemplateLines);

            await _context.SaveChangesAsync();
        }

        public async Task ToggleActivationAsync(int id, int loggedInUserId)
        {
            EmailTemplateLine emailTemplateLine = await _context.EmailTemplateLines.FirstAsync(c => c.EmailTemplateLineId == id);

            emailTemplateLine.ExpiryDate = emailTemplateLine.ExpiryDate == null ? DateTime.Now : (DateTime?)null;
            emailTemplateLine.ModifiedUserId = loggedInUserId;

            await _context.SaveChangesAsync();
        }
    }
}
