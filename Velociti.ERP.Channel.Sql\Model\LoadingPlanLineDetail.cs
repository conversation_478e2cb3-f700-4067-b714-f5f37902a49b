﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("LoadingPlanLineDetails", Schema = "sales")]
    public partial class LoadingPlanLineDetail
    {
        [Key]
        public int LoadingPlanLineDetailId { get; set; }
        public int? LoadingPlanLineId { get; set; }
        public int? WarehouseId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string PalletCode { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(LoadingPlanLineId))]
        [InverseProperty("LoadingPlanLineDetails")]
        public virtual LoadingPlanLine LoadingPlanLine { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.LoadingPlanLineDetails))]
        public virtual Warehous Warehouse { get; set; }
    }
}
