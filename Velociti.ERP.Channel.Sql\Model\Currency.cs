﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Currencies", Schema = "adm")]
    public partial class Currency
    {
        public Currency()
        {
            Accounts = new HashSet<Account>();
            Companies = new HashSet<Company>();
            CurrencyExchangeRates = new HashSet<CurrencyExchangeRate>();
            CustomerPayments = new HashSet<CustomerPayment>();
            Customers = new HashSet<Customer>();
            Departments = new HashSet<Department>();
            Divisions = new HashSet<Division>();
            GeneralLedgerLines = new HashSet<GeneralLedgerLine>();
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            InboundReceipts = new HashSet<InboundReceipt>();
            ManualJournals = new HashSet<ManualJournal>();
            OutboundPayments = new HashSet<OutboundPayment>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            PurchaseOrderAdditionalCharges = new HashSet<PurchaseOrderAdditionalCharge>();
            PurchaseOrders = new HashSet<PurchaseOrder>();
            PurchaseReturns = new HashSet<PurchaseReturn>();
            Quotations = new HashSet<Quotation>();
            SalesInvoices = new HashSet<SalesInvoice>();
            SalesOrders = new HashSet<SalesOrder>();
            SalesReturns = new HashSet<SalesReturn>();
            ServiceInvoices = new HashSet<ServiceInvoice>();
            ShipmentCostLines = new HashSet<ShipmentCostLine>();
            ShipmentQualityControlReturns = new HashSet<ShipmentQualityControlReturn>();
        }

        [Key]
        public int CurrencyId { get; set; }
        [StringLength(10)]
        public string CurrencyCode { get; set; }
        [StringLength(50)]
        public string CurrencyName { get; set; }
        [StringLength(10)]
        public string CurrencySymbol { get; set; }
        [StringLength(255)]
        public string CurrencyDescription { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? BuyingRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? SellingRate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? CurrencyRateDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [InverseProperty(nameof(Account.Currency))]
        public virtual ICollection<Account> Accounts { get; set; }
        [InverseProperty(nameof(Company.Currency))]
        public virtual ICollection<Company> Companies { get; set; }
        [InverseProperty(nameof(CurrencyExchangeRate.Currency))]
        public virtual ICollection<CurrencyExchangeRate> CurrencyExchangeRates { get; set; }
        [InverseProperty(nameof(CustomerPayment.Currency))]
        public virtual ICollection<CustomerPayment> CustomerPayments { get; set; }
        [InverseProperty(nameof(Customer.BillingCurrency))]
        public virtual ICollection<Customer> Customers { get; set; }
        [InverseProperty(nameof(Department.Currency))]
        public virtual ICollection<Department> Departments { get; set; }
        [InverseProperty(nameof(Division.Currency))]
        public virtual ICollection<Division> Divisions { get; set; }
        [InverseProperty(nameof(GeneralLedgerLine.Currency))]
        public virtual ICollection<GeneralLedgerLine> GeneralLedgerLines { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.Currency))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(InboundReceipt.Currency))]
        public virtual ICollection<InboundReceipt> InboundReceipts { get; set; }
        [InverseProperty(nameof(ManualJournal.Currency))]
        public virtual ICollection<ManualJournal> ManualJournals { get; set; }
        [InverseProperty(nameof(OutboundPayment.Currency))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
        [InverseProperty(nameof(PaymentAdvice.Currency))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(PurchaseOrderAdditionalCharge.Currency))]
        public virtual ICollection<PurchaseOrderAdditionalCharge> PurchaseOrderAdditionalCharges { get; set; }
        [InverseProperty(nameof(PurchaseOrder.Currency))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        [InverseProperty(nameof(PurchaseReturn.Currency))]
        public virtual ICollection<PurchaseReturn> PurchaseReturns { get; set; }
        [InverseProperty(nameof(Quotation.Currency))]
        public virtual ICollection<Quotation> Quotations { get; set; }
        [InverseProperty(nameof(SalesInvoice.Currency))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
        [InverseProperty(nameof(SalesOrder.Currency))]
        public virtual ICollection<SalesOrder> SalesOrders { get; set; }
        [InverseProperty(nameof(SalesReturn.Currency))]
        public virtual ICollection<SalesReturn> SalesReturns { get; set; }
        [InverseProperty(nameof(ServiceInvoice.Currency))]
        public virtual ICollection<ServiceInvoice> ServiceInvoices { get; set; }
        [InverseProperty(nameof(ShipmentCostLine.Currency))]
        public virtual ICollection<ShipmentCostLine> ShipmentCostLines { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturn.Currency))]
        public virtual ICollection<ShipmentQualityControlReturn> ShipmentQualityControlReturns { get; set; }
    }
}
