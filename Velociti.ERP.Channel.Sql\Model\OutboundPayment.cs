﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("OutboundPayments", Schema = "fin")]
    public partial class OutboundPayment
    {
        public OutboundPayment()
        {
            OutboundPaymentLines = new HashSet<OutboundPaymentLine>();
        }

        [Key]
        public int OutboundPaymentId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(50)]
        public string RefNumber { get; set; }
        public int? SupplierId { get; set; }
        public int? CustomerId { get; set; }
        public int? EmployeeId { get; set; }
        public int? AccountId { get; set; }
        public int? TaxGroupId { get; set; }
        public int? PayBookId { get; set; }
        public int? ChequeBookId { get; set; }
        public int? ChequeNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ChequeDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? BankingDate { get; set; }
        public bool? IsPostDatedCheque { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [StringLength(255)]
        public string PayeeName { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "money")]
        public decimal? SettlementValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
        [Column(TypeName = "money")]
        public decimal? AvailableAdvances { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("OutboundPayments")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("OutboundPayments")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("OutboundPayments")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("OutboundPayments")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("OutboundPayments")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("OutboundPayments")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(PayBookId))]
        [InverseProperty("OutboundPayments")]
        public virtual PayBook PayBook { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("OutboundPayments")]
        public virtual Supplier Supplier { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("OutboundPayments")]
        public virtual TaxGroup TaxGroup { get; set; }
        [InverseProperty(nameof(OutboundPaymentLine.OutboundPayment))]
        public virtual ICollection<OutboundPaymentLine> OutboundPaymentLines { get; set; }
    }
}
