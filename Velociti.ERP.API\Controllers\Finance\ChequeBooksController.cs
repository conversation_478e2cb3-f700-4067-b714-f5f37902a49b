﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class ChequeBooksController : ControllerBase
    {
        private readonly IChequeBookService _chequeBookService;

        public ChequeBooksController(IChequeBookService chequeBookService)
        {
            _chequeBookService = chequeBookService;
        }

        [HttpGet]
        [Route("{payBookId}")]
        public async Task<IActionResult> GetAll(int payBookId)
        {
            return Ok(await _chequeBookService.GetAllByPayBookIdAsync(payBookId));
        }

        [HttpGet]
        [Route("ShortList/{payBookId}")]
        public async Task<IActionResult> GetShortList(int payBookId)
        {
            return Ok(await _chequeBookService.GetShortListByPayBookIdAsync(payBookId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ChequeBook chequeBook)
        {
            await _chequeBookService.SaveAsync(chequeBook);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> ToggleActivation([FromBody]ChequeBook chequeBook)
        {
            await _chequeBookService.ToggleActivationAsync(chequeBook);

            return Ok();
        }
    }
}