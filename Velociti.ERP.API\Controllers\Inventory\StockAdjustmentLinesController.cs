﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]  
    public class StockAdjustmentLinesController : ControllerBase
    {
        private readonly IStockAdjustmentLineService _stockAdjustmentLineService;  

        public StockAdjustmentLinesController(IStockAdjustmentLineService stockAdjustmentLineService)        
        {
            _stockAdjustmentLineService = stockAdjustmentLineService;
        }

        [HttpGet]
        [Route("{stockAdjustmentId}/company/{companyId}")]
        public async Task<IActionResult> Get(int stockAdjustmentId, int companyId)
        {
            return Ok(await _stockAdjustmentLineService.GetAsync(stockAdjustmentId, companyId));
        }

        [HttpPost]
        [Route("{warehouseProductId}/company/{companyId}/Warehouse/{warehouseId}")]
        public async Task<IActionResult> GetStockAdjustmentLine(int warehouseProductId, int companyId, int warehouseId, [FromBody]StockAdjustmentLine stockAdjustmentLine)  
        {
            return Ok(await _stockAdjustmentLineService.AddStockAdjustmentLineAsync(warehouseProductId, companyId, warehouseId, stockAdjustmentLine));
        }

        [HttpPut("Update")]
        public async Task<IActionResult> UpdateStockAdjustmentLine([FromBody] StockAdjustmentLine stockAdjustmentLine)
        {
            if (stockAdjustmentLine == null || stockAdjustmentLine.StockAdjustmentLineId <= 0)
                return BadRequest("Invalid stock adjustment line data.");
            var updatedLine = await _stockAdjustmentLineService.UpdateStockAdjustmentLineAsync(stockAdjustmentLine);
            if (updatedLine == null)
                return NotFound("StockAdjustmentLine not found.");
            return Ok(updatedLine);
        }
        }
}