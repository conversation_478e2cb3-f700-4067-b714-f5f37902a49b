﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class UserDepartmentsController : ControllerBase
    {
        private readonly IUserDepartmentService _userDepartmentService;

        public UserDepartmentsController(IUserDepartmentService userDepartmentService)
        {
            _userDepartmentService = userDepartmentService;
        }

        [HttpGet]
        [Route("Assigned/user/{userId}")]
        public async Task<IActionResult> GetAssignedDepartments(int userId)
        {
            return Ok(await _userDepartmentService.GetAssignedDepartmentsAsync(userId));
        }

        [HttpGet]
        [Route("shortList/user/{userId}/company/{companyId}")]
        public async Task<IActionResult> GetAssignedDepartmentShortList(int userId, int companyId)
        {
            return Ok(await _userDepartmentService.GetAssignedDepartmentsShortListAsync(userId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]IEnumerable<UserDepartment> userDepartments)
        {
            if (userDepartments == null)
                return BadRequest();

            await _userDepartmentService.SaveAsync(userDepartments);

            return Ok();
        }

        [HttpPut]
        [Route("{id}")]
        public async Task<IActionResult> ToggleDefault(int id)
        {
            await _userDepartmentService.ToggleDefaultAsync(id);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivationAsync(int id)
        {
            await _userDepartmentService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}