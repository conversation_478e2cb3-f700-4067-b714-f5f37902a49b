﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("EmailTemplates", Schema = "adm")]
    public partial class EmailTemplate
    {
        public EmailTemplate()
        {
            EmailTemplateLines = new HashSet<EmailTemplateLine>();
        }

        [Key]
        public int EmailTemplateId { get; set; }
        public int? CompanyId { get; set; }
        public byte? TypeEnum { get; set; }
        [Required]
        [StringLength(100)]
        public string TemplateCode { get; set; }
        [StringLength(255)]
        public string TemplateName { get; set; }
        [StringLength(255)]
        public string Subject { get; set; }
        [StringLength(1000)]
        public string Body { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [InverseProperty(nameof(EmailTemplateLine.EmailTemplate))]
        public virtual ICollection<EmailTemplateLine> EmailTemplateLines { get; set; }
    }
}
