﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BillOfOperationLineOverheads", Schema = "man")]
    public partial class BillOfOperationLineOverhead
    {
        [Key]
        public int BillOfOperationLineId { get; set; }
        [Key]
        public int SupportDataId { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? Amount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfOperationLineId))]
        [InverseProperty("BillOfOperationLineOverheads")]
        public virtual BillOfOperationLine BillOfOperationLine { get; set; }
        [ForeignKey(nameof(SupportDataId))]
        [InverseProperty("BillOfOperationLineOverheads")]
        public virtual SupportData SupportData { get; set; }
    }
}
