﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductionOrderLineDetails", Schema = "man")]
    public partial class ProductionOrderLineDetail
    {
        [Key]
        public int ProductionOrderLineDetailId { get; set; }
        public int? ProductionOrderLineId { get; set; }
        public int? BillOfOperationLineDetailId { get; set; }
        public int? ProductId { get; set; }
        public int? OverheadInformationId { get; set; }
        [StringLength(50)]
        public string Barcode { get; set; }
        public long? Quantity { get; set; }
        public long? ActualQuantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? OverheadCost { get; set; }
        [StringLength(50)]
        public string UsedBatchNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(OverheadInformationId))]
        [InverseProperty(nameof(SupportData.ProductionOrderLineDetails))]
        public virtual SupportData OverheadInformation { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ProductionOrderLineDetails")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductionOrderLineId))]
        [InverseProperty("ProductionOrderLineDetails")]
        public virtual ProductionOrderLine ProductionOrderLine { get; set; }
    }
}
