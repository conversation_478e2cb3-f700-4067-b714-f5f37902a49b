﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class ServiceInvoiceLinesController : ControllerBase
    {
        private readonly IServiceInvoiceLineService _serviceInvoiceLineService;

        public ServiceInvoiceLinesController(IServiceInvoiceLineService serviceInvoiceLineService)
        {
            _serviceInvoiceLineService = serviceInvoiceLineService;
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ServiceInvoiceLine serviceInvoiceLine)
        {
            await _serviceInvoiceLineService.SaveAsync(serviceInvoiceLine);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Remove([FromBody]ServiceInvoiceLine serviceInvoiceLine)
        {
            await _serviceInvoiceLineService.DeleteAsync(serviceInvoiceLine);

            return Ok();
        }
    }
}