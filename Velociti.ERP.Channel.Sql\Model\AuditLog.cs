﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("AuditLogs", Schema = "cmn")]
    public partial class AuditLog
    {
        [Key]
        public int AuditLogId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string EventDocNumber { get; set; }
        public byte? EventDocTypeEnum { get; set; }
        [StringLength(1000)]
        public string EventDescription { get; set; }
        [StringLength(1000)]
        public string AdditionalData { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("AuditLogs")]
        public virtual Company Company { get; set; }
    }
}
