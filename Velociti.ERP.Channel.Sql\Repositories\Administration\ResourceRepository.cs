﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class ResourceRepository : IResourceRepository
    {
        private readonly MarangoniERPContext _context;

        public ResourceRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Resource>> GetAllAsync(int moduleId)
        {
            return await _context.Resources.Where(p=> p.ModuleId == moduleId).ToListAsync();
        }
    }
}
