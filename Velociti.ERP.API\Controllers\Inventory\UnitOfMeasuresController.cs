﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class UnitOfMeasuresController : ControllerBase
    {
        private readonly IUnitOfMeasureService _unitOfMeasureService;

        public UnitOfMeasuresController(IUnitOfMeasureService unitOfMeasureService)
        {
            _unitOfMeasureService = unitOfMeasureService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> Get(int companyId)
        {
            return Ok(await _unitOfMeasureService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("Single/{unitOfMeasureId}")]
        public async Task<IActionResult> FindById(int unitOfMeasureId)
        {
            return Ok(await _unitOfMeasureService.FindByIdAsync(unitOfMeasureId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _unitOfMeasureService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/product/{productId}")]
        public async Task<IActionResult> GetShortListByProduct(int productId)
        {
            var list = await _unitOfMeasureService.GetShortListByProductAsync(productId);

            return Ok(list);
        }

        [HttpDelete]
        [Route("{unitOfMeasureId}/User/{modifiedUserId}")]
        public async Task<IActionResult> ToggleActivation(int unitOfMeasureId, int modifiedUserId)
        {
            await _unitOfMeasureService.ToggleActivationAsync(unitOfMeasureId, modifiedUserId);

            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]UnitOfMeasure unitOfMeasure)
        {
            if (unitOfMeasure == null)
                return BadRequest();

            await _unitOfMeasureService.SaveAsync(unitOfMeasure);

            return Ok();
        }
    }
}