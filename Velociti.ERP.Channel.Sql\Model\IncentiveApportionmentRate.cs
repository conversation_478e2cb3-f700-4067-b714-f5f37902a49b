﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    public partial class IncentiveApportionmentRate
    {
        [Key]
        public int IncentiveApportionmentRatesId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ProductionDirectRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ProductionInDirectRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? GeneralShiftRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? LabourShiftRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Commitment01YearRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? NoPayMonthlyDeductionRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? LateAttendanceMonthlyDeductionRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? CommitmentLessThan6MonthsRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? UnauthorizedAbsenteeismDeductionRate { get; set; }
        [Column("30MinuteExceedLateAttendanceRate", TypeName = "decimal(18, 4)")]
        public decimal? _30minuteExceedLateAttendanceRate { get; set; }
        public int? GeneralShiftsPerMonth { get; set; }
        public int? LabourShiftsPerMonth { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
    }
}
