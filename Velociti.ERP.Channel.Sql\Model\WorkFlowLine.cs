﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("WorkFlowLines", Schema = "cmn")]
    public partial class WorkFlowLine
    {
        public WorkFlowLine()
        {
            TxnWorkFlowLines = new HashSet<TxnWorkFlowLine>();
        }

        [Key]
        public int WorkFlowLineId { get; set; }
        public int WorkFlowId { get; set; }
        public int SequenceNumber { get; set; }
        public int? DesignationId { get; set; }
        public int? UserId { get; set; }
        public byte? CombinationEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DesignationId))]
        [InverseProperty("WorkFlowLines")]
        public virtual Designation Designation { get; set; }
        [ForeignKey(nameof(UserId))]
        [InverseProperty("WorkFlowLines")]
        public virtual User User { get; set; }
        [ForeignKey(nameof(WorkFlowId))]
        [InverseProperty("WorkFlowLines")]
        public virtual WorkFlow WorkFlow { get; set; }
        [InverseProperty(nameof(TxnWorkFlowLine.WorkFlowLine))]
        public virtual ICollection<TxnWorkFlowLine> TxnWorkFlowLines { get; set; }
    }
}
