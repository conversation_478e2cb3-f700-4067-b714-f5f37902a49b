﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class InternalReturnsController : ControllerBase  
    {
        private readonly IInternalReturnService _internalReturnService;    

        public InternalReturnsController(IInternalReturnService internalReturnService)
        {
            _internalReturnService = internalReturnService;
        }

        [HttpGet]
        [Route("Single/{internalReturnId}")]
        public async Task<IActionResult> FindById(int internalReturnId) 
        {
            return Ok(await _internalReturnService.FindByIdAsync(internalReturnId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _internalReturnService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]InternalReturn internalReturn)
        {
            await _internalReturnService.SaveAsync(internalReturn);

            return Ok();
        }

        [HttpDelete]
        [Route("{internalReturnId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int internalReturnId, int userId)  
        {
            await _internalReturnService.CancelAsync(internalReturnId, userId);  

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]InternalReturn internalReturn)
        {
            switch (internalReturn.Action)
            {
                case "submit": await _internalReturnService.SubmitAsync(internalReturn.InternalReturnId, internalReturn.ModifiedUserId.Value); break;
                case "reverse": await _internalReturnService.ReversedAsync(internalReturn.InternalReturnId, internalReturn.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}