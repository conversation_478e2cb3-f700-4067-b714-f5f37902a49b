﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _notificationService;

        public NotificationController(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        [HttpGet]
        [Route("Notifications/{userId}")]
        public async Task<ActionResult<Response<IEnumerable<Notification>>>> GetNotificationByUserId(int userId)
        {
            return Ok(await _notificationService.GetNotificationByUserIdAsync(userId));
        }

        [HttpPut]
        public async Task<IActionResult> UpdateNotificatonAsync([FromBody] Notification notification)
        {
            await _notificationService.UpdateNotificationAsync(notification.NotificationId, notification.ModifiedUserId.Value, (DateTime)notification.ViewedDateTime);
            return Ok();
        }

        [HttpPut]
        [Route("ClearAll")]
        public async Task<IActionResult> ClearAll([FromBody] Notification notification)
        {
            await _notificationService.UpdateAllNotificatonAsync(notification.ModifiedUserId.Value, (DateTime)notification.ViewedDateTime);
            return Ok();
        }
    }
}
