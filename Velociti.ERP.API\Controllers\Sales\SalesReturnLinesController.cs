﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [ApiController]
    public class SalesReturnLinesController : ControllerBase
    {
        private readonly ISalesReturnLineService salesReturnLineService;

        public SalesReturnLinesController(ISalesReturnLineService salesReturnLineService)
        {
            this.salesReturnLineService = salesReturnLineService;
        }
        [HttpGet]
        [Route("{salesReturnId}/Company/{companyId}")]
        public async Task<IActionResult> GetAll(int salesReturnId, int companyId)
        {
            return Ok(await this.salesReturnLineService.GetByHeaderIdAsync(companyId, salesReturnId));
        }

        [HttpGet]
        [Route("Edit/{salesReturnId}/Company/{companyId}")]
        public async Task<IActionResult> GetForEdit(int salesReturnId, int companyId)
        {
            return Ok(await this.salesReturnLineService.GetByHeaderIdForEditAsync(companyId, salesReturnId));
        }
    }
}