﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseOrderLines", Schema = "prc")]
    public partial class PurchaseOrderLine
    {
        public PurchaseOrderLine()
        {
            PurchaseOrderLineDetails = new HashSet<PurchaseOrderLineDetail>();
        }

        [Key]
        public int PurchaseOrderLineId { get; set; }
        public int PurchaseOrderId { get; set; }
        public int? PurchaseRequisitionNoteLineId { get; set; }
        public int? QuotationLineId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ExpectedDeliveryDate { get; set; }
        [Column(TypeName = "money")]
        public decimal? TargetRate { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? SupplierQuantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }
        public int? DeliveryWeek { get; set; }
        public int? LeadDays { get; set; }
        [StringLength(50)]
        public string WarrantyPeriod { get; set; }
        [StringLength(255)]
        public string Remarks { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Tolerance { get; set; }
        public int? FixedAssetGroupId { get; set; }
        public int? FixedAssetId { get; set; }
        public int? FixedAssetRegisterId { get; set; }
        public bool? IsIndividualRegistryMech { get; set; }
        public bool? IsAcquisitionWithTax { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.PurchaseOrderLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.PurchaseOrderLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(FixedAssetId))]
        [InverseProperty("PurchaseOrderLines")]
        public virtual FixedAsset FixedAsset { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("PurchaseOrderLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(PurchaseOrderId))]
        [InverseProperty("PurchaseOrderLines")]
        public virtual PurchaseOrder PurchaseOrder { get; set; }
        [ForeignKey(nameof(PurchaseRequisitionNoteLineId))]
        [InverseProperty("PurchaseOrderLines")]
        public virtual PurchaseRequisitionNoteLine PurchaseRequisitionNoteLine { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("PurchaseOrderLines")]
        public virtual TaxGroup TaxGroup { get; set; }
        [InverseProperty(nameof(PurchaseOrderLineDetail.PurchaseOrderLine))]
        public virtual ICollection<PurchaseOrderLineDetail> PurchaseOrderLineDetails { get; set; }
    }
}
