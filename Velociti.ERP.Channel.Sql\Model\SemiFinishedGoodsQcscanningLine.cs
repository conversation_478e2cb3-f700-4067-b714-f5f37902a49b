﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SemiFinishedGoodsQCScanningLines", Schema = "inv")]
    public partial class SemiFinishedGoodsQcscanningLine
    {
        [Key]
        [Column("SemiFinishedGoodsQCScanningLineId")]
        public int SemiFinishedGoodsQcscanningLineId { get; set; }
        [Column("SemiFinishedGoodsQCScanningId")]
        public int SemiFinishedGoodsQcscanningId { get; set; }
        public int ParameterId { get; set; }
        [Column(TypeName = "decimal(10, 3)")]
        public decimal? Value { get; set; }
        public byte? StatusEnum { get; set; }
        public byte? SubStatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ParameterId))]
        [InverseProperty(nameof(Qcparameter.SemiFinishedGoodsQcscanningLines))]
        public virtual Qcparameter Parameter { get; set; }
        [ForeignKey(nameof(SemiFinishedGoodsQcscanningId))]
        [InverseProperty("SemiFinishedGoodsQcscanningLines")]
        public virtual SemiFinishedGoodsQcscanning SemiFinishedGoodsQcscanning { get; set; }
    }
}
