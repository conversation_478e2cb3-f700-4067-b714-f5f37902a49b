﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("LeaveApplicationForms", Schema = "hr")]
    public partial class LeaveApplicationForm
    {
        [Key]
        public int LeaveApplicationFormId { get; set; }
        public int? ApplyingEmployeeId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? StartDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? EndDate { get; set; }
        public byte? LeaveTypeEnum { get; set; }
        public int? CoveringEmployeeId { get; set; }
        [StringLength(1000)]
        public string Reasons { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ApplyingEmployeeId))]
        [InverseProperty(nameof(Employee.LeaveApplicationFormApplyingEmployees))]
        public virtual Employee ApplyingEmployee { get; set; }
        [ForeignKey(nameof(CoveringEmployeeId))]
        [InverseProperty(nameof(Employee.LeaveApplicationFormCoveringEmployees))]
        public virtual Employee CoveringEmployee { get; set; }
    }
}
