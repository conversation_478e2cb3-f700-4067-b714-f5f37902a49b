﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Products", Schema = "inv")]
    public partial class Product
    {
        public Product()
        {
            BillOfMaterialLines = new HashSet<BillOfMaterialLine>();
            BillOfOperationLineDetails = new HashSet<BillOfOperationLineDetail>();
            BillOfOperations = new HashSet<BillOfOperation>();
            BundleProductLines = new HashSet<BundleProductLine>();
            BundleProducts = new HashSet<BundleProduct>();
            ConsignmentPurchaseOrderLines = new HashSet<ConsignmentPurchaseOrderLine>();
            ConsignmentStockTransferLines = new HashSet<ConsignmentStockTransferLine>();
            CostingWarehous = new HashSet<CostingWarehous>();
            DiscountSchemeCriteriaLines = new HashSet<DiscountSchemeCriteriaLine>();
            DiscountSchemeLines = new HashSet<DiscountSchemeLine>();
            ExchangeOrderLines = new HashSet<ExchangeOrderLine>();
            FreeIssueCriteriaLines = new HashSet<FreeIssueCriteriaLine>();
            FreeIssueLines = new HashSet<FreeIssueLine>();
            GoodsDispatchNoteLines = new HashSet<GoodsDispatchNoteLine>();
            GoodsReceiveNoteLines = new HashSet<GoodsReceiveNoteLine>();
            HoreturnStockTransferIntegrationLines = new HashSet<HoreturnStockTransferIntegrationLine>();
            HoreturnStockTransferLines = new HashSet<HoreturnStockTransferLine>();
            InternalDispatchLineDetails = new HashSet<InternalDispatchLineDetail>();
            InternalDispatchLines = new HashSet<InternalDispatchLine>();
            InternalOrderLines = new HashSet<InternalOrderLine>();
            InternalReturnLines = new HashSet<InternalReturnLine>();
            InternalServiceInvoiceLineDetails = new HashSet<InternalServiceInvoiceLineDetail>();
            InternalServiceInvoiceLines = new HashSet<InternalServiceInvoiceLine>();
            LoadingPlanLines = new HashSet<LoadingPlanLine>();
            LoanOrderLines = new HashSet<LoanOrderLine>();
            MachineProductComponents = new HashSet<MachineProductComponent>();
            MachineVisualPlanLines = new HashSet<MachineVisualPlanLine>();
            MaterialRequisitionNoteLines = new HashSet<MaterialRequisitionNoteLine>();
            MaterialRequisitionPlanLines = new HashSet<MaterialRequisitionPlanLine>();
            MaterialRequisitionPlans = new HashSet<MaterialRequisitionPlan>();
            MouldProducts = new HashSet<MouldProduct>();
            PackingListLines = new HashSet<PackingListLine>();
            PriceLists = new HashSet<PriceList>();
            ProductDies = new HashSet<ProductDy>();
            ProductLicenceRegistrations = new HashSet<ProductLicenceRegistration>();
            ProductPackingMethods = new HashSet<ProductPackingMethod>();
            ProductUnitOfMeasures = new HashSet<ProductUnitOfMeasure>();
            ProductionOrderLineDetails = new HashSet<ProductionOrderLineDetail>();
            ProductionOrders = new HashSet<ProductionOrder>();
            ProductionPlanLines = new HashSet<ProductionPlanLine>();
            ProductionPlans = new HashSet<ProductionPlan>();
            PurchaseOrderLines = new HashSet<PurchaseOrderLine>();
            PurchaseRequisitionNoteLines = new HashSet<PurchaseRequisitionNoteLine>();
            PurchaseReturnLines = new HashSet<PurchaseReturnLine>();
            QcparameterMappings = new HashSet<QcparameterMapping>();
            QuotationLines = new HashSet<QuotationLine>();
            RawMaterialPlannings = new HashSet<RawMaterialPlanning>();
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SalesOrderLines = new HashSet<SalesOrderLine>();
            SalesReturnLines = new HashSet<SalesReturnLine>();
            SalesServiceOrderLines = new HashSet<SalesServiceOrderLine>();
            SemiFinishedGoodsProductionPlanSummaries = new HashSet<SemiFinishedGoodsProductionPlanSummary>();
            SemiFinishedGoodsProductionPlans = new HashSet<SemiFinishedGoodsProductionPlan>();
            SemiFinishedGoodsQcscannings = new HashSet<SemiFinishedGoodsQcscanning>();
            ServiceInquiryLines = new HashSet<ServiceInquiryLine>();
            ServiceInvoiceLines = new HashSet<ServiceInvoiceLine>();
            ShipmentQualityControlLines = new HashSet<ShipmentQualityControlLine>();
            ShipmentQualityControlReturnLines = new HashSet<ShipmentQualityControlReturnLine>();
            StockAdjustmentLines = new HashSet<StockAdjustmentLine>();
            StockTakeLines = new HashSet<StockTakeLine>();
            StockTransferLines = new HashSet<StockTransferLine>();
            StockTransferReceiptLines = new HashSet<StockTransferReceiptLine>();
            SubContractOrderLines = new HashSet<SubContractOrderLine>();
            SubContractOrders = new HashSet<SubContractOrder>();
            SupplierProducts = new HashSet<SupplierProduct>();
            TouchScreenHistories = new HashSet<TouchScreenHistory>();
            TxnLineHistories = new HashSet<TxnLineHistory>();
            TyreSpecifications = new HashSet<TyreSpecification>();
            WarehouseProducts = new HashSet<WarehouseProduct>();
            WarehouseStockIssues = new HashSet<WarehouseStockIssue>();
            WarehouseStockQualityControls = new HashSet<WarehouseStockQualityControl>();
        }

        [Key]
        public int ProductId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        [Column("ERPCode")]
        [StringLength(50)]
        public string Erpcode { get; set; }
        [StringLength(255)]
        public string ProductDescription { get; set; }
        public int? DefaultUnitOfMeasureId { get; set; }
        public int? CategoryEnum { get; set; }
        public int? LicenseTypeEnum { get; set; }
        public int? ProductHierarchyId1 { get; set; }
        public int? ProductHierarchyId2 { get; set; }
        public int? ProductHierarchyId3 { get; set; }
        public int? ProductHierarchyId4 { get; set; }
        public int? ProductHierarchyId5 { get; set; }
        public int? ProductHierarchyId6 { get; set; }
        public int? BrandId { get; set; }
        public int? ProductTypeId { get; set; }
        public bool? IsManufacturedProduct { get; set; }
        public byte? CostingMethodEnum { get; set; }
        public byte? MovementMethodEnum { get; set; }
        public byte? SerialBatchCheckEnum { get; set; }
        public byte? StockManagementMethodEnum { get; set; }
        public byte? NatureEnum { get; set; }
        public int? DefaultWarehouseId { get; set; }
        public int? MaximumLevel { get; set; }
        public int? SafetyLevel { get; set; }
        public int? ReorderLevel { get; set; }
        public int? ReorderQuantity { get; set; }
        public int? BufferStock { get; set; }
        public decimal? PurchaseOrderPrice {  get; set; }
        public decimal? LastCost { get; set; }
        public int? LeadDays { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Tolerance { get; set; }
        [Column("HSCode")]
        [StringLength(50)]
        public string Hscode { get; set; }
        [Column("IsGRNQuality")]
        public bool? IsGrnquality { get; set; }
        public int? NextLotNumber { get; set; }
        [StringLength(200)]
        public string ExtendedDescription { get; set; }
        [StringLength(255)]
        public string RegistrationName { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? AverageWeight { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? SupplierId { get; set; }

        public int? TempId { get; set; }
        public int? ReceipeId { get; set; }

        public bool? IsVatable { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }

        [ForeignKey(nameof(SupplierId))]
        public virtual Supplier Supplier { get; set; }

        [ForeignKey(nameof(BrandId))]
        [InverseProperty(nameof(SupportData.ProductBrands))]
        public virtual SupportData Brand { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Products")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DefaultUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.Products))]
        public virtual UnitOfMeasure DefaultUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DefaultWarehouseId))]
        [InverseProperty(nameof(Warehous.Products))]
        public virtual Warehous DefaultWarehouse { get; set; }
        [ForeignKey(nameof(ProductHierarchyId1))]
        [InverseProperty(nameof(ProductHierarchy.ProductProductHierarchyId1Navigation))]
        public virtual ProductHierarchy ProductHierarchyId1Navigation { get; set; }
        [ForeignKey(nameof(ProductHierarchyId2))]
        [InverseProperty(nameof(ProductHierarchy.ProductProductHierarchyId2Navigation))]
        public virtual ProductHierarchy ProductHierarchyId2Navigation { get; set; }
        [ForeignKey(nameof(ProductHierarchyId3))]
        [InverseProperty(nameof(ProductHierarchy.ProductProductHierarchyId3Navigation))]
        public virtual ProductHierarchy ProductHierarchyId3Navigation { get; set; }
        [ForeignKey(nameof(ProductHierarchyId4))]
        [InverseProperty(nameof(ProductHierarchy.ProductProductHierarchyId4Navigation))]
        public virtual ProductHierarchy ProductHierarchyId4Navigation { get; set; }
        [ForeignKey(nameof(ProductHierarchyId5))]
        [InverseProperty(nameof(ProductHierarchy.ProductProductHierarchyId5Navigation))]
        public virtual ProductHierarchy ProductHierarchyId5Navigation { get; set; }
        [ForeignKey(nameof(ProductHierarchyId6))]
        [InverseProperty(nameof(ProductHierarchy.ProductProductHierarchyId6Navigation))]
        public virtual ProductHierarchy ProductHierarchyId6Navigation { get; set; }
        [ForeignKey(nameof(ProductTypeId))]
        [InverseProperty(nameof(SupportData.ProductProductTypes))]
        public virtual SupportData ProductType { get; set; }
        [InverseProperty(nameof(BillOfMaterialLine.Product))]
        public virtual ICollection<BillOfMaterialLine> BillOfMaterialLines { get; set; }
        [InverseProperty(nameof(BillOfOperationLineDetail.Product))]
        public virtual ICollection<BillOfOperationLineDetail> BillOfOperationLineDetails { get; set; }
        [InverseProperty(nameof(BillOfOperation.Product))]
        public virtual ICollection<BillOfOperation> BillOfOperations { get; set; }
        [InverseProperty(nameof(BundleProductLine.Product))]
        public virtual ICollection<BundleProductLine> BundleProductLines { get; set; }
        [InverseProperty(nameof(BundleProduct.Product))]
        public virtual ICollection<BundleProduct> BundleProducts { get; set; }
        [InverseProperty(nameof(ConsignmentPurchaseOrderLine.Product))]
        public virtual ICollection<ConsignmentPurchaseOrderLine> ConsignmentPurchaseOrderLines { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransferLine.Product))]
        public virtual ICollection<ConsignmentStockTransferLine> ConsignmentStockTransferLines { get; set; }
        [InverseProperty("Product")]
        public virtual ICollection<CostingWarehous> CostingWarehous { get; set; }
        [InverseProperty(nameof(DiscountSchemeCriteriaLine.Product))]
        public virtual ICollection<DiscountSchemeCriteriaLine> DiscountSchemeCriteriaLines { get; set; }
        [InverseProperty(nameof(DiscountSchemeLine.Product))]
        public virtual ICollection<DiscountSchemeLine> DiscountSchemeLines { get; set; }
        [InverseProperty(nameof(ExchangeOrderLine.Product))]
        public virtual ICollection<ExchangeOrderLine> ExchangeOrderLines { get; set; }
        [InverseProperty(nameof(FreeIssueCriteriaLine.Product))]
        public virtual ICollection<FreeIssueCriteriaLine> FreeIssueCriteriaLines { get; set; }
        [InverseProperty(nameof(FreeIssueLine.Product))]
        public virtual ICollection<FreeIssueLine> FreeIssueLines { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLine.Product))]
        public virtual ICollection<GoodsDispatchNoteLine> GoodsDispatchNoteLines { get; set; }
        [InverseProperty(nameof(GoodsReceiveNoteLine.Product))]
        public virtual ICollection<GoodsReceiveNoteLine> GoodsReceiveNoteLines { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferIntegrationLine.Product))]
        public virtual ICollection<HoreturnStockTransferIntegrationLine> HoreturnStockTransferIntegrationLines { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferLine.Product))]
        public virtual ICollection<HoreturnStockTransferLine> HoreturnStockTransferLines { get; set; }
        [InverseProperty(nameof(InternalDispatchLineDetail.Product))]
        public virtual ICollection<InternalDispatchLineDetail> InternalDispatchLineDetails { get; set; }
        [InverseProperty(nameof(InternalDispatchLine.Product))]
        public virtual ICollection<InternalDispatchLine> InternalDispatchLines { get; set; }
        [InverseProperty(nameof(InternalOrderLine.Product))]
        public virtual ICollection<InternalOrderLine> InternalOrderLines { get; set; }
        [InverseProperty(nameof(InternalReturnLine.Product))]
        public virtual ICollection<InternalReturnLine> InternalReturnLines { get; set; }
        [InverseProperty(nameof(InternalServiceInvoiceLineDetail.Product))]
        public virtual ICollection<InternalServiceInvoiceLineDetail> InternalServiceInvoiceLineDetails { get; set; }
        [InverseProperty(nameof(InternalServiceInvoiceLine.Product))]
        public virtual ICollection<InternalServiceInvoiceLine> InternalServiceInvoiceLines { get; set; }
        [InverseProperty(nameof(LoadingPlanLine.Product))]
        public virtual ICollection<LoadingPlanLine> LoadingPlanLines { get; set; }
        [InverseProperty(nameof(LoanOrderLine.Product))]
        public virtual ICollection<LoanOrderLine> LoanOrderLines { get; set; }
        [InverseProperty(nameof(MachineProductComponent.Product))]
        public virtual ICollection<MachineProductComponent> MachineProductComponents { get; set; }
        [InverseProperty(nameof(MachineVisualPlanLine.Product))]
        public virtual ICollection<MachineVisualPlanLine> MachineVisualPlanLines { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNoteLine.Product))]
        public virtual ICollection<MaterialRequisitionNoteLine> MaterialRequisitionNoteLines { get; set; }
        [InverseProperty(nameof(MaterialRequisitionPlanLine.Product))]
        public virtual ICollection<MaterialRequisitionPlanLine> MaterialRequisitionPlanLines { get; set; }
        [InverseProperty(nameof(MaterialRequisitionPlan.Product))]
        public virtual ICollection<MaterialRequisitionPlan> MaterialRequisitionPlans { get; set; }
        [InverseProperty(nameof(MouldProduct.Product))]
        public virtual ICollection<MouldProduct> MouldProducts { get; set; }
        [InverseProperty(nameof(PackingListLine.Product))]
        public virtual ICollection<PackingListLine> PackingListLines { get; set; }
        [InverseProperty(nameof(PriceList.Product))]
        public virtual ICollection<PriceList> PriceLists { get; set; }
        [InverseProperty(nameof(ProductDy.Product))]
        public virtual ICollection<ProductDy> ProductDies { get; set; }
        [InverseProperty(nameof(ProductLicenceRegistration.Product))]
        public virtual ICollection<ProductLicenceRegistration> ProductLicenceRegistrations { get; set; }
        [InverseProperty(nameof(ProductPackingMethod.Product))]
        public virtual ICollection<ProductPackingMethod> ProductPackingMethods { get; set; }
        [InverseProperty(nameof(ProductUnitOfMeasure.Product))]
        public virtual ICollection<ProductUnitOfMeasure> ProductUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ProductionOrderLineDetail.Product))]
        public virtual ICollection<ProductionOrderLineDetail> ProductionOrderLineDetails { get; set; }
        [InverseProperty(nameof(ProductionOrder.Product))]
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; }
        [InverseProperty(nameof(ProductionPlanLine.Product))]
        public virtual ICollection<ProductionPlanLine> ProductionPlanLines { get; set; }
        [InverseProperty(nameof(ProductionPlan.Product))]
        public virtual ICollection<ProductionPlan> ProductionPlans { get; set; }
        [InverseProperty(nameof(PurchaseOrderLine.Product))]
        public virtual ICollection<PurchaseOrderLine> PurchaseOrderLines { get; set; }
        [InverseProperty(nameof(PurchaseRequisitionNoteLine.Product))]
        public virtual ICollection<PurchaseRequisitionNoteLine> PurchaseRequisitionNoteLines { get; set; }
        [InverseProperty(nameof(PurchaseReturnLine.Product))]
        public virtual ICollection<PurchaseReturnLine> PurchaseReturnLines { get; set; }
        [InverseProperty(nameof(QcparameterMapping.Product))]
        public virtual ICollection<QcparameterMapping> QcparameterMappings { get; set; }
        [InverseProperty(nameof(QuotationLine.Product))]
        public virtual ICollection<QuotationLine> QuotationLines { get; set; }
        [InverseProperty(nameof(RawMaterialPlanning.Product))]
        public virtual ICollection<RawMaterialPlanning> RawMaterialPlannings { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.Product))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SalesOrderLine.Product))]
        public virtual ICollection<SalesOrderLine> SalesOrderLines { get; set; }
        [InverseProperty(nameof(SalesReturnLine.Product))]
        public virtual ICollection<SalesReturnLine> SalesReturnLines { get; set; }
        [InverseProperty(nameof(SalesServiceOrderLine.Product))]
        public virtual ICollection<SalesServiceOrderLine> SalesServiceOrderLines { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsProductionPlanSummary.Product))]
        public virtual ICollection<SemiFinishedGoodsProductionPlanSummary> SemiFinishedGoodsProductionPlanSummaries { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsProductionPlan.Product))]
        public virtual ICollection<SemiFinishedGoodsProductionPlan> SemiFinishedGoodsProductionPlans { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsQcscanning.Product))]
        public virtual ICollection<SemiFinishedGoodsQcscanning> SemiFinishedGoodsQcscannings { get; set; }
        [InverseProperty(nameof(ServiceInquiryLine.Product))]
        public virtual ICollection<ServiceInquiryLine> ServiceInquiryLines { get; set; }
        [InverseProperty(nameof(ServiceInvoiceLine.Product))]
        public virtual ICollection<ServiceInvoiceLine> ServiceInvoiceLines { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlLine.Product))]
        public virtual ICollection<ShipmentQualityControlLine> ShipmentQualityControlLines { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturnLine.Product))]
        public virtual ICollection<ShipmentQualityControlReturnLine> ShipmentQualityControlReturnLines { get; set; }
        [InverseProperty(nameof(StockAdjustmentLine.Product))]
        public virtual ICollection<StockAdjustmentLine> StockAdjustmentLines { get; set; }
        [InverseProperty(nameof(StockTakeLine.Product))]
        public virtual ICollection<StockTakeLine> StockTakeLines { get; set; }
        [InverseProperty(nameof(StockTransferLine.Product))]
        public virtual ICollection<StockTransferLine> StockTransferLines { get; set; }
        [InverseProperty(nameof(StockTransferReceiptLine.Product))]
        public virtual ICollection<StockTransferReceiptLine> StockTransferReceiptLines { get; set; }
        [InverseProperty(nameof(SubContractOrderLine.Product))]
        public virtual ICollection<SubContractOrderLine> SubContractOrderLines { get; set; }
        [InverseProperty(nameof(SubContractOrder.Product))]
        public virtual ICollection<SubContractOrder> SubContractOrders { get; set; }
        [InverseProperty(nameof(SupplierProduct.Product))]
        public virtual ICollection<SupplierProduct> SupplierProducts { get; set; }
        [InverseProperty(nameof(TouchScreenHistory.Product))]
        public virtual ICollection<TouchScreenHistory> TouchScreenHistories { get; set; }
        [InverseProperty(nameof(TxnLineHistory.Product))]
        public virtual ICollection<TxnLineHistory> TxnLineHistories { get; set; }
        [InverseProperty(nameof(TyreSpecification.Product))]
        public virtual ICollection<TyreSpecification> TyreSpecifications { get; set; }
        [InverseProperty(nameof(WarehouseProduct.Product))]
        public virtual ICollection<WarehouseProduct> WarehouseProducts { get; set; }
        [InverseProperty(nameof(WarehouseStockIssue.Product))]
        public virtual ICollection<WarehouseStockIssue> WarehouseStockIssues { get; set; }
        [InverseProperty(nameof(WarehouseStockQualityControl.Product))]
        public virtual ICollection<WarehouseStockQualityControl> WarehouseStockQualityControls { get; set; }
    }
}
