﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Chart_Of_Account")]
    public partial class IChartOfAccount
    {
        public double? Category { get; set; }
        [StringLength(255)]
        public string AccountCode { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        [StringLength(255)]
        public string Type { get; set; }
        [StringLength(255)]
        public string ParentCode { get; set; }
    }
}
