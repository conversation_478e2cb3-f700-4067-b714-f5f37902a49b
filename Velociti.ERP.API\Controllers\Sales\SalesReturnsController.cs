﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Microsoft.Extensions.Options;
using Velociti.ERP.Domain.Utils;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [ApiController]
    public class SalesReturnsController : ControllerBase
    {
        private readonly AppSettings _appSettings;
        private readonly ISalesReturnsService _salesReturnsService;
        private readonly IShipmentQualityControlService _shipmentQualityControlServicee;

        public SalesReturnsController(IOptions<AppSettings> appSettings, ISalesReturnsService salesReturnsService, IShipmentQualityControlService shipmentQualityControlService)
        {
            _appSettings = appSettings.Value;
            _salesReturnsService = salesReturnsService;
            _shipmentQualityControlServicee = shipmentQualityControlService;
        }
        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]SalesReturn salesReturn)
        {
            await _salesReturnsService.SaveAsync(salesReturn);

            return Ok();
        }
        [HttpGet]
        [Route("Single/{salesReturnrId}")]
        public async Task<IActionResult> GetById(int salesReturnrId)
        {

            var result = await _salesReturnsService.GetByIdAsync(salesReturnrId);
            return Ok(result);

        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, int returnType, byte orderTypeEnum)
        {
            return Ok(await _salesReturnsService.GetAllAsync(companyId, userId, startDate, endDate, returnType,(int)Module.DocumentType.SalesReturn));
        }

        [HttpGet]
        [Route("LedgerLines/{salesReturnId}/Company/{companyId}/Customer/{customerId}/Currency/{currencyId}")]
        public async Task<IActionResult> GetLedgerLinesAsync(int salesReturnId, int companyId, int customerId, int currencyId)
        {
            return Ok(await _salesReturnsService.GetLedgerLinesAsync(salesReturnId, companyId, customerId, currencyId));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesReturn salesReturn)
        {
            int id = 0;
            switch (salesReturn.Action)
            {
                case "submit":
                    await _salesReturnsService.SubmitAsync(salesReturn);
                    break;
                case "cancel":
                    await _salesReturnsService.CancelAsync(salesReturn);
                    break;
                case "convertToGRN":
                   id = await _salesReturnsService.ConvertToGRNAsync(salesReturn.SalesReturnId,salesReturn.ModifiedUserId.Value, salesReturn.Client);
                    //if(salesReturn.TypeEnum == 2 && _appSettings.Client.Equals("Nemsui"))
                    //    await _shipmentQualityControlServicee.SubmitAsync(id, salesReturn.ModifiedUserId.Value, _appSettings.Client);
                    break;
            }

            return Ok(id);
        }
    }
}