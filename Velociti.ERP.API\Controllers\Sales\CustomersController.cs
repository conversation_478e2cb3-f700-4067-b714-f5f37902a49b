﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class CustomersController : ControllerBase
    {
        private readonly ICustomerService _customerService;

        public CustomersController(ICustomerService customerService)
        {
            _customerService = customerService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> Get(int companyId)
        {
            return Ok(await _customerService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("Single/{customerId}")]
        public async Task<IActionResult> FindById(int customerId)
        {
            var list = await _customerService.FindByIdAsync(customerId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _customerService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortListWithPaging")]
        public IActionResult GetShortListWithPaging(int companyId, string searchBy, int page, int take)
        {
            int totalRecordCount = 0;
            int skip = (page - 1) * take;
            var records = _customerService.GetShortListWithPagingAsync(companyId, searchBy, skip, take, ref totalRecordCount);

            var hasMoreRecords = totalRecordCount > records.Count();
            var dto = new
            {
                results = records,
                pagination = hasMoreRecords
            };

            return Ok(dto);
        }

        [HttpGet]
        [Route("SubContractorShortList/Company/{companyId}")]
        public async Task<IActionResult> GetSubContractorShortList(int companyId)
        {
            return Ok(await _customerService.GetSubContractorShortListAsync(companyId));
        }

        [HttpGet]
        [Route("Single/SearchParam/{searchParam}")]
        public async Task<IActionResult> FindBySearchParam(string searchParam)
        {
            var list = await _customerService.FindBySearchParamAsync(searchParam);

            return Ok(list);
        }

        [HttpDelete]
        [Route("{customerId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int customerId, int userId)
        {
            await _customerService.ToggleActivationAsync(customerId, userId);

            return Ok();
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Category/{customerCategoryEnum}/Type/{customerTypeId}/Group/{customerGroupId}")]
        public async Task<IActionResult> GetShortList(int companyId, int customerCategoryEnum, int customerTypeId, int customerGroupId)
        {
            return Ok(await _customerService.GetShortListAsync(companyId, customerCategoryEnum, customerTypeId, customerGroupId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Customer customer)
        {
            if (customer == null)
                return BadRequest();

            await _customerService.SaveAsync(customer);

            return Ok();
        }
    }
}