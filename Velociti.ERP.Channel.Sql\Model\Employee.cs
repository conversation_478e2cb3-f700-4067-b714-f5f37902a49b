﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Employees", Schema = "hr")]
    public partial class Employee
    {
        public Employee()
        {
            Accounts = new HashSet<Account>();
            Attendances = new HashSet<Attendance>();
            EmployeeAccounts = new HashSet<EmployeeAccount>();
            EmployeeContacts = new HashSet<EmployeeContact>();
            EmployeeIncrements = new HashSet<EmployeeIncrement>();
            EmployeeLeaves = new HashSet<EmployeeLeaf>();
            FixedAssetMaintenances = new HashSet<FixedAssetMaintenance>();
            FixedAssets = new HashSet<FixedAsset>();
            GeneralLedgerLines = new HashSet<GeneralLedgerLine>();
            InboundReceipts = new HashSet<InboundReceipt>();
            InternalDispatches = new HashSet<InternalDispatch>();
            InternalOrders = new HashSet<InternalOrder>();
            InternalReturns = new HashSet<InternalReturn>();
            InternalServiceInvoices = new HashSet<InternalServiceInvoice>();
            LeaveApplicationFormApplyingEmployees = new HashSet<LeaveApplicationForm>();
            LeaveApplicationFormCoveringEmployees = new HashSet<LeaveApplicationForm>();
            MaterialRequisitionNotes = new HashSet<MaterialRequisitionNote>();
            OutboundPayments = new HashSet<OutboundPayment>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            PettyCashes = new HashSet<PettyCash>();
            ProductionOrderLines = new HashSet<ProductionOrderLine>();
            PurchaseOrders = new HashSet<PurchaseOrder>();
            PurchaseRequisitionNotes = new HashSet<PurchaseRequisitionNote>();
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SalesInvoiceRecommendedPersons = new HashSet<SalesInvoice>();
            SalesInvoiceSalesRepEmployees = new HashSet<SalesInvoice>();
            SalesOrders = new HashSet<SalesOrder>();
            SalesServiceOrderLines = new HashSet<SalesServiceOrderLine>();
            ServiceInquiryLines = new HashSet<ServiceInquiryLine>();
            ServiceInvoices = new HashSet<ServiceInvoice>();
            UserCompanies = new HashSet<UserCompany>();
            WorkstationCadres = new HashSet<WorkstationCadre>();
        }

        [Key]
        public int EmployeeId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        [StringLength(50)]
        public string EmployeeCode { get; set; }
        [Column("EPFNumber")]
        [StringLength(50)]
        public string Epfnumber { get; set; }
        [StringLength(255)]
        public string FullName { get; set; }
        [StringLength(255)]
        public string NameWithInitials { get; set; }
        [Column("NICNumber")]
        [StringLength(15)]
        public string Nicnumber { get; set; }
        [StringLength(15)]
        public string PassportNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DateOfBirth { get; set; }
        public byte? GenderEnum { get; set; }
        [StringLength(255)]
        public string Address { get; set; }
        [StringLength(15)]
        public string Telephone { get; set; }
        [StringLength(15)]
        public string Mobile { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DateOfEmployment { get; set; }
        public int? EmployeeGroupId { get; set; }
        public byte? EmployeeCategoryEnum { get; set; }
        public byte? RelationToProductionEnum { get; set; }
        public int? DesignationId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DateOfPermanency { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DateOfResignation { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossSalary { get; set; }
        public byte? ShiftEnum { get; set; }
        [Column(TypeName = "money")]
        public decimal? AvgHourlyRate { get; set; }
        public bool? IsDefault { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Employees")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("Employees")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(DesignationId))]
        [InverseProperty("Employees")]
        public virtual Designation Designation { get; set; }
        [InverseProperty(nameof(Account.Employee))]
        public virtual ICollection<Account> Accounts { get; set; }
        [InverseProperty(nameof(Attendance.Employee))]
        public virtual ICollection<Attendance> Attendances { get; set; }
        [InverseProperty(nameof(EmployeeAccount.Employee))]
        public virtual ICollection<EmployeeAccount> EmployeeAccounts { get; set; }
        [InverseProperty(nameof(EmployeeContact.Employee))]
        public virtual ICollection<EmployeeContact> EmployeeContacts { get; set; }
        [InverseProperty(nameof(EmployeeIncrement.Employee))]
        public virtual ICollection<EmployeeIncrement> EmployeeIncrements { get; set; }
        [InverseProperty(nameof(EmployeeLeaf.Employee))]
        public virtual ICollection<EmployeeLeaf> EmployeeLeaves { get; set; }
        [InverseProperty(nameof(FixedAssetMaintenance.Employee))]
        public virtual ICollection<FixedAssetMaintenance> FixedAssetMaintenances { get; set; }
        [InverseProperty(nameof(FixedAsset.Employee))]
        public virtual ICollection<FixedAsset> FixedAssets { get; set; }
        [InverseProperty(nameof(GeneralLedgerLine.Employee))]
        public virtual ICollection<GeneralLedgerLine> GeneralLedgerLines { get; set; }
        [InverseProperty(nameof(InboundReceipt.Employee))]
        public virtual ICollection<InboundReceipt> InboundReceipts { get; set; }
        [InverseProperty(nameof(InternalDispatch.Employee))]
        public virtual ICollection<InternalDispatch> InternalDispatches { get; set; }
        [InverseProperty(nameof(InternalOrder.Employee))]
        public virtual ICollection<InternalOrder> InternalOrders { get; set; }
        [InverseProperty(nameof(InternalReturn.Employee))]
        public virtual ICollection<InternalReturn> InternalReturns { get; set; }
        [InverseProperty(nameof(InternalServiceInvoice.Employee))]
        public virtual ICollection<InternalServiceInvoice> InternalServiceInvoices { get; set; }
        [InverseProperty(nameof(LeaveApplicationForm.ApplyingEmployee))]
        public virtual ICollection<LeaveApplicationForm> LeaveApplicationFormApplyingEmployees { get; set; }
        [InverseProperty(nameof(LeaveApplicationForm.CoveringEmployee))]
        public virtual ICollection<LeaveApplicationForm> LeaveApplicationFormCoveringEmployees { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNote.Employee))]
        public virtual ICollection<MaterialRequisitionNote> MaterialRequisitionNotes { get; set; }
        [InverseProperty(nameof(OutboundPayment.Employee))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
        [InverseProperty(nameof(PaymentAdvice.Employee))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(PettyCash.Employee))]
        public virtual ICollection<PettyCash> PettyCashes { get; set; }
        [InverseProperty(nameof(ProductionOrderLine.Employee))]
        public virtual ICollection<ProductionOrderLine> ProductionOrderLines { get; set; }
        [InverseProperty(nameof(PurchaseOrder.Employee))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        [InverseProperty(nameof(PurchaseRequisitionNote.Employee))]
        public virtual ICollection<PurchaseRequisitionNote> PurchaseRequisitionNotes { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.Therapist))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SalesInvoice.RecommendedPerson))]
        public virtual ICollection<SalesInvoice> SalesInvoiceRecommendedPersons { get; set; }
        [InverseProperty(nameof(SalesInvoice.SalesRepEmployee))]
        public virtual ICollection<SalesInvoice> SalesInvoiceSalesRepEmployees { get; set; }
        [InverseProperty(nameof(SalesOrder.SalesRepEmployee))]
        public virtual ICollection<SalesOrder> SalesOrders { get; set; }
        [InverseProperty(nameof(SalesServiceOrderLine.Therapist))]
        public virtual ICollection<SalesServiceOrderLine> SalesServiceOrderLines { get; set; }
        [InverseProperty(nameof(ServiceInquiryLine.Therapist))]
        public virtual ICollection<ServiceInquiryLine> ServiceInquiryLines { get; set; }
        [InverseProperty(nameof(ServiceInvoice.Employee))]
        public virtual ICollection<ServiceInvoice> ServiceInvoices { get; set; }
        [InverseProperty(nameof(UserCompany.Employee))]
        public virtual ICollection<UserCompany> UserCompanies { get; set; }
        [InverseProperty(nameof(WorkstationCadre.Employee))]
        public virtual ICollection<WorkstationCadre> WorkstationCadres { get; set; }
    }
}
