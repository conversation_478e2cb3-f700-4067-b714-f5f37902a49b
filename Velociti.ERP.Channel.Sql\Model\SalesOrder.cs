﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesOrders", Schema = "sales")]
    public partial class SalesOrder
    {
        public SalesOrder()
        {
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            PackingLists = new HashSet<PackingList>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            SalesInvoices = new HashSet<SalesInvoice>();
            SalesOrderLines = new HashSet<SalesOrderLine>();
        }

        [Key]
        public int SalesOrderId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? CustomerId { get; set; }
        [StringLength(255)]
        public string PayingParty { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        public int? DispatchWarehouseId { get; set; }
        public int? SalesRepEmployeeId { get; set; }
        public int? PriceListId { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ExpectedDispatchDate { get; set; }
        public int? PaymentTermId { get; set; }
        [StringLength(255)]
        public string DestinationPort { get; set; }
        public int? LeadDays { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        public byte? ShippingModeEnum { get; set; }
        public byte? ShippingTermEnum { get; set; }
        public int? ShipmentPackingMethodId { get; set; }
        public int? TxnWorkFlowId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [StringLength(1000)]
        public string StatusReason { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public byte? OrderTypeEnum { get; set; }
        public int? TaxApplicabilityId { get; set; }
        [StringLength(50)]
        public string ReferenceNumber { get; set; }
        public bool? IsConsignment { get; set; }
        public int? TempId { get; set; }
        [Column("SFA_PO_DocNumber")]
        [StringLength(50)]
        public string SfaPoDocNumber { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SalesOrders")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("SalesOrders")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("SalesOrders")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DispatchWarehouseId))]
        [InverseProperty(nameof(Warehous.SalesOrders))]
        public virtual Warehous DispatchWarehouse { get; set; }
        [ForeignKey(nameof(PriceListId))]
        [InverseProperty(nameof(SupportData.SalesOrders))]
        public virtual SupportData PriceList { get; set; }
        [ForeignKey(nameof(SalesRepEmployeeId))]
        [InverseProperty(nameof(Employee.SalesOrders))]
        public virtual Employee SalesRepEmployee { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("SalesOrders")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.SalesOrder))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(PackingList.SalesOrder))]
        public virtual ICollection<PackingList> PackingLists { get; set; }
        [InverseProperty(nameof(PaymentAdvice.SalesOrder))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(SalesInvoice.SalesOrder))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
        [InverseProperty(nameof(SalesOrderLine.SalesOrder))]
        public virtual ICollection<SalesOrderLine> SalesOrderLines { get; set; }
    }
}
