﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ShipmentCosts", Schema = "inv")]
    public partial class ShipmentCost
    {
        public ShipmentCost()
        {
            CostingWarehous = new HashSet<CostingWarehous>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            ShipmentCostLines = new HashSet<ShipmentCostLine>();
        }

        [Key]
        public int ShipmentCostId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [Required]
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public int? ShipmentQualityControlId { get; set; }
        public int? CompanyId { get; set; }
        public byte? StatusEnum { get; set; }
        public byte? ApportionmentEnum { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalAllocatedCharges { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalUnAllocatedCharges { get; set; }
        [StringLength(50)]
        public string ReferenceNumber { get; set; }
        public byte? AssetTypeEnum { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ShipmentCosts")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("ShipmentCosts")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(ShipmentQualityControlId))]
        [InverseProperty("ShipmentCosts")]
        public virtual ShipmentQualityControl ShipmentQualityControl { get; set; }
        [InverseProperty("ShipmentCost")]
        public virtual ICollection<CostingWarehous> CostingWarehous { get; set; }
        [InverseProperty(nameof(PaymentAdvice.ShipmentCost))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(ShipmentCostLine.ShipmentCost))]
        public virtual ICollection<ShipmentCostLine> ShipmentCostLines { get; set; }
    }
}
