﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalDispatchLineDetails", Schema = "inv")]
    public partial class InternalDispatchLineDetail
    {
        [Key]
        public int InternalDispatchLineDetailId { get; set; }
        public int? InternalDispatchLineId { get; set; }
        public int? ProductId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public int? QuantityInBase { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(InternalDispatchLineId))]
        [InverseProperty("InternalDispatchLineDetails")]
        public virtual InternalDispatchLine InternalDispatchLine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("InternalDispatchLineDetails")]
        public virtual Product Product { get; set; }
    }
}
