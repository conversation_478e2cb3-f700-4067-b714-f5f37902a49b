﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("HOReturnStockTransferLines", Schema = "inv")]
    public partial class HoreturnStockTransferLine
    {
        public HoreturnStockTransferLine()
        {
            HoreturnStockTransferDetails = new HashSet<HoreturnStockTransferDetail>();
        }

        [Key]
        [Column("HOReturnStockTransferLineId")]
        public int HoreturnStockTransferLineId { get; set; }
        [Column("HOReturnStockTransferId")]
        public int? HoreturnStockTransferId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.HoreturnStockTransferLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.HoreturnStockTransferLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(HoreturnStockTransferId))]
        [InverseProperty("HoreturnStockTransferLines")]
        public virtual HoreturnStockTransfer HoreturnStockTransfer { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("HoreturnStockTransferLines")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferDetail.HoreturnStockTransferLine))]
        public virtual ICollection<HoreturnStockTransferDetail> HoreturnStockTransferDetails { get; set; }
    }
}
