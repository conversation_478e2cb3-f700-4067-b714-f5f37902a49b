﻿using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using OfficeOpenXml;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class RawMaterialPlanningsController : ControllerBase  
    {
        private readonly IRawMaterialPlanningService _rawMaterialPlanningService;

        public RawMaterialPlanningsController(IRawMaterialPlanningService rawMaterialPlanningService)
        {
            _rawMaterialPlanningService = rawMaterialPlanningService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _rawMaterialPlanningService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("UploadedLines")]
        public async Task<IActionResult> GetUploadedLines(int userId)
        {
            return Ok(await _rawMaterialPlanningService.GetAllUploadedProductsAsync(userId));
        }

        [HttpPost]
        [Route("Upload")]
        public IActionResult UploadSave([FromBody]List<RawMaterialPlanning> rawMaterialPlanningsList)
        {
            if (rawMaterialPlanningsList == null)
                return BadRequest();

            _rawMaterialPlanningService.UploadSaveAsync(rawMaterialPlanningsList);

            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Save(int companyId, bool isMonth, DateTime date, int modifiedUserId)
        {
            await _rawMaterialPlanningService.SaveAsync(companyId, isMonth, date, modifiedUserId);

            return Ok();
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> GetActiveAllProducts(int companyId)
        {
            return Ok(await _rawMaterialPlanningService.GetActiveProductsAsync(companyId));
        }
    }
}