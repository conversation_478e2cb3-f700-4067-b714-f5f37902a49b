﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class ServiceOrderJobController : ControllerBase
    {
        private readonly ISalesServiceOrderJobService _salesServiceOrderJobService;

        public ServiceOrderJobController(ISalesServiceOrderJobService salesServiceOrderJobService)
        {
            _salesServiceOrderJobService = salesServiceOrderJobService;
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]SalesServiceOrderJob salesServiceOrderJob)
        {
            switch (salesServiceOrderJob.Action)
            {
                case "generate": return Ok(await _salesServiceOrderJobService.GenerateSalesServiceOrderJobAsync(salesServiceOrderJob));
                case "save": await _salesServiceOrderJobService.SaveAsync(salesServiceOrderJob); break;
            }

            return Ok();
        }

        [HttpGet]
        [Route("Single/{salesServiceOrderJobId}")]
        public async Task<IActionResult> FindById(int salesServiceOrderJobId)
        {
            return Ok(await _salesServiceOrderJobService.FindByIdAsync(salesServiceOrderJobId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesServiceOrderJobService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesServiceOrderJobService.GetForApprovalAsync(companyId, userId, startDate, endDate));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesServiceOrderJob salesServiceOrderJob)
        {
            switch (salesServiceOrderJob.Action)
            {
                case "submit": await _salesServiceOrderJobService.SubmitAsync(salesServiceOrderJob.SalesServiceOrderJobId, salesServiceOrderJob.ModifiedUserId.Value, (int)salesServiceOrderJob.CompanyId); break;
                case "send for approval": await _salesServiceOrderJobService.SendForApprovalAsync(salesServiceOrderJob.SalesServiceOrderJobId, (int)salesServiceOrderJob.CompanyId, salesServiceOrderJob.ModifiedUserId.Value); break;
                case "update status": await _salesServiceOrderJobService.UpdateStatusAsync(salesServiceOrderJob.SalesServiceOrderJobId, salesServiceOrderJob.StatusEnum.Value, salesServiceOrderJob.ModifiedUserId.Value); break;
                case "update work flow": await _salesServiceOrderJobService.UpdateWorkFlowStatusAsync(salesServiceOrderJob); break;
                case "reverse": await _salesServiceOrderJobService.ReverseAsync(salesServiceOrderJob.SalesServiceOrderJobId, (int)salesServiceOrderJob.CompanyId, salesServiceOrderJob.ModifiedUserId.Value); break;

            }

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/User/{userId}")]
        public async Task<IActionResult> Cancel(int id, int userId)
        {
            await _salesServiceOrderJobService.CancelAsync(id, userId);

            return Ok();
        }
    }
}