﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GeneralLedgerSettlementLines", Schema = "fin")]
    public partial class GeneralLedgerSettlementLine
    {
        [Key]
        public int GeneralLedgerSettlementLineId { get; set; }
        public int GeneralLedgerSettlementId { get; set; }
        public int GeneralLedgerLineId { get; set; }
        [Column(TypeName = "money")]
        public decimal? BalanceAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? AppliedAmount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifiedDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(GeneralLedgerLineId))]
        [InverseProperty("GeneralLedgerSettlementLines")]
        public virtual GeneralLedgerLine GeneralLedgerLine { get; set; }
        [ForeignKey(nameof(GeneralLedgerSettlementId))]
        [InverseProperty("GeneralLedgerSettlementLines")]
        public virtual GeneralLedgerSettlement GeneralLedgerSettlement { get; set; }
    }
}
