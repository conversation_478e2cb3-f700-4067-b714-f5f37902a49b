﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Procurement;
using static Velociti.ERP.Domain.Entities.Administration.Module;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class PurchaseReturnsController : ControllerBase
    {
        private readonly IPurchaseReturnService _purchaseReturnService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public PurchaseReturnsController(IPurchaseReturnService purchaseReturnService, IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _purchaseReturnService = purchaseReturnService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _purchaseReturnService.GetAllAsync(companyId, startDate, endDate));
        }

        [HttpGet]
        [Route("Single/{purchaseReturnId}")]
        public async Task<IActionResult> GetById(int purchaseReturnId)
        {
            return Ok(await _purchaseReturnService.GetByIdAsync(purchaseReturnId));
        }

        [HttpGet]
        [Route("Company/{companyId}/GRN/{grnId}")]
        public async Task<IActionResult> GetByGRN(int companyId, int grnId)
        {
            return Ok(await _purchaseReturnService.GetByGRNIdAsync(grnId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]PurchaseReturn purchaseReturn)
        {
            await _purchaseReturnService.SaveAsync(purchaseReturn);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]PurchaseReturn purchaseReturn)
        {
            switch (purchaseReturn.Action)
            {
                case "submit":
                    await _purchaseReturnService.SubmitAsync(purchaseReturn);
                    break;
                case "reverse":
                    await _purchaseReturnService.ReverseAsync(purchaseReturn);
                    break;
                case "cancel":
                    await _purchaseReturnService.CancelAsync(purchaseReturn);
                    break;
                case "convert":
                    return Ok(await _goodsDispatchNoteService.ConvertAsync(purchaseReturn.PurchaseReturnId, DocumentType.PurchaseReturn, purchaseReturn.ModifiedUserId));
            }

            return Ok();
        }
    }
}