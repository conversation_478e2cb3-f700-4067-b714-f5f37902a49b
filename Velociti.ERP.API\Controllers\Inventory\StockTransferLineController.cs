﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class StockTransferLineController : ControllerBase
    {
        private readonly IStockTransferLineService _stockTransferLineService;
        public StockTransferLineController(IStockTransferLineService stockTransferLineService)
        {
            this._stockTransferLineService = stockTransferLineService;

        }
        [HttpGet]
        [Route("{stockTransferId}/company/{defaultCompanyId}")]
        public async Task<IActionResult> Get(int stockTransferId, int defaultCompanyId)
        {
            return Ok(await _stockTransferLineService.GetAsync(stockTransferId, defaultCompanyId));
        }

        [HttpGet]
        [Route("Details/{stockTransferLineId}")]
        public async Task<IActionResult> LineDetails(int stockTransferLineId)
        {
            return Ok(await _stockTransferLineService.GetLineDetailsAsync(stockTransferLineId));
        }

        [HttpGet]
        [Route("Products/{stockTransferId}")]
        public async Task<IActionResult> LoadProducts(int stockTransferId)
        {
            return Ok(await _stockTransferLineService.LoadProducts(stockTransferId));
        }
    }
}