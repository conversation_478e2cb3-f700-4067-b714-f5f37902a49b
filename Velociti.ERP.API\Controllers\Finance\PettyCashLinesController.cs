﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class PettyCashLinesController : ControllerBase
    {
        private readonly IPettyCashLineService _pettyCashLineService;

        public PettyCashLinesController(IPettyCashLineService pettyCashLineService)
        {
            _pettyCashLineService = pettyCashLineService;
        }

        [HttpGet]
        [Route("{pettyCashId}")]
        public async Task<IActionResult> GetByHeaderIdAsync(int pettyCashId)
        {
            return Ok(await _pettyCashLineService.GetByHeaderIdAsync(pettyCashId));
        }
    }
}