﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class OutboundPaymentLinesController : ControllerBase
    {
        private readonly IOutboundPaymentLineService _outboundPaymentLineService;

        public OutboundPaymentLinesController(IOutboundPaymentLineService outboundPaymentLineService)
        {
            _outboundPaymentLineService = outboundPaymentLineService;
        }

        [HttpGet]
        [Route("{outboundPaymentId}")]
        public async Task<IActionResult> GetByHeader(int outboundPaymentId)
        {
            return Ok(await _outboundPaymentLineService.GetByHeaderIdAsync(outboundPaymentId));
        }

        [HttpGet]
        [Route("SettlementDocuments/{outboundPaymentId}")]
        public async Task<IActionResult> GetSettlementDocumentsAsync(int outboundPaymentId)
        {
            return Ok(await _outboundPaymentLineService.GetSettlementDocumentsAsync(outboundPaymentId));
        }

        [HttpGet]
        [Route("{outboundPaymentId}/Supplier/{supplierId}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetSupplierPaymentVoucherLines(int outboundPaymentId,int supplierId, int currencyId, int companyId)
        {
            return Ok(await _outboundPaymentLineService.GetSupplierPaymentVoucherLinesAsync(outboundPaymentId, supplierId, currencyId, companyId));
        }

        //[HttpGet]
        //[Route("{outboundPaymentId}/Supplier/{supplierId}/IOU/{iOUId}/Currency/{currencyId}/Company/{companyId}")]
        //public async Task<IActionResult> GetIOUPaymentVoucherLines(int outboundPaymentId, int supplierId, int iOUId, int currencyId, int companyId)
        //{
        //    return Ok(await _outboundPaymentLineService.GetIOUPaymentVoucherLinesAsync(outboundPaymentId, supplierId, iOUId, currencyId, companyId));
        //}

        [HttpGet]
        [Route("{outboundPaymentId}/Customer/{customerId}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetCustomerPaymentVoucherLines(int outboundPaymentId,int customerId, int currencyId, int companyId)
        {
            return Ok(await _outboundPaymentLineService.GetCustomerPaymentVoucherLinesAsync(outboundPaymentId, customerId, currencyId, companyId));
        }

        [HttpGet]
        [Route("{outboundPaymentId}/Employee/{employeeId}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetEmployeePaymentVoucherLines(int outboundPaymentId,int employeeId, int currencyId, int companyId)
        {
            return Ok(await _outboundPaymentLineService.GetEmployeePaymentVoucherLinesAsync(outboundPaymentId, employeeId, currencyId, companyId));
        }

        [HttpGet]
        [Route("{outboundPaymentId}/Type/{typeEnum}/Account/{accountId}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetPaymentVoucherLines(int outboundPaymentId, byte typeEnum, int accountId, int currencyId, int companyId)
        {
            return Ok(await _outboundPaymentLineService.GetPaymentVoucherLinesByTypeAsync(outboundPaymentId, accountId, currencyId, typeEnum, companyId));
        }
    }
}