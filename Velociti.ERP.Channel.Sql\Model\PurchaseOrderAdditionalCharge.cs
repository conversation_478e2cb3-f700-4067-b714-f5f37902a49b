﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseOrderAdditionalCharges", Schema = "prc")]
    public partial class PurchaseOrderAdditionalCharge
    {
        public PurchaseOrderAdditionalCharge()
        {
            PurchaseOrderAdditionalChargeLines = new HashSet<PurchaseOrderAdditionalChargeLine>();
        }

        [Key]
        public int PurchaseOrderAdditionalChargeId { get; set; }
        public int? PurchaseOrderId { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "money")]
        public decimal? ExchangeRate { get; set; }
        public byte? ApportionmentEnum { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalCharges { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalAllocatedCharges { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalUnAllocatedCharges { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("PurchaseOrderAdditionalCharges")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(PurchaseOrderId))]
        [InverseProperty("PurchaseOrderAdditionalCharges")]
        public virtual PurchaseOrder PurchaseOrder { get; set; }
        [InverseProperty(nameof(PurchaseOrderAdditionalChargeLine.PurchaseOrderAdditionalCharge))]
        public virtual ICollection<PurchaseOrderAdditionalChargeLine> PurchaseOrderAdditionalChargeLines { get; set; }
    }
}
