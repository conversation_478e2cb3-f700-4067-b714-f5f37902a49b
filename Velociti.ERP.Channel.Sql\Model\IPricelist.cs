﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Pricelist")]
    public partial class IPricelist
    {
        [StringLength(255)]
        public string PriceListName { get; set; }
        [StringLength(255)]
        public string ProductCode { get; set; }
        public double? Price { get; set; }
    }
}
