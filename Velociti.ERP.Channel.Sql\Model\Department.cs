﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Departments", Schema = "adm")]
    public partial class Department
    {
        public Department()
        {
            BankAdjustmentCostAllocations = new HashSet<BankAdjustmentCostAllocation>();
            BankAdjustments = new HashSet<BankAdjustment>();
            ConsignmentStockTransfers = new HashSet<ConsignmentStockTransfer>();
            CustomerPayments = new HashSet<CustomerPayment>();
            Divisions = new HashSet<Division>();
            Employees = new HashSet<Employee>();
            ExchangeOrders = new HashSet<ExchangeOrder>();
            FixedAssets = new HashSet<FixedAsset>();
            GeneralLedgerLines = new HashSet<GeneralLedgerLine>();
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            GoodsReceiveNotes = new HashSet<GoodsReceiveNote>();
            HoreturnStockTransfers = new HashSet<HoreturnStockTransfer>();
            InboundReceiptCostAllocations = new HashSet<InboundReceiptCostAllocation>();
            InboundReceipts = new HashSet<InboundReceipt>();
            InternalDispatches = new HashSet<InternalDispatch>();
            InternalOrders = new HashSet<InternalOrder>();
            InternalReturns = new HashSet<InternalReturn>();
            InternalServiceInvoices = new HashSet<InternalServiceInvoice>();
            LoanOrders = new HashSet<LoanOrder>();
            ManualJournalCostAllocations = new HashSet<ManualJournalCostAllocation>();
            ManualJournals = new HashSet<ManualJournal>();
            MaterialRequisitionNotes = new HashSet<MaterialRequisitionNote>();
            OutboundPaymentCostAllocations = new HashSet<OutboundPaymentCostAllocation>();
            OutboundPayments = new HashSet<OutboundPayment>();
            PaymentAdviceCostAllocations = new HashSet<PaymentAdviceCostAllocation>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            PettyCashCostAllocations = new HashSet<PettyCashCostAllocation>();
            PettyCashes = new HashSet<PettyCash>();
            PurchaseOrders = new HashSet<PurchaseOrder>();
            PurchaseRequisitionNotes = new HashSet<PurchaseRequisitionNote>();
            Quotations = new HashSet<Quotation>();
            SalesInvoices = new HashSet<SalesInvoice>();
            SalesReturns = new HashSet<SalesReturn>();
            SalesServiceOrders = new HashSet<SalesServiceOrder>();
            ServiceInquiries = new HashSet<ServiceInquiry>();
            ShipmentCosts = new HashSet<ShipmentCost>();
            ShipmentQualityControlReturns = new HashSet<ShipmentQualityControlReturn>();
            ShipmentQualityControls = new HashSet<ShipmentQualityControl>();
            StockAdjustments = new HashSet<StockAdjustment>();
            StockCountSheets = new HashSet<StockCountSheet>();
            StockTransferReceipts = new HashSet<StockTransferReceipt>();
            StockTransfers = new HashSet<StockTransfer>();
            SubContractOrders = new HashSet<SubContractOrder>();
            UserDepartments = new HashSet<UserDepartment>();
        }

        [Key]
        public int DepartmentId { get; set; }
        public int CompanyId { get; set; }
        [StringLength(50)]
        public string DepaortmentCode { get; set; }
        [StringLength(255)]
        public string DepartmentName { get; set; }
        [StringLength(20)]
        public string Telephone { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string Website { get; set; }
        public int? CurrencyId { get; set; }
        public byte? OperationType { get; set; }
        public int? WarehouseId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Departments")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("Departments")]
        public virtual Currency Currency { get; set; }
        [InverseProperty(nameof(BankAdjustmentCostAllocation.Department))]
        public virtual ICollection<BankAdjustmentCostAllocation> BankAdjustmentCostAllocations { get; set; }
        [InverseProperty(nameof(BankAdjustment.Department))]
        public virtual ICollection<BankAdjustment> BankAdjustments { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransfer.Department))]
        public virtual ICollection<ConsignmentStockTransfer> ConsignmentStockTransfers { get; set; }
        [InverseProperty(nameof(CustomerPayment.Department))]
        public virtual ICollection<CustomerPayment> CustomerPayments { get; set; }
        [InverseProperty(nameof(Division.Department))]
        public virtual ICollection<Division> Divisions { get; set; }
        [InverseProperty(nameof(Employee.Department))]
        public virtual ICollection<Employee> Employees { get; set; }
        [InverseProperty(nameof(ExchangeOrder.Department))]
        public virtual ICollection<ExchangeOrder> ExchangeOrders { get; set; }
        [InverseProperty(nameof(FixedAsset.Department))]
        public virtual ICollection<FixedAsset> FixedAssets { get; set; }
        [InverseProperty(nameof(GeneralLedgerLine.Department))]
        public virtual ICollection<GeneralLedgerLine> GeneralLedgerLines { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.Department))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(GoodsReceiveNote.Department))]
        public virtual ICollection<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        [InverseProperty(nameof(HoreturnStockTransfer.Department))]
        public virtual ICollection<HoreturnStockTransfer> HoreturnStockTransfers { get; set; }
        [InverseProperty(nameof(InboundReceiptCostAllocation.Department))]
        public virtual ICollection<InboundReceiptCostAllocation> InboundReceiptCostAllocations { get; set; }
        [InverseProperty(nameof(InboundReceipt.Department))]
        public virtual ICollection<InboundReceipt> InboundReceipts { get; set; }
        [InverseProperty(nameof(InternalDispatch.Department))]
        public virtual ICollection<InternalDispatch> InternalDispatches { get; set; }
        [InverseProperty(nameof(InternalOrder.Department))]
        public virtual ICollection<InternalOrder> InternalOrders { get; set; }
        [InverseProperty(nameof(InternalReturn.Department))]
        public virtual ICollection<InternalReturn> InternalReturns { get; set; }
        [InverseProperty(nameof(InternalServiceInvoice.Department))]
        public virtual ICollection<InternalServiceInvoice> InternalServiceInvoices { get; set; }
        [InverseProperty(nameof(LoanOrder.Department))]
        public virtual ICollection<LoanOrder> LoanOrders { get; set; }
        [InverseProperty(nameof(ManualJournalCostAllocation.Department))]
        public virtual ICollection<ManualJournalCostAllocation> ManualJournalCostAllocations { get; set; }
        [InverseProperty(nameof(ManualJournal.Department))]
        public virtual ICollection<ManualJournal> ManualJournals { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNote.Department))]
        public virtual ICollection<MaterialRequisitionNote> MaterialRequisitionNotes { get; set; }
        [InverseProperty(nameof(OutboundPaymentCostAllocation.Department))]
        public virtual ICollection<OutboundPaymentCostAllocation> OutboundPaymentCostAllocations { get; set; }
        [InverseProperty(nameof(OutboundPayment.Department))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
        [InverseProperty(nameof(PaymentAdviceCostAllocation.Department))]
        public virtual ICollection<PaymentAdviceCostAllocation> PaymentAdviceCostAllocations { get; set; }
        [InverseProperty(nameof(PaymentAdvice.Department))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(PettyCashCostAllocation.Department))]
        public virtual ICollection<PettyCashCostAllocation> PettyCashCostAllocations { get; set; }
        [InverseProperty(nameof(PettyCash.Department))]
        public virtual ICollection<PettyCash> PettyCashes { get; set; }
        [InverseProperty(nameof(PurchaseOrder.Department))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        [InverseProperty(nameof(PurchaseRequisitionNote.Department))]
        public virtual ICollection<PurchaseRequisitionNote> PurchaseRequisitionNotes { get; set; }
        [InverseProperty(nameof(Quotation.Department))]
        public virtual ICollection<Quotation> Quotations { get; set; }
        [InverseProperty(nameof(SalesInvoice.Department))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
        [InverseProperty(nameof(SalesReturn.Department))]
        public virtual ICollection<SalesReturn> SalesReturns { get; set; }
        [InverseProperty(nameof(SalesServiceOrder.Department))]
        public virtual ICollection<SalesServiceOrder> SalesServiceOrders { get; set; }
        [InverseProperty(nameof(ServiceInquiry.Department))]
        public virtual ICollection<ServiceInquiry> ServiceInquiries { get; set; }
        [InverseProperty(nameof(ShipmentCost.Department))]
        public virtual ICollection<ShipmentCost> ShipmentCosts { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturn.Department))]
        public virtual ICollection<ShipmentQualityControlReturn> ShipmentQualityControlReturns { get; set; }
        [InverseProperty(nameof(ShipmentQualityControl.Department))]
        public virtual ICollection<ShipmentQualityControl> ShipmentQualityControls { get; set; }
        [InverseProperty(nameof(StockAdjustment.Department))]
        public virtual ICollection<StockAdjustment> StockAdjustments { get; set; }
        [InverseProperty(nameof(StockCountSheet.Department))]
        public virtual ICollection<StockCountSheet> StockCountSheets { get; set; }
        [InverseProperty(nameof(StockTransferReceipt.Department))]
        public virtual ICollection<StockTransferReceipt> StockTransferReceipts { get; set; }
        [InverseProperty(nameof(StockTransfer.Department))]
        public virtual ICollection<StockTransfer> StockTransfers { get; set; }
        [InverseProperty(nameof(SubContractOrder.Department))]
        public virtual ICollection<SubContractOrder> SubContractOrders { get; set; }
        [InverseProperty(nameof(UserDepartment.Department))]
        public virtual ICollection<UserDepartment> UserDepartments { get; set; }
    }
}
