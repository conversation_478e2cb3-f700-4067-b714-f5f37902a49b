﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Procurement;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesDesksController : ControllerBase  
    {
        private readonly ISalesDeskService _salesDeskService;  

        public SalesDesksController(ISalesDeskService salesDeskService)  
        {
            _salesDeskService = salesDeskService;
        }

        [HttpGet]
        [Route("Submitted/{companyId}/startDate/{startDate}/endDate/{endDate}")]
        public async Task<IActionResult> GetSubmitted(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesDeskService.GetSubmittedLinesAsync(companyId, startDate, endDate));
        }
    }
}