﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class CustomerContactsController : ControllerBase    
    {
        private readonly ICustomerContactService _customerContactService;    

        public CustomerContactsController(ICustomerContactService customerContactService)    
        {
            _customerContactService = customerContactService;
        }

        [HttpGet]
        [Route("{customerId}")]
        public async Task<IActionResult> Get(int customerId)  
        {
            return Ok(await _customerContactService.GetByCustomerIdAsync(customerId));    
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]CustomerContact customerContact)    
        {
            if (customerContact == null)
                return BadRequest();

            await _customerContactService.SaveAsync(customerContact);

            return Ok();
        }

        [HttpDelete]
        [Route("{customerId}/Contact/{contactId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int customerId, int contactId, int userId)
        {
            await _customerContactService.ToggleActivationAsync(customerId, contactId, userId);

            return Ok();
        }
    }
}