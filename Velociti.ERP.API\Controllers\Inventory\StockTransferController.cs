﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Entities.Inventory;
using Microsoft.AspNetCore.Authorization;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class StockTransferController : ControllerBase
    {
        private readonly IStockTranserService _stockTranserService;
        public StockTransferController(IStockTranserService stockTranserService )
        {

            _stockTranserService = stockTranserService;
          
        }
        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/{productId}")]
        public async Task<IActionResult> GetProducts(int fromWarehouseId,int toWarehouseId,int productId)
        {
            var result = await _stockTranserService.GetStockByWarehouses(fromWarehouseId,toWarehouseId, productId);
            return Ok(result);
        }

        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/Barcode/{barcode}")]
        public async Task<IActionResult> GetProductByBarcode(int fromWarehouseId, int toWarehouseId, string barcode)
        {
            var result = await _stockTranserService.GetStockByBarcodeAsync(fromWarehouseId, toWarehouseId, barcode);
            return Ok(result);
        }

        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/Pallet/{palletCode}")]
        public async Task<IActionResult> GetProductsByPallet(int fromWarehouseId, int toWarehouseId, string palletCode)
        {
            var result = await _stockTranserService.GetStockByPalletAsync(fromWarehouseId, toWarehouseId, palletCode);
            return Ok(result);
        }

        [HttpGet]
        [Route("{transferOrderId}")]
        public async Task<IActionResult> GetById(int transferOrderId)  
        {
            return Ok(await _stockTranserService.FindByIdAsync(transferOrderId));  
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _stockTranserService.GetShortListAsync(companyId);

            return Ok(list);
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody]StockTransfer stockTransfer)
        {
            return Ok(await _stockTranserService.SaveAsync(stockTransfer));
        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte typeEnum)
        {
            return Ok(await _stockTranserService.GetAll(companyId, userId, startDate, endDate, typeEnum));
        }
        [HttpGet]
        [Route("Single/{stockTransferId}")]
        public async Task<IActionResult> GetStockTransfer(int stockTransferId)
        {
            return Ok(await _stockTranserService.GetStockTransfer(stockTransferId));
        }
        [HttpPut]
        public async Task<IActionResult> Update([FromBody]StockTransfer stockTransfer)
        {
            switch (stockTransfer.Action)
            {
                case "submit":
                    await _stockTranserService.SubmitStockTransfer(stockTransfer.StockTransferId, stockTransfer.ModifiedUserId.Value);
                    break;
                case "convert":
                    return Ok(await _stockTranserService.ConvertAsync(stockTransfer));
            }

            return Ok();
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("TransferFromMachine")]
        public async Task<IActionResult> TransferFromMachine([FromBody]List<MachineStockTransfer> stockTransfers)
        {
            try
            {
                await _stockTranserService.SaveMachineStockTransferAsync(stockTransfers);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("TransferFromWarehouseToMachine")]
        public async Task<IActionResult> TransferFromWarehouseToMachine([FromBody]List<MachineStockTransfer> stockTransfers)
        {
            try
            {
                await _stockTranserService.SaveFromWarehouseToMachineAsync(stockTransfers);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpDelete]
        [Route("{stockTransferId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int stockTransferId, int userId)
        {
            await _stockTranserService.CancelAsync(stockTransferId, userId);
            return Ok();
        }
    }
}