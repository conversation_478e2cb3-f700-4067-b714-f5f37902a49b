﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockAdjustmentLines", Schema = "inv")]
    public partial class StockAdjustmentLine
    {
        [Key]
        public int StockAdjustmentLineId { get; set; }
        public int? StockAdjustmentId { get; set; }
        public int? ProductId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int UnitOfMeasureId { get; set; }
        public decimal? AllocatedQuantity { get; set; }
        public decimal? OnHandQuantity { get; set; }
        public int? PhysicalQuantity { get; set; }
        public decimal? AdjustmentQuantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }
        public decimal Total { get; set; }

        [ForeignKey(nameof(ProductId))]
        [InverseProperty("StockAdjustmentLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(StockAdjustmentId))]
        [InverseProperty("StockAdjustmentLines")]
        public virtual StockAdjustment StockAdjustment { get; set; }
        [ForeignKey(nameof(UnitOfMeasureId))]
        [InverseProperty("StockAdjustmentLines")]
        public virtual UnitOfMeasure UnitOfMeasure { get; set; }
    }
}
