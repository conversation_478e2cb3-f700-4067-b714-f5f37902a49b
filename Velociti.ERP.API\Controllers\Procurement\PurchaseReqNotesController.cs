﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class PurchaseReqNotesController : ControllerBase
    {
        private readonly IPurchaseRequisitionNoteService _purchaseRequisitionNoteService;
        private readonly ILogger<PurchaseReqNotesController> _logger;

        public PurchaseReqNotesController(IPurchaseRequisitionNoteService purchaseRequisitionNoteService, ILogger<PurchaseReqNotesController> logger)
        {
            _purchaseRequisitionNoteService = purchaseRequisitionNoteService;
            _logger = logger;
        }

        [HttpGet]
        [Route("Single/{purchaseRequisitionNoteId}")]
        public async Task<IActionResult> FindById(int purchaseRequisitionNoteId)
        {
            return Ok(await _purchaseRequisitionNoteService.FindByIdAsync(purchaseRequisitionNoteId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            try
            {
                return Ok(await _purchaseRequisitionNoteService.GetAllAsync(companyId, userId, startDate, endDate));
            }
            catch (Exception ex)
            {

                _logger.LogError(ex, ex.Message);
            }

            return Ok();
            
        }

        [HttpPost]
        [Route("PurchaseRequisitionNoteLine")]
        public async Task<IActionResult> GetPurchaseRequisitionNoteLine([FromBody]PurchaseRequisitionNoteLine purchaseRequisitionNoteLine)
        {
            return Ok(await _purchaseRequisitionNoteService.AddPurchaseRequisitionNoteLineAsync(purchaseRequisitionNoteLine));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]PurchaseRequisitionNote purchaseRequisitionNote)
        {
            await _purchaseRequisitionNoteService.SaveAsync(purchaseRequisitionNote);

            return Ok();
        }

        [HttpDelete]
        [Route("{purchaseRequisitionNoteId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int purchaseRequisitionNoteId, int userId)
        {
            await _purchaseRequisitionNoteService.CancelAsync(purchaseRequisitionNoteId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]PurchaseRequisitionNote purchaseRequisitionNote)
        {
            switch (purchaseRequisitionNote.Action)
            {
                case "submit": await _purchaseRequisitionNoteService.SubmitAsync(purchaseRequisitionNote.PurchaseRequisitionNoteId, purchaseRequisitionNote.ModifiedUserId.Value); break;
                case "reverse": await _purchaseRequisitionNoteService.ReverseAsync(purchaseRequisitionNote.PurchaseRequisitionNoteId, purchaseRequisitionNote.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}