﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesServiceOrdersController : ControllerBase
    {
        private readonly ISalesServiceOrderService _salesServiceOrderService;

        public SalesServiceOrdersController(ISalesServiceOrderService salesServiceOrderService)
        {
            _salesServiceOrderService = salesServiceOrderService;
        }

        [HttpGet]
        [Route("Single/{salesServiceOrderId}")]
        public async Task<IActionResult> FindById(int salesServiceOrderId)
        {
            return Ok(await _salesServiceOrderService.FindByIdAsync(salesServiceOrderId));
        }

        [HttpPost]
        [Route("Employee/{loggedInEmployeeId}")]
        public async Task<IActionResult> Post(int loggedInEmployeeId, [FromBody]SalesServiceOrder salesOrder)
        {

            await _salesServiceOrderService.SaveAsync(loggedInEmployeeId, salesOrder);

            return Ok();
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesServiceOrderService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesServiceOrder salesServiceOrder)
        {
            switch (salesServiceOrder.Action)
            {
                case "submit": await _salesServiceOrderService.SubmitAsync(salesServiceOrder.SalesServiceOrderId, salesServiceOrder.ModifiedUserId.Value); break;
                case "reverse": await _salesServiceOrderService.ReverseAsync(salesServiceOrder.SalesServiceOrderId, salesServiceOrder.ModifiedUserId.Value); break;

            }

            return Ok();
        }

        [HttpGet]
        [Route("ShortList/Customer/{customerId}")]
        public async Task<IActionResult> GetShortListByCustomerAsync(int customerId)
        {
            return Ok(await _salesServiceOrderService.GetShortListByCustomerAsync(customerId));
        }
    }
}