﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;
//using static Velociti.ERP.Domain.Entities..SupportData;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [ApiController]
    public class CustomerController : ControllerBase
    {
        private readonly ICustomerService _customerService;  

        public CustomerController(ICustomerService customerService)  
        {
            _customerService = customerService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<ActionResult<IEnumerable<Customer>>> Get(int companyId)
        {
            return Ok(await _customerService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("{customerId}")]  
        public async Task<ActionResult<Customer>> FindById(int customerId)  
        {
            return Ok(await _customerService.FindByIdAsync(customerId));
        }  

        [HttpGet]
        [Route("ShortList/Company/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            return Ok(await _customerService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Category/{customerCategoryEnum}/Type/{customerTypeId}/Group/{customerGroupId}")]
        public async Task<IActionResult> GetShortList(int companyId, int customerCategoryEnum, int customerTypeId, int customerGroupId)
        {
            return Ok(await _customerService.GetShortListAsync(companyId, customerCategoryEnum, customerTypeId, customerGroupId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Customer customer)
        {
            if (customer == null)
                return BadRequest();

            await _customerService.SaveAsync(customer);

            return Ok();
        }

        [HttpDelete]
        [Route("{customerId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int customerId, int userId)  
        {
            await _customerService.ToggleActivationAsync(customerId, userId);

            return Ok();
        }
    }
}