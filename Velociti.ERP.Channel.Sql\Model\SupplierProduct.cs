﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SupplierProducts", Schema = "inv")]
    public partial class SupplierProduct
    {
        [Key]
        public int SupplierId { get; set; }
        [Key]
        public int ProductId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SupplierProducts")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("SupplierProducts")]
        public virtual Supplier Supplier { get; set; }
    }
}
