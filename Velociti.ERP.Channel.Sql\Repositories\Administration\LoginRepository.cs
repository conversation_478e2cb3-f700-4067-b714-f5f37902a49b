﻿using System;
using System.Collections.Generic;
using System.Text;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using System.Threading.Tasks;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class LoginRepository : ILoginRepository
    {
        private readonly MarangoniERPContext _context;

        public LoginRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<Login> FindAsync(string username)
        {
            try
            {
                return await _context.Logins.Include(p => p.LoginNavigation).AsNoTracking().SingleOrDefaultAsync(u => u.Username.Equals(username) && u.ExpiryDate == null);
            }
            catch
            {

                throw;
            }
        }

        public async Task SaveAsync(Login login)
        {
            _context.Logins.Attach(login);

            var entry = _context.Entry(login);
            entry.Property(p => p.LockoutEnabled).IsModified = true;
            entry.Property(p => p.AccessFailedCount).IsModified = true;
            entry.Property(p => p.LockoutEndDate).IsModified = true;

            await _context.SaveChangesAsync();
        }
    }
}
