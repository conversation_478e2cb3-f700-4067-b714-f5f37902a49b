﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Authorize]
    [Route("api/Administration/[controller]")]
    [ApiController]
    public class PasswordPoliciesController : ControllerBase
    {
        private readonly IPasswordPolicyService _passwordPolicyService;

        public PasswordPoliciesController(IPasswordPolicyService passwordPolicyService)
        {
            _passwordPolicyService = passwordPolicyService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetByCompany(int companyId)
        {
            return Ok(await _passwordPolicyService.GetByCompanyIdAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]PasswordPolicy passwordPolicy)
        {
            await _passwordPolicyService.SaveAsync(passwordPolicy);

            return Ok();
        }
    }
}