﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class ServiceInvoicesController : ControllerBase
    {
        private readonly IServiceInvoiceService _serviceInvoiceService;

        public ServiceInvoicesController(IServiceInvoiceService serviceInvoiceService)
        {
            _serviceInvoiceService = serviceInvoiceService;
        }

        [HttpGet]
        [Route("Single/{serviceInvoiceId}")]
        public async Task<IActionResult> GetById(int serviceInvoiceId)
        {
            return Ok(await _serviceInvoiceService.GetByIdAsync(serviceInvoiceId));
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            return Ok(await _serviceInvoiceService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Supplier/{supplierId}")]
        public async Task<IActionResult> GetShortList(int companyId, int supplierId)
        {
            return Ok(await _serviceInvoiceService.GetShortListAsync(companyId, supplierId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _serviceInvoiceService.GetAllAsync(companyId, startDate, endDate));
        }

        [HttpGet]
        [Route("SettlementDocuments/{serviceInvoiceId}")]
        public async Task<IActionResult> GetSettlemetDocumentsAsync(int serviceInvoiceId)
        {
            return Ok(await _serviceInvoiceService.GetSettlementDocumentsAsync(serviceInvoiceId));
        }

        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]ServiceInvoice serviceInvoice)
        {
            await _serviceInvoiceService.SaveAsync(serviceInvoice);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]ServiceInvoice serviceInvoice)
        {
            switch (serviceInvoice.Action)
            {
                case "submit": await _serviceInvoiceService.SubmitAsync(serviceInvoice); break;
                case "reverse": await _serviceInvoiceService.ReverseAsync(serviceInvoice); break;
                case "cancel": await _serviceInvoiceService.CancelAsync(serviceInvoice); break;
            }

            return Ok();
        }
    }
}