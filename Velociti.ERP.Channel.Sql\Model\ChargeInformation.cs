﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ChargeInformations", Schema = "fin")]
    public partial class ChargeInformation
    {
        public ChargeInformation()
        {
            ChargeInfoChargeGroups = new HashSet<ChargeInfoChargeGroup>();
        }

        [Key]
        public int ChargeInformationId { get; set; }
        public int CompanyId { get; set; }
        [StringLength(50)]
        public string Code { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        [Column("IsCIF")]
        public bool? IsCif { get; set; }
        [Column("IsFOB")]
        public bool? IsFob { get; set; }
        [Column("IsCNF")]
        public bool? IsCnf { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ChargeInformations")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(ChargeInfoChargeGroup.ChargeInformation))]
        public virtual ICollection<ChargeInfoChargeGroup> ChargeInfoChargeGroups { get; set; }
    }
}
