﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FixedAssetRegistry", Schema = "fa")]
    public partial class FixedAssetRegistry
    {
        [Key]
        public int FixedAssetRegistryId { get; set; }
        public int? FixedAssetId { get; set; }
        public int? RefDocTypeEnum { get; set; }
        [StringLength(32)]
        public string RefDocNumber { get; set; }
        public int? UsefulLife { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? AcquisitionValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? AccumulatedDepreciation { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? SalvageValue { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? NetBookValue { get; set; }
        public byte? DepreciationInterval { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DepreciationStartDate { get; set; }
        public int? RemainingDepPeriods { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(FixedAssetId))]
        [InverseProperty("FixedAssetRegistries")]
        public virtual FixedAsset FixedAsset { get; set; }
    }
}
