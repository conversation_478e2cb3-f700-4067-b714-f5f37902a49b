﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class AccountMappingLinesController : ControllerBase
    {
        private readonly IAccountMappingLineService _accountMappingLineService;

        public AccountMappingLinesController(IAccountMappingLineService accountMappingLineService)
        {
            _accountMappingLineService = accountMappingLineService;
        }

        [HttpGet]
        [Route("{accountMappingId}")]
        public async Task<IActionResult> Get(int accountMappingId)
        {
            var records = await _accountMappingLineService.GetAsync(accountMappingId);

            return Ok(records);
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]AccountMappingLine accountMappingLine)
        {
            await _accountMappingLineService.SaveAsync(accountMappingLine);

            return Ok();
        }

        [HttpPut]
        [Route("{accountMappingLineId}/User/{userId}")]
        public async Task<IActionResult> UpdateGLAccount(int accountMappingLineId, int userId)  
        {
            await _accountMappingLineService.UpdateGLAccountAsync(accountMappingLineId, userId);

            return Ok();
        }
    }
}