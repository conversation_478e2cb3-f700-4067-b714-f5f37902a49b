﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Entities.Inventory;
using Microsoft.AspNetCore.Authorization;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class ConsignmentStockTransferLineController : ControllerBase
    {
        private readonly IConsignmentStockTransferLineService _consignmentStocktockTranserLineService;
        public ConsignmentStockTransferLineController(IConsignmentStockTransferLineService consignmentStockTranserLineService)
        {

            _consignmentStocktockTranserLineService = consignmentStockTranserLineService;

        }
        [HttpGet]
        [Route("Products/{consignmentStockTransferId}")]
        public async Task<IActionResult> LoadProducts(int consignmentStockTransferId)
        {
            return Ok(await _consignmentStocktockTranserLineService.LoadProducts(consignmentStockTransferId));
        }

        [HttpGet]
        [Route("PUrchaseOrder/{consignmentStockTransferId}")]
        public async Task<IActionResult> LoadPOLines(int consignmentStockTransferId)
        {
            return Ok(await _consignmentStocktockTranserLineService.LoadPOLines(consignmentStockTransferId));
        }

        [HttpGet]
        [Route("{stockTransferId}/company/{defaultCompanyId}")]
        public async Task<IActionResult> Get(int stockTransferId, int defaultCompanyId)
        {
            return Ok(await _consignmentStocktockTranserLineService.GetAsync(stockTransferId, defaultCompanyId));
        }

        [HttpGet]
        [Route("Details/{stockTransferLineId}")]
        public async Task<IActionResult> LineDetails(int stockTransferLineId)
        {
            return Ok(await _consignmentStocktockTranserLineService.GetLineDetailsAsync(stockTransferLineId));
        }
    }
}
