2025-10-13 07:47:53,647 [9] INFO  Web.Controllers.AccountController - Login POST started.
2025-10-13 07:47:53,675 [9] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-10-13 07:47:53,896 [9] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"<PERSON>anka","password":"12345"}
2025-10-13 07:47:53,896 [9] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-10-13 07:48:00,339 [9] DEBUG Web.Controllers.AccountController - Response Status Code: OK
2025-10-13 07:48:00,339 [9] DEBUG Web.Controllers.AccountController - Response Content: {"id":3,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.1fZY7fga9wP-d2f4M2qgpONI4_8ZiKloD3YiDHe_rWU","refreshToken":"4bf70cd2-b01b-4ecc-ac4a-012261c0c06e","expiresIn":2073600,"error":null,"user":{"userId":3,"titleId":2,"title":null,"userCode":"U1","fullname":"Pasan Madhawa","displayName":"Navitsa System Admin","email":"<EMAIL>","signature":"","signatureFile":null,"isFilteredByDepartment":false,"employeeId":0,"defaultCompanyId":2,"defaultDepartmentId":0,"creationDate":"2020-08-14T12:00:51.62","expiryDate":null,"modifiedUserId":2,"loginId":0,"username":null,"passwordHash":null,"login":{"loginId":3,"username":"asanka","passwordHash":"827ccb0eea8a706c4c34a16891f84e7b","modifiedUserId":2},"designationTypeEnum":254,"designationName":"Administrator","notificationMessage":null},"permissions":[]}
2025-10-13 07:48:00,469 [9] INFO  Web.Controllers.AccountController - Authentication succeeded for user: Navitsa System Admin
2025-10-13 07:48:00,469 [9] DEBUG Web.Controllers.AccountController - Claims created for user Navitsa System Admin
2025-10-13 07:48:00,469 [9] INFO  Web.Controllers.AccountController - User Navitsa System Admin signed in successfully.
2025-10-13 13:58:45,334 [59] INFO  Web.Controllers.AccountController - Login POST started.
2025-10-13 13:58:45,384 [59] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-10-13 13:58:45,788 [59] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-10-13 13:58:45,788 [59] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-10-13 13:58:59,812 [59] DEBUG Web.Controllers.AccountController - Response Status Code: OK
2025-10-13 13:58:59,812 [59] DEBUG Web.Controllers.AccountController - Response Content: {"id":3,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.jMsDkj2gARTMV3N2H86--2HMWYFgMvs5CKZQp_259q0","refreshToken":"345fc26d-0c5e-403c-a6be-e68c988eb626","expiresIn":2073600,"error":null,"user":{"userId":3,"titleId":2,"title":null,"userCode":"U1","fullname":"Pasan Madhawa","displayName":"Navitsa System Admin","email":"<EMAIL>","signature":"","signatureFile":null,"isFilteredByDepartment":false,"employeeId":0,"defaultCompanyId":2,"defaultDepartmentId":0,"creationDate":"2020-08-14T12:00:51.62","expiryDate":null,"modifiedUserId":2,"loginId":0,"username":null,"passwordHash":null,"login":{"loginId":3,"username":"asanka","passwordHash":"827ccb0eea8a706c4c34a16891f84e7b","modifiedUserId":2},"designationTypeEnum":254,"designationName":"Administrator","notificationMessage":null},"permissions":[]}
2025-10-13 13:59:00,613 [59] INFO  Web.Controllers.AccountController - Authentication succeeded for user: Navitsa System Admin
2025-10-13 13:59:00,619 [59] DEBUG Web.Controllers.AccountController - Claims created for user Navitsa System Admin
2025-10-13 13:59:00,623 [59] INFO  Web.Controllers.AccountController - User Navitsa System Admin signed in successfully.
