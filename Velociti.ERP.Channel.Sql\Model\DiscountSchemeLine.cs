﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("DiscountSchemeLines", Schema = "sales")]
    public partial class DiscountSchemeLine
    {
        [Key]
        public int DiscountSchemeLineId { get; set; }
        public int DiscountSchemeId { get; set; }
        public int? ProductCategoryId { get; set; }
        public int? ProductHierarchyId2 { get; set; }
        public int? ProductBrandId { get; set; }
        public int? ProductId { get; set; }
        public int DiscountValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DiscountSchemeId))]
        [InverseProperty("DiscountSchemeLines")]
        public virtual DiscountScheme DiscountScheme { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("DiscountSchemeLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductBrandId))]
        [InverseProperty(nameof(SupportData.DiscountSchemeLines))]
        public virtual SupportData ProductBrand { get; set; }
        [ForeignKey(nameof(ProductHierarchyId2))]
        [InverseProperty(nameof(ProductHierarchy.DiscountSchemeLines))]
        public virtual ProductHierarchy ProductHierarchyId2Navigation { get; set; }
    }
}
