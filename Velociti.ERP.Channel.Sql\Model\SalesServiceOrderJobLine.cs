﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesServiceOrderJobLines", Schema = "sales")]
    public partial class SalesServiceOrderJobLine
    {
        [Key]
        public int SalesServiceOrderJobLineId { get; set; }
        public int SalesServiceOrderJobId { get; set; }
        public int? ProductId { get; set; }
        public int? ProductCategoryId { get; set; }
        public int? Quantity { get; set; }
        public int? SecondaryUnitOfMeasureId { get; set; }
        [Column(TypeName = "money")]
        public decimal? MaterialCost { get; set; }
        public bool? IsMandatoryProduct { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(SalesServiceOrderJobId))]
        [InverseProperty("SalesServiceOrderJobLines")]
        public virtual SalesServiceOrderJob SalesServiceOrderJob { get; set; }
    }
}
