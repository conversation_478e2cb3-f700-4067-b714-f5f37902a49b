﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Sql.Services.Inventory;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;
using Microsoft.Extensions.Configuration;
using System.Data;
using System.Data.SqlClient;
using Velociti.ERP.Channel.Sql.Model;


namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductionNotesController : ControllerBase
    {
        private readonly IProductionNotesService _productionNotesService;
        private readonly IConfiguration _configuration;

        public ProductionNotesController(IProductionNotesService productionNotesService, IConfiguration configuration)
        {
            _productionNotesService = productionNotesService;
            _configuration = configuration;
        }

        [HttpGet]
        [Route("Single/{productionNoteId}")]
        public async Task<IActionResult> FindById(int productionNoteId)
        {
            return Ok(await _productionNotesService.FindByIdAsync(productionNoteId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _productionNotesService.GetAllAsync(companyId, userId, startDate, endDate));
        }


        [HttpPost("Save")]
        public async Task<IActionResult> Save([FromBody] Domain.Entities.Inventory.ProductionNote productionNote)
        {
            switch (productionNote.Action)
            {
                //case "generate": await _purchaseOrderService.GeneratePurchaseOrderAsync(purchaseOrder); break;
                case "update": await _productionNotesService.SaveAsync(productionNote); break;
                case "new": await _productionNotesService.SaveAsync(productionNote); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{productionNoteId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int productionNoteId, int userId)
        {
            await _productionNotesService.CancelAsync(productionNoteId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody] Domain.Entities.Inventory.ProductionNote productionNote)
        {
            switch (productionNote.Action)
            {
                case "submit": await _productionNotesService.SubmitAsync(productionNote.ProductionNoteId, productionNote.ModifiedUserId.Value); break;
                //case "convert": await _internalOrderService.ConvertAsync(internalOrder.InternalOrderId, internalOrder.ModifiedUserId.Value); break;
            }

            return Ok();
        }
        //[HttpPost("UpdateStocks/{internalOrderId}")]
        //public IActionResult UpdateStocks(int internalOrderId)
        //{
        //    try
        //    {
        //        // Get connection string from appsettings.json
        //        string connectionString = _configuration.GetConnectionString("DefaultConnection");

        //        using (SqlConnection connection = new SqlConnection(connectionString))
        //        {
        //            using (SqlCommand command = new SqlCommand("sp_Update_Stocks_WAT", connection))
        //            {
        //                command.CommandType = CommandType.StoredProcedure;
        //                command.Parameters.AddWithValue("@WATID", internalOrderId);

        //                connection.Open();
        //                command.ExecuteNonQuery();
        //            }
        //        }

        //        return Ok(new { success = true, message = "Stock updated successfully." });
        //    }
        //    catch (Exception ex)
        //    {
        //        return StatusCode(500, new { success = false, message = "Error: " + ex.Message });
        //    }
        //}

    }
}