﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("AppSettings", Schema = "adm")]
    public partial class AppSetting
    {
        [Key]
        public int AppSettingsId { get; set; }
        public int? CompanyId { get; set; }
        [Column("TouchScreenPIN")]
        [StringLength(10)]
        public string TouchScreenPin { get; set; }
        public bool? TaxInclusivePricing { get; set; }
        [Column("IsAutoLoadGDNLineDetails")]
        public bool? IsAutoLoadGdnlineDetails { get; set; }
        [Column("IsAutoLoadGRNLineDetails")]
        public bool? IsAutoLoadGrnlineDetails { get; set; }
        public bool? IsZeroCostForStockClaimReturn { get; set; }
        public bool? IsFinanceSettlementCanExceed { get; set; }
        [Column(TypeName = "date")]
        public DateTime? EffectiveDate { get; set; }
        public int? TempId { get; set; }
        [Column("ConsignmentHOReturnWarehouseId")]
        public int? ConsignmentHoreturnWarehouseId { get; set; }
        public int? ConsignmentStockTransferWarehouseId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("AppSettings")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ConsignmentHoreturnWarehouseId))]
        [InverseProperty(nameof(Warehous.AppSettingConsignmentHoreturnWarehouses))]
        public virtual Warehous ConsignmentHoreturnWarehouse { get; set; }
        [ForeignKey(nameof(ConsignmentStockTransferWarehouseId))]
        [InverseProperty(nameof(Warehous.AppSettingConsignmentStockTransferWarehouses))]
        public virtual Warehous ConsignmentStockTransferWarehouse { get; set; }
    }
}
