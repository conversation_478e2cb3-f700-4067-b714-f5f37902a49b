﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [ApiController]
    [Authorize]
    public class MouldsController : ControllerBase
    {
        private readonly IMouldService _mouldService;

        public MouldsController(IMouldService mouldService)
        {
            _mouldService = mouldService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _mouldService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _mouldService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Mould mould)
        {
            await _mouldService.SaveAsync(mould);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> ToggleActivation([FromBody]Mould mould)
        {
            await _mouldService.ToggleActivationAsync(mould);

            return Ok();
        }
    }
}