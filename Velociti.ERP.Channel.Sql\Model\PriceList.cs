﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PriceLists", Schema = "sales")]
    public partial class PriceList
    {
        [Key]
        public int Id { get; set; }
        public int? PriceListId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "money")]
        public decimal? Provision { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }
        [Column("SFAStatusEnum")]
        public byte? SfastatusEnum { get; set; }
        [Column("SFAMessage")]
        [StringLength(1000)]
        public string Sfamessage { get; set; }

        [ForeignKey(nameof(PriceListId))]
        [InverseProperty(nameof(SupportData.PriceLists))]
        public virtual SupportData PriceListNavigation { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("PriceLists")]
        public virtual Product Product { get; set; }
    }
}
