﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MenuCategory", Schema = "dbo")]
    public partial class MenuCategory
    {
        [Key]
        public int MenuCatId { get; set; }
        public string MenuCatCode { get; set; }
        public string MenuCatName { get; set; }
        public int MenuCatColour { get; set; }
    }
}
