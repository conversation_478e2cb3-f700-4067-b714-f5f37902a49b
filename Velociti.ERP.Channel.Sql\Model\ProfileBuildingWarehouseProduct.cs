﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProfileBuildingWarehouseProducts", Schema = "man")]
    public partial class ProfileBuildingWarehouseProduct
    {
        [Key]
        public int ProfileBuildingWarehouseProductId { get; set; }
        public int? MachineId { get; set; }
        public int? WarehouseProductId { get; set; }
        public int? SequenceNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(MachineId))]
        [InverseProperty("ProfileBuildingWarehouseProducts")]
        public virtual Machine Machine { get; set; }
    }
}
