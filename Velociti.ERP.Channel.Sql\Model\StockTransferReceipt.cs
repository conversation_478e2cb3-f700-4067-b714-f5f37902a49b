﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockTransferReceipts", Schema = "inv")]
    public partial class StockTransferReceipt
    {
        public StockTransferReceipt()
        {
            StockTransferReceiptLines = new HashSet<StockTransferReceiptLine>();
        }

        [Key]
        public int StockTransferReceiptId { get; set; }
        public int? StockTransferId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? PostDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? FromWarehouseId { get; set; }
        public int? ToWarehouseId { get; set; }
        [StringLength(255)]
        public string VehicleDetails { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("StockTransferReceipts")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("StockTransferReceipts")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(FromWarehouseId))]
        [InverseProperty(nameof(Warehous.StockTransferReceiptFromWarehouses))]
        public virtual Warehous FromWarehouse { get; set; }
        [ForeignKey(nameof(StockTransferId))]
        [InverseProperty("StockTransferReceipts")]
        public virtual StockTransfer StockTransfer { get; set; }
        [ForeignKey(nameof(ToWarehouseId))]
        [InverseProperty(nameof(Warehous.StockTransferReceiptToWarehouses))]
        public virtual Warehous ToWarehouse { get; set; }
        [InverseProperty(nameof(StockTransferReceiptLine.StockTransferReceipt))]
        public virtual ICollection<StockTransferReceiptLine> StockTransferReceiptLines { get; set; }
    }
}
