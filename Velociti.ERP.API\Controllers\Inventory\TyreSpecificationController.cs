﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class TyreSpecificationController : ControllerBase
    {
        private readonly ITyreSpecificationsService _tyreSpecificationsService;

        public TyreSpecificationController(ITyreSpecificationsService tyreSpecificationsService)
        {
            this._tyreSpecificationsService = tyreSpecificationsService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            var result = await this._tyreSpecificationsService.GetAllAsync(companyId);
            return Ok(result);
        }

        [HttpGet]
        [Route("{productId}")]
        public async Task<IActionResult> Get(int productId)
        {
            var result = await this._tyreSpecificationsService.FindByProductIdAsync(productId);
            return Ok(result);
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody]TyreSpecification specification)
        {
            await this._tyreSpecificationsService.SaveAsync(specification);
            return Ok();
        }

        [HttpDelete]
        [Route("{tyreSpecificationId}/loggedInUser/{loggedInUserId}")]
        public async Task<IActionResult> ToggleActivation(int tyreSpecificationId, int loggedInUserId)
        {
            await _tyreSpecificationsService.ToggleActivationAsync(tyreSpecificationId, loggedInUserId);

            return Ok();
        }
    }
}