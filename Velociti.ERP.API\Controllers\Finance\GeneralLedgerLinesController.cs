﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class GeneralLedgerLinesController : ControllerBase
    {
        private readonly IGeneralLedgerLineService _generalLedgerLineService;

        public GeneralLedgerLinesController(IGeneralLedgerLineService generalLedgerLineService)
        {
            _generalLedgerLineService = generalLedgerLineService;
        }

        [Route("Company/{companyId}/userId/{userId}/Start/{start}/End/{end}")]
        public async Task<IActionResult> GetAllAsync(int companyId, int userId, DateTime start, DateTime end)
        {
            return Ok(await _generalLedgerLineService.GetAllAsync(companyId, userId, start, end));
        }

        [HttpGet]
        [Route("{bankReconciliationId}/Account/{accountId}")]
        public async Task<IActionResult> GetByHeaderId(int bankReconciliationId, int accountId)
        {
            return Ok(await _generalLedgerLineService.GetByAccountIdAsync(bankReconciliationId, accountId));
        }

        [HttpGet]
        [Route("TransactionShortList/IsAll/{isAll}")]
        public async Task<IActionResult> GetTransactionShortList(byte isAll)
        {
            var list = await _generalLedgerLineService.GetTransactionShortListAsync(isAll == 1);

            return Ok(list);
        }

        [HttpGet]
        [Route("TransactionShortListWithPaging")]
        public IActionResult GetTransactionShortListWithPaging(int companyId, string searchBy, int page, int take)
        {
            int totalRecordCount = 0;
            int skip = (page - 1) * take;
            var records = _generalLedgerLineService.GetTransactionsWithPagingAsync(companyId, searchBy, skip, take, ref totalRecordCount);

            var hasMoreRecords = totalRecordCount > records.Count();
            var dto = new
            {
                results = records,
                pagination = hasMoreRecords
            };

            return Ok(dto);
        }
    }
}