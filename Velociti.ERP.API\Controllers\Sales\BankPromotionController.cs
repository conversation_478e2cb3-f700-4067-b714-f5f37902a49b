﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class BankPromotionController : ControllerBase
    {
        private readonly IBankPromotionService _bankpromotionService;

        public BankPromotionController(IBankPromotionService bankpromotionService)
        {
            _bankpromotionService = bankpromotionService;
        }

        [HttpGet]
        [Route("Promotions/GridData")]
        public async Task<IActionResult> Get()
        {
            try
            {
                var promotions = await _bankpromotionService.GetAllAsync();
                return Ok(promotions);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet("Promotions/BankList")]
        public async Task<IActionResult> GetBankList()
        {
            try
            {
                var banks = await _bankpromotionService.GetBankListAsync();
                var json = JsonConvert.SerializeObject(banks);

                Console.WriteLine(json);

                return Ok(banks);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpGet]
        [Route("Promotions/CardTypeList")]
        public async Task<IActionResult> GetCardTypeList()
        {
            try
            {
                var cardTypes = await _bankpromotionService.GetCardTypeListAsync();
                var json = JsonConvert.SerializeObject(cardTypes);

                Console.WriteLine(json);

                return Ok(cardTypes);
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody] Domain.Entities.Sales.BankPromotion bankpromotion)
        {
            if (bankpromotion == null)
                return BadRequest("Promotion data is required.");

            try
            {
                await _bankpromotionService.SaveAsync(bankpromotion);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("Promotions/UpdateAllActive")]
        public async Task<IActionResult> UpdateAllActive([FromBody] BankPromotionUpdateActiveRequest request)
        {
            try
            {
                await _bankpromotionService.UpdateAllActiveAsync(request.Active);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [HttpPut("Promotions/UpdateActiveByCode")]
        public async Task<IActionResult> UpdateActiveByCode([FromBody] BankPromotionUpdateActiveRequest request)
        {
            if (string.IsNullOrWhiteSpace(request.PromotionCode))
                return BadRequest("Promotion code is required.");
            try
            {
                await _bankpromotionService.UpdateActiveByCodeAsync(request.PromotionCode, request.Active);
                return Ok();
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }
}