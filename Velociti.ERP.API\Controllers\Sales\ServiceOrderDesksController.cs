﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class ServiceOrderDesksController : ControllerBase
    {
        private readonly IServiceOrderDeskService _serviceOrderDeskService;

        public ServiceOrderDesksController(IServiceOrderDeskService serviceOrderDeskService)
        {
            _serviceOrderDeskService = serviceOrderDeskService;
        }

        [HttpGet]
        public async Task<IActionResult> GetSubmitted(int companyId,  DateTime startDate, DateTime endDate, int userId = 0)
        {
            return Ok(await _serviceOrderDeskService.GetSubmittedLinesAsync(companyId, userId, startDate, endDate));
        }
    }
}