﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("LoanOrderLines", Schema = "prc")]
    public partial class LoanOrderLine
    {
        [Key]
        public int LoanOrderLineId { get; set; }
        public int LoanOrderId { get; set; }
        public int? ProductId { get; set; }
        public int? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalCost { get; set; }
        public int? ReasonId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.LoanOrderLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.LoanOrderLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(LoanOrderId))]
        [InverseProperty("LoanOrderLines")]
        public virtual LoanOrder LoanOrder { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("LoanOrderLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ReasonId))]
        [InverseProperty(nameof(SupportData.LoanOrderLines))]
        public virtual SupportData Reason { get; set; }
    }
}
