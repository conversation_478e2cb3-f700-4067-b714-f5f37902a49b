﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ConsignmentStockTransferLines", Schema = "inv")]
    public partial class ConsignmentStockTransferLine
    {
        public ConsignmentStockTransferLine()
        {
            ConsignmentStockTransferDetails = new HashSet<ConsignmentStockTransferDetail>();
        }

        [Key]
        public int ConsignmentStockTransferLineId { get; set; }
        public int? ConsignmentStockTransferId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ConsignmentStockTransferLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ConsignmentStockTransferId))]
        [InverseProperty("ConsignmentStockTransferLines")]
        public virtual ConsignmentStockTransfer ConsignmentStockTransfer { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ConsignmentStockTransferLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ConsignmentStockTransferLines")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransferDetail.ConsignmentStockTransferLine))]
        public virtual ICollection<ConsignmentStockTransferDetail> ConsignmentStockTransferDetails { get; set; }
    }
}
