﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesInvoiceChargeGroupsController : ControllerBase  
    {
        private readonly ISalesInvoiceChargeGroupService _salesInvoiceChargeGroupService;

        public SalesInvoiceChargeGroupsController(ISalesInvoiceChargeGroupService salesInvoiceChargeGroupService)
        {
            _salesInvoiceChargeGroupService = salesInvoiceChargeGroupService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int salesInvoiceId)  
        {
            return Ok(await _salesInvoiceChargeGroupService.GetAsync(salesInvoiceId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]SalesInvoiceChargeGroup salesInvoiceChargeGroup)
        {
            await _salesInvoiceChargeGroupService.SaveAsync(salesInvoiceChargeGroup);

            return Ok();
        }

        [HttpDelete]
        [Route("{salesInvoiceId}/ChargeGroup/{chargeGroupId}")]
        public async Task<IActionResult> Delete(int salesInvoiceId, int chargeGroupId)
        {
            await _salesInvoiceChargeGroupService.CancelAsync(salesInvoiceId, chargeGroupId);

            return Ok();
        }
    }
}