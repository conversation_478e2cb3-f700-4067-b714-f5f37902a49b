﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BankReconciliationLines", Schema = "fin")]
    public partial class BankReconciliationLine
    {
        [Key]
        public int BankReconciliationLineId { get; set; }
        public int BankReconciliationId { get; set; }
        public int GeneralLedgerLineId { get; set; }
        [Column(TypeName = "money")]
        public decimal Amount { get; set; }
        public bool IsCr { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BankReconciliationId))]
        [InverseProperty("BankReconciliationLines")]
        public virtual BankReconciliation BankReconciliation { get; set; }
        [ForeignKey(nameof(GeneralLedgerLineId))]
        [InverseProperty("BankReconciliationLines")]
        public virtual GeneralLedgerLine GeneralLedgerLine { get; set; }
    }
}
