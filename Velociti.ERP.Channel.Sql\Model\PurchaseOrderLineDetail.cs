﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseOrderLineDetails", Schema = "prc")]
    public partial class PurchaseOrderLineDetail
    {
        [Key]
        public int PurchaseOrderLineDetailId { get; set; }
        public int? PurchaseOrderLineId { get; set; }
        public int? FixedAssetId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(FixedAssetId))]
        [InverseProperty("PurchaseOrderLineDetails")]
        public virtual FixedAsset FixedAsset { get; set; }
        [ForeignKey(nameof(PurchaseOrderLineId))]
        [InverseProperty("PurchaseOrderLineDetails")]
        public virtual PurchaseOrderLine PurchaseOrderLine { get; set; }
    }
}
