﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class PurchaseReturnLinesController : ControllerBase
    {
        private readonly IPurchaseReturnLineService _purchaseReturnLineService;

        public PurchaseReturnLinesController(IPurchaseReturnLineService purchaseReturnLineService)
        {
            _purchaseReturnLineService = purchaseReturnLineService;
        }

        [HttpGet]
        [Route("{purchaseReturnId}")]
        public async Task<IActionResult> GetById(int purchaseReturnId)
        {
            return Ok(await _purchaseReturnLineService.GetByHeaderIdAsync(purchaseReturnId));
        }
    }
}