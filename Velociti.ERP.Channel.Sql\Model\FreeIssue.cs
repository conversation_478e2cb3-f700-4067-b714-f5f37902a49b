﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FreeIssues", Schema = "sales")]
    public partial class FreeIssue
    {
        public FreeIssue()
        {
            FreeIssueCriteriaLines = new HashSet<FreeIssueCriteriaLine>();
            FreeIssueCustomers = new HashSet<FreeIssueCustomer>();
            FreeIssueLines = new HashSet<FreeIssueLine>();
            PromotionLines = new HashSet<PromotionLine>();
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
        }

        [Key]
        public int FreeIssueId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string FreeIssueCode { get; set; }
        public int? StatusEnum { get; set; }
        [StringLength(150)]
        public string Description { get; set; }
        public int? CustomerTypeId { get; set; }
        public int? CustomerGroupId { get; set; }
        public int? CustomerCategoryId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime StartDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDate { get; set; }
        public byte TypeEnum { get; set; }
        public byte CalculationTypeEnum { get; set; }
        public int? MinProductCount { get; set; }
        public int? PromotionApplicableTypeEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("FreeIssues")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerGroupId))]
        [InverseProperty(nameof(SupportData.FreeIssueCustomerGroups))]
        public virtual SupportData CustomerGroup { get; set; }
        [ForeignKey(nameof(CustomerTypeId))]
        [InverseProperty(nameof(SupportData.FreeIssueCustomerTypes))]
        public virtual SupportData CustomerType { get; set; }
        [InverseProperty(nameof(FreeIssueCriteriaLine.FreeIssue))]
        public virtual ICollection<FreeIssueCriteriaLine> FreeIssueCriteriaLines { get; set; }
        [InverseProperty(nameof(FreeIssueCustomer.FreeIssue))]
        public virtual ICollection<FreeIssueCustomer> FreeIssueCustomers { get; set; }
        [InverseProperty(nameof(FreeIssueLine.FreeIssue))]
        public virtual ICollection<FreeIssueLine> FreeIssueLines { get; set; }
        [InverseProperty(nameof(PromotionLine.FreeIssue))]
        public virtual ICollection<PromotionLine> PromotionLines { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.FreeIssue))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
    }
}
