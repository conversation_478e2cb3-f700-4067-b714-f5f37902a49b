﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MaterialRequisitionNotes", Schema = "inv")]
    public partial class MaterialRequisitionNote
    {
        public MaterialRequisitionNote()
        {
            InternalDispatches = new HashSet<InternalDispatch>();
            MaterialRequisitionNoteLines = new HashSet<MaterialRequisitionNoteLine>();
            ServiceInvoiceMatarialRequestNotes = new HashSet<ServiceInvoiceMatarialRequestNote>();
        }

        [Key]
        public int MaterialRequisitionNoteId { get; set; }
        public int? CompanyId { get; set; }
        public int? EmployeeId { get; set; }
        public int? DepartmentId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? DeliveryWarehouseId { get; set; }
        public int? RequestReasonId { get; set; }
        public int? FixedAssetId { get; set; }
        public int? FixedAssetGroupId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("MaterialRequisitionNotes")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DeliveryWarehouseId))]
        [InverseProperty(nameof(Warehous.MaterialRequisitionNotes))]
        public virtual Warehous DeliveryWarehouse { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("MaterialRequisitionNotes")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("MaterialRequisitionNotes")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(FixedAssetId))]
        [InverseProperty("MaterialRequisitionNotes")]
        public virtual FixedAsset FixedAsset { get; set; }
        [ForeignKey(nameof(FixedAssetGroupId))]
        [InverseProperty(nameof(SupportData.MaterialRequisitionNoteFixedAssetGroups))]
        public virtual SupportData FixedAssetGroup { get; set; }
        [ForeignKey(nameof(RequestReasonId))]
        [InverseProperty(nameof(SupportData.MaterialRequisitionNoteRequestReasons))]
        public virtual SupportData RequestReason { get; set; }
        [InverseProperty(nameof(InternalDispatch.MaterialRequisitionNote))]
        public virtual ICollection<InternalDispatch> InternalDispatches { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNoteLine.MaterialRequisitionNote))]
        public virtual ICollection<MaterialRequisitionNoteLine> MaterialRequisitionNoteLines { get; set; }
        [InverseProperty(nameof(ServiceInvoiceMatarialRequestNote.MaterialRequisitionNote))]
        public virtual ICollection<ServiceInvoiceMatarialRequestNote> ServiceInvoiceMatarialRequestNotes { get; set; }
    }
}
