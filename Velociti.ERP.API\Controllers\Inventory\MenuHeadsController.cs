﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using DocumentFormat.OpenXml.Office2010.ExcelAc;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Services.Inventory;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;
using InventoryMenuItem = Velociti.ERP.Domain.Entities.Inventory.MenuItem;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class MenuHeadsController : ControllerBase
    {
        private readonly IMenuHeadService _menuHeadService;

        public MenuHeadsController(IMenuHeadService menuHeadService)
        {
            _menuHeadService = menuHeadService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _menuHeadService.GetAllAsync(companyId));
        }

        //[HttpGet]
        //[Route("ActiveAll/Company/{companyId}")]
        //public async Task<IActionResult> GetActiveAll(int companyId)
        //{
        //    return Ok(await _menuHeadService.GetActiveAll(companyId));
        //}

        [HttpPost]
        public async Task<IActionResult> SaveMenuItem([FromBody] InventoryMenuItem menuItem)
        {
            //await _productService.SaveAsync(product);
             //Console.WriteLine($"HeadLines Count: {menuItem.MenuHeadLinem?.Count}");
            return Ok(await _menuHeadService.SaveMenuItem(menuItem));
        }

        [HttpGet]
        [Route("FindById/{menuItemId}")]
        public async Task<IActionResult> FindById(int menuItemId)
        {
            return Ok(await _menuHeadService.FindById(menuItemId));
        }

        [HttpDelete]
        [Route("{menuItemId}")]
        public async Task<IActionResult> ToggleActivationAsync(int menuItemId)
        {
           await _menuHeadService.ToggleActivationAsync(menuItemId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody] InventoryMenuItem menuItem)
        {
            switch (menuItem.Action)
            {
                case "submit":
                    await _menuHeadService.SubmitAsync(menuItem);
                    break;
              
                //case "cancel":
                //    await _menuHeadService.CancelAsync(purchaseReturn);
                //    break;
                
            }

            return Ok();
        }

    }
}
