﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class FinancialStatementLinesController : ControllerBase  
    {
        private readonly IFinancialStatementLineService _financialStatementLineService;    

        public FinancialStatementLinesController(IFinancialStatementLineService financialStatementLineService)      
        {
            _financialStatementLineService = financialStatementLineService;
        }

        [HttpGet]
        [Route("{financialStatementId}")]  
        public async Task<IActionResult> Get(int financialStatementId)  
        {
            return Ok(await _financialStatementLineService.GetAsync(financialStatementId));
        }

        [HttpGet]
        [Route("ShortList/{financialStatementId}")]
        public async Task<IActionResult> GetShortList(int financialStatementId)
        {
            var list = await _financialStatementLineService.GetShortListAsync(financialStatementId);

            return Ok(list);
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]FinancialStatementLine financialStatementLine)  
        {
            await _financialStatementLineService.SaveAsync(financialStatementLine);
            
            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivation(int id)
        {
            await _financialStatementLineService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}