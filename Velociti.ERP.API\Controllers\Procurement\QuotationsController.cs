﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;
using DevExtreme.AspNet.Data;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class QuotationsController : ControllerBase
    {
        private readonly IQuotationService _quotationService;

        public QuotationsController(IQuotationService quotationService)
        {
            _quotationService = quotationService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _quotationService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("All")]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, [FromQuery]DataSourceLoadOptionsBase loadOptions)
        {
            return Ok(await _quotationService.GetAllAsync(companyId, userId, startDate, endDate, loadOptions));
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _quotationService.GetForApprovalAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("{quotationId}")]
        public async Task<IActionResult> GetById(int quotationId)
        {
            return Ok(await _quotationService.GetByIdAsync(quotationId));
        }

        [HttpPost]
        [Route("Generate")]
        public async Task<IActionResult> Generate([FromBody]List<Quotation> quotations)
        {
            await _quotationService.GenerateQuotationsAsync(quotations);

            return Ok();
        }

        [HttpDelete]
        [Route("{quotationId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int quotationId, int userId)
        {
            await _quotationService.CancelAsync(quotationId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]Quotation quotation)
        {
            switch (quotation.Action)
            {
                case "submit": await _quotationService.SubmitAsync(quotation.QuotationId, quotation.ModifiedUserId.Value); break;
                case "confirm": await _quotationService.ConfirmAsync(quotation); break;
                case "complete": await _quotationService.CompleteAsync(quotation.QuotationId, quotation.ModifiedUserId.Value); break;
                case "reverse": await _quotationService.ReverseAsync(quotation); break;
                case "update": await _quotationService.SaveAsync(quotation); break;
                case "copy": await _quotationService.CloneAsync(quotation); break;
                case "convert": return Ok(await _quotationService.ConvertToPurchaseOrderAsync(quotation));
                case "send for approval": await _quotationService.SendForApprovalAsync(quotation.QuotationId, quotation.CompanyId.Value, quotation.ModifiedUserId.Value); break;
                case "update work flow": await _quotationService.UpdateWorkFlowStatusAsync(quotation); break;
            }

            return Ok();
        }
    }
}