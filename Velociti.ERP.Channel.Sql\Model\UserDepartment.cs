﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("UserDepartments", Schema = "adm")]
    public partial class UserDepartment
    {
        [Key]
        public int UserDepartmentId { get; set; }
        public int UserId { get; set; }
        public int DepartmentId { get; set; }
        public bool? IsDefault { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("UserDepartments")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(UserId))]
        [InverseProperty("UserDepartments")]
        public virtual User User { get; set; }
    }
}
