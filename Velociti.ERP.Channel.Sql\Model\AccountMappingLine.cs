﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("AccountMappingLines", Schema = "fin")]
    public partial class AccountMappingLine
    {
        public AccountMappingLine()
        {
            AccountMappingLineDetails = new HashSet<AccountMappingLineDetail>();
        }

        [Key]
        public int AccountMappingLineId { get; set; }
        public int? AccountMappingId { get; set; }
        public byte? GeneralLedgerAccountEnum { get; set; }
        [StringLength(255)]
        public string GeneralLedgerAccountName { get; set; }
        public int? ChartOfAccountId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountMappingId))]
        [InverseProperty("AccountMappingLines")]
        public virtual AccountMapping AccountMapping { get; set; }
        [ForeignKey(nameof(ChartOfAccountId))]
        [InverseProperty("AccountMappingLines")]
        public virtual ChartOfAccount ChartOfAccount { get; set; }
        [InverseProperty(nameof(AccountMappingLineDetail.AccountMappingLine))]
        public virtual ICollection<AccountMappingLineDetail> AccountMappingLineDetails { get; set; }
    }
}
