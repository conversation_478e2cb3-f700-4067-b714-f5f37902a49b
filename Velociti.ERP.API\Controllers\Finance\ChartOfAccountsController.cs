﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class ChartOfAccountsController : ControllerBase
    {
        private readonly IChartOfAccountService _chartOfAccountService;

        public ChartOfAccountsController(IChartOfAccountService chartOfAccountService)
        {
            _chartOfAccountService = chartOfAccountService;
        }

        [HttpGet]
        [Route("Hierarchy/{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _chartOfAccountService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _chartOfAccountService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortListByType/{typeEnum}/company/{companyId}")]
        public async Task<IActionResult> GetShortListByType(int companyId, byte typeEnum)
        {
            var list = await _chartOfAccountService.GetShortListByTypeAsync(companyId, typeEnum);

            return Ok(list);
        }

        [HttpGet]
        [Route("DetailShortList/{companyId}")]
        public async Task<IActionResult> GetDetailShortList(int companyId)  
        {
            var list = await _chartOfAccountService.GetDetailShortListAsync(companyId);  

            return Ok(list);
        }

        [HttpGet]
        [Route("AnalysisCodeShortList/{companyId}")]
        public async Task<IActionResult> GetAnalysisCodeShortList(int companyId)
        {
            return Ok(await _chartOfAccountService.GetAnalysisCodeShortListAsync(companyId));
        }

        [HttpGet]
        [Route("ShortListByAnalysisCode/{analysisCode}")]
        public async Task<IActionResult> GetShortList(string analysisCode)
        {
            return Ok(await _chartOfAccountService.GetShortListAsync(analysisCode));
        }

        [HttpGet]
        [Route("ShortList/{companyId}/Levels/{levels}")]
        public async Task<IActionResult> GetShortListByLevelsAsync(int companyId, string levels)
        {
            return Ok(await _chartOfAccountService.GetShortListByLevelsAsync(companyId, levels.Split(',')));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]ChartOfAccount chartOfAccount)
        {
            await _chartOfAccountService.SaveAsync(chartOfAccount);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Delete([FromBody]ChartOfAccount chartOfAccount)
        {
            await _chartOfAccountService.ToggleActivationAsync(chartOfAccount.ChartOfAccountId, chartOfAccount.ModifiedUserId);

            return Ok();
        }
    }
}