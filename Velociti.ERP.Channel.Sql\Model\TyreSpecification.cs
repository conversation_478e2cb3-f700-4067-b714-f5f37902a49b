﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("TyreSpecifications", Schema = "inv")]
    public partial class TyreSpecification
    {
        public TyreSpecification()
        {
            SalesReturnLines = new HashSet<SalesReturnLine>();
        }

        [Key]
        public int TyreSpecificationId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string TyreSpecificationCode { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? NetWeight { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? GrossWeight { get; set; }
        [StringLength(255)]
        public string Design { get; set; }
        [StringLength(50)]
        public string Measure { get; set; }
        [StringLength(50)]
        public string RimSize { get; set; }
        [StringLength(255)]
        public string Structure { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column("NoOfTyresPerPallet ")]
        public int? NoOfTyresPerPallet { get; set; }
        public int? ProductId { get; set; }
        public int? NoOfTyresPerBox { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("TyreSpecifications")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("TyreSpecifications")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(SalesReturnLine.TyreSpecification))]
        public virtual ICollection<SalesReturnLine> SalesReturnLines { get; set; }
    }
}
