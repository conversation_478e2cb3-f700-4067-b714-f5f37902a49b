﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class ResourcesController : ControllerBase
    {
        private readonly IResourceService _resourceService;

        public ResourcesController(IResourceService resourceService)
        {
            _resourceService = resourceService;
        }

        [HttpGet]
        [Route("ShortList/{moduleId}")]
        public async Task<IActionResult> GetShortList(int moduleId)
        {
            return Ok(await _resourceService.GetShortListAsync(moduleId));
        }

        [HttpGet]
        [Route("ForPermission/company/{companyId}/module/{moduleId}/role/{roleId}")]
        public async Task<IActionResult> GetForAssign(int companyId, int moduleId, int roleId)
        {
            return Ok(await _resourceService.GetForAssignAsync(companyId, moduleId, roleId));
        }
    }
}