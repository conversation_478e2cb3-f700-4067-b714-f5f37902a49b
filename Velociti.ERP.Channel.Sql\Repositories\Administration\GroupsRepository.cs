﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class GroupsRepository : IGroupsRepository
    {
        private readonly MarangoniERPContext _context;

        public GroupsRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<Group> FindByIdAsync(int groupId)
        {
            return await _context.Groups.FirstOrDefaultAsync(c => c.GroupId == groupId);
        }

        public async Task<IEnumerable<Group>> GetAllAsync()
        {
            return await _context.Groups.ToListAsync();
        }

        public async Task SaveAsync(Group group)
        {
            if (group.GroupId == default)
            {
                await _context.Groups.AddAsync(group);
            }
            else
            {
                _context.Groups.Attach(group);
                _context.Entry(group).State = EntityState.Modified;
            }

            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var record = await _context.Groups.FirstAsync(c => c.GroupId == id);

            _context.Groups.Remove(record);
            await _context.SaveChangesAsync();
        }
    }
}
