﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("COAMappings", Schema = "fin")]
    public partial class Coamapping
    {
        [Key]
        [Column("COAMappingId")]
        public int CoamappingId { get; set; }
        public int CompanyId { get; set; }
        public byte? DocTypeEnum { get; set; }
        public int? ParentChartOfAccountId { get; set; }
        public int? ChildChartOfAccountId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChildChartOfAccountId))]
        [InverseProperty(nameof(ChartOfAccount.CoamappingChildChartOfAccounts))]
        public virtual ChartOfAccount ChildChartOfAccount { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Coamappings")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ParentChartOfAccountId))]
        [InverseProperty(nameof(ChartOfAccount.CoamappingParentChartOfAccounts))]
        public virtual ChartOfAccount ParentChartOfAccount { get; set; }
    }
}
