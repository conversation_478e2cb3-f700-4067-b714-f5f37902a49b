﻿using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class WarehousesController : ControllerBase
    {
        private readonly IWarehouseService _warehouseService;

        public WarehousesController(IWarehouseService warehouseService)
        {
            _warehouseService = warehouseService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> Get(int companyId)
        {
            return Ok(await _warehouseService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("Customer/{customerId}")]
        public async Task<IActionResult> GetAllByCustomerIdAsync(int customerId)
        {
            return Ok(await _warehouseService.GetAllByCustomerIdAsync(customerId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _warehouseService.GetShortListAsync(companyId, false);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Department/{departmentId}")]
        public async Task<IActionResult> GetComapnyDepartmentShortList(int companyId, int departmentId)
        {
            var list = await _warehouseService.GetComapnyDepartmentShortList(companyId, departmentId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortListAll/{companyId}")]
        public async Task<IActionResult> GetShortListAll(int companyId)
        {
            var list = await _warehouseService.GetShortListAsync(companyId, true);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortListWithWIP/{companyId}")]
        public async Task<IActionResult> GetShortListWithWIP(int companyId)
        {
            Console.WriteLine("#######################" + companyId);
            var list = await _warehouseService.GetShortListWithWIPAsync(companyId, true);

            return Ok(list);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("ShortListByNature/nature/{natureEnum}/company/{companyId}")]
        public async Task<IActionResult> GetShortListByNature(byte natureEnum, int companyId)
        {
            var list = await _warehouseService.GetShortListByNatureAsync(companyId, (Warehouse.WarehouseNature)natureEnum);

            return Ok(list);
        }

        [HttpGet]
        [Route("SemiFinishedGoodsShortList/{companyId}")]
        public async Task<IActionResult> GetSemiFinishedGoodsShortList(int companyId)  
        {
            var list = await _warehouseService.GetSemiFinishedGoodsShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("FinishedGoodsShortList/{companyId}")]
        public async Task<IActionResult> GetFinishedGoodsShortList(int companyId)
        {
            var list = await _warehouseService.GetFinishedGoodsShortListAsync(companyId);
             
            return Ok(list);
        }

        [HttpGet]
        [Route("ShortListVirtualPhysical/{companyId}")]
        public async Task<IActionResult> ShortListVirtualPhysical(int companyId)
        {
            var list = await _warehouseService.GetShortListVirtualPhysicalAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList")]
        public async Task<IActionResult> GetShortListByCodeAndCompanyAsync(int companyId, string code)
        {
            return Ok(await _warehouseService.GetShortListByCodeAndCompanyAsync(code, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Warehouse warehouse)
        {
            if (warehouse == null)
                return BadRequest();

            await _warehouseService.SaveAsync(warehouse);

            return Ok();
        }

        [HttpDelete]
        [Route("{warehouseId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int warehouseId, int userId)
        {
            await _warehouseService.ToggleActivationAsync(warehouseId, userId);

            return Ok();
        }
    }
}