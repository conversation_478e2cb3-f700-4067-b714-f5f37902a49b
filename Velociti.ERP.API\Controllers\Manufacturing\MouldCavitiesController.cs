﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class MouldCavitiesController : ControllerBase
    {
        private readonly IMouldCavityService _mouldCavityService;

        public MouldCavitiesController(IMouldCavityService mouldCavityService)
        {
            _mouldCavityService = mouldCavityService;
        }

        [HttpGet]
        [Route("{mouldId}")]
        public async Task<IActionResult> GetAll(int mouldId)
        {
            return Ok(await _mouldCavityService.GetAllAsync(mouldId));
        }

        [HttpGet]
        [Route("ShortList/Machine/{machineId}")]
        public async Task<IActionResult> GetShortListByMachine(int machineId, int dayLightNo)
        {
            return Ok(await _mouldCavityService.GetShortListByMachineAsync(machineId));
        }

        [HttpGet]
        [Route("ShortList/Machine/{machineId}/DayLight/{dayLightNo}")]
        public async Task<IActionResult> GetShortListByMachineAndDayLight(int machineId, int dayLightNo)
        {
            return Ok(await _mouldCavityService.GetShortListByMachineAndDayLightAsync(machineId, dayLightNo));
        }

        [HttpPost]
        [Route("{companyId}")]
        public async Task<IActionResult> Save([FromBody]MouldCavity mouldCavity, int companyId)
        {
            await _mouldCavityService.SaveAsync(mouldCavity, companyId);

            return Ok();
        }

        [HttpDelete]
        [Route("{companyId}")]
        public async Task<IActionResult> ToggleActivation([FromBody]MouldCavity mouldCavity, int companyId)
        {
            await _mouldCavityService.DeleteAsync(mouldCavity.CavityId, companyId, mouldCavity.ModifiedUserId.Value);

            return Ok();
        }
    }
}