﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BankAdjustmentLines", Schema = "fin")]
    public partial class BankAdjustmentLine
    {
        public BankAdjustmentLine()
        {
            BankAdjustmentCostAllocations = new HashSet<BankAdjustmentCostAllocation>();
        }

        [Key]
        public int BankAdjustmentLineId { get; set; }
        public int? BankAdjustmentId { get; set; }
        public int? ChartOfAccountId { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BankAdjustmentId))]
        [InverseProperty("BankAdjustmentLines")]
        public virtual BankAdjustment BankAdjustment { get; set; }
        [ForeignKey(nameof(ChartOfAccountId))]
        [InverseProperty("BankAdjustmentLines")]
        public virtual ChartOfAccount ChartOfAccount { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("BankAdjustmentLines")]
        public virtual TaxGroup TaxGroup { get; set; }
        [InverseProperty(nameof(BankAdjustmentCostAllocation.BankAdjustmentLine))]
        public virtual ICollection<BankAdjustmentCostAllocation> BankAdjustmentCostAllocations { get; set; }
    }
}
