﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesServiceOrderLinesController : ControllerBase
    {
        private readonly ISalesServiceOrderLineService _salesServiceOrderLineService;

        public SalesServiceOrderLinesController(ISalesServiceOrderLineService salesServiceOrderLineService)
        {
            _salesServiceOrderLineService = salesServiceOrderLineService;
        }

        [HttpPost]
        public async Task<IActionResult> GetPurchaseRequisitionNoteLine([FromBody]SalesServiceOrderLine salesServiceOrderLine)
        {
            return Ok(await _salesServiceOrderLineService.AddSalesServiceOrderLineAsync(salesServiceOrderLine));
        }

        [HttpGet]
        [Route("{salesServiceOrderId}")]
        public async Task<IActionResult> Get(int salesServiceOrderId)
        {
            return Ok(await _salesServiceOrderLineService.GetAsync(salesServiceOrderId));
        }
    }
}