﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Common;

namespace Velociti.ERP.API.Controllers.Common
{
    [Route("api/Common/[controller]")]
    [Authorize]
    [ApiController]
    public class WorkFlowLinesController : ControllerBase
    {
        private readonly IWorkFlowLineService _workFlowLineService;

        public WorkFlowLinesController(IWorkFlowLineService workFlowLineService)
        {
            _workFlowLineService = workFlowLineService;
        }

        [HttpGet]
        [Route("{workFlowId}")]
        public async Task<IActionResult> Get(int workFlowId)
        {
            return Ok(await _workFlowLineService.GetAsync(workFlowId));
        }
    }
}