﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesServiceOrders", Schema = "sales")]
    public partial class SalesServiceOrder
    {
        public SalesServiceOrder()
        {
            SalesServiceOrderLines = new HashSet<SalesServiceOrderLine>();
        }

        [Key]
        public int SalesServiceOrderId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? CustomerId { get; set; }
        public int? ConsultancyId { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [StringLength(1000)]
        public string StatusReason { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [StringLength(50)]
        public string ReferenceNumber { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("SalesServiceOrders")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("SalesServiceOrders")]
        public virtual Department Department { get; set; }
        [InverseProperty(nameof(SalesServiceOrderLine.SalesServiceOrder))]
        public virtual ICollection<SalesServiceOrderLine> SalesServiceOrderLines { get; set; }
    }
}
