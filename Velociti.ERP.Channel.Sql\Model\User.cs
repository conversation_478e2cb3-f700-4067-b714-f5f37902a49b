﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Users", Schema = "adm")]
    public partial class User
    {
        public User()
        {
            FixedAssets = new HashSet<FixedAsset>();
            SemiFinishedGoodsQcscannings = new HashSet<SemiFinishedGoodsQcscanning>();
            Tokens = new HashSet<Token>();
            TxnWorkFlowLines = new HashSet<TxnWorkFlowLine>();
            UserCompanies = new HashSet<UserCompany>();
            UserDepartments = new HashSet<UserDepartment>();
            UserDivisions = new HashSet<UserDivision>();
            UserGroups = new HashSet<UserGroup>();
            UserRoles = new HashSet<UserRole>();
            UserSupervisors = new HashSet<UserSupervisor>();
            WorkFlowLines = new HashSet<WorkFlowLine>();
        }

        [Key]
        public int UserId { get; set; }
        [StringLength(50)]
        public string UserCode { get; set; }
        public int? TitleId { get; set; }
        [Required]
        [StringLength(100)]
        public string Fullname { get; set; }
        [Required]
        [StringLength(32)]
        public string DisplayName { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        public byte[] Signature { get; set; }
        public bool? IsFilteredByDepartment { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(TitleId))]
        [InverseProperty(nameof(SupportData.Users))]
        public virtual SupportData Title { get; set; }
        [InverseProperty("LoginNavigation")]
        public virtual Login Login { get; set; }
        [InverseProperty(nameof(FixedAsset.User))]
        public virtual ICollection<FixedAsset> FixedAssets { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsQcscanning.QcengineerUser))]
        public virtual ICollection<SemiFinishedGoodsQcscanning> SemiFinishedGoodsQcscannings { get; set; }
        [InverseProperty(nameof(Token.User))]
        public virtual ICollection<Token> Tokens { get; set; }
        [InverseProperty(nameof(TxnWorkFlowLine.User))]
        public virtual ICollection<TxnWorkFlowLine> TxnWorkFlowLines { get; set; }
        [InverseProperty(nameof(UserCompany.User))]
        public virtual ICollection<UserCompany> UserCompanies { get; set; }
        [InverseProperty(nameof(UserDepartment.User))]
        public virtual ICollection<UserDepartment> UserDepartments { get; set; }
        [InverseProperty(nameof(UserDivision.User))]
        public virtual ICollection<UserDivision> UserDivisions { get; set; }
        [InverseProperty(nameof(UserGroup.User))]
        public virtual ICollection<UserGroup> UserGroups { get; set; }
        [InverseProperty(nameof(UserRole.User))]
        public virtual ICollection<UserRole> UserRoles { get; set; }
        [InverseProperty(nameof(UserSupervisor.User))]
        public virtual ICollection<UserSupervisor> UserSupervisors { get; set; }
        [InverseProperty(nameof(WorkFlowLine.User))]
        public virtual ICollection<WorkFlowLine> WorkFlowLines { get; set; }
    }
}
