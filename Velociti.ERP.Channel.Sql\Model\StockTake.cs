﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockTakes", Schema = "inv")]
    public partial class StockTake
    {
        public StockTake()
        {
            StockTakeLines = new HashSet<StockTakeLine>();
        }

        [Key]
        public int StockTakeId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? WarehouseId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? TxnWorkFlowId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("StockTakes")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("StockTakes")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.StockTakes))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(StockTakeLine.StockTake))]
        public virtual ICollection<StockTakeLine> StockTakeLines { get; set; }
    }
}
