﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class CommercialInvoicesController : ControllerBase
    {
        private readonly ICommercialInvoiceService _commercialInvoiceService;
        private readonly ISalesInvoiceService _salesInvoiceService;

        public CommercialInvoicesController(ICommercialInvoiceService commercialInvoiceService
            , ISalesInvoiceService salesInvoiceService)
        {
            _commercialInvoiceService = commercialInvoiceService;
            _salesInvoiceService = salesInvoiceService;
        }

        [HttpGet]
        [Route("Single/{salesInvoiceId}")]
        public async Task<IActionResult> FindById(int salesInvoiceId)
        {
            return Ok(await _salesInvoiceService.FindByIdAsync(salesInvoiceId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _commercialInvoiceService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]SalesInvoice salesInvoice)
        {
            switch (salesInvoice.Action)
            {
                case "save": await _commercialInvoiceService.SaveAsync(salesInvoice); break;
                case "generate": return Ok(await _commercialInvoiceService.GenerateCommercialInvoiceAsync(salesInvoice));
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{salesInvoiceId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int salesInvoiceId, int userId)
        {
            await _commercialInvoiceService.CancelAsync(salesInvoiceId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesInvoice salesInvoice)
        {
            switch (salesInvoice.Action)
            {
                case "copy": await _commercialInvoiceService.CreateNewVersionAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value); break;
                case "submit": await _commercialInvoiceService.SubmitAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value); break;
                case "confirm": await _commercialInvoiceService.ConfirmAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value); break;
                case "create LP": return Ok(await _commercialInvoiceService.CreateLoadingPlanVersionAsync(salesInvoice.SalesInvoiceId, salesInvoice.ModifiedUserId.Value));
            }

            return Ok();
        }
    }
}