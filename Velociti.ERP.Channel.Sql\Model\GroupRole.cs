﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GroupRoles", Schema = "adm")]
    public partial class GroupRole
    {
        [Key]
        public int GroupId { get; set; }
        [Key]
        public int RoleId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(GroupId))]
        [InverseProperty("GroupRoles")]
        public virtual Group Group { get; set; }
        [ForeignKey(nameof(RoleId))]
        [InverseProperty("GroupRoles")]
        public virtual Role Role { get; set; }
    }
}
