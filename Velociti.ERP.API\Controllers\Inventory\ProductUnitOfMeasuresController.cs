﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Authorize]
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class ProductUnitOfMeasuresController : ControllerBase
    {
        private readonly IProductUnitOfMeasureService _unitOfMeasureService;

        public ProductUnitOfMeasuresController(IProductUnitOfMeasureService unitOfMeasureService)
        {
            this._unitOfMeasureService = unitOfMeasureService;
        }

        [HttpGet]
        [Route("{productId}")]
        public async Task<IActionResult> Get(int productId)
        {
            return Ok( await this._unitOfMeasureService.GetAllByProduct(productId));
        }

        [HttpGet]
        [Route("ShortList/{productId}")]
        public async Task<IActionResult> GetShortList(int productId)
        {
            return Ok( await this._unitOfMeasureService.GetShortListAsync(productId));
        }

        [HttpGet]
        [Route("ShortList/{productId}/UOM/{uomId}")]
        public async Task<IActionResult> GetSecondaryUOM(int productId, int uomId)
        {
            return Ok(await this._unitOfMeasureService.GetProductSecondaryUnitOfMeasure(productId, uomId));
        }

        [HttpGet]
        [Route("SecondaryUOM/product/{productId}")]
        public async Task<IActionResult> GetSecondaryUOM(int productId)
        {
            return Ok(await this._unitOfMeasureService.GetProductSecondaryUnitOfMeasure(productId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ProductUnitOfMeasure unitOfMeasure)
        {
            if (unitOfMeasure == null)
                return BadRequest();

            await _unitOfMeasureService.SaveAsync(unitOfMeasure);

            return Ok();
        }

        [HttpDelete]
        [Route("{unitOfMeasureId}/Product/{productId}/User/{modifiedUserId}")]
        public async Task<IActionResult> ToggleActivation(int unitOfMeasureId,int productId, int modifiedUserId)
        {
            await _unitOfMeasureService.ToggleActivationAsync(productId,unitOfMeasureId, modifiedUserId);

            return Ok();
        }

    }
}