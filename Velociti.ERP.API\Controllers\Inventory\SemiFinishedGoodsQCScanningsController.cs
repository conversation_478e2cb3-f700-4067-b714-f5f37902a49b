﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class SemiFinishedGoodsQCScanningsController : ControllerBase    
    {
        private readonly ISemiFinishedGoodsQCScanningService _semiFinishedGoodsQCScanningService;      

        public SemiFinishedGoodsQCScanningsController(ISemiFinishedGoodsQCScanningService semiFinishedGoodsQCScanningService)
        {
            _semiFinishedGoodsQCScanningService = semiFinishedGoodsQCScanningService;
        }

        [HttpGet]
        [Route("Single/{semiFinishedGoodsQCScanningId}")]
        public async Task<IActionResult> FindById(int semiFinishedGoodsQCScanningId) 
        {
            return Ok(await _semiFinishedGoodsQCScanningService.FindByIdAsync(semiFinishedGoodsQCScanningId));
        }

        [HttpGet]
        [Route("SingleByBarcode/product/{productId}/barcode/{barcode}")]
        public async Task<IActionResult> FindByBarcode(int productId, string barcode)
        {
            return Ok(await _semiFinishedGoodsQCScanningService.FindByBarcodeAsync(productId, barcode));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, DateTime startDate, DateTime endDate, int userId)
        {
            return Ok(await _semiFinishedGoodsQCScanningService.GetAllAsync(companyId, startDate, endDate, userId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]SemiFinishedGoodsQCScanning semiFinishedGoodsQCScanning)
        {
            await _semiFinishedGoodsQCScanningService.SaveAsync(semiFinishedGoodsQCScanning);

            return Ok();
        }

        [HttpDelete]
        [Route("{semiFinishedGoodsQCScanningId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int semiFinishedGoodsQCScanningId, int userId)  
        {
            await _semiFinishedGoodsQCScanningService.CancelAsync(semiFinishedGoodsQCScanningId, userId);  

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SemiFinishedGoodsQCScanning semiFinishedGoodsQCScanning)
        {
            switch (semiFinishedGoodsQCScanning.Action)
            {
                case "validate": 
                    await _semiFinishedGoodsQCScanningService.ValidateAsync(semiFinishedGoodsQCScanning.SemiFinishedGoodsQcscanningId, semiFinishedGoodsQCScanning.ModifiedUserId.Value); 
                    break;
                case "cancel":
                    await _semiFinishedGoodsQCScanningService.CancelAsync(semiFinishedGoodsQCScanning.SemiFinishedGoodsQcscanningId, semiFinishedGoodsQCScanning.ModifiedUserId.Value);
                    break;
                case "scrap":
                    await _semiFinishedGoodsQCScanningService.SubmitAsync(semiFinishedGoodsQCScanning.SemiFinishedGoodsQcscanningId, semiFinishedGoodsQCScanning.ModifiedUserId.Value, (byte)SemiFinishedGoodsQCScanning.Status.Scrap, semiFinishedGoodsQCScanning.ReworkAction);
                    break;
                case "rework":
                    await _semiFinishedGoodsQCScanningService.SubmitAsync(semiFinishedGoodsQCScanning.SemiFinishedGoodsQcscanningId, semiFinishedGoodsQCScanning.ModifiedUserId.Value, (byte)SemiFinishedGoodsQCScanning.Status.Rework, semiFinishedGoodsQCScanning.ReworkAction);
                    break;
            }

            return Ok();
        }
    }
}