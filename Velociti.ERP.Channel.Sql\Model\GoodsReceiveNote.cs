﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GoodsReceiveNotes", Schema = "inv")]
    public partial class GoodsReceiveNote
    {
        public GoodsReceiveNote()
        {
            BankAdjustmentCostAllocations = new HashSet<BankAdjustmentCostAllocation>();
            ExchangeOrders = new HashSet<ExchangeOrder>();
            GoodsReceiveNoteLines = new HashSet<GoodsReceiveNoteLine>();
            InboundReceiptCostAllocations = new HashSet<InboundReceiptCostAllocation>();
            ManualJournalCostAllocations = new HashSet<ManualJournalCostAllocation>();
            OutboundPaymentCostAllocations = new HashSet<OutboundPaymentCostAllocation>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            PettyCashCostAllocations = new HashSet<PettyCashCostAllocation>();
            PurchaseReturns = new HashSet<PurchaseReturn>();
            ServiceInvoices = new HashSet<ServiceInvoice>();
            ShipmentQualityControls = new HashSet<ShipmentQualityControl>();
        }

        [Key]
        public int GoodsReceiveNoteId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        public int? PurchaseOrderId { get; set; }
        public int? LoanOrderId { get; set; }
        public int? SupplierId { get; set; }
        public string VATNo { get; set; }
        public int? SupplementaryManufacturerId { get; set; }
        [StringLength(255)]
        public string SupplierAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        public int? CustomerId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DueDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? PostDate { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [StringLength(50)]
        public string InvoiceNumber { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        [StringLength(50)]
        public string SupplierReference { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? ShipmentPackingMethodId { get; set; }
        [StringLength(255)]
        public string TermsAndConditionsId { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ShipmentOriginatedDate { get; set; }
        public int? ShipmentOriginatedWeek { get; set; }
        public int? PaymentTermId { get; set; }
        [StringLength(255)]
        public string PortOfShipment { get; set; }
        [StringLength(50)]
        public string VehicleNo { get; set; }
        [StringLength(50)]
        public string VesselNo { get; set; }
        [StringLength(50)]
        public string ContainerNo { get; set; }
        public int? ShippingTermId { get; set; }
        public int? AccountId { get; set; }
        public byte? AssetTypeEnum { get; set; }
        public int? TxnWorkFlowId { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("GoodsReceiveNotes")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("GoodsReceiveNotes")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("GoodsReceiveNotes")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("GoodsReceiveNotes")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(PurchaseOrderId))]
        [InverseProperty("GoodsReceiveNotes")]
        public virtual PurchaseOrder PurchaseOrder { get; set; }
        [ForeignKey(nameof(SupplementaryManufacturerId))]
        [InverseProperty("GoodsReceiveNotes")]
        public virtual SupplementaryManufacturer SupplementaryManufacturer { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("GoodsReceiveNotes")]
        public virtual Supplier Supplier { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("GoodsReceiveNotes")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [InverseProperty(nameof(BankAdjustmentCostAllocation.GoodsReceiveNote))]
        public virtual ICollection<BankAdjustmentCostAllocation> BankAdjustmentCostAllocations { get; set; }
        [InverseProperty(nameof(ExchangeOrder.GoodsReceiveNote))]
        public virtual ICollection<ExchangeOrder> ExchangeOrders { get; set; }
        [InverseProperty(nameof(GoodsReceiveNoteLine.GoodsReceiveNote))]
        public virtual ICollection<GoodsReceiveNoteLine> GoodsReceiveNoteLines { get; set; }
        [InverseProperty(nameof(InboundReceiptCostAllocation.GoodsReceiveNote))]
        public virtual ICollection<InboundReceiptCostAllocation> InboundReceiptCostAllocations { get; set; }
        [InverseProperty(nameof(ManualJournalCostAllocation.GoodsReceiveNote))]
        public virtual ICollection<ManualJournalCostAllocation> ManualJournalCostAllocations { get; set; }
        [InverseProperty(nameof(OutboundPaymentCostAllocation.GoodsReceiveNote))]
        public virtual ICollection<OutboundPaymentCostAllocation> OutboundPaymentCostAllocations { get; set; }
        [InverseProperty(nameof(PaymentAdvice.GoodsReceiveNote))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(PettyCashCostAllocation.GoodsReceiveNote))]
        public virtual ICollection<PettyCashCostAllocation> PettyCashCostAllocations { get; set; }
        [InverseProperty(nameof(PurchaseReturn.GoodsReceiveNote))]
        public virtual ICollection<PurchaseReturn> PurchaseReturns { get; set; }
        [InverseProperty(nameof(ServiceInvoice.GoodsReceiveNote))]
        public virtual ICollection<ServiceInvoice> ServiceInvoices { get; set; }
        [InverseProperty(nameof(ShipmentQualityControl.GoodsReceiveNote))]
        public virtual ICollection<ShipmentQualityControl> ShipmentQualityControls { get; set; }
    }
}
