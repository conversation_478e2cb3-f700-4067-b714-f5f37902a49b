﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_WorkStation_Cadre")]
    public partial class IWorkStationCadre
    {
        [StringLength(255)]
        public string Category { get; set; }
        [StringLength(255)]
        public string Type { get; set; }
        [StringLength(255)]
        public string Machine { get; set; }
        [StringLength(255)]
        public string D { get; set; }
        [StringLength(255)]
        public string E { get; set; }
        [StringLength(255)]
        public string F { get; set; }
        [StringLength(255)]
        public string G { get; set; }
        [StringLength(255)]
        public string H { get; set; }
        [StringLength(255)]
        public string I { get; set; }
        [StringLength(255)]
        public string J { get; set; }
        [StringLength(255)]
        public string K { get; set; }
        [StringLength(255)]
        public string L { get; set; }
        [StringLength(255)]
        public string M { get; set; }
        [StringLength(255)]
        public string N { get; set; }
        [StringLength(255)]
        public string O { get; set; }
        [StringLength(255)]
        public string P { get; set; }
        [StringLength(255)]
        public string Q { get; set; }
    }
}
