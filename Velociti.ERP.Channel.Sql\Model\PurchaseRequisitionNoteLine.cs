﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseRequisitionNoteLines", Schema = "prc")]
    public partial class PurchaseRequisitionNoteLine
    {
        public PurchaseRequisitionNoteLine()
        {
            PurchaseOrderLines = new HashSet<PurchaseOrderLine>();
            QuotationLines = new HashSet<QuotationLine>();
        }

        [Key]
        public int PurchaseRequisitionNoteLineId { get; set; }
        public int PurchaseRequisitionNoteId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? WarehouseId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [StringLength(50)]
        public string WarrantyPeriod { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ExpectedDeliveryDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ClosedDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.PurchaseRequisitionNoteLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.PurchaseRequisitionNoteLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("PurchaseRequisitionNoteLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(PurchaseRequisitionNoteId))]
        [InverseProperty("PurchaseRequisitionNoteLines")]
        public virtual PurchaseRequisitionNote PurchaseRequisitionNote { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.PurchaseRequisitionNoteLines))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(PurchaseOrderLine.PurchaseRequisitionNoteLine))]
        public virtual ICollection<PurchaseOrderLine> PurchaseOrderLines { get; set; }
        [InverseProperty(nameof(QuotationLine.PurchaseRequisitionNoteLine))]
        public virtual ICollection<QuotationLine> QuotationLines { get; set; }
    }
}
