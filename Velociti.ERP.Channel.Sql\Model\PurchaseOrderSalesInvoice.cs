﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseOrderSalesInvoices", Schema = "prc")]
    public partial class PurchaseOrderSalesInvoice
    {
        [Key]
        public int PurchaseOrderSalesInvoiceId { get; set; }
        public int PurchaseOrderId { get; set; }
        public int SalesInvoiceId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(PurchaseOrderId))]
        [InverseProperty("PurchaseOrderSalesInvoices")]
        public virtual PurchaseOrder PurchaseOrder { get; set; }
        [ForeignKey(nameof(SalesInvoiceId))]
        [InverseProperty("PurchaseOrderSalesInvoices")]
        public virtual SalesInvoice SalesInvoice { get; set; }
    }
}
