﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Sql.Services.Inventory;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductBrandDiscountController : ControllerBase
    {
        private readonly IProductBrandDiscountService _productBrandDiscountService;

        public ProductBrandDiscountController(IProductBrandDiscountService productBrandDiscountService)
        {
            _productBrandDiscountService = productBrandDiscountService;
        }

        [HttpGet]
        [Route("GetAll/{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _productBrandDiscountService.GetProductBrandDisountsAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]ProductBrandDiscount productBrandDiscount)
        {
            await _productBrandDiscountService.UpdateProductBrandDiscount(productBrandDiscount);
            return Ok();
        }
    }
}