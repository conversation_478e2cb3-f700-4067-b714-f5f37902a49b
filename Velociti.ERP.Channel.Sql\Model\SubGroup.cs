﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SubGroups", Schema = "adm")]
    public partial class SubGroup
    {
        public SubGroup()
        {
            Companies = new HashSet<Company>();
        }

        [Key]
        public int SubGroupId { get; set; }
        public int HoldingId { get; set; }
        [Required]
        [StringLength(50)]
        public string SubGroupCode { get; set; }
        [Required]
        [StringLength(255)]
        public string SubGroupName { get; set; }
        [StringLength(255)]
        public string StreetAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        public byte[] CompanyLogo { get; set; }
        [StringLength(50)]
        public string TaxRegistrationNo { get; set; }
        [StringLength(20)]
        public string Telephone { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string Website { get; set; }
        public int? CurrencyId { get; set; }
        public int? DateFormatId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? FiscalYear { get; set; }
        [Column(TypeName = "date")]
        public DateTime? FinancialPeriod { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(HoldingId))]
        [InverseProperty("SubGroups")]
        public virtual Holding Holding { get; set; }
        [InverseProperty(nameof(Company.SubGroup))]
        public virtual ICollection<Company> Companies { get; set; }
    }
}
