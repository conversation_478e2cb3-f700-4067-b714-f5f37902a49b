﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("HRSettings", Schema = "hr")]
    public partial class Hrsetting
    {
        [Key]
        [Column("HRSettingsId")]
        public int HrsettingsId { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? NoPayDeductAmount { get; set; }
        public int? MaxNoPayCount { get; set; }
        public int? CompanyId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
    }
}
