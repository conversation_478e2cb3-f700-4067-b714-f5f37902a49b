﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.FixedAssets;
using Velociti.ERP.Domain.Services.FixedAssets;

namespace Velociti.ERP.API.Controllers.FixedAssets
{
    [Route("api/FixedAssets/[controller]")]
    [Authorize]
    [ApiController]
    public class FixedAssetMaintenanceController : ControllerBase  
    {
        private readonly IFixedAssetMaintenanceService _fixedAssetMaintenanceService;  

        public FixedAssetMaintenanceController(IFixedAssetMaintenanceService fixedAssetMaintenanceService)
        {
            _fixedAssetMaintenanceService = fixedAssetMaintenanceService;
        }

        [HttpGet]
        [Route("FixedAsset/{fixedAssetId}")]
        public async Task<IActionResult> GetAll(int fixedAssetId)
        {
            return Ok(await _fixedAssetMaintenanceService.GetAllAsync(fixedAssetId));
        }

        [HttpGet]
        [Route("{fixedAssetMaintenanceId}")]
        public async Task<IActionResult> GetById(int fixedAssetMaintenanceId)
        {
            return Ok(await _fixedAssetMaintenanceService.FindByIdAsync(fixedAssetMaintenanceId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]FixedAssetMaintenance fixedAssetMaintenance)
        {
            return Ok(await _fixedAssetMaintenanceService.SaveAsync(fixedAssetMaintenance));
        }

        [HttpDelete]
        [Route("{fixedAssetMaintenanceId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int fixedAssetMaintenanceId, int userId)
        {
            await _fixedAssetMaintenanceService.ToggleActivationAsync(fixedAssetMaintenanceId, userId);

            return Ok();
        }
    }
}