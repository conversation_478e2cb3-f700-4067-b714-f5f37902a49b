﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("EmployeeAccounts", Schema = "hr")]
    public partial class EmployeeAccount
    {
        [Key]
        public int EmployeeId { get; set; }
        [Key]
        public int AccountId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("EmployeeAccounts")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("EmployeeAccounts")]
        public virtual Employee Employee { get; set; }
    }
}
