﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Attendances", Schema = "hr")]
    public partial class Attendance
    {
        [Key]
        public int AttendanceId { get; set; }
        public int? CompanyId { get; set; }
        public int? EmployeeId { get; set; }
        [Column(TypeName = "date")]
        public DateTime Date { get; set; }
        [Column(TypeName = "time(0)")]
        public TimeSpan? TimeIn { get; set; }
        [Column(TypeName = "time(0)")]
        public TimeSpan? TimeOut { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public bool? IsFreezed { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Attendances")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("Attendances")]
        public virtual Employee Employee { get; set; }
    }
}
