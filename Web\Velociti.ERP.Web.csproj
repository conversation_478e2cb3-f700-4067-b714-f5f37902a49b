﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props" Condition="Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" />
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>
    </ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{CC189447-4579-434E-BD28-628E14FAEEB0}</ProjectGuid>
    <ProjectTypeGuids>{349c5851-65df-11da-9384-00065b846f21};{fae04ec0-301f-11d3-bf4b-00c04f79efbc}</ProjectTypeGuids>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Web</RootNamespace>
    <AssemblyName>Web</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <MvcBuildViews>false</MvcBuildViews>
    <UseIISExpress>true</UseIISExpress>
    <Use64BitIISExpress />
    <IISExpressSSLPort>44355</IISExpressSSLPort>
    <IISExpressAnonymousAuthentication />
    <IISExpressWindowsAuthentication />
    <IISExpressUseClassicPipelineMode />
    <UseGlobalApplicationHostFile />
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Azure.Core, Version=********, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.1.38.0\lib\net472\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Identity, Version=1.11.4.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Identity.1.11.4\lib\netstandard2.0\Azure.Identity.dll</HintPath>
    </Reference>
    <Reference Include="ClosedXML, Version=0.102.2.0, Culture=neutral, PublicKeyToken=fd1eb21b62ae805b, processorArchitecture=MSIL">
      <HintPath>..\packages\ClosedXML.0.102.2\lib\netstandard2.0\ClosedXML.dll</HintPath>
    </Reference>
    <Reference Include="DevExpress.Charts.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.CodeParser.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.Desktop.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Drawing.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Data.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.DataAccess.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Office.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.PivotGrid.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v25.1.Export, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Sparkline.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Web.ASPxThemes.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.ASPxGantt.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.ASPxTreeList.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.Mvc5.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.Resources.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Web.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGauges.v25.1.Core, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v25.1, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v25.1.Web, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v25.1.Web.WebForms, Version=25.1.3.0, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DocumentFormat.OpenXml, Version=2.16.0.0, Culture=neutral, PublicKeyToken=8fb06cb64d019a17, processorArchitecture=MSIL">
      <HintPath>..\packages\DocumentFormat.OpenXml.2.16.0\lib\net46\DocumentFormat.OpenXml.dll</HintPath>
    </Reference>
    <Reference Include="EPPlus, Version=4.5.3.2, Culture=neutral, PublicKeyToken=ea159fdaa78159a1, processorArchitecture=MSIL">
      <HintPath>..\packages\EPPlus.4.5.3.2\lib\net40\EPPlus.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader, Version=3.6.0.0, Culture=neutral, PublicKeyToken=93517dbe6a4012fa, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelDataReader.3.6.0\lib\net45\ExcelDataReader.dll</HintPath>
    </Reference>
    <Reference Include="ExcelDataReader.DataSet, Version=3.6.0.0, Culture=neutral, PublicKeyToken=93517dbe6a4012fa, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelDataReader.DataSet.3.6.0\lib\net35\ExcelDataReader.DataSet.dll</HintPath>
    </Reference>
    <Reference Include="ExcelNumberFormat, Version=1.1.0.0, Culture=neutral, PublicKeyToken=23c6f5d73be07eca, processorArchitecture=MSIL">
      <HintPath>..\packages\ExcelNumberFormat.1.1.0\lib\net20\ExcelNumberFormat.dll</HintPath>
    </Reference>
    <Reference Include="Irony, Version=1.0.11.0, Culture=neutral, PublicKeyToken=ca48ace7223ead47, processorArchitecture=MSIL">
      <HintPath>..\packages\Irony.NetCore.1.0.11\lib\net461\Irony.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=3.0.4.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.3.0.4\lib\net462\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.4\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.4\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.8.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.Cryptography, Version=8.0.0.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.Cryptography.8.0.0\lib\net462\Microsoft.Bcl.Cryptography.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\lib\net45\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="Microsoft.Data.SqlClient, Version=*******, Culture=neutral, PublicKeyToken=23ec7fc2d6eaa4a5, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.SqlClient.6.0.1\lib\net462\Microsoft.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Abstractions, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Abstractions.8.0.0\lib\net462\Microsoft.Extensions.Caching.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Caching.Memory, Version=8.0.0.1, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Caching.Memory.8.0.1\lib\net462\Microsoft.Extensions.Caching.Memory.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.DependencyInjection.Abstractions, Version=8.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.DependencyInjection.Abstractions.8.0.2\lib\net462\Microsoft.Extensions.DependencyInjection.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Logging.Abstractions, Version=8.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Logging.Abstractions.8.0.2\lib\net462\Microsoft.Extensions.Logging.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Options, Version=8.0.0.2, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Options.8.0.2\lib\net462\Microsoft.Extensions.Options.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Extensions.Primitives, Version=8.0.0.0, Culture=neutral, PublicKeyToken=adb9793829ddae60, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Extensions.Primitives.8.0.0\lib\net462\Microsoft.Extensions.Primitives.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client, Version=4.61.3.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.4.61.3\lib\net462\Microsoft.Identity.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client.Extensions.Msal, Version=4.61.3.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.Extensions.Msal.4.61.3\lib\netstandard2.0\Microsoft.Identity.Client.Extensions.Msal.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.7.5.0\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.7.5.0\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.7.5.0\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.7.5.0\lib\net472\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.OpenIdConnect.7.5.0\lib\net472\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.7.5.0\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.4.2.2\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.SystemWeb, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.SystemWeb.4.2.2\lib\net45\Microsoft.Owin.Host.SystemWeb.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.4.2.2\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.4.2.2\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.4.2.2\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.DataVisualization, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.DataVisualization.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.Design, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.Design.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.ProcessingObjectModel, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.ProcessingObjectModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebDesign, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.WebDesign.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.ReportingServices.ReportViewerControl.WebForms.150.1652.0\lib\net40\Microsoft.ReportViewer.WebForms.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Types, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Types.14.0.314.76\lib\net40\Microsoft.SqlServer.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Web.Infrastructure, Version=1.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Web.Infrastructure.1.0.0.0\lib\net40\Microsoft.Web.Infrastructure.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=1*******, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
    </Reference>
    <Reference Include="PresentationCore" />
    <Reference Include="ReportViewerForMvc, Version=1.1.1.0, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\Chaso.ReportViewerForMvc.1.1.1.1\lib\net461\ReportViewerForMvc.dll</HintPath>
    </Reference>
    <Reference Include="SixLabors.Fonts, Version=1.0.0.0, Culture=neutral, PublicKeyToken=d998eea7b14cab13, processorArchitecture=MSIL">
      <HintPath>..\packages\SixLabors.Fonts.1.0.0\lib\netstandard2.0\SixLabors.Fonts.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.5.1\lib\net461\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ClientModel, Version=1.0.0.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ClientModel.1.0.0\lib\netstandard2.0\System.ClientModel.dll</HintPath>
    </Reference>
    <Reference Include="System.ComponentModel.Composition" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Data.SqlClient, Version=4.6.1.6, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Data.SqlClient.4.8.6\lib\net461\System.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=8.0.0.1, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.8.0.1\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.Drawing" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.7.5.0\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.4.3.0\lib\net462\System.IO.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.IO.Compression" />
    <Reference Include="System.IO.FileSystem.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.AccessControl.5.0.0\lib\net461\System.IO.FileSystem.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Packaging, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Packaging.6.0.0\lib\net461\System.IO.Packaging.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory, Version=4.0.1.2, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.5.5\lib\net461\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=1.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.1.0.2\lib\net461\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=1.0.2.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.1.0.2\lib\net461\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http, Version=4.1.1.3, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Net.Http.4.3.4\lib\net46\System.Net.Http.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.7\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=4.1.4.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.5.0\lib\net46\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime, Version=4.1.1.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.4.3.1\lib\net462\System.Runtime.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.0.0\lib\net461\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.5.0.0\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Algorithms, Version=4.2.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Algorithms.4.3.0\lib\net463\System.Security.Cryptography.Algorithms.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Encoding, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Encoding.4.3.0\lib\net46\System.Security.Cryptography.Encoding.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.Pkcs, Version=8.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Pkcs.8.0.1\lib\net462\System.Security.Cryptography.Pkcs.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.Primitives, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.Primitives.4.3.0\lib\net46\System.Security.Cryptography.Primitives.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData, Version=4.0.5.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.ProtectedData.4.7.0\lib\net461\System.Security.Cryptography.ProtectedData.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.X509Certificates, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.X509Certificates.4.3.0\lib\net461\System.Security.Cryptography.X509Certificates.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.6.0.0\lib\net461\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=********, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.6.0.10\lib\net461\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.RegularExpressions, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.RegularExpressions.4.3.1\lib\net463\System.Text.RegularExpressions.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.5.4\lib\net461\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Cors.5.2.7\lib\net45\System.Web.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.DynamicData" />
    <Reference Include="System.Web.Entity" />
    <Reference Include="System.Web.ApplicationServices" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Web.Extensions" />
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Abstractions" />
    <Reference Include="System.Web.Helpers, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.Helpers.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.7\lib\net45\System.Web.Http.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.Cors, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Cors.5.2.7\lib\net45\System.Web.Http.Cors.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.7\lib\net45\System.Web.Http.WebHost.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Mvc, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Mvc.5.2.7\lib\net45\System.Web.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Razor.3.2.7\lib\net45\System.Web.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Routing" />
    <Reference Include="System.Web.WebPages, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Deployment, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Deployment.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.WebPages.Razor, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebPages.3.2.7\lib\net45\System.Web.WebPages.Razor.dll</HintPath>
    </Reference>
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Configuration" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Net.Http.WebRequest">
    </Reference>
    <Reference Include="System.Web.Optimization">
      <HintPath>..\packages\Microsoft.AspNet.Web.Optimization.1.1.3\lib\net40\System.Web.Optimization.dll</HintPath>
    </Reference>
    <Reference Include="System.Xml.Linq" />
    <Reference Include="Unity.Abstractions, Version=5.11.1.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Abstractions.5.11.1\lib\net47\Unity.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Container, Version=5.11.1.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Container.5.11.1\lib\net47\Unity.Container.dll</HintPath>
    </Reference>
    <Reference Include="Unity.Mvc, Version=5.11.1.0, Culture=neutral, PublicKeyToken=489b6accfaf20ef0, processorArchitecture=MSIL">
      <HintPath>..\packages\Unity.Mvc.5.11.1\lib\net47\Unity.Mvc.dll</HintPath>
    </Reference>
    <Reference Include="WebActivatorEx, Version=2.0.0.0, Culture=neutral, PublicKeyToken=7b26dc2a43f6a0d4, processorArchitecture=MSIL">
      <HintPath>..\packages\WebActivatorEx.2.2.0\lib\net40\WebActivatorEx.dll</HintPath>
    </Reference>
    <Reference Include="WebGrease">
      <Private>True</Private>
      <HintPath>..\packages\WebGrease.1.6.0\lib\WebGrease.dll</HintPath>
    </Reference>
    <Reference Include="Antlr3.Runtime">
      <Private>True</Private>
      <HintPath>..\packages\Antlr.3.5.0.2\lib\Antlr3.Runtime.dll</HintPath>
    </Reference>
    <Reference Include="WindowsBase" />
    <Reference Include="XLParser, Version=1.5.2.0, Culture=neutral, PublicKeyToken=63397e1e46bb91b4, processorArchitecture=MSIL">
      <HintPath>..\packages\XLParser.1.5.2\lib\net461\XLParser.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="App_Start\BundleConfig.cs" />
    <Compile Include="App_Start\FilterConfig.cs" />
    <Compile Include="App_Start\RouteConfig.cs" />
    <Compile Include="App_Start\UnityConfig.cs" />
    <Compile Include="App_Start\UnityMvcActivator.cs" />
    <Compile Include="App_Start\WebApiConfig.cs" />
    <Compile Include="Auth\IUserSession.cs" />
    <Compile Include="Auth\UserSession.cs" />
    <Compile Include="BLL\ReportsBL.cs" />
    <Compile Include="BLL\UtilsBL.cs" />
    <Compile Include="Controllers\Inventory\MonthEndController.cs" />
    <Compile Include="Filters\RequireCurrentPeriodAttribute.cs" />
    <Compile Include="Controllers\AccountController.cs" />
    <Compile Include="Controllers\Administration\EmailTemplatesController.cs" />
    <Compile Include="Controllers\Administration\AuthorizationController.cs" />
    <Compile Include="Controllers\Administration\CurrencyController.cs" />
    <Compile Include="Controllers\Administration\AppSettingsController.cs" />
    <Compile Include="Controllers\Administration\PasswordPoliciesController.cs" />
    <Compile Include="Controllers\Administration\AgingBucketsController.cs" />
    <Compile Include="Controllers\Administration\ReportDesignerController.cs" />
    <Compile Include="Controllers\Administration\UsersController.cs" />
    <Compile Include="Controllers\Common\SupportDataController.cs" />
    <Compile Include="Controllers\Common\WorkFlowsController.cs" />
    <Compile Include="Controllers\DataDumpsController.cs" />
    <Compile Include="Controllers\Finance\AccountMappingsController.cs" />
    <Compile Include="Controllers\Finance\AccountsController.cs" />
    <Compile Include="Controllers\Finance\BankAdjustmentsController.cs" />
    <Compile Include="Controllers\Finance\BankReconciliationsController.cs" />
    <Compile Include="Controllers\Finance\ChargeInformationsController.cs" />
    <Compile Include="Controllers\Finance\ChartOfAccountsController.cs" />
    <Compile Include="Controllers\Finance\CurrencyExchangeRatesController.cs" />
    <Compile Include="Controllers\Finance\FinancialStatementsController.cs" />
    <Compile Include="Controllers\Finance\GeneralLedgerLinesController.cs" />
    <Compile Include="Controllers\Finance\InboundReceiptsController.cs" />
    <Compile Include="Controllers\Finance\ManualJournalsController.cs" />
    <Compile Include="Controllers\Finance\OutboundPaymentsController.cs" />
    <Compile Include="Controllers\Finance\PayBooksController.cs" />
    <Compile Include="Controllers\Finance\PaymentAdvicesController.cs" />
    <Compile Include="Controllers\Finance\PettyCashOperationsController.cs" />
    <Compile Include="Controllers\Finance\ReceiptAdvicesController.cs" />
    <Compile Include="Controllers\Finance\TaxGroupsController.cs" />
    <Compile Include="Controllers\Finance\TaxTypesController.cs" />
    <Compile Include="Controllers\Foundation\BaseController.cs" />
    <Compile Include="Controllers\HomeController.cs" />
    <Compile Include="Controllers\Administration\OrganizationController.cs" />
    <Compile Include="Controllers\Inventory\BundleProductsController.cs" />
    <Compile Include="Controllers\Inventory\ConsignmentStockTransferController.cs" />
    <Compile Include="Controllers\Inventory\GoodIssueNoteController.cs" />
    <Compile Include="Controllers\Inventory\GoodsDispatchNotesController.cs" />
    <Compile Include="Controllers\Inventory\GRNSController.cs" />
    <Compile Include="Controllers\Inventory\HOReturnStockTransferController.cs" />
    <Compile Include="Controllers\Inventory\InternalDispatchesController.cs" />
    <Compile Include="Controllers\Inventory\MenuHeadsController.cs" />
    <Compile Include="Controllers\Inventory\ProductionNotesController.cs" />
    <Compile Include="Controllers\Inventory\QCParameterMappingsController.cs" />
    <Compile Include="Controllers\Inventory\InventoryReportsController.cs" />
    <Compile Include="Controllers\Inventory\ShipmentCostsController.cs" />
    <Compile Include="Controllers\Inventory\StockAllocationsController.cs" />
    <Compile Include="Controllers\Inventory\StockCountSheetsController.cs" />
    <Compile Include="Controllers\Inventory\ProductBrandDiscountController.cs" />
    <Compile Include="Controllers\Inventory\RawMaterialPlanningsController.cs" />
    <Compile Include="Controllers\Inventory\StockTransferFIFOController.cs" />
    <Compile Include="Controllers\Inventory\StockTransferReceiptsController.cs" />
    <Compile Include="Controllers\Inventory\ShipmentQualityControlReturnsController.cs" />
    <Compile Include="Controllers\Inventory\StockAdjustmentsController.cs" />
    <Compile Include="Controllers\Inventory\InternalReturnsController.cs" />
    <Compile Include="Controllers\Inventory\MRNSController.cs" />
    <Compile Include="Controllers\Inventory\ProductHierarchyController.cs" />
    <Compile Include="Controllers\Inventory\InternalOrdersController.cs" />
    <Compile Include="Controllers\Inventory\ShipmentQualityControlController.cs" />
    <Compile Include="Controllers\Inventory\StockTakesController.cs" />
    <Compile Include="Controllers\Inventory\StockTransferController.cs" />
    <Compile Include="Controllers\Inventory\PalatalizingController.cs" />
    <Compile Include="Controllers\Inventory\TyreSpecificationsController.cs" />
    <Compile Include="Controllers\Inventory\QCParametersController.cs" />
    <Compile Include="Controllers\Inventory\UnitOfMeasuresController.cs" />
    <Compile Include="Controllers\Inventory\ProductsController.cs" />
    <Compile Include="Controllers\Inventory\WarehousesController.cs" />
    <Compile Include="Controllers\Inventory\ProductLicenceRegistrationController.cs" />
    <Compile Include="Controllers\Manufacturing\BillOfOperationsController.cs" />
    <Compile Include="Controllers\Manufacturing\MachineMouldsController.cs" />
    <Compile Include="Controllers\Manufacturing\BillOfMaterialsController.cs" />
    <Compile Include="Controllers\Manufacturing\MouldsController.cs" />
    <Compile Include="Controllers\Manufacturing\MachineVisualPlansController.cs" />
    <Compile Include="Controllers\Manufacturing\DailyProductionPlanController.cs" />
    <Compile Include="Controllers\Manufacturing\MaterialRequisitionPlanningController.cs" />
    <Compile Include="Controllers\Manufacturing\ProductDiesController.cs" />
    <Compile Include="Controllers\Manufacturing\ProductionOrdersController.cs" />
    <Compile Include="Controllers\Manufacturing\SemiFinishedGoodsProductionPlanController.cs" />
    <Compile Include="Controllers\Manufacturing\ProductionPlanController.cs" />
    <Compile Include="Controllers\Manufacturing\MachinesController.cs" />
    <Compile Include="Controllers\Manufacturing\MouldProductsController.cs" />
    <Compile Include="Controllers\Manufacturing\TouchScreeensController.cs" />
    <Compile Include="Controllers\Procurement\BankAccountsController.cs" />
    <Compile Include="Controllers\Procurement\ExchangeOrdersController.cs" />
    <Compile Include="Controllers\Procurement\InboundLoanController.cs" />
    <Compile Include="Controllers\Procurement\OutboundLoanController.cs" />
    <Compile Include="Controllers\Procurement\PurchaseDeskController.cs" />
    <Compile Include="Controllers\Procurement\PurchaseOrdersController.cs" />
    <Compile Include="Controllers\Procurement\PurchaseReqNotesController.cs" />
    <Compile Include="Controllers\Procurement\PurchaseReturnsController.cs" />
    <Compile Include="Controllers\Procurement\QuotationsController.cs" />
    <Compile Include="Controllers\Procurement\ServiceInvoicesController.cs" />
    <Compile Include="Controllers\Procurement\InternalServiceInvoicesController.cs" />
    <Compile Include="Controllers\Procurement\ServiceOrdersController.cs" />
    <Compile Include="Controllers\Procurement\SubContractOrdersController.cs" />
    <Compile Include="Controllers\Procurement\SupplementaryManufacturersController.cs" />
    <Compile Include="Controllers\Procurement\SuppliersController.cs" />
    <Compile Include="Controllers\ReportsController.cs" />
    <Compile Include="Controllers\Sales\BankPromotionController.cs" />
    <Compile Include="Controllers\Sales\CommercialInvoicesController.cs" />
    <Compile Include="Controllers\Sales\ConsignmentSalesInvoicesController.cs" />
    <Compile Include="Controllers\Sales\CustomerPaymentsController.cs" />
    <Compile Include="Controllers\Sales\CustomersController.cs" />
    <Compile Include="Controllers\Sales\DiscountSchemesController.cs" />
    <Compile Include="Controllers\Sales\FreeIssuesController.cs" />
    <Compile Include="Controllers\Sales\LoadingBaysController.cs" />
    <Compile Include="Controllers\Sales\LoadingPlansController.cs" />
    <Compile Include="Controllers\Sales\PackingListsController.cs" />
    <Compile Include="Controllers\Sales\PerformaInvoiceController.cs" />
    <Compile Include="Controllers\Sales\PriceListsController.cs" />
    <Compile Include="Controllers\Sales\PromotionController.cs" />
    <Compile Include="Controllers\Sales\PromotionDeskController.cs" />
    <Compile Include="Controllers\Sales\SalesCashierController.cs" />
    <Compile Include="Controllers\Sales\SalesDeskController.cs" />
    <Compile Include="Controllers\Sales\SalesInquiriesController.cs" />
    <Compile Include="Controllers\Sales\SalesInvoicesController.cs" />
    <Compile Include="Controllers\Sales\SalesInvoicesWalkInController.cs" />
    <Compile Include="Controllers\Sales\SalesOrdersController.cs" />
    <Compile Include="Controllers\Sales\SalesReturnsController.cs" />
    <Compile Include="Controllers\Sales\SalesServiceInvoicesController.cs" />
    <Compile Include="Controllers\Sales\SalesServiceOrdersController.cs" />
    <Compile Include="Controllers\Sales\ServiceInquiriesController.cs" />
    <Compile Include="Controllers\Sales\ServiceOrderDeskController.cs" />
    <Compile Include="Controllers\Sales\ServiceOrderJobController.cs" />
    <Compile Include="CreditCardDetailsDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CreditCardDetailsDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DailySalesInvoiceDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DailySalesInvoiceDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DiscountSummeryDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DiscountSummeryDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="DTOs\FinancialStatementDTO.cs" />
    <Compile Include="DTOs\ViewFormDTO.cs" />
    <Compile Include="Global.asax.cs">
      <DependentUpon>Global.asax</DependentUpon>
    </Compile>
    <Compile Include="INDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>INDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="InvoiceWiseDiscountDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>InvoiceWiseDiscountDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="IssueNoteDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>IssueNoteDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Models\AuthResponse.cs" />
    <Compile Include="Models\Enums.cs" />
    <Compile Include="Models\PurchaseOrderEmailViewModel.cs" />
    <Compile Include="Models\ReportInfoModel.cs" />
    <Compile Include="Models\ReportServerCredentials.cs" />
    <Compile Include="Models\AttendencesViewModel.cs" />
    <Compile Include="Models\TransactionDocumentViewModel.cs" />
    <Compile Include="Models\GDNsViewModel.cs" />
    <Compile Include="Models\SalesOrderViewModel.cs" />
    <Compile Include="Models\RawMaterialPlanningViewModel.cs" />
    <Compile Include="Models\WarehouseProductsViewModel.cs" />
    <Compile Include="Models\LoginViewModel.cs" />
    <Compile Include="newNemsuErpDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>newNemsuErpDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="PickMeDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>PickMeDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="PoReportDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>PoReportDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="PurchaseReturnLines.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>PurchaseReturnLines.xsd</DependentUpon>
    </Compile>
    <Compile Include="PurchaseReturns.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>PurchaseReturns.xsd</DependentUpon>
    </Compile>
    <Compile Include="Receipts\Common\PRN_Receipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Common\PRN_Receipt.Designer.cs">
      <DependentUpon>PRN_Receipt.cs</DependentUpon>
    </Compile>
    <Compile Include="Receipts\MITL\Cheque_MITL.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\Cheque_MITL.Designer.cs">
      <DependentUpon>Cheque_MITL.cs</DependentUpon>
    </Compile>
    <Compile Include="Receipts\MITL\FumigationCertificateReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\BillingInstructionReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\PackingListReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\LoadingPlanReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\PurchaseReturnReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\VGMDocumentReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\SalesInvoiceReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\GRNReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\PaymentAdviceReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\CommercialInvoiceReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\PettyCashReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\PaymentVoucherReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\IOUReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\InboundReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\PerformaInvoiceReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\PurchaseOrderFinalReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\SalesServiceInvoiceReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\ServiceInvoiceReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\ServiceOrderReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\MITL\PurchaseOrderReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\BankReconcilationReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\GRNReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\InboundReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\MenuHeadPrint.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\MenuHeadPrint.Designer.cs">
      <DependentUpon>MenuHeadPrint.cs</DependentUpon>
    </Compile>
    <Compile Include="Receipts\Nemsui\ReceiptAdviceReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\PaymentAdviceReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\PaymentVoucherReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\GDNReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\SalesInvoiceReceiptPP_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\SalesReturnReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\SalesReturnVATReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\SalesOrderReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\PettyCashReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\PurchaseOrderFinalReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\SalesInvoiceVATReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\SalesInvoiceReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\SalesServiceInvoiceReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\ServiceInvoiceReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\ServiceOrderReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\StockAdjustmentReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\StockAdjustmentReceipt.Designer.cs">
      <DependentUpon>StockAdjustmentReceipt.cs</DependentUpon>
    </Compile>
    <Compile Include="Receipts\Nemsui\StockTransferReceipt_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\WastageIssueReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Nemsui\WastageIssueReceipt.Designer.cs">
      <DependentUpon>WastageIssueReceipt.cs</DependentUpon>
    </Compile>
    <Compile Include="Receipts\Sothys\Cheque_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\Cheque_Sothys.Designer.cs">
      <DependentUpon>Cheque_Sothys.cs</DependentUpon>
    </Compile>
    <Compile Include="Receipts\Sothys\InboundReceipt_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\PaymentVoucherReceipt_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\PurchaseOrder_Sothys_SimahAsia.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\PurchaseOrder_Sothys_SesdermaLaboratories.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\SalesInvoiceReceipt_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\CustomerPaymentReceipt_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\StockTransferReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\StockCountSheetReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\SalesInvoiceWalkInReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\SalesServiceInvoiceReceipt_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Receipts\Sothys\ServiceInquiryReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="ReportStorage\CustomWebDocumentViewerReportResolver.cs" />
    <Compile Include="ReportStorage\ERPDataSourceWizardConnectionStringsProvider.cs" />
    <Compile Include="ReportStorage\ERPReportStorageWebExtension.cs" />
    <Compile Include="ReportStorage\ReportData.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ReportData.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\DefaultReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\BarcodeLabelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\GRNBarcodeLabelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\PalletBarcodeLabelReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\BillCancellationReportDetail.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>BillCancellationReportDetail.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\BillCancellationReportDetailDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>BillCancellationReportDetailDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\BillCancellationReportSummaryDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>BillCancellationReportSummaryDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\BillCancellationReportSummeryDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>BillCancellationReportSummeryDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\CommercialCreditCardReportDetailDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CommercialCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\CounterWiseSaleDetailReportDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CounterWiseSaleDetailReportDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\CounterWiseSaleReportSummaryDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CounterWiseSaleReportSummaryDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\CreditCardReportDetailDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>CreditCardReportDetailDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\DailySalesSummaryDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DailySalesSummaryDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\DelivaryReportDetailsDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DelivaryReportDetailsDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\GRNListingDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>GRNListingDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\HNBCreditCardReportDetailDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>HNBCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\InvoiceWiseCreditDetailsDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>InvoiceWiseCreditDetailsDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\InvoiceWiseSaleReportDetailsDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>InvoiceWiseSaleReportDetailsDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\LocationWiseSalesSummeryDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>LocationWiseSalesSummeryDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\menuDetailReportDataSet.Designer.cs" />
    <Compile Include="Reports\Nemsui\MenuWiseSaleDataSet.cs">
      <DependentUpon>MenuWiseSaleDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\MenuWiseSaleDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MenuWiseSaleDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\MenuWiseSaleReportSummaryDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>MenuWiseSaleReportSummaryDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\NDBCreditCardReportDetailDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>NDBCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\PickMeReportDetailDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>PickMeReportDetailDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\ReceipeDetailReportDataSet1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>ReceipeDetailReportDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\rptAODReceipt_SalesServiceInvoice_Nemsu.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptAODReceipt_StockTransfer_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptGatePassAOD_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptGatePassReceipt_SalesServiceInvoice_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptGatePass_StockTransfer_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptGRNDetailReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptGRNSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptInboundReceiptDetailReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptIOUDetails_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptOutboundPaymentDetailReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptPaymentAdviceDetailReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptReceiptAdviceDetailReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\rptSalesInvoiceDetails_Nemsui.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Nemsui\SampathCreditCardReportDetailDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SampathCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\SRNListingDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SRNListingDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\StockBalanceDataSet.Designer.cs" />
    <Compile Include="Reports\Nemsui\stockValuationDataSet.Designer.cs" />
    <Compile Include="Reports\Nemsui\stockVarianceDataSet.cs">
      <DependentUpon>stockVarianceDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\stockVarianceDataSet.Designer.cs">
      <DependentUpon>stockVarianceDataSet.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\UberReportDetailDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>UberReportDetailDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="Reports\Nemsui\WastageDataSet.Designer.cs" />
    <Compile Include="Reports\Sothys\rptConsignmentSalesInvoiceDetail_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCostOfSales_SalesServiceInvoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCustomerServiceInfoDetail_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCustomerCertainAgeDetail_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCustomerBirthdayDetail_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCustomerRetentionReport_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptGRNSummaryReport_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCustomerPaymentDetailReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptStockMovement_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptSalesCommissionDetail_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptJobCommissionDetail_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptGLDetailNew_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptConsignmentSalesInvoiceSummary_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptStockTransfer_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptPurchaseOrderSummary_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCreditSalesDetail_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptPurchaseOrder_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCreditSalesSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptSalesServiceInvoiceSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptJobCostBreakout_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptTotalTurnover.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptSalesInvoiceWalkInDetail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptSalesInvoiceWalkInSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCostOfSales_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptCustomerLoyality_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptTurnoverMarginService.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptTurnoverMargin.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptSalesServiceInvoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptStockValuation.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptTrialBalance_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptGLDetail_Sothys.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\Sothys\rptGoodsReceiveNoteDetail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptSalesSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptBankReconciliationSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptChemicalAndCompoundStockReconciliationSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptCompoundStockReconciliationDetails.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptCustomerAgingDetailReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptCustomerAgingSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptDailyProduction.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptDailyProductionPlanning.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptBankReconcilation.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptMonthlyQualityDetail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptMonthlyQualitySummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptRawMaterialPlanning.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptPendingPOStatus.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptGLDetail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptMonthlyProduction.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptStockMovementSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptStockReconciliationSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptStockReconciliationSummary.Designer.cs">
      <DependentUpon>rptStockReconciliationSummary.cs</DependentUpon>
    </Compile>
    <Compile Include="Reports\MITL\rptStockSummary.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptStockMovement.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptStockReconciliationDetail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptSupplierAgingSummaryReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptSupplierLedger.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptGatePassReceipt_SalesServiceInvoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptAODReceipt_SalesServiceInvoice.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptAODReceipt_StockTransfer2.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptAODReceipt.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptAODReceiptNew.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptAOD_ServiceOrder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptGatePass_ServiceOrder.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptGatePassAOD.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptAODReceipt_StockTransfer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptGatePass_StockTransfer.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptGatePass.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptSalesOrderDetails.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptCustomerLedger.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptQuotations.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptSupplierAgingDetailReport.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptVatAndSVat.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Reports\MITL\rptWIPStock.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="SPDailySalesInvoiceWiseDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SPDailySalesInvoiceWiseDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="SqlServerTypes\Loader.cs" />
    <Compile Include="SRNDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SRNDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="SRNReportDataset.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>SRNReportDataset.xsd</DependentUpon>
    </Compile>
    <Compile Include="ssrstemplates\ReceiptTemplate.aspx.cs">
      <DependentUpon>ReceiptTemplate.aspx</DependentUpon>
      <SubType>ASPXCodeBehind</SubType>
    </Compile>
    <Compile Include="ssrstemplates\ReceiptTemplate.aspx.designer.cs">
      <DependentUpon>ReceiptTemplate.aspx</DependentUpon>
    </Compile>
    <Compile Include="Startup.cs" />
    <Compile Include="StockAdjustDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>StockAdjustDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="StockAdjustmentDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>StockAdjustmentDataSet.xsd</DependentUpon>
    </Compile>
    <Compile Include="stockCardDataSet.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>stockCardDataSet.xsd</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="assets\css\pages\error\error-1.css" />
    <Content Include="assets\css\pages\error\error-1.min.css" />
    <Content Include="assets\css\pages\error\error-1.rtl.css" />
    <Content Include="assets\css\pages\error\error-1.rtl.min.css" />
    <Content Include="assets\css\pages\error\error-2.css" />
    <Content Include="assets\css\pages\error\error-2.min.css" />
    <Content Include="assets\css\pages\error\error-2.rtl.css" />
    <Content Include="assets\css\pages\error\error-2.rtl.min.css" />
    <Content Include="assets\css\pages\error\error-3.css" />
    <Content Include="assets\css\pages\error\error-3.min.css" />
    <Content Include="assets\css\pages\error\error-3.rtl.css" />
    <Content Include="assets\css\pages\error\error-3.rtl.min.css" />
    <Content Include="assets\css\pages\error\error-4.css" />
    <Content Include="assets\css\pages\error\error-4.min.css" />
    <Content Include="assets\css\pages\error\error-4.rtl.css" />
    <Content Include="assets\css\pages\error\error-4.rtl.min.css" />
    <Content Include="assets\css\pages\error\error-5.css" />
    <Content Include="assets\css\pages\error\error-5.min.css" />
    <Content Include="assets\css\pages\error\error-5.rtl.css" />
    <Content Include="assets\css\pages\error\error-5.rtl.min.css" />
    <Content Include="assets\css\pages\error\error-6.css" />
    <Content Include="assets\css\pages\error\error-6.min.css" />
    <Content Include="assets\css\pages\error\error-6.rtl.css" />
    <Content Include="assets\css\pages\error\error-6.rtl.min.css" />
    <Content Include="assets\css\pages\faq\faq-1.css" />
    <Content Include="assets\css\pages\faq\faq-1.min.css" />
    <Content Include="assets\css\pages\faq\faq-1.rtl.css" />
    <Content Include="assets\css\pages\faq\faq-1.rtl.min.css" />
    <Content Include="assets\css\pages\inbox\inbox.css" />
    <Content Include="assets\css\pages\inbox\inbox.min.css" />
    <Content Include="assets\css\pages\inbox\inbox.rtl.css" />
    <Content Include="assets\css\pages\inbox\inbox.rtl.min.css" />
    <Content Include="assets\css\pages\invoices\invoice-1.css" />
    <Content Include="assets\css\pages\invoices\invoice-1.min.css" />
    <Content Include="assets\css\pages\invoices\invoice-1.rtl.css" />
    <Content Include="assets\css\pages\invoices\invoice-1.rtl.min.css" />
    <Content Include="assets\css\pages\invoices\invoice-2.css" />
    <Content Include="assets\css\pages\invoices\invoice-2.min.css" />
    <Content Include="assets\css\pages\invoices\invoice-2.rtl.css" />
    <Content Include="assets\css\pages\invoices\invoice-2.rtl.min.css" />
    <Content Include="assets\css\pages\login\login-1.css" />
    <Content Include="assets\css\pages\login\login-1.min.css" />
    <Content Include="assets\css\pages\login\login-1.rtl.css" />
    <Content Include="assets\css\pages\login\login-1.rtl.min.css" />
    <Content Include="assets\css\pages\login\login-2.css" />
    <Content Include="assets\css\pages\login\login-2.min.css" />
    <Content Include="assets\css\pages\login\login-2.rtl.css" />
    <Content Include="assets\css\pages\login\login-2.rtl.min.css" />
    <Content Include="assets\css\pages\login\login-3.css" />
    <Content Include="assets\css\pages\login\login-3.min.css" />
    <Content Include="assets\css\pages\login\login-3.rtl.css" />
    <Content Include="assets\css\pages\login\login-3.rtl.min.css" />
    <Content Include="assets\css\pages\login\login-4.css" />
    <Content Include="assets\css\pages\login\login-4.min.css" />
    <Content Include="assets\css\pages\login\login-4.rtl.css" />
    <Content Include="assets\css\pages\login\login-4.rtl.min.css" />
    <Content Include="assets\css\pages\login\login-5.css" />
    <Content Include="assets\css\pages\login\login-5.min.css" />
    <Content Include="assets\css\pages\login\login-5.rtl.css" />
    <Content Include="assets\css\pages\login\login-5.rtl.min.css" />
    <Content Include="assets\css\pages\login\login-6.css" />
    <Content Include="assets\css\pages\login\login-6.min.css" />
    <Content Include="assets\css\pages\login\login-6.rtl.css" />
    <Content Include="assets\css\pages\login\login-6.rtl.min.css" />
    <Content Include="assets\css\pages\pricing\pricing-1.css" />
    <Content Include="assets\css\pages\pricing\pricing-1.min.css" />
    <Content Include="assets\css\pages\pricing\pricing-1.rtl.css" />
    <Content Include="assets\css\pages\pricing\pricing-1.rtl.min.css" />
    <Content Include="assets\css\pages\pricing\pricing-2.css" />
    <Content Include="assets\css\pages\pricing\pricing-2.min.css" />
    <Content Include="assets\css\pages\pricing\pricing-2.rtl.css" />
    <Content Include="assets\css\pages\pricing\pricing-2.rtl.min.css" />
    <Content Include="assets\css\pages\pricing\pricing-3.css" />
    <Content Include="assets\css\pages\pricing\pricing-3.min.css" />
    <Content Include="assets\css\pages\pricing\pricing-3.rtl.css" />
    <Content Include="assets\css\pages\pricing\pricing-3.rtl.min.css" />
    <Content Include="assets\css\pages\pricing\pricing-4.css" />
    <Content Include="assets\css\pages\pricing\pricing-4.min.css" />
    <Content Include="assets\css\pages\pricing\pricing-4.rtl.css" />
    <Content Include="assets\css\pages\pricing\pricing-4.rtl.min.css" />
    <Content Include="assets\css\pages\support-center\faq-1.css" />
    <Content Include="assets\css\pages\support-center\faq-1.min.css" />
    <Content Include="assets\css\pages\support-center\faq-1.rtl.css" />
    <Content Include="assets\css\pages\support-center\faq-1.rtl.min.css" />
    <Content Include="assets\css\pages\support-center\faq-2.css" />
    <Content Include="assets\css\pages\support-center\faq-2.min.css" />
    <Content Include="assets\css\pages\support-center\faq-2.rtl.css" />
    <Content Include="assets\css\pages\support-center\faq-2.rtl.min.css" />
    <Content Include="assets\css\pages\support-center\faq-3.css" />
    <Content Include="assets\css\pages\support-center\faq-3.min.css" />
    <Content Include="assets\css\pages\support-center\faq-3.rtl.css" />
    <Content Include="assets\css\pages\support-center\faq-3.rtl.min.css" />
    <Content Include="assets\css\pages\support-center\feedback.css" />
    <Content Include="assets\css\pages\support-center\feedback.min.css" />
    <Content Include="assets\css\pages\support-center\feedback.rtl.css" />
    <Content Include="assets\css\pages\support-center\feedback.rtl.min.css" />
    <Content Include="assets\css\pages\support-center\home-1.css" />
    <Content Include="assets\css\pages\support-center\home-1.min.css" />
    <Content Include="assets\css\pages\support-center\home-1.rtl.css" />
    <Content Include="assets\css\pages\support-center\home-1.rtl.min.css" />
    <Content Include="assets\css\pages\support-center\home-2.css" />
    <Content Include="assets\css\pages\support-center\home-2.min.css" />
    <Content Include="assets\css\pages\support-center\home-2.rtl.css" />
    <Content Include="assets\css\pages\support-center\home-2.rtl.min.css" />
    <Content Include="assets\css\pages\support-center\license.css" />
    <Content Include="assets\css\pages\support-center\license.min.css" />
    <Content Include="assets\css\pages\support-center\license.rtl.css" />
    <Content Include="assets\css\pages\support-center\license.rtl.min.css" />
    <Content Include="assets\css\pages\wizard\wizard-1.css" />
    <Content Include="assets\css\pages\wizard\wizard-1.min.css" />
    <Content Include="assets\css\pages\wizard\wizard-1.rtl.css" />
    <Content Include="assets\css\pages\wizard\wizard-1.rtl.min.css" />
    <Content Include="assets\css\pages\wizard\wizard-2.css" />
    <Content Include="assets\css\pages\wizard\wizard-2.min.css" />
    <Content Include="assets\css\pages\wizard\wizard-2.rtl.css" />
    <Content Include="assets\css\pages\wizard\wizard-2.rtl.min.css" />
    <Content Include="assets\css\pages\wizard\wizard-3.css" />
    <Content Include="assets\css\pages\wizard\wizard-3.min.css" />
    <Content Include="assets\css\pages\wizard\wizard-3.rtl.css" />
    <Content Include="assets\css\pages\wizard\wizard-3.rtl.min.css" />
    <Content Include="assets\css\pages\wizard\wizard-4.css" />
    <Content Include="assets\css\pages\wizard\wizard-4.min.css" />
    <Content Include="assets\css\pages\wizard\wizard-4.rtl.css" />
    <Content Include="assets\css\pages\wizard\wizard-4.rtl.min.css" />
    <Content Include="assets\css\site.css" />
    <Content Include="assets\css\skins\aside\dark.css" />
    <Content Include="assets\css\skins\aside\dark.min.css" />
    <Content Include="assets\css\skins\aside\dark.rtl.css" />
    <Content Include="assets\css\skins\aside\dark.rtl.min.css" />
    <Content Include="assets\css\skins\aside\light.css" />
    <Content Include="assets\css\skins\aside\light.min.css" />
    <Content Include="assets\css\skins\aside\light.rtl.css" />
    <Content Include="assets\css\skins\aside\light.rtl.min.css" />
    <Content Include="assets\css\skins\brand\dark.css" />
    <Content Include="assets\css\skins\brand\dark.min.css" />
    <Content Include="assets\css\skins\brand\dark.rtl.css" />
    <Content Include="assets\css\skins\brand\dark.rtl.min.css" />
    <Content Include="assets\css\skins\brand\light.css" />
    <Content Include="assets\css\skins\brand\light.min.css" />
    <Content Include="assets\css\skins\brand\light.rtl.css" />
    <Content Include="assets\css\skins\brand\light.rtl.min.css" />
    <Content Include="assets\css\skins\header\base\dark.css" />
    <Content Include="assets\css\skins\header\base\dark.min.css" />
    <Content Include="assets\css\skins\header\base\dark.rtl.css" />
    <Content Include="assets\css\skins\header\base\dark.rtl.min.css" />
    <Content Include="assets\css\skins\header\base\light.css" />
    <Content Include="assets\css\skins\header\base\light.min.css" />
    <Content Include="assets\css\skins\header\base\light.rtl.css" />
    <Content Include="assets\css\skins\header\base\light.rtl.min.css" />
    <Content Include="assets\css\skins\header\menu\dark.css" />
    <Content Include="assets\css\skins\header\menu\dark.min.css" />
    <Content Include="assets\css\skins\header\menu\dark.rtl.css" />
    <Content Include="assets\css\skins\header\menu\dark.rtl.min.css" />
    <Content Include="assets\css\skins\header\menu\light.css" />
    <Content Include="assets\css\skins\header\menu\light.min.css" />
    <Content Include="assets\css\skins\header\menu\light.rtl.css" />
    <Content Include="assets\css\skins\header\menu\light.rtl.min.css" />
    <Content Include="assets\css\style.bundle.css" />
    <Content Include="assets\css\style.bundle.min.css" />
    <Content Include="assets\css\style.bundle.rtl.css" />
    <Content Include="assets\css\style.bundle.rtl.min.css" />
    <Content Include="assets\css\theme_change.css" />
    <Content Include="assets\js\pages\builder.js" />
    <Content Include="assets\js\pages\builder.min.js" />
    <Content Include="assets\js\pages\components\base\dropdown.js" />
    <Content Include="assets\js\pages\components\base\dropdown.min.js" />
    <Content Include="assets\js\pages\components\calendar\background-events.js" />
    <Content Include="assets\js\pages\components\calendar\background-events.min.js" />
    <Content Include="assets\js\pages\components\calendar\basic.js" />
    <Content Include="assets\js\pages\components\calendar\basic.min.js" />
    <Content Include="assets\js\pages\components\calendar\external-events.js" />
    <Content Include="assets\js\pages\components\calendar\external-events.min.js" />
    <Content Include="assets\js\pages\components\calendar\google.js" />
    <Content Include="assets\js\pages\components\calendar\google.min.js" />
    <Content Include="assets\js\pages\components\calendar\list-view.js" />
    <Content Include="assets\js\pages\components\calendar\list-view.min.js" />
    <Content Include="assets\js\pages\components\charts\amcharts\charts.js" />
    <Content Include="assets\js\pages\components\charts\amcharts\charts.min.js" />
    <Content Include="assets\js\pages\components\charts\amcharts\maps.js" />
    <Content Include="assets\js\pages\components\charts\amcharts\maps.min.js" />
    <Content Include="assets\js\pages\components\charts\amcharts\stock-charts.js" />
    <Content Include="assets\js\pages\components\charts\amcharts\stock-charts.min.js" />
    <Content Include="assets\js\pages\components\charts\flotcharts.js" />
    <Content Include="assets\js\pages\components\charts\flotcharts.min.js" />
    <Content Include="assets\js\pages\components\charts\google-charts.js" />
    <Content Include="assets\js\pages\components\charts\google-charts.min.js" />
    <Content Include="assets\js\pages\components\charts\morris-charts.js" />
    <Content Include="assets\js\pages\components\charts\morris-charts.min.js" />
    <Content Include="assets\js\pages\components\extended\blockui.js" />
    <Content Include="assets\js\pages\components\extended\blockui.min.js" />
    <Content Include="assets\js\pages\components\extended\bootstrap-notify.js" />
    <Content Include="assets\js\pages\components\extended\bootstrap-notify.min.js" />
    <Content Include="assets\js\pages\components\extended\dual-listbox.js" />
    <Content Include="assets\js\pages\components\extended\dual-listbox.min.js" />
    <Content Include="assets\js\pages\components\extended\perfect-scrollbar.js" />
    <Content Include="assets\js\pages\components\extended\perfect-scrollbar.min.js" />
    <Content Include="assets\js\pages\components\extended\sweetalert2.js" />
    <Content Include="assets\js\pages\components\extended\sweetalert2.min.js" />
    <Content Include="assets\js\pages\components\extended\toastr.js" />
    <Content Include="assets\js\pages\components\extended\toastr.min.js" />
    <Content Include="assets\js\pages\components\extended\treeview.js" />
    <Content Include="assets\js\pages\components\extended\treeview.min.js" />
    <Content Include="assets\js\pages\components\maps\google-maps.js" />
    <Content Include="assets\js\pages\components\maps\google-maps.min.js" />
    <Content Include="assets\js\pages\components\maps\jqvmap.js" />
    <Content Include="assets\js\pages\components\maps\jqvmap.min.js" />
    <Content Include="assets\js\pages\components\maps\jvectormap.js" />
    <Content Include="assets\js\pages\components\maps\jvectormap.min.js" />
    <Content Include="assets\js\pages\components\portlets\draggable.js" />
    <Content Include="assets\js\pages\components\portlets\draggable.min.js" />
    <Content Include="assets\js\pages\components\portlets\tools.js" />
    <Content Include="assets\js\pages\components\portlets\tools.min.js" />
    <Content Include="assets\js\pages\components\utils\idle-timer.js" />
    <Content Include="assets\js\pages\components\utils\idle-timer.min.js" />
    <Content Include="assets\js\pages\components\utils\session-timeout.js" />
    <Content Include="assets\js\pages\components\utils\session-timeout.min.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\column-rendering.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\column-rendering.min.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\column-visibility.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\column-visibility.min.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\footer-callback.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\footer-callback.min.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\multiple-controls.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\multiple-controls.min.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\row-callback.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\row-callback.min.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\row-grouping.js" />
    <Content Include="assets\js\pages\crud\datatables\advanced\row-grouping.min.js" />
    <Content Include="assets\js\pages\crud\datatables\basic\basic.js" />
    <Content Include="assets\js\pages\crud\datatables\basic\basic.min.js" />
    <Content Include="assets\js\pages\crud\datatables\basic\headers.js" />
    <Content Include="assets\js\pages\crud\datatables\basic\headers.min.js" />
    <Content Include="assets\js\pages\crud\datatables\basic\paginations.js" />
    <Content Include="assets\js\pages\crud\datatables\basic\paginations.min.js" />
    <Content Include="assets\js\pages\crud\datatables\basic\scrollable.js" />
    <Content Include="assets\js\pages\crud\datatables\basic\scrollable.min.js" />
    <Content Include="assets\js\pages\crud\datatables\data-sources\ajax-client-side.js" />
    <Content Include="assets\js\pages\crud\datatables\data-sources\ajax-client-side.min.js" />
    <Content Include="assets\js\pages\crud\datatables\data-sources\ajax-server-side.js" />
    <Content Include="assets\js\pages\crud\datatables\data-sources\ajax-server-side.min.js" />
    <Content Include="assets\js\pages\crud\datatables\data-sources\html.js" />
    <Content Include="assets\js\pages\crud\datatables\data-sources\html.min.js" />
    <Content Include="assets\js\pages\crud\datatables\data-sources\javascript.js" />
    <Content Include="assets\js\pages\crud\datatables\data-sources\javascript.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\buttons.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\buttons.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\colreorder.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\colreorder.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\fixedcolumns.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\fixedcolumns.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\fixedheader.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\fixedheader.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\keytable.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\keytable.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\responsive.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\responsive.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\rowgroup.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\rowgroup.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\rowreorder.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\rowreorder.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\scroller.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\scroller.min.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\select.js" />
    <Content Include="assets\js\pages\crud\datatables\extensions\select.min.js" />
    <Content Include="assets\js\pages\crud\datatables\search-options\advanced-search.js" />
    <Content Include="assets\js\pages\crud\datatables\search-options\advanced-search.min.js" />
    <Content Include="assets\js\pages\crud\datatables\search-options\column-search.js" />
    <Content Include="assets\js\pages\crud\datatables\search-options\column-search.min.js" />
    <Content Include="assets\js\pages\crud\file-upload\dropzonejs.js" />
    <Content Include="assets\js\pages\crud\file-upload\dropzonejs.min.js" />
    <Content Include="assets\js\pages\crud\file-upload\uppy.js" />
    <Content Include="assets\js\pages\crud\file-upload\uppy.min.js" />
    <Content Include="assets\js\pages\crud\forms\validation\form-controls.js" />
    <Content Include="assets\js\pages\crud\forms\validation\form-controls.min.js" />
    <Content Include="assets\js\pages\crud\forms\validation\form-widgets.js" />
    <Content Include="assets\js\pages\crud\forms\validation\form-widgets.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\autosize.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\autosize.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-datepicker.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-datepicker.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-daterangepicker.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-daterangepicker.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-datetimepicker.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-datetimepicker.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-markdown.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-markdown.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-maxlength.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-maxlength.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-multipleselectsplitter.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-multipleselectsplitter.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-select.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-select.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-switch.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-switch.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-timepicker.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-timepicker.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-touchspin.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\bootstrap-touchspin.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\clipboard.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\clipboard.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\form-repeater.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\form-repeater.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\google-recaptcha.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\google-recaptcha.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\input-mask.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\input-mask.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\ion-range-slider.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\ion-range-slider.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\nouislider.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\nouislider.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\quill.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\quill.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\select2.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\select2.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\summernote.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\summernote.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\tagify.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\tagify.min.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\typeahead.js" />
    <Content Include="assets\js\pages\crud\forms\widgets\typeahead.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\column-rendering.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\column-rendering.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\column-width.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\column-width.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\modal.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\modal.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\record-selection.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\record-selection.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\row-details.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\row-details.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\vertical.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\advanced\vertical.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\api\events.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\api\events.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\api\methods.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\api\methods.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\data-ajax.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\data-ajax.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\data-json.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\data-json.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\data-local.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\data-local.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\html-table.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\html-table.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\local-sort.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\local-sort.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\translation.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\base\translation.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\child\data-ajax.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\child\data-ajax.min.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\child\data-local.js" />
    <Content Include="assets\js\pages\crud\metronic-datatable\child\data-local.min.js" />
    <Content Include="assets\js\pages\custom\chat\chat.js" />
    <Content Include="assets\js\pages\custom\chat\chat.min.js" />
    <Content Include="assets\js\pages\custom\contacts\add-contact.js" />
    <Content Include="assets\js\pages\custom\contacts\add-contact.min.js" />
    <Content Include="assets\js\pages\custom\contacts\edit-contact.js" />
    <Content Include="assets\js\pages\custom\contacts\edit-contact.min.js" />
    <Content Include="assets\js\pages\custom\contacts\list-columns.js" />
    <Content Include="assets\js\pages\custom\contacts\list-columns.min.js" />
    <Content Include="assets\js\pages\custom\contacts\list-datatable.js" />
    <Content Include="assets\js\pages\custom\contacts\list-datatable.min.js" />
    <Content Include="assets\js\pages\custom\inbox\inbox.js" />
    <Content Include="assets\js\pages\custom\inbox\inbox.min.js" />
    <Content Include="assets\js\pages\custom\login\login-1.js" />
    <Content Include="assets\js\pages\custom\login\login-1.min.js" />
    <Content Include="assets\js\pages\custom\login\login-general.js" />
    <Content Include="assets\js\pages\custom\login\login-general.min.js" />
    <Content Include="assets\js\pages\custom\projects\add-project.js" />
    <Content Include="assets\js\pages\custom\projects\add-project.min.js" />
    <Content Include="assets\js\pages\custom\projects\list-datatable.js" />
    <Content Include="assets\js\pages\custom\projects\list-datatable.min.js" />
    <Content Include="assets\js\pages\custom\user\add-user.js" />
    <Content Include="assets\js\pages\custom\user\add-user.min.js" />
    <Content Include="assets\js\pages\custom\user\edit-user.js" />
    <Content Include="assets\js\pages\custom\user\edit-user.min.js" />
    <Content Include="assets\js\pages\custom\user\list-datatable.js" />
    <Content Include="assets\js\pages\custom\user\list-datatable.min.js" />
    <Content Include="assets\js\pages\custom\user\profile.js" />
    <Content Include="assets\js\pages\custom\user\profile.min.js" />
    <Content Include="assets\js\pages\custom\wizard\wizard-1.js" />
    <Content Include="assets\js\pages\custom\wizard\wizard-1.min.js" />
    <Content Include="assets\js\pages\custom\wizard\wizard-2.js" />
    <Content Include="assets\js\pages\custom\wizard\wizard-2.min.js" />
    <Content Include="assets\js\pages\custom\wizard\wizard-3.js" />
    <Content Include="assets\js\pages\custom\wizard\wizard-3.min.js" />
    <Content Include="assets\js\pages\custom\wizard\wizard-4.js" />
    <Content Include="assets\js\pages\custom\wizard\wizard-4.min.js" />
    <Content Include="assets\js\pages\dashboard.js" />
    <Content Include="assets\js\pages\dashboard.min.js" />
    <Content Include="assets\js\pages\my-script.js" />
    <Content Include="assets\js\pages\my-script.min.js" />
    <Content Include="assets\js\scripts.bundle.js" />
    <Content Include="assets\js\scripts.bundle.min.js" />
    <Content Include="assets\media\background-image\laughf jade.png" />
    <Content Include="assets\media\bg\300.jpg" />
    <Content Include="assets\media\bg\350.jpg" />
    <Content Include="assets\media\bg\400.jpg" />
    <Content Include="assets\media\bg\450.jpg" />
    <Content Include="assets\media\bg\ajax-loader.gif" />
    <Content Include="assets\media\bg\bg-1.jpg" />
    <Content Include="assets\media\bg\bg-2.jpg" />
    <Content Include="assets\media\bg\bg-3.jpg" />
    <Content Include="assets\media\bg\bg-4.jpg" />
    <Content Include="assets\media\bg\bg-5.jpg" />
    <Content Include="assets\media\bg\bg-6.jpg" />
    <Content Include="assets\media\bg\bg-6.png" />
    <Content Include="assets\media\bg\bg-7.jpg" />
    <Content Include="assets\media\bg\bg-8.jpg" />
    <Content Include="assets\media\bg\bg-9.jpg" />
    <Content Include="assets\media\bg\demo4-bg-footer.jpg" />
    <Content Include="assets\media\bg\home-2-bg.svg" />
    <Content Include="assets\media\bg\sc-home1-bg.png" />
    <Content Include="assets\media\blog\blog1.jpg" />
    <Content Include="assets\media\blog\blog2.jpg" />
    <Content Include="assets\media\blog\blog3.jpg" />
    <Content Include="assets\media\blog\blog4.jpg" />
    <Content Include="assets\media\blog\blog5.jpg" />
    <Content Include="assets\media\client-logos\logo1.png" />
    <Content Include="assets\media\client-logos\logo2.png" />
    <Content Include="assets\media\client-logos\logo3.png" />
    <Content Include="assets\media\client-logos\logo4.png" />
    <Content Include="assets\media\client-logos\logo5.png" />
    <Content Include="assets\media\company-logos\laughfs.jpg" />
    <Content Include="assets\media\company-logos\login-logo.png" />
    <Content Include="assets\media\company-logos\logo-01.svg" />
    <Content Include="assets\media\company-logos\logo-1.png" />
    <Content Include="assets\media\company-logos\logo-2.png" />
    <Content Include="assets\media\company-logos\logo-3.png" />
    <Content Include="assets\media\company-logos\logo-4.png" />
    <Content Include="assets\media\company-logos\logo_client_color.png" />
    <Content Include="assets\media\company-logos\logo_client_white.png" />
    <Content Include="assets\media\company-logos\logo_invoice_dark.png" />
    <Content Include="assets\media\company-logos\logo_invoice_white.png" />
    <Content Include="assets\media\company-logos\main-logo.png" />
    <Content Include="assets\media\company-logos\super.PNG" />
    <Content Include="assets\media\company-logos\super1.png" />
    <Content Include="assets\media\demos\demo11\bg-1.jpg" />
    <Content Include="assets\media\demos\demo12\bg-1.jpg" />
    <Content Include="assets\media\demos\demo4\footer.jpg" />
    <Content Include="assets\media\demos\demo4\header.jpg" />
    <Content Include="assets\media\demos\demo8\bg-1.jpg" />
    <Content Include="assets\media\demos\demo9\bg-300.jpg" />
    <Content Include="assets\media\demos\demo9\bg-350.jpg" />
    <Content Include="assets\media\demos\demo9\bg-400.jpg" />
    <Content Include="assets\media\demos\demo9\bg-450.jpg" />
    <Content Include="assets\media\demos\preview\demo1.jpg" />
    <Content Include="assets\media\demos\preview\demo10.jpg" />
    <Content Include="assets\media\demos\preview\demo11.jpg" />
    <Content Include="assets\media\demos\preview\demo12.jpg" />
    <Content Include="assets\media\demos\preview\demo13.jpg" />
    <Content Include="assets\media\demos\preview\demo14.jpg" />
    <Content Include="assets\media\demos\preview\demo2.jpg" />
    <Content Include="assets\media\demos\preview\demo3.jpg" />
    <Content Include="assets\media\demos\preview\demo4.jpg" />
    <Content Include="assets\media\demos\preview\demo5.jpg" />
    <Content Include="assets\media\demos\preview\demo6.jpg" />
    <Content Include="assets\media\demos\preview\demo7.jpg" />
    <Content Include="assets\media\demos\preview\demo8.jpg" />
    <Content Include="assets\media\demos\preview\demo9.jpg" />
    <Content Include="assets\media\envato\screen1.jpg" />
    <Content Include="assets\media\envato\screen2.jpg" />
    <Content Include="assets\media\error\bg1.jpg" />
    <Content Include="assets\media\error\bg2.jpg" />
    <Content Include="assets\media\error\bg3.jpg" />
    <Content Include="assets\media\error\bg4.jpg" />
    <Content Include="assets\media\error\bg5.jpg" />
    <Content Include="assets\media\error\bg6.jpg" />
    <Content Include="assets\media\files\css.svg" />
    <Content Include="assets\media\files\csv.svg" />
    <Content Include="assets\media\files\doc.svg" />
    <Content Include="assets\media\files\html.svg" />
    <Content Include="assets\media\files\javascript.svg" />
    <Content Include="assets\media\files\jpg.svg" />
    <Content Include="assets\media\files\mp4.svg" />
    <Content Include="assets\media\files\pdf.svg" />
    <Content Include="assets\media\files\xml.svg" />
    <Content Include="assets\media\files\zip.svg" />
    <Content Include="assets\media\flags\001-austria.svg" />
    <Content Include="assets\media\flags\002-belgium.svg" />
    <Content Include="assets\media\flags\003-switzerland.svg" />
    <Content Include="assets\media\flags\004-indonesia.svg" />
    <Content Include="assets\media\flags\005-portugal.svg" />
    <Content Include="assets\media\flags\006-turkey.svg" />
    <Content Include="assets\media\flags\007-malasya.svg" />
    <Content Include="assets\media\flags\008-saudi-arabia.svg" />
    <Content Include="assets\media\flags\009-australia.svg" />
    <Content Include="assets\media\flags\010-india.svg" />
    <Content Include="assets\media\flags\011-brazil.svg" />
    <Content Include="assets\media\flags\012-uk.svg" />
    <Content Include="assets\media\flags\013-russia.svg" />
    <Content Include="assets\media\flags\014-japan.svg" />
    <Content Include="assets\media\flags\015-china.svg" />
    <Content Include="assets\media\flags\016-spain.svg" />
    <Content Include="assets\media\flags\017-germany.svg" />
    <Content Include="assets\media\flags\018-south-korea.svg" />
    <Content Include="assets\media\flags\019-france.svg" />
    <Content Include="assets\media\flags\020-flag.svg" />
    <Content Include="assets\media\icons\excel.png" />
    <Content Include="assets\media\icons\exchange.svg" />
    <Content Include="assets\media\icons\logout.svg" />
    <Content Include="assets\media\icons\pdf.png" />
    <Content Include="assets\media\icons\question.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Brassiere.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Briefcase.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Cap.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Crown.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Dress.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Hanger.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Hat.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Panties.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Shirt.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Shoes.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Shorts.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Sneakers.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Socks.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Sun-glasses.svg" />
    <Content Include="assets\media\icons\svg\Clothes\T-Shirt.svg" />
    <Content Include="assets\media\icons\svg\Clothes\Tie.svg" />
    <Content Include="assets\media\icons\svg\Code\Backspace.svg" />
    <Content Include="assets\media\icons\svg\Code\CMD.svg" />
    <Content Include="assets\media\icons\svg\Code\Code.svg" />
    <Content Include="assets\media\icons\svg\Code\Commit.svg" />
    <Content Include="assets\media\icons\svg\Code\Compiling.svg" />
    <Content Include="assets\media\icons\svg\Code\Control.svg" />
    <Content Include="assets\media\icons\svg\Code\Done-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Error-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Git#1.svg" />
    <Content Include="assets\media\icons\svg\Code\Git#2.svg" />
    <Content Include="assets\media\icons\svg\Code\Git#3.svg" />
    <Content Include="assets\media\icons\svg\Code\Git#4.svg" />
    <Content Include="assets\media\icons\svg\Code\Github.svg" />
    <Content Include="assets\media\icons\svg\Code\Info-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Left-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Loading.svg" />
    <Content Include="assets\media\icons\svg\Code\Lock-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Lock-overturning.svg" />
    <Content Include="assets\media\icons\svg\Code\Minus.svg" />
    <Content Include="assets\media\icons\svg\Code\Option.svg" />
    <Content Include="assets\media\icons\svg\Code\Plus.svg" />
    <Content Include="assets\media\icons\svg\Code\Puzzle.svg" />
    <Content Include="assets\media\icons\svg\Code\Question-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Right-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Settings#4.svg" />
    <Content Include="assets\media\icons\svg\Code\Shift.svg" />
    <Content Include="assets\media\icons\svg\Code\Spy.svg" />
    <Content Include="assets\media\icons\svg\Code\Stop.svg" />
    <Content Include="assets\media\icons\svg\Code\Terminal.svg" />
    <Content Include="assets\media\icons\svg\Code\Thunder-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Time-schedule.svg" />
    <Content Include="assets\media\icons\svg\Code\Warning-1-circle.svg" />
    <Content Include="assets\media\icons\svg\Code\Warning-2.svg" />
    <Content Include="assets\media\icons\svg\Communication\Active-call.svg" />
    <Content Include="assets\media\icons\svg\Communication\Add-user.svg" />
    <Content Include="assets\media\icons\svg\Communication\Address-card.svg" />
    <Content Include="assets\media\icons\svg\Communication\Adress-book#1.svg" />
    <Content Include="assets\media\icons\svg\Communication\Adress-book#2.svg" />
    <Content Include="assets\media\icons\svg\Communication\Archive.svg" />
    <Content Include="assets\media\icons\svg\Communication\Call#1.svg" />
    <Content Include="assets\media\icons\svg\Communication\Call.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat#1.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat#2.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat#4.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat#5.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat#6.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat-check.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat-error.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat-locked.svg" />
    <Content Include="assets\media\icons\svg\Communication\Chat-smile.svg" />
    <Content Include="assets\media\icons\svg\Communication\Clipboard-check.svg" />
    <Content Include="assets\media\icons\svg\Communication\Clipboard-list.svg" />
    <Content Include="assets\media\icons\svg\Communication\Contact#1.svg" />
    <Content Include="assets\media\icons\svg\Communication\Delete-user.svg" />
    <Content Include="assets\media\icons\svg\Communication\Dial-numbers.svg" />
    <Content Include="assets\media\icons\svg\Communication\Flag.svg" />
    <Content Include="assets\media\icons\svg\Communication\Forward.svg" />
    <Content Include="assets\media\icons\svg\Communication\Group-chat.svg" />
    <Content Include="assets\media\icons\svg\Communication\Group.svg" />
    <Content Include="assets\media\icons\svg\Communication\Incoming-box.svg" />
    <Content Include="assets\media\icons\svg\Communication\Incoming-call.svg" />
    <Content Include="assets\media\icons\svg\Communication\Incoming-mail.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-%40.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-attachment.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-box.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-error.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-heart.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-locked.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-notification.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-opened.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail-unocked.svg" />
    <Content Include="assets\media\icons\svg\Communication\Mail.svg" />
    <Content Include="assets\media\icons\svg\Communication\Missed-call.svg" />
    <Content Include="assets\media\icons\svg\Communication\Outgoing-box.svg" />
    <Content Include="assets\media\icons\svg\Communication\Outgoing-call.svg" />
    <Content Include="assets\media\icons\svg\Communication\Outgoing-mail.svg" />
    <Content Include="assets\media\icons\svg\Communication\Readed-mail.svg" />
    <Content Include="assets\media\icons\svg\Communication\Reply-all.svg" />
    <Content Include="assets\media\icons\svg\Communication\Reply.svg" />
    <Content Include="assets\media\icons\svg\Communication\Right.svg" />
    <Content Include="assets\media\icons\svg\Communication\RSS.svg" />
    <Content Include="assets\media\icons\svg\Communication\Safe-chat.svg" />
    <Content Include="assets\media\icons\svg\Communication\Send.svg" />
    <Content Include="assets\media\icons\svg\Communication\Sending mail.svg" />
    <Content Include="assets\media\icons\svg\Communication\Sending.svg" />
    <Content Include="assets\media\icons\svg\Communication\Share.svg" />
    <Content Include="assets\media\icons\svg\Communication\Shield-thunder.svg" />
    <Content Include="assets\media\icons\svg\Communication\Shield-user.svg" />
    <Content Include="assets\media\icons\svg\Communication\Snoozed-mail.svg" />
    <Content Include="assets\media\icons\svg\Communication\Spam.svg" />
    <Content Include="assets\media\icons\svg\Communication\Thumbtack.svg" />
    <Content Include="assets\media\icons\svg\Communication\Urgent-mail.svg" />
    <Content Include="assets\media\icons\svg\Communication\Write.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Baking-glove.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Bowl.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Chef.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Cooking-book.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Cooking-pot.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Cutting board.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Dinner.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Dish.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Dishes.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Fork-spoon-knife.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Fork-spoon.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Fork.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Frying-pan.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Grater.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Kitchen-scale.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Knife#1.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Knife#2.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Knife&amp;fork#1.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Knife&amp;fork#2.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Ladle.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Rolling-pin.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Saucepan.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Shovel.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Sieve.svg" />
    <Content Include="assets\media\icons\svg\Cooking\Spoon.svg" />
    <Content Include="assets\media\icons\svg\Design\Adjust.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-center-down.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-center-up.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-center.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-left-down.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-left-up.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-left.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-right-down.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-right-up.svg" />
    <Content Include="assets\media\icons\svg\Design\Anchor-right.svg" />
    <Content Include="assets\media\icons\svg\Design\Arrows.svg" />
    <Content Include="assets\media\icons\svg\Design\Bezier-curve.svg" />
    <Content Include="assets\media\icons\svg\Design\Border.svg" />
    <Content Include="assets\media\icons\svg\Design\Brush.svg" />
    <Content Include="assets\media\icons\svg\Design\Bucket.svg" />
    <Content Include="assets\media\icons\svg\Design\Cap-1.svg" />
    <Content Include="assets\media\icons\svg\Design\Cap-2.svg" />
    <Content Include="assets\media\icons\svg\Design\Cap-3.svg" />
    <Content Include="assets\media\icons\svg\Design\Circle.svg" />
    <Content Include="assets\media\icons\svg\Design\Color-profile.svg" />
    <Content Include="assets\media\icons\svg\Design\Color.svg" />
    <Content Include="assets\media\icons\svg\Design\Component.svg" />
    <Content Include="assets\media\icons\svg\Design\Crop.svg" />
    <Content Include="assets\media\icons\svg\Design\Difference.svg" />
    <Content Include="assets\media\icons\svg\Design\Edit.svg" />
    <Content Include="assets\media\icons\svg\Design\Eraser.svg" />
    <Content Include="assets\media\icons\svg\Design\Flatten.svg" />
    <Content Include="assets\media\icons\svg\Design\Flip-horizontal.svg" />
    <Content Include="assets\media\icons\svg\Design\Flip-vertical.svg" />
    <Content Include="assets\media\icons\svg\Design\Horizontal.svg" />
    <Content Include="assets\media\icons\svg\Design\Image.svg" />
    <Content Include="assets\media\icons\svg\Design\Interselect.svg" />
    <Content Include="assets\media\icons\svg\Design\Join-1.svg" />
    <Content Include="assets\media\icons\svg\Design\Join-2.svg" />
    <Content Include="assets\media\icons\svg\Design\Join-3.svg" />
    <Content Include="assets\media\icons\svg\Design\Layers.svg" />
    <Content Include="assets\media\icons\svg\Design\Line.svg" />
    <Content Include="assets\media\icons\svg\Design\Magic.svg" />
    <Content Include="assets\media\icons\svg\Design\Mask.svg" />
    <Content Include="assets\media\icons\svg\Design\Patch.svg" />
    <Content Include="assets\media\icons\svg\Design\Pen&amp;ruller.svg" />
    <Content Include="assets\media\icons\svg\Design\Pen-tool-vector.svg" />
    <Content Include="assets\media\icons\svg\Design\Pencil.svg" />
    <Content Include="assets\media\icons\svg\Design\Picker.svg" />
    <Content Include="assets\media\icons\svg\Design\Pixels.svg" />
    <Content Include="assets\media\icons\svg\Design\Polygon.svg" />
    <Content Include="assets\media\icons\svg\Design\Position.svg" />
    <Content Include="assets\media\icons\svg\Design\Rectangle.svg" />
    <Content Include="assets\media\icons\svg\Design\Saturation.svg" />
    <Content Include="assets\media\icons\svg\Design\Select.svg" />
    <Content Include="assets\media\icons\svg\Design\Sketch.svg" />
    <Content Include="assets\media\icons\svg\Design\Stamp.svg" />
    <Content Include="assets\media\icons\svg\Design\Substract.svg" />
    <Content Include="assets\media\icons\svg\Design\Target.svg" />
    <Content Include="assets\media\icons\svg\Design\Triangle.svg" />
    <Content Include="assets\media\icons\svg\Design\Union.svg" />
    <Content Include="assets\media\icons\svg\Design\Vertical.svg" />
    <Content Include="assets\media\icons\svg\Design\Zoom minus.svg" />
    <Content Include="assets\media\icons\svg\Design\Zoom plus.svg" />
    <Content Include="assets\media\icons\svg\Devices\Airpods.svg" />
    <Content Include="assets\media\icons\svg\Devices\Android.svg" />
    <Content Include="assets\media\icons\svg\Devices\Apple-Watch.svg" />
    <Content Include="assets\media\icons\svg\Devices\Battery-charging.svg" />
    <Content Include="assets\media\icons\svg\Devices\Battery-empty.svg" />
    <Content Include="assets\media\icons\svg\Devices\Battery-full.svg" />
    <Content Include="assets\media\icons\svg\Devices\Battery-half.svg" />
    <Content Include="assets\media\icons\svg\Devices\Bluetooth.svg" />
    <Content Include="assets\media\icons\svg\Devices\Camera.svg" />
    <Content Include="assets\media\icons\svg\Devices\Cardboard-vr.svg" />
    <Content Include="assets\media\icons\svg\Devices\Cassete.svg" />
    <Content Include="assets\media\icons\svg\Devices\CPU#1.svg" />
    <Content Include="assets\media\icons\svg\Devices\CPU#2.svg" />
    <Content Include="assets\media\icons\svg\Devices\Diagnostics.svg" />
    <Content Include="assets\media\icons\svg\Devices\Display#1.svg" />
    <Content Include="assets\media\icons\svg\Devices\Display#2.svg" />
    <Content Include="assets\media\icons\svg\Devices\Display#3.svg" />
    <Content Include="assets\media\icons\svg\Devices\Gameboy.svg" />
    <Content Include="assets\media\icons\svg\Devices\Gamepad#1.svg" />
    <Content Include="assets\media\icons\svg\Devices\Gamepad#2.svg" />
    <Content Include="assets\media\icons\svg\Devices\Generator.svg" />
    <Content Include="assets\media\icons\svg\Devices\Hard-drive.svg" />
    <Content Include="assets\media\icons\svg\Devices\Headphones.svg" />
    <Content Include="assets\media\icons\svg\Devices\Homepod.svg" />
    <Content Include="assets\media\icons\svg\Devices\iMac.svg" />
    <Content Include="assets\media\icons\svg\Devices\iPhone-back.svg" />
    <Content Include="assets\media\icons\svg\Devices\iPhone-x-back.svg" />
    <Content Include="assets\media\icons\svg\Devices\iPhone-X.svg" />
    <Content Include="assets\media\icons\svg\Devices\Keyboard.svg" />
    <Content Include="assets\media\icons\svg\Devices\Laptop-macbook.svg" />
    <Content Include="assets\media\icons\svg\Devices\Laptop.svg" />
    <Content Include="assets\media\icons\svg\Devices\LTE#1.svg" />
    <Content Include="assets\media\icons\svg\Devices\LTE#2.svg" />
    <Content Include="assets\media\icons\svg\Devices\Mic.svg" />
    <Content Include="assets\media\icons\svg\Devices\Midi.svg" />
    <Content Include="assets\media\icons\svg\Devices\Mouse.svg" />
    <Content Include="assets\media\icons\svg\Devices\Phone.svg" />
    <Content Include="assets\media\icons\svg\Devices\Printer.svg" />
    <Content Include="assets\media\icons\svg\Devices\Radio.svg" />
    <Content Include="assets\media\icons\svg\Devices\Router#1.svg" />
    <Content Include="assets\media\icons\svg\Devices\Router#2.svg" />
    <Content Include="assets\media\icons\svg\Devices\SD-card.svg" />
    <Content Include="assets\media\icons\svg\Devices\Server.svg" />
    <Content Include="assets\media\icons\svg\Devices\Speaker.svg" />
    <Content Include="assets\media\icons\svg\Devices\Tablet.svg" />
    <Content Include="assets\media\icons\svg\Devices\TV#1.svg" />
    <Content Include="assets\media\icons\svg\Devices\TV#2.svg" />
    <Content Include="assets\media\icons\svg\Devices\Usb-storage.svg" />
    <Content Include="assets\media\icons\svg\Devices\USB.svg" />
    <Content Include="assets\media\icons\svg\Devices\Video-camera.svg" />
    <Content Include="assets\media\icons\svg\Devices\Watch#1.svg" />
    <Content Include="assets\media\icons\svg\Devices\Watch#2.svg" />
    <Content Include="assets\media\icons\svg\Devices\Wi-fi.svg" />
    <Content Include="assets\media\icons\svg\Electric\Air-conditioning.svg" />
    <Content Include="assets\media\icons\svg\Electric\air-dryer.svg" />
    <Content Include="assets\media\icons\svg\Electric\Blender.svg" />
    <Content Include="assets\media\icons\svg\Electric\Fan.svg" />
    <Content Include="assets\media\icons\svg\Electric\Fridge.svg" />
    <Content Include="assets\media\icons\svg\Electric\Gas-stove.svg" />
    <Content Include="assets\media\icons\svg\Electric\Highvoltage.svg" />
    <Content Include="assets\media\icons\svg\Electric\Iron.svg" />
    <Content Include="assets\media\icons\svg\Electric\Kettle.svg" />
    <Content Include="assets\media\icons\svg\Electric\Mixer.svg" />
    <Content Include="assets\media\icons\svg\Electric\Outlet.svg" />
    <Content Include="assets\media\icons\svg\Electric\Range-hood.svg" />
    <Content Include="assets\media\icons\svg\Electric\Shutdown.svg" />
    <Content Include="assets\media\icons\svg\Electric\Socket-eu.svg" />
    <Content Include="assets\media\icons\svg\Electric\Socket-us.svg" />
    <Content Include="assets\media\icons\svg\Electric\Washer.svg" />
    <Content Include="assets\media\icons\svg\Files\Cloud-download.svg" />
    <Content Include="assets\media\icons\svg\Files\Cloud-upload.svg" />
    <Content Include="assets\media\icons\svg\Files\Compilation.svg" />
    <Content Include="assets\media\icons\svg\Files\Compiled-file.svg" />
    <Content Include="assets\media\icons\svg\Files\Deleted-file.svg" />
    <Content Include="assets\media\icons\svg\Files\Deleted-folder.svg" />
    <Content Include="assets\media\icons\svg\Files\Download.svg" />
    <Content Include="assets\media\icons\svg\Files\Downloaded file.svg" />
    <Content Include="assets\media\icons\svg\Files\Downloads-folder.svg" />
    <Content Include="assets\media\icons\svg\Files\Export.svg" />
    <Content Include="assets\media\icons\svg\Files\File-cloud.svg" />
    <Content Include="assets\media\icons\svg\Files\File-done.svg" />
    <Content Include="assets\media\icons\svg\Files\File-minus.svg" />
    <Content Include="assets\media\icons\svg\Files\File-plus.svg" />
    <Content Include="assets\media\icons\svg\Files\File.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-check.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-cloud.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-error.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-heart.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-minus.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-plus.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-solid.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-star.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder-thunder.svg" />
    <Content Include="assets\media\icons\svg\Files\Folder.svg" />
    <Content Include="assets\media\icons\svg\Files\Group-folders.svg" />
    <Content Include="assets\media\icons\svg\Files\Import.svg" />
    <Content Include="assets\media\icons\svg\Files\Locked-folder.svg" />
    <Content Include="assets\media\icons\svg\Files\Media-folder.svg" />
    <Content Include="assets\media\icons\svg\Files\Media.svg" />
    <Content Include="assets\media\icons\svg\Files\Music.svg" />
    <Content Include="assets\media\icons\svg\Files\Pictures#1.svg" />
    <Content Include="assets\media\icons\svg\Files\Pictures#2.svg" />
    <Content Include="assets\media\icons\svg\Files\Protected-file.svg" />
    <Content Include="assets\media\icons\svg\Files\Selected-file.svg" />
    <Content Include="assets\media\icons\svg\Files\Share.svg" />
    <Content Include="assets\media\icons\svg\Files\Upload-folder.svg" />
    <Content Include="assets\media\icons\svg\Files\Upload.svg" />
    <Content Include="assets\media\icons\svg\Files\Uploaded-file.svg" />
    <Content Include="assets\media\icons\svg\Files\User-folder.svg" />
    <Content Include="assets\media\icons\svg\Food\Beer.svg" />
    <Content Include="assets\media\icons\svg\Food\Bottle#1.svg" />
    <Content Include="assets\media\icons\svg\Food\Bottle#2.svg" />
    <Content Include="assets\media\icons\svg\Food\Bread.svg" />
    <Content Include="assets\media\icons\svg\Food\Bucket.svg" />
    <Content Include="assets\media\icons\svg\Food\Burger.svg" />
    <Content Include="assets\media\icons\svg\Food\Cake.svg" />
    <Content Include="assets\media\icons\svg\Food\Carrot.svg" />
    <Content Include="assets\media\icons\svg\Food\Cheese.svg" />
    <Content Include="assets\media\icons\svg\Food\Chicken.svg" />
    <Content Include="assets\media\icons\svg\Food\Coffee#1.svg" />
    <Content Include="assets\media\icons\svg\Food\Coffee#2.svg" />
    <Content Include="assets\media\icons\svg\Food\Cookie.svg" />
    <Content Include="assets\media\icons\svg\Food\Dinner.svg" />
    <Content Include="assets\media\icons\svg\Food\Fish.svg" />
    <Content Include="assets\media\icons\svg\Food\French Bread.svg" />
    <Content Include="assets\media\icons\svg\Food\Glass-martini.svg" />
    <Content Include="assets\media\icons\svg\Food\Ice-cream#1.svg" />
    <Content Include="assets\media\icons\svg\Food\Ice-cream#2.svg" />
    <Content Include="assets\media\icons\svg\Food\Miso-soup.svg" />
    <Content Include="assets\media\icons\svg\Food\Orange.svg" />
    <Content Include="assets\media\icons\svg\Food\Pizza.svg" />
    <Content Include="assets\media\icons\svg\Food\Sushi.svg" />
    <Content Include="assets\media\icons\svg\Food\Two-bottles.svg" />
    <Content Include="assets\media\icons\svg\Food\Wine.svg" />
    <Content Include="assets\media\icons\svg\General\Attachment#1.svg" />
    <Content Include="assets\media\icons\svg\General\Attachment#2.svg" />
    <Content Include="assets\media\icons\svg\General\Binocular.svg" />
    <Content Include="assets\media\icons\svg\General\Bookmark.svg" />
    <Content Include="assets\media\icons\svg\General\Clip.svg" />
    <Content Include="assets\media\icons\svg\General\Clipboard.svg" />
    <Content Include="assets\media\icons\svg\General\Cursor.svg" />
    <Content Include="assets\media\icons\svg\General\Dislike.svg" />
    <Content Include="assets\media\icons\svg\General\Duplicate.svg" />
    <Content Include="assets\media\icons\svg\General\Edit.svg" />
    <Content Include="assets\media\icons\svg\General\Expand-arrows.svg" />
    <Content Include="assets\media\icons\svg\General\Fire.svg" />
    <Content Include="assets\media\icons\svg\General\Folder.svg" />
    <Content Include="assets\media\icons\svg\General\Half-heart.svg" />
    <Content Include="assets\media\icons\svg\General\Half-star.svg" />
    <Content Include="assets\media\icons\svg\General\Heart.svg" />
    <Content Include="assets\media\icons\svg\General\Hidden.svg" />
    <Content Include="assets\media\icons\svg\General\Like.svg" />
    <Content Include="assets\media\icons\svg\General\Lock.svg" />
    <Content Include="assets\media\icons\svg\General\Notification#2.svg" />
    <Content Include="assets\media\icons\svg\General\Notifications#1.svg" />
    <Content Include="assets\media\icons\svg\General\Other#1.svg" />
    <Content Include="assets\media\icons\svg\General\Other#2.svg" />
    <Content Include="assets\media\icons\svg\General\Sad.svg" />
    <Content Include="assets\media\icons\svg\General\Save.svg" />
    <Content Include="assets\media\icons\svg\General\Scale.svg" />
    <Content Include="assets\media\icons\svg\General\Scissors.svg" />
    <Content Include="assets\media\icons\svg\General\Search.svg" />
    <Content Include="assets\media\icons\svg\General\Settings#3.svg" />
    <Content Include="assets\media\icons\svg\General\Settings-1.svg" />
    <Content Include="assets\media\icons\svg\General\Settings-2.svg" />
    <Content Include="assets\media\icons\svg\General\Shield-check.svg" />
    <Content Include="assets\media\icons\svg\General\Shield-disabled.svg" />
    <Content Include="assets\media\icons\svg\General\Shield-protected.svg" />
    <Content Include="assets\media\icons\svg\General\Size.svg" />
    <Content Include="assets\media\icons\svg\General\Smile.svg" />
    <Content Include="assets\media\icons\svg\General\Star.svg" />
    <Content Include="assets\media\icons\svg\General\Thunder-move.svg" />
    <Content Include="assets\media\icons\svg\General\Thunder.svg" />
    <Content Include="assets\media\icons\svg\General\Trash.svg" />
    <Content Include="assets\media\icons\svg\General\Unlock.svg" />
    <Content Include="assets\media\icons\svg\General\Update.svg" />
    <Content Include="assets\media\icons\svg\General\User.svg" />
    <Content Include="assets\media\icons\svg\General\Visible.svg" />
    <Content Include="assets\media\icons\svg\Home\Air-ballon.svg" />
    <Content Include="assets\media\icons\svg\Home\Alarm-clock.svg" />
    <Content Include="assets\media\icons\svg\Home\Armchair.svg" />
    <Content Include="assets\media\icons\svg\Home\Bag-chair.svg" />
    <Content Include="assets\media\icons\svg\Home\Bath.svg" />
    <Content Include="assets\media\icons\svg\Home\Bed.svg" />
    <Content Include="assets\media\icons\svg\Home\Book-open.svg" />
    <Content Include="assets\media\icons\svg\Home\Book.svg" />
    <Content Include="assets\media\icons\svg\Home\Box.svg" />
    <Content Include="assets\media\icons\svg\Home\Broom.svg" />
    <Content Include="assets\media\icons\svg\Home\Building.svg" />
    <Content Include="assets\media\icons\svg\Home\Bulb#1.svg" />
    <Content Include="assets\media\icons\svg\Home\Bulb#2.svg" />
    <Content Include="assets\media\icons\svg\Home\Chair#1.svg" />
    <Content Include="assets\media\icons\svg\Home\Chair#2.svg" />
    <Content Include="assets\media\icons\svg\Home\Clock.svg" />
    <Content Include="assets\media\icons\svg\Home\Commode#1.svg" />
    <Content Include="assets\media\icons\svg\Home\Commode#2.svg" />
    <Content Include="assets\media\icons\svg\Home\Couch.svg" />
    <Content Include="assets\media\icons\svg\Home\Cupboard.svg" />
    <Content Include="assets\media\icons\svg\Home\Curtains.svg" />
    <Content Include="assets\media\icons\svg\Home\Deer.svg" />
    <Content Include="assets\media\icons\svg\Home\Door-open.svg" />
    <Content Include="assets\media\icons\svg\Home\Earth.svg" />
    <Content Include="assets\media\icons\svg\Home\Fireplace.svg" />
    <Content Include="assets\media\icons\svg\Home\Flashlight.svg" />
    <Content Include="assets\media\icons\svg\Home\Flower#1.svg" />
    <Content Include="assets\media\icons\svg\Home\Flower#2.svg" />
    <Content Include="assets\media\icons\svg\Home\Flower#3.svg" />
    <Content Include="assets\media\icons\svg\Home\Globe.svg" />
    <Content Include="assets\media\icons\svg\Home\Home-heart.svg" />
    <Content Include="assets\media\icons\svg\Home\Home.svg" />
    <Content Include="assets\media\icons\svg\Home\Key.svg" />
    <Content Include="assets\media\icons\svg\Home\Ladder.svg" />
    <Content Include="assets\media\icons\svg\Home\Lamp#1.svg" />
    <Content Include="assets\media\icons\svg\Home\Lamp#2.svg" />
    <Content Include="assets\media\icons\svg\Home\Library.svg" />
    <Content Include="assets\media\icons\svg\Home\Mailbox.svg" />
    <Content Include="assets\media\icons\svg\Home\Mirror.svg" />
    <Content Include="assets\media\icons\svg\Home\Picture.svg" />
    <Content Include="assets\media\icons\svg\Home\Ruller.svg" />
    <Content Include="assets\media\icons\svg\Home\Stairs.svg" />
    <Content Include="assets\media\icons\svg\Home\Timer.svg" />
    <Content Include="assets\media\icons\svg\Home\Toilet.svg" />
    <Content Include="assets\media\icons\svg\Home\Towel.svg" />
    <Content Include="assets\media\icons\svg\Home\Trash.svg" />
    <Content Include="assets\media\icons\svg\Home\Water-mixer.svg" />
    <Content Include="assets\media\icons\svg\Home\Weight#1.svg" />
    <Content Include="assets\media\icons\svg\Home\Weight#2.svg" />
    <Content Include="assets\media\icons\svg\Home\Wood#1.svg" />
    <Content Include="assets\media\icons\svg\Home\Wood#2.svg" />
    <Content Include="assets\media\icons\svg\Home\Wood-horse.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-3d.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-4-blocks.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-arrange.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-grid.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-horizontal.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-left-panel-1.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-left-panel-2.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-right-panel-1.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-right-panel-2.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-top-panel-1.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-top-panel-2.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-top-panel-3.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-top-panel-4.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-top-panel-5.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-top-panel-6.svg" />
    <Content Include="assets\media\icons\svg\Layout\Layout-vertical.svg" />
    <Content Include="assets\media\icons\svg\Map\Compass.svg" />
    <Content Include="assets\media\icons\svg\Map\Direction#1.svg" />
    <Content Include="assets\media\icons\svg\Map\Direction#2.svg" />
    <Content Include="assets\media\icons\svg\Map\Location-arrow.svg" />
    <Content Include="assets\media\icons\svg\Map\Marker#1.svg" />
    <Content Include="assets\media\icons\svg\Map\Marker#2.svg" />
    <Content Include="assets\media\icons\svg\Map\Position.svg" />
    <Content Include="assets\media\icons\svg\Media\Add-music.svg" />
    <Content Include="assets\media\icons\svg\Media\Airplay-video.svg" />
    <Content Include="assets\media\icons\svg\Media\Airplay.svg" />
    <Content Include="assets\media\icons\svg\Media\Back.svg" />
    <Content Include="assets\media\icons\svg\Media\Backward.svg" />
    <Content Include="assets\media\icons\svg\Media\CD.svg" />
    <Content Include="assets\media\icons\svg\Media\DVD.svg" />
    <Content Include="assets\media\icons\svg\Media\Eject.svg" />
    <Content Include="assets\media\icons\svg\Media\Equalizer.svg" />
    <Content Include="assets\media\icons\svg\Media\Forward.svg" />
    <Content Include="assets\media\icons\svg\Media\Media-library#1.svg" />
    <Content Include="assets\media\icons\svg\Media\Media-library#2.svg" />
    <Content Include="assets\media\icons\svg\Media\Media-library#3.svg" />
    <Content Include="assets\media\icons\svg\Media\Movie-Lane #2.svg" />
    <Content Include="assets\media\icons\svg\Media\Movie-lane#1.svg" />
    <Content Include="assets\media\icons\svg\Media\Music-cloud.svg" />
    <Content Include="assets\media\icons\svg\Media\Music-note.svg" />
    <Content Include="assets\media\icons\svg\Media\Music.svg" />
    <Content Include="assets\media\icons\svg\Media\Mute.svg" />
    <Content Include="assets\media\icons\svg\Media\Next.svg" />
    <Content Include="assets\media\icons\svg\Media\Pause.svg" />
    <Content Include="assets\media\icons\svg\Media\Play.svg" />
    <Content Include="assets\media\icons\svg\Media\Playlist#1.svg" />
    <Content Include="assets\media\icons\svg\Media\Playlist#2.svg" />
    <Content Include="assets\media\icons\svg\Media\Rec.svg" />
    <Content Include="assets\media\icons\svg\Media\Repeat-one.svg" />
    <Content Include="assets\media\icons\svg\Media\Repeat.svg" />
    <Content Include="assets\media\icons\svg\Media\Shuffle.svg" />
    <Content Include="assets\media\icons\svg\Media\Volume-down.svg" />
    <Content Include="assets\media\icons\svg\Media\Volume-full.svg" />
    <Content Include="assets\media\icons\svg\Media\Volume-half.svg" />
    <Content Include="assets\media\icons\svg\Media\Volume-up.svg" />
    <Content Include="assets\media\icons\svg\Media\Vynil.svg" />
    <Content Include="assets\media\icons\svg\Media\Youtube.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Angle-double-down.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Angle-double-left.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Angle-double-right.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Angle-double-up.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Angle-down.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Angle-left.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Angle-right.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-down.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-from-bottom.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-from-left.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-from-right.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-from-top.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-left.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-right.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-to-bottom.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-to-left.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-to-right.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-to-up.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrow-up.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrows-h.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Arrows-v.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Check.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Close.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Double-check.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Down-2.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Down-left.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Down-right.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Exchange.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Left 3.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Left-2.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Minus.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Plus.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Right 3.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Right-2.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Route.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Sign-in.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Sign-out.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Stockholm-icons\Navigation\Angle-up.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Up-2.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Up-down.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Up-left.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Up-right.svg" />
    <Content Include="assets\media\icons\svg\Navigation\Waiting.svg" />
    <Content Include="assets\media\icons\svg\Shopping\ATM.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Bag#1.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Bag#2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Barcode-read.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Barcode-scan.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Barcode.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Bitcoin.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Box#1.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Box#2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Box#3.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Calculator.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Cart#1.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Cart#2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Cart#3.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Chart-bar#1.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Chart-bar#2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Chart-bar#3.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Chart-line#1.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Chart-line#2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Chart-pie.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Credit-card.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Dollar.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Euro.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Gift.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Loader.svg" />
    <Content Include="assets\media\icons\svg\Shopping\MC.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Money.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Pound.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Price #1.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Price #2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Rouble.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Safe.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Sale#1.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Sale#2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Settings.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Sort#1.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Sort#2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Sort#3.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Ticket.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Wallet#2.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Wallet#3.svg" />
    <Content Include="assets\media\icons\svg\Shopping\Wallet.svg" />
    <Content Include="assets\media\icons\svg\Text\Align-auto.svg" />
    <Content Include="assets\media\icons\svg\Text\Align-center.svg" />
    <Content Include="assets\media\icons\svg\Text\Align-justify.svg" />
    <Content Include="assets\media\icons\svg\Text\Align-left.svg" />
    <Content Include="assets\media\icons\svg\Text\Align-right.svg" />
    <Content Include="assets\media\icons\svg\Text\Article.svg" />
    <Content Include="assets\media\icons\svg\Text\Bold.svg" />
    <Content Include="assets\media\icons\svg\Text\Bullet-list.svg" />
    <Content Include="assets\media\icons\svg\Text\Code.svg" />
    <Content Include="assets\media\icons\svg\Text\Edit-text.svg" />
    <Content Include="assets\media\icons\svg\Text\Filter.svg" />
    <Content Include="assets\media\icons\svg\Text\Font.svg" />
    <Content Include="assets\media\icons\svg\Text\H1.svg" />
    <Content Include="assets\media\icons\svg\Text\H2.svg" />
    <Content Include="assets\media\icons\svg\Text\Itallic.svg" />
    <Content Include="assets\media\icons\svg\Text\Menu.svg" />
    <Content Include="assets\media\icons\svg\Text\Paragraph.svg" />
    <Content Include="assets\media\icons\svg\Text\Quote#1.svg" />
    <Content Include="assets\media\icons\svg\Text\Quote#2.svg" />
    <Content Include="assets\media\icons\svg\Text\Redo.svg" />
    <Content Include="assets\media\icons\svg\Text\Strikethrough.svg" />
    <Content Include="assets\media\icons\svg\Text\Text-height.svg" />
    <Content Include="assets\media\icons\svg\Text\Text-width.svg" />
    <Content Include="assets\media\icons\svg\Text\Text.svg" />
    <Content Include="assets\media\icons\svg\Text\Underline.svg" />
    <Content Include="assets\media\icons\svg\Text\Undo.svg" />
    <Content Include="assets\media\icons\svg\Tools\Angle Grinder.svg" />
    <Content Include="assets\media\icons\svg\Tools\Axe.svg" />
    <Content Include="assets\media\icons\svg\Tools\Brush.svg" />
    <Content Include="assets\media\icons\svg\Tools\Compass.svg" />
    <Content Include="assets\media\icons\svg\Tools\Hummer#2.svg" />
    <Content Include="assets\media\icons\svg\Tools\Hummer.svg" />
    <Content Include="assets\media\icons\svg\Tools\Pantone.svg" />
    <Content Include="assets\media\icons\svg\Tools\Road-Cone.svg" />
    <Content Include="assets\media\icons\svg\Tools\Roller.svg" />
    <Content Include="assets\media\icons\svg\Tools\Roulette.svg" />
    <Content Include="assets\media\icons\svg\Tools\Screwdriver.svg" />
    <Content Include="assets\media\icons\svg\Tools\Shovel.svg" />
    <Content Include="assets\media\icons\svg\Tools\Spatula.svg" />
    <Content Include="assets\media\icons\svg\Tools\Swiss-knife.svg" />
    <Content Include="assets\media\icons\svg\Tools\Tools.svg" />
    <Content Include="assets\media\icons\svg\Weather\Celcium.svg" />
    <Content Include="assets\media\icons\svg\Weather\Cloud#1.svg" />
    <Content Include="assets\media\icons\svg\Weather\Cloud#2.svg" />
    <Content Include="assets\media\icons\svg\Weather\Cloud-fog.svg" />
    <Content Include="assets\media\icons\svg\Weather\Cloud-sun.svg" />
    <Content Include="assets\media\icons\svg\Weather\Cloud-wind.svg" />
    <Content Include="assets\media\icons\svg\Weather\Cloudy-night.svg" />
    <Content Include="assets\media\icons\svg\Weather\Cloudy.svg" />
    <Content Include="assets\media\icons\svg\Weather\Day-rain.svg" />
    <Content Include="assets\media\icons\svg\Weather\Fahrenheit.svg" />
    <Content Include="assets\media\icons\svg\Weather\Fog.svg" />
    <Content Include="assets\media\icons\svg\Weather\Moon.svg" />
    <Content Include="assets\media\icons\svg\Weather\Night-fog.svg" />
    <Content Include="assets\media\icons\svg\Weather\Night-rain.svg" />
    <Content Include="assets\media\icons\svg\Weather\Rain#1.svg" />
    <Content Include="assets\media\icons\svg\Weather\Rain#2.svg" />
    <Content Include="assets\media\icons\svg\Weather\Rain#5.svg" />
    <Content Include="assets\media\icons\svg\Weather\Rainbow.svg" />
    <Content Include="assets\media\icons\svg\Weather\Snow#1.svg" />
    <Content Include="assets\media\icons\svg\Weather\Snow#2.svg" />
    <Content Include="assets\media\icons\svg\Weather\Snow#3.svg" />
    <Content Include="assets\media\icons\svg\Weather\Snow.svg" />
    <Content Include="assets\media\icons\svg\Weather\Storm.svg" />
    <Content Include="assets\media\icons\svg\Weather\Sun-fog.svg" />
    <Content Include="assets\media\icons\svg\Weather\Sun.svg" />
    <Content Include="assets\media\icons\svg\Weather\Suset#1.svg" />
    <Content Include="assets\media\icons\svg\Weather\Suset#2.svg" />
    <Content Include="assets\media\icons\svg\Weather\Temperature-empty.svg" />
    <Content Include="assets\media\icons\svg\Weather\Temperature-full.svg" />
    <Content Include="assets\media\icons\svg\Weather\Temperature-half.svg" />
    <Content Include="assets\media\icons\svg\Weather\Thunder-night.svg" />
    <Content Include="assets\media\icons\svg\Weather\Thunder.svg" />
    <Content Include="assets\media\icons\svg\Weather\Umbrella.svg" />
    <Content Include="assets\media\icons\svg\Weather\Wind.svg" />
    <Content Include="assets\media\icons\txt.png" />
    <Content Include="assets\media\icons\warning.svg" />
    <Content Include="assets\media\icons\word.png" />
    <Content Include="assets\media\illustrations\contact.svg" />
    <Content Include="assets\media\illustrations\data-points.svg" />
    <Content Include="assets\media\illustrations\working.svg" />
    <Content Include="assets\media\images\lastLine.png" />
    <Content Include="assets\media\logos\favicon.ico" />
    <Content Include="assets\media\logos\logo-10-sm.png" />
    <Content Include="assets\media\logos\logo-10.png" />
    <Content Include="assets\media\logos\logo-11-sm.png" />
    <Content Include="assets\media\logos\logo-11.png" />
    <Content Include="assets\media\logos\logo-12.png" />
    <Content Include="assets\media\logos\logo-2-sm.png" />
    <Content Include="assets\media\logos\logo-2.png" />
    <Content Include="assets\media\logos\logo-3.png" />
    <Content Include="assets\media\logos\logo-4-sm.png" />
    <Content Include="assets\media\logos\logo-4.png" />
    <Content Include="assets\media\logos\logo-5-sm.png" />
    <Content Include="assets\media\logos\logo-5.png" />
    <Content Include="assets\media\logos\logo-6-sm.png" />
    <Content Include="assets\media\logos\logo-6.png" />
    <Content Include="assets\media\logos\logo-7-sm.png" />
    <Content Include="assets\media\logos\logo-7.png" />
    <Content Include="assets\media\logos\logo-8-inverse.png" />
    <Content Include="assets\media\logos\logo-8-sm.png" />
    <Content Include="assets\media\logos\logo-8.png" />
    <Content Include="assets\media\logos\logo-9-sm.png" />
    <Content Include="assets\media\logos\logo-9.png" />
    <Content Include="assets\media\logos\logo-dark-sm.png" />
    <Content Include="assets\media\logos\logo-dark.png" />
    <Content Include="assets\media\logos\logo-light-sm.png" />
    <Content Include="assets\media\logos\logo-light.png" />
    <Content Include="assets\media\logos\logo-mini-2-md.png" />
    <Content Include="assets\media\logos\logo-mini-lg.png" />
    <Content Include="assets\media\logos\logo-mini-md.png" />
    <Content Include="assets\media\logos\logo-mini-sm.png" />
    <Content Include="assets\media\misc\bg-1.jpg" />
    <Content Include="assets\media\misc\bg-2.jpg" />
    <Content Include="assets\media\products\product1.jpg" />
    <Content Include="assets\media\products\product10.jpg" />
    <Content Include="assets\media\products\product11.jpg" />
    <Content Include="assets\media\products\product12.jpg" />
    <Content Include="assets\media\products\product13.jpg" />
    <Content Include="assets\media\products\product14.jpg" />
    <Content Include="assets\media\products\product15.jpg" />
    <Content Include="assets\media\products\product16.jpg" />
    <Content Include="assets\media\products\product17.jpg" />
    <Content Include="assets\media\products\product18.jpg" />
    <Content Include="assets\media\products\product19.jpg" />
    <Content Include="assets\media\products\product2.jpg" />
    <Content Include="assets\media\products\product20.jpg" />
    <Content Include="assets\media\products\product21.jpg" />
    <Content Include="assets\media\products\product22.jpg" />
    <Content Include="assets\media\products\product23.jpg" />
    <Content Include="assets\media\products\product24.jpg" />
    <Content Include="assets\media\products\product25.jpg" />
    <Content Include="assets\media\products\product26.jpg" />
    <Content Include="assets\media\products\product27.jpg" />
    <Content Include="assets\media\products\product28.jpg" />
    <Content Include="assets\media\products\product3.jpg" />
    <Content Include="assets\media\products\product4.jpg" />
    <Content Include="assets\media\products\product5.jpg" />
    <Content Include="assets\media\products\product6.jpg" />
    <Content Include="assets\media\products\product7.jpg" />
    <Content Include="assets\media\products\product8.jpg" />
    <Content Include="assets\media\products\product9.jpg" />
    <Content Include="assets\media\project-logos\1.png" />
    <Content Include="assets\media\project-logos\2.png" />
    <Content Include="assets\media\project-logos\3.png" />
    <Content Include="assets\media\project-logos\4.png" />
    <Content Include="assets\media\project-logos\5.png" />
    <Content Include="assets\media\project-logos\6.png" />
    <Content Include="assets\media\project-logos\7.png" />
    <Content Include="assets\media\users\100_1.jpg" />
    <Content Include="assets\media\users\100_10.jpg" />
    <Content Include="assets\media\users\100_11.jpg" />
    <Content Include="assets\media\users\100_12.jpg" />
    <Content Include="assets\media\users\100_13.jpg" />
    <Content Include="assets\media\users\100_14.jpg" />
    <Content Include="assets\media\users\100_2.jpg" />
    <Content Include="assets\media\users\100_3.jpg" />
    <Content Include="assets\media\users\100_4.jpg" />
    <Content Include="assets\media\users\100_5.jpg" />
    <Content Include="assets\media\users\100_6.jpg" />
    <Content Include="assets\media\users\100_7.jpg" />
    <Content Include="assets\media\users\100_8.jpg" />
    <Content Include="assets\media\users\100_9.jpg" />
    <Content Include="assets\media\users\300_1.jpg" />
    <Content Include="assets\media\users\300_10.jpg" />
    <Content Include="assets\media\users\300_11.jpg" />
    <Content Include="assets\media\users\300_12.jpg" />
    <Content Include="assets\media\users\300_13.jpg" />
    <Content Include="assets\media\users\300_14.jpg" />
    <Content Include="assets\media\users\300_15.jpg" />
    <Content Include="assets\media\users\300_16.jpg" />
    <Content Include="assets\media\users\300_17.jpg" />
    <Content Include="assets\media\users\300_18.jpg" />
    <Content Include="assets\media\users\300_19.jpg" />
    <Content Include="assets\media\users\300_2.jpg" />
    <Content Include="assets\media\users\300_20.jpg" />
    <Content Include="assets\media\users\300_21.jpg" />
    <Content Include="assets\media\users\300_22.jpg" />
    <Content Include="assets\media\users\300_23.jpg" />
    <Content Include="assets\media\users\300_24.jpg" />
    <Content Include="assets\media\users\300_25.jpg" />
    <Content Include="assets\media\users\300_3.jpg" />
    <Content Include="assets\media\users\300_4.jpg" />
    <Content Include="assets\media\users\300_5.jpg" />
    <Content Include="assets\media\users\300_6.jpg" />
    <Content Include="assets\media\users\300_7.jpg" />
    <Content Include="assets\media\users\300_8.jpg" />
    <Content Include="assets\media\users\300_9.jpg" />
    <Content Include="assets\media\users\default.jpg" />
    <Content Include="assets\media\users\user1.jpg" />
    <Content Include="assets\media\users\user2.jpg" />
    <Content Include="assets\media\users\user3.jpg" />
    <Content Include="assets\media\users\user4.jpg" />
    <Content Include="assets\media\users\user5.jpg" />
    <Content Include="assets\plugins\custom\datatables.net-autofill-bs4\css\autoFill.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-autofill-bs4\js\autoFill.bootstrap4.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-autofill\js\dataTables.autoFill.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-bs4\css\dataTables.bootstrap4.css" />
    <Content Include="assets\plugins\custom\datatables.net-bs4\js\dataTables.bootstrap4.js" />
    <Content Include="assets\plugins\custom\datatables.net-buttons-bs4\css\buttons.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-buttons-bs4\js\buttons.bootstrap4.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-buttons\js\buttons.colVis.js" />
    <Content Include="assets\plugins\custom\datatables.net-buttons\js\buttons.flash.js" />
    <Content Include="assets\plugins\custom\datatables.net-buttons\js\buttons.html5.js" />
    <Content Include="assets\plugins\custom\datatables.net-buttons\js\buttons.print.js" />
    <Content Include="assets\plugins\custom\datatables.net-buttons\js\dataTables.buttons.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-colreorder-bs4\css\colReorder.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-colreorder\js\dataTables.colReorder.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-fixedcolumns-bs4\css\fixedColumns.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-fixedcolumns\js\dataTables.fixedColumns.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-fixedheader-bs4\css\fixedHeader.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-fixedheader\js\dataTables.fixedHeader.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-keytable-bs4\css\keyTable.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-keytable\js\dataTables.keyTable.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-responsive-bs4\css\responsive.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-responsive-bs4\js\responsive.bootstrap4.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-responsive\js\dataTables.responsive.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-rowgroup-bs4\css\rowGroup.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-rowgroup\js\dataTables.rowGroup.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-rowreorder-bs4\css\rowReorder.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-rowreorder\js\dataTables.rowReorder.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-scroller-bs4\css\scroller.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-scroller\js\dataTables.scroller.min.js" />
    <Content Include="assets\plugins\custom\datatables.net-select-bs4\css\select.bootstrap4.min.css" />
    <Content Include="assets\plugins\custom\datatables.net-select\js\dataTables.select.min.js" />
    <Content Include="assets\plugins\custom\datatables.net\js\jquery.dataTables.js" />
    <Content Include="assets\plugins\custom\flot\dist\es5\jquery.flot.js" />
    <Content Include="assets\plugins\custom\flot\source\jquery.flot.axislabels.js" />
    <Content Include="assets\plugins\custom\flot\source\jquery.flot.categories.js" />
    <Content Include="assets\plugins\custom\flot\source\jquery.flot.crosshair.js" />
    <Content Include="assets\plugins\custom\flot\source\jquery.flot.pie.js" />
    <Content Include="assets\plugins\custom\flot\source\jquery.flot.resize.js" />
    <Content Include="assets\plugins\custom\flot\source\jquery.flot.stack.js" />
    <Content Include="assets\plugins\custom\fullcalendar\core\main.css" />
    <Content Include="assets\plugins\custom\fullcalendar\core\main.js" />
    <Content Include="assets\plugins\custom\fullcalendar\daygrid\main.css" />
    <Content Include="assets\plugins\custom\fullcalendar\daygrid\main.js" />
    <Content Include="assets\plugins\custom\fullcalendar\google-calendar\main.js" />
    <Content Include="assets\plugins\custom\fullcalendar\interaction\main.js" />
    <Content Include="assets\plugins\custom\fullcalendar\list\main.css" />
    <Content Include="assets\plugins\custom\fullcalendar\list\main.js" />
    <Content Include="assets\plugins\custom\fullcalendar\timegrid\main.css" />
    <Content Include="assets\plugins\custom\fullcalendar\timegrid\main.js" />
    <Content Include="assets\plugins\custom\gmaps\gmaps.js" />
    <Content Include="assets\plugins\custom\jqvmap\dist\jquery.vmap.js" />
    <Content Include="assets\plugins\custom\jqvmap\dist\jqvmap.css" />
    <Content Include="assets\plugins\custom\jqvmap\dist\maps\jquery.vmap.europe.js" />
    <Content Include="assets\plugins\custom\jqvmap\dist\maps\jquery.vmap.germany.js" />
    <Content Include="assets\plugins\custom\jqvmap\dist\maps\jquery.vmap.russia.js" />
    <Content Include="assets\plugins\custom\jqvmap\dist\maps\jquery.vmap.usa.js" />
    <Content Include="assets\plugins\custom\jqvmap\dist\maps\jquery.vmap.world.js" />
    <Content Include="assets\plugins\custom\jstree\dist\jstree.js" />
    <Content Include="assets\plugins\custom\jstree\dist\themes\default\style.css" />
    <Content Include="assets\plugins\custom\jstree\dist\themes\default\throbber.gif" />
    <Content Include="assets\plugins\custom\jszip\dist\jszip.min.js" />
    <Content Include="assets\plugins\custom\js\global\integration\plugins\datatables.init.js" />
    <Content Include="assets\plugins\custom\media\plugins\jstree\32px.png" />
    <Content Include="assets\plugins\custom\pdfmake\build\pdfmake.min.js" />
    <Content Include="assets\plugins\custom\pdfmake\build\vfs_fonts.js" />
    <Content Include="assets\plugins\custom\plugins\jquery-ui\images\ui-icons_444444_256x240.png" />
    <Content Include="assets\plugins\custom\plugins\jquery-ui\images\ui-icons_555555_256x240.png" />
    <Content Include="assets\plugins\custom\plugins\jquery-ui\images\ui-icons_777620_256x240.png" />
    <Content Include="assets\plugins\custom\plugins\jquery-ui\images\ui-icons_777777_256x240.png" />
    <Content Include="assets\plugins\custom\plugins\jquery-ui\images\ui-icons_cc0000_256x240.png" />
    <Content Include="assets\plugins\custom\plugins\jquery-ui\images\ui-icons_ffffff_256x240.png" />
    <Content Include="assets\plugins\custom\plugins\jquery-ui\jquery-ui.min.css" />
    <Content Include="assets\plugins\custom\plugins\jquery-ui\jquery-ui.min.js" />
    <Content Include="assets\plugins\custom\uppy\dist\uppy.min.css" />
    <Content Include="assets\plugins\custom\uppy\dist\uppy.min.js" />
    <Content Include="assets\plugins\general\animate.css\animate.css" />
    <Content Include="assets\plugins\general\autosize\dist\autosize.js" />
    <Content Include="assets\plugins\general\block-ui\jquery.blockUI.js" />
    <Content Include="assets\plugins\general\bootstrap-datepicker\dist\css\bootstrap-datepicker3.css" />
    <Content Include="assets\plugins\general\bootstrap-datepicker\dist\js\bootstrap-datepicker.min.js" />
    <Content Include="assets\plugins\general\bootstrap-daterangepicker\daterangepicker.css" />
    <Content Include="assets\plugins\general\bootstrap-daterangepicker\daterangepicker.js" />
    <Content Include="assets\plugins\general\bootstrap-datetime-picker\css\bootstrap-datetimepicker.css" />
    <Content Include="assets\plugins\general\bootstrap-datetime-picker\js\bootstrap-datetimepicker.min.js" />
    <Content Include="assets\plugins\general\bootstrap-markdown\css\bootstrap-markdown.min.css" />
    <Content Include="assets\plugins\general\bootstrap-markdown\js\bootstrap-markdown.js" />
    <Content Include="assets\plugins\general\bootstrap-maxlength\src\bootstrap-maxlength.js" />
    <Content Include="assets\plugins\general\bootstrap-notify\bootstrap-notify.min.js" />
    <Content Include="assets\plugins\general\bootstrap-select\dist\css\bootstrap-select.css" />
    <Content Include="assets\plugins\general\bootstrap-select\dist\js\bootstrap-select.js" />
    <Content Include="assets\plugins\general\bootstrap-switch\dist\css\bootstrap3\bootstrap-switch.css" />
    <Content Include="assets\plugins\general\bootstrap-switch\dist\js\bootstrap-switch.js" />
    <Content Include="assets\plugins\general\bootstrap-timepicker\css\bootstrap-timepicker.css" />
    <Content Include="assets\plugins\general\bootstrap-timepicker\js\bootstrap-timepicker.min.js" />
    <Content Include="assets\plugins\general\bootstrap-touchspin\dist\jquery.bootstrap-touchspin.css" />
    <Content Include="assets\plugins\general\bootstrap-touchspin\dist\jquery.bootstrap-touchspin.js" />
    <Content Include="assets\plugins\general\bootstrap\dist\js\bootstrap.min.js" />
    <Content Include="assets\plugins\general\chart.js\dist\Chart.bundle.js" />
    <Content Include="assets\plugins\general\clipboard\dist\clipboard.min.js" />
    <Content Include="assets\plugins\general\counterup\jquery.counterup.js" />
    <Content Include="assets\plugins\general\dompurify\dist\purify.js" />
    <Content Include="assets\plugins\general\dropzone\dist\dropzone.css" />
    <Content Include="assets\plugins\general\dropzone\dist\dropzone.js" />
    <Content Include="assets\plugins\general\dual-listbox\dist\dual-listbox.css" />
    <Content Include="assets\plugins\general\dual-listbox\dist\dual-listbox.js" />
    <Content Include="assets\plugins\general\es6-promise-polyfill\promise.min.js" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\css\all.min.css" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-brands-400.svg" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-regular-400.svg" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-solid-900.svg" />
    <Content Include="assets\plugins\general\handlebars\dist\handlebars.js" />
    <Content Include="assets\plugins\general\inputmask\dist\inputmask\inputmask.date.extensions.js" />
    <Content Include="assets\plugins\general\inputmask\dist\inputmask\inputmask.numeric.extensions.js" />
    <Content Include="assets\plugins\general\inputmask\dist\jquery.inputmask.bundle.js" />
    <Content Include="assets\plugins\general\ion-rangeslider\css\ion.rangeSlider.css" />
    <Content Include="assets\plugins\general\ion-rangeslider\js\ion.rangeSlider.js" />
    <Content Include="assets\plugins\general\jquery-form\dist\jquery.form.min.js" />
    <Content Include="assets\plugins\general\jquery-validation\dist\additional-methods.js" />
    <Content Include="assets\plugins\general\jquery-validation\dist\jquery.validate.js" />
    <Content Include="assets\plugins\general\jquery.repeater\src\jquery.input.js" />
    <Content Include="assets\plugins\general\jquery.repeater\src\lib.js" />
    <Content Include="assets\plugins\general\jquery.repeater\src\repeater.js" />
    <Content Include="assets\plugins\general\jquery\dist\jquery.js" />
    <Content Include="assets\plugins\general\js-cookie\src\js.cookie.js" />
    <Content Include="assets\plugins\general\js\global\integration\plugins\bootstrap-datepicker.init.js" />
    <Content Include="assets\plugins\general\js\global\integration\plugins\bootstrap-markdown.init.js" />
    <Content Include="assets\plugins\general\js\global\integration\plugins\bootstrap-notify.init.js" />
    <Content Include="assets\plugins\general\js\global\integration\plugins\bootstrap-switch.init.js" />
    <Content Include="assets\plugins\general\js\global\integration\plugins\bootstrap-timepicker.init.js" />
    <Content Include="assets\plugins\general\js\global\integration\plugins\dropzone.init.js" />
    <Content Include="assets\plugins\general\js\global\integration\plugins\jquery-validation.init.js" />
    <Content Include="assets\plugins\general\js\global\integration\plugins\sweetalert2.init.js" />
    <Content Include="assets\plugins\general\markdown\lib\markdown.js" />
    <Content Include="assets\plugins\general\moment\min\moment.min.js" />
    <Content Include="assets\plugins\general\morris.js\morris.css" />
    <Content Include="assets\plugins\general\morris.js\morris.js" />
    <Content Include="assets\plugins\general\nouislider\distribute\nouislider.css" />
    <Content Include="assets\plugins\general\nouislider\distribute\nouislider.js" />
    <Content Include="assets\plugins\general\owl.carousel\dist\assets\ajax-loader.gif" />
    <Content Include="assets\plugins\general\owl.carousel\dist\assets\owl.carousel.css" />
    <Content Include="assets\plugins\general\owl.carousel\dist\assets\owl.theme.default.css" />
    <Content Include="assets\plugins\general\owl.carousel\dist\assets\owl.video.play.png" />
    <Content Include="assets\plugins\general\owl.carousel\dist\owl.carousel.js" />
    <Content Include="assets\plugins\general\perfect-scrollbar\css\perfect-scrollbar.css" />
    <Content Include="assets\plugins\general\perfect-scrollbar\dist\perfect-scrollbar.js" />
    <Content Include="assets\plugins\general\plugins\bootstrap-multiselectsplitter\bootstrap-multiselectsplitter.min.js" />
    <Content Include="assets\plugins\general\plugins\bootstrap-session-timeout\dist\bootstrap-session-timeout.min.js" />
    <Content Include="assets\plugins\general\plugins\flaticon2\flaticon.css" />
    <Content Include="assets\plugins\general\plugins\flaticon2\font\Flaticon2.svg" />
    <Content Include="assets\plugins\general\plugins\flaticon\flaticon.css" />
    <Content Include="assets\plugins\general\plugins\flaticon\font\Flaticon.svg" />
    <Content Include="assets\plugins\general\plugins\jquery-idletimer\idle-timer.min.js" />
    <Content Include="assets\plugins\general\plugins\line-awesome\css\line-awesome.css" />
    <Content Include="assets\plugins\general\plugins\line-awesome\fonts\line-awesome.svg" />
    <Content Include="assets\plugins\general\popper.js\dist\umd\popper.js" />
    <Content Include="assets\plugins\general\quill\dist\quill.js" />
    <Content Include="assets\plugins\general\quill\dist\quill.snow.css" />
    <Content Include="assets\plugins\general\raphael\raphael.js" />
    <Content Include="assets\plugins\general\select2\dist\css\select2.css" />
    <Content Include="assets\plugins\general\select2\dist\js\select2.full.js" />
    <Content Include="assets\plugins\general\socicon\css\socicon.css" />
    <Content Include="assets\plugins\general\socicon\font\socicon.svg" />
    <Content Include="assets\plugins\general\sticky-js\dist\sticky.min.js" />
    <Content Include="assets\plugins\general\summernote\dist\summernote.css" />
    <Content Include="assets\plugins\general\summernote\dist\summernote.js" />
    <Content Include="assets\plugins\general\sweetalert2\dist\sweetalert2.css" />
    <Content Include="assets\plugins\general\sweetalert2\dist\sweetalert2.min.js" />
    <Content Include="assets\plugins\general\tether\dist\css\tether.css" />
    <Content Include="assets\plugins\general\toastr\build\toastr.css" />
    <Content Include="assets\plugins\general\toastr\build\toastr.min.js" />
    <Content Include="assets\plugins\general\tooltip.js\dist\umd\tooltip.min.js" />
    <Content Include="assets\plugins\general\typeahead.js\dist\typeahead.bundle.js" />
    <Content Include="assets\plugins\general\waypoints\lib\jquery.waypoints.js" />
    <Content Include="assets\plugins\general\wnumb\wNumb.js" />
    <Content Include="assets\plugins\general\yaireo\tagify\dist\tagify.css" />
    <Content Include="assets\plugins\general\yaireo\tagify\dist\tagify.min.js" />
    <Content Include="assets\plugins\general\yaireo\tagify\dist\tagify.polyfills.min.js" />
    <Compile Include="Controllers\Inventory\SemiFinishedGoodsQCScanningsController.cs" />
    <Content Include="bin\Web.dll" />
    <Content Include="bin\Web.pdb" />
    <Content Include="Content\bootstrap-theme.css" />
    <Content Include="Content\bootstrap-theme.min.css" />
    <Content Include="Content\bootstrap.css" />
    <Content Include="Content\bootstrap.min.css" />
    <Content Include="Documents\PO\Sample.txt" />
    <Content Include="favicon.ico" />
    <Content Include="fonts\glyphicons-halflings-regular.svg" />
    <Content Include="Global.asax" />
    <Compile Include="Reports\MITL\rptStockDetail.cs">
      <SubType>Component</SubType>
    </Compile>
    <Content Include="ReportViewerWebForm.aspx" />
    <Content Include="scripts\bootstrap.js" />
    <Content Include="scripts\bootstrap.min.js" />
    <Content Include="scripts\common.js" />
    <Content Include="scripts\dropzone\basic.css" />
    <Content Include="scripts\dropzone\basic.min.css" />
    <Content Include="scripts\dropzone\dropzone-amd-module.js" />
    <Content Include="scripts\dropzone\dropzone-amd-module.min.js" />
    <Content Include="scripts\dropzone\dropzone.css" />
    <Content Include="scripts\dropzone\dropzone.js" />
    <Content Include="scripts\dropzone\dropzone.min.css" />
    <Content Include="scripts\dropzone\dropzone.min.js" />
    <Content Include="scripts\dx.aspnet.data.js" />
    <Content Include="IssueNoteDataSet.xsc">
      <DependentUpon>IssueNoteDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="INDataSet.xsc">
      <DependentUpon>INDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="fonts\glyphicons-halflings-regular.woff2" />
    <Content Include="fonts\glyphicons-halflings-regular.woff" />
    <Content Include="fonts\glyphicons-halflings-regular.ttf" />
    <Content Include="fonts\glyphicons-halflings-regular.eot" />
    <Content Include="Content\bootstrap.min.css.map" />
    <Content Include="Content\bootstrap.css.map" />
    <Content Include="Content\bootstrap-theme.min.css.map" />
    <Content Include="Content\bootstrap-theme.css.map" />
    <Content Include="CreditCardDetailsDataSet.xsc">
      <DependentUpon>CreditCardDetailsDataSet.xsd</DependentUpon>
    </Content>
    <None Include="CreditCardDetailsDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>CreditCardDetailsDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="CreditCardDetailsDataSet.xss">
      <DependentUpon>CreditCardDetailsDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="DailySalesInvoiceDataSet.xsc">
      <DependentUpon>DailySalesInvoiceDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DailySalesInvoiceDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DailySalesInvoiceDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="DailySalesInvoiceDataSet.xss">
      <DependentUpon>DailySalesInvoiceDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="DiscountSummeryDataSet.xsc">
      <DependentUpon>DiscountSummeryDataSet.xsd</DependentUpon>
    </Content>
    <None Include="DiscountSummeryDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DiscountSummeryDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="DiscountSummeryDataSet.xss">
      <DependentUpon>DiscountSummeryDataSet.xsd</DependentUpon>
    </Content>
    <None Include="INDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>INDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="INDataSet.xss">
      <DependentUpon>INDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="InvoiceWiseDiscountDataSet.xsc">
      <DependentUpon>InvoiceWiseDiscountDataSet.xsd</DependentUpon>
    </Content>
    <None Include="InvoiceWiseDiscountDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>InvoiceWiseDiscountDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="InvoiceWiseDiscountDataSet.xss">
      <DependentUpon>InvoiceWiseDiscountDataSet.xsd</DependentUpon>
    </Content>
    <None Include="IssueNoteDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>IssueNoteDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="IssueNoteDataSet.xss">
      <DependentUpon>IssueNoteDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="PoReportDataSet.xsc">
      <DependentUpon>PoReportDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="newNemsuErpDataSet.xsc">
      <DependentUpon>newNemsuErpDataSet.xsd</DependentUpon>
    </Content>
    <None Include="newNemsuErpDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>newNemsuErpDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="newNemsuErpDataSet.xss">
      <DependentUpon>newNemsuErpDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="PickMeDataSet.xsc">
      <DependentUpon>PickMeDataSet.xsd</DependentUpon>
    </Content>
    <None Include="PickMeDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>PickMeDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="PickMeDataSet.xss">
      <DependentUpon>PickMeDataSet.xsd</DependentUpon>
    </Content>
    <None Include="PoReportDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>PoReportDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="PoReportDataSet.xss">
      <DependentUpon>PoReportDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\menuDetailReportDataSet.xsc">
      <DependentUpon>menuDetailReportDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\GRNListingDataSet.xsc">
      <DependentUpon>GRNListingDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\BillCancellationReportDetailDataSet.xsc">
      <DependentUpon>BillCancellationReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\BillCancellationReportDetail.xsc">
      <DependentUpon>BillCancellationReportDetail.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\BillCancellationReportDetail.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>BillCancellationReportDetail.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\BillCancellationReportDetail.xss">
      <DependentUpon>BillCancellationReportDetail.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\BillCancellationReportDetailDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>BillCancellationReportDetailDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\BillCancellationReportDetailDataSet.xss">
      <DependentUpon>BillCancellationReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\BillCancellationReportSummaryDataSet.xsc">
      <DependentUpon>BillCancellationReportSummaryDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\BillCancellationReportSummaryDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>BillCancellationReportSummaryDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\BillCancellationReportSummaryDataSet.xss">
      <DependentUpon>BillCancellationReportSummaryDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\CommercialCreditCardReportDetailDataSet.xsc">
      <DependentUpon>CommercialCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\BillCancellationReportSummeryDataSet.xsc">
      <DependentUpon>BillCancellationReportSummeryDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\BillCancellationReportSummeryDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>BillCancellationReportSummeryDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\BillCancellationReportSummeryDataSet.xss">
      <DependentUpon>BillCancellationReportSummeryDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\CommercialCreditCardReportDetailDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>CommercialCreditCardReportDetailDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\CommercialCreditCardReportDetailDataSet.xss">
      <DependentUpon>CommercialCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\CounterWiseSaleReportSummaryDataSet.xsc">
      <DependentUpon>CounterWiseSaleReportSummaryDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\CounterWiseSaleDetailReportDataSet.xsc">
      <DependentUpon>CounterWiseSaleDetailReportDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\CounterWiseSaleDetailReportDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>CounterWiseSaleDetailReportDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\CounterWiseSaleDetailReportDataSet.xss">
      <DependentUpon>CounterWiseSaleDetailReportDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\CounterWiseSaleReportSummaryDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>CounterWiseSaleReportSummaryDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\CounterWiseSaleReportSummaryDataSet.xss">
      <DependentUpon>CounterWiseSaleReportSummaryDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\DailySalesSummaryDataSet.xsc">
      <DependentUpon>DailySalesSummaryDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\CreditCardReportDetailDataSet.xsc">
      <DependentUpon>CreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\CreditCardReportDetailDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>CreditCardReportDetailDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\CreditCardReportDetailDataSet.xss">
      <DependentUpon>CreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\DailySalesSummaryDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DailySalesSummaryDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\DailySalesSummaryDataSet.xss">
      <DependentUpon>DailySalesSummaryDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\DelivaryReportDetailsDataSet.xsc">
      <DependentUpon>DelivaryReportDetailsDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\DelivaryReportDetailsDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DelivaryReportDetailsDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\DelivaryReportDetailsDataSet.xss">
      <DependentUpon>DelivaryReportDetailsDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\GRNListingDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>GRNListingDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\GRNListingDataSet.xss">
      <DependentUpon>GRNListingDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseCreditDetailsDataSet.xsc">
      <DependentUpon>InvoiceWiseCreditDetailsDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\HNBCreditCardReportDetailDataSet.xsc">
      <DependentUpon>HNBCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\HNBCreditCardReportDetailDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>HNBCreditCardReportDetailDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\HNBCreditCardReportDetailDataSet.xss">
      <DependentUpon>HNBCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\InvoiceWiseCreditDetailsDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>InvoiceWiseCreditDetailsDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\InvoiceWiseCreditDetailsDataSet.xss">
      <DependentUpon>InvoiceWiseCreditDetailsDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\LocationWiseSalesSummeryDataSet.xsc">
      <DependentUpon>LocationWiseSalesSummeryDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseSaleReportDetailsDataSet.xsc">
      <DependentUpon>InvoiceWiseSaleReportDetailsDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\InvoiceWiseSaleReportDetailsDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>InvoiceWiseSaleReportDetailsDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\InvoiceWiseSaleReportDetailsDataSet.xss">
      <DependentUpon>InvoiceWiseSaleReportDetailsDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\LocationWiseSalesSummeryDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>LocationWiseSalesSummeryDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\LocationWiseSalesSummeryDataSet.xss">
      <DependentUpon>LocationWiseSalesSummeryDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\menuDetailReportDataSet.xsd" />
    <Content Include="Reports\Nemsui\menuDetailReportDataSet.xss">
      <DependentUpon>menuDetailReportDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\POListingDataSet.xsc">
      <DependentUpon>POListingDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\MenuWiseSaleDataSet.xsc">
      <DependentUpon>MenuWiseSaleDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\MenuWiseSaleDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>MenuWiseSaleDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\MenuWiseSaleDataSet.xss">
      <DependentUpon>MenuWiseSaleDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\PickMeReportDetailDataSet.xsc">
      <DependentUpon>PickMeReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\NDBCreditCardReportDetailDataSet.xsc">
      <DependentUpon>NDBCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\MenuWiseSaleReportSummaryDataSet.xsc">
      <DependentUpon>MenuWiseSaleReportSummaryDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\MenuWiseSaleReportSummaryDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>MenuWiseSaleReportSummaryDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\MenuWiseSaleReportSummaryDataSet.xss">
      <DependentUpon>MenuWiseSaleReportSummaryDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\NDBCreditCardReportDetailDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>NDBCreditCardReportDetailDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\NDBCreditCardReportDetailDataSet.xss">
      <DependentUpon>NDBCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\PickMeReportDetailDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>PickMeReportDetailDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\PickMeReportDetailDataSet.xss">
      <DependentUpon>PickMeReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\POListingDataSet.xsd" />
    <Content Include="Reports\Nemsui\POListingDataSet.xss">
      <DependentUpon>POListingDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\rptAODReceipt_SalesServiceInvoice_Nemsu.cs.bak" />
    <Content Include="Reports\Nemsui\rptAODReceipt_StockTransfer_Nemsui.cs.bak" />
    <Content Include="Reports\Nemsui\rptGatePassAOD_Nemsui.cs.bak" />
    <Content Include="Reports\Nemsui\rptGatePassReceipt_SalesServiceInvoice_Nemsui.cs.bak" />
    <Content Include="Reports\Nemsui\rptGatePass_StockTransfer_Nemsui.cs.bak" />
    <Content Include="Reports\Nemsui\rptGRNDetailReport.cs.bak" />
    <Content Include="Reports\Nemsui\rptGRNSummaryReport.cs.bak" />
    <Content Include="Reports\Nemsui\rptInboundReceiptDetailReport.cs.bak" />
    <Content Include="Reports\Nemsui\rptIOUDetails_Nemsui.cs.bak" />
    <Content Include="Reports\Nemsui\rptOutboundPaymentDetailReport.cs.bak" />
    <Content Include="Reports\Nemsui\rptPaymentAdviceDetailReport.cs.bak" />
    <Content Include="Reports\Nemsui\rptReceiptAdviceDetailReport.cs.bak" />
    <Content Include="Reports\Nemsui\rptSalesInvoiceDetails_Nemsui.cs.bak" />
    <Content Include="Reports\Nemsui\StockBalanceDataSet.xsc">
      <DependentUpon>StockBalanceDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\SRNListingDataSet.xsc">
      <DependentUpon>SRNListingDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\SampathCreditCardReportDetailDataSet.xsc">
      <DependentUpon>SampathCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\ReceipeDetailReportDataSet.xsc">
      <DependentUpon>ReceipeDetailReportDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\ReceipeDetailReportDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>ReceipeDetailReportDataSet1.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\ReceipeDetailReportDataSet.xss">
      <DependentUpon>ReceipeDetailReportDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\SampathCreditCardReportDetailDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SampathCreditCardReportDetailDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\SampathCreditCardReportDetailDataSet.xss">
      <DependentUpon>SampathCreditCardReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\SRNListingDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SRNListingDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\SRNListingDataSet.xss">
      <DependentUpon>SRNListingDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\StockBalanceDataSet.xsd" />
    <Content Include="Reports\Nemsui\StockBalanceDataSet.xss">
      <DependentUpon>StockBalanceDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\stockValuationDataSet.xsc">
      <DependentUpon>stockValuationDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\stockValuationDataSet.xsd" />
    <Content Include="Reports\Nemsui\stockValuationDataSet.xss">
      <DependentUpon>stockValuationDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\stockVarianceDataSet.xsc">
      <DependentUpon>stockVarianceDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\stockVarianceDataSet.xsd" />
    <Content Include="Reports\Nemsui\stockVarianceDataSet.xss">
      <DependentUpon>stockVarianceDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\WastageDataSet.xsc">
      <DependentUpon>WastageDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Reports\Nemsui\UberReportDetailDataSet.xsc">
      <DependentUpon>UberReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\UberReportDetailDataSet.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>UberReportDetailDataSet.Designer.cs</LastGenOutput>
    </None>
    <Content Include="Reports\Nemsui\UberReportDetailDataSet.xss">
      <DependentUpon>UberReportDetailDataSet.xsd</DependentUpon>
    </Content>
    <None Include="Reports\Nemsui\WastageDataSet.xsd" />
    <Content Include="Reports\Nemsui\WastageDataSet.xss">
      <DependentUpon>WastageDataSet.xsd</DependentUpon>
    </Content>
    <None Include="scripts\jquery-3.3.1.intellisense.js" />
    <Content Include="scripts\jquery-3.3.1.js" />
    <Content Include="scripts\jquery-3.3.1.min.js" />
    <Content Include="scripts\jquery-3.3.1.slim.js" />
    <Content Include="scripts\jquery-3.3.1.slim.min.js" />
    <None Include="scripts\jquery.validate-vsdoc.js" />
    <Content Include="scripts\jquery.validate.js" />
    <Content Include="scripts\jquery.validate.min.js" />
    <Content Include="scripts\jquery.validate.unobtrusive.js" />
    <Content Include="scripts\jquery.validate.unobtrusive.min.js" />
    <Content Include="scripts\signature_pad.js" />
    <Content Include="scripts\signature_pad.min.js" />
    <Content Include="scripts\txnWorkFlowStatusLines.js" />
    <Content Include="SqlServerTypes\readme.htm" />
    <Content Include="SqlServerTypes\x64\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x64\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\msvcr120.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="SqlServerTypes\x86\SqlServerSpatial140.dll">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="ssrstemplates\ReceiptTemplate.aspx" />
    <Content Include="Views\Manufacturing\ProductionPlan\ManualPlan.cshtml" />
    <Content Include="Web.config" />
    <Content Include="Web.Debug.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Web.Release.config">
      <DependentUpon>Web.config</DependentUpon>
    </Content>
    <Content Include="Views\Web.config" />
    <Content Include="Views\_ViewStart.cshtml" />
    <Content Include="Views\Shared\Error.cshtml" />
    <Content Include="Views\Shared\_Layout.cshtml" />
    <Content Include="Views\Home\About.cshtml" />
    <Content Include="Views\Home\Contact.cshtml" />
    <Content Include="Views\Home\Index.cshtml" />
    <Content Include="Views\Inventory\Reports\IssueNoteReport.cshtml" />
    <Content Include="Views\Inventory\Reports\StockAdjustmentReport.cshtml" />
    <Content Include="StockAdjustmentDataSet.xsc">
      <DependentUpon>StockAdjustmentDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="scripts\jquery-3.3.1.slim.min.map" />
    <Content Include="scripts\jquery-3.3.1.min.map" />
    <Content Include="StockAdjustDataSet.xsc">
      <DependentUpon>StockAdjustDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="SPDailySalesInvoiceWiseDataSet.xsc">
      <DependentUpon>SPDailySalesInvoiceWiseDataSet.xsd</DependentUpon>
    </Content>
    <None Include="SPDailySalesInvoiceWiseDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SPDailySalesInvoiceWiseDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="SPDailySalesInvoiceWiseDataSet.xss">
      <DependentUpon>SPDailySalesInvoiceWiseDataSet.xsd</DependentUpon>
    </Content>
    <None Include="StockAdjustDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>StockAdjustDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="StockAdjustDataSet.xss">
      <DependentUpon>StockAdjustDataSet.xsd</DependentUpon>
    </Content>
    <None Include="StockAdjustmentDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>StockAdjustmentDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="StockAdjustmentDataSet.xss">
      <DependentUpon>StockAdjustmentDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Views\Inventory\Reports\POListingReport.cshtml" />
    <Content Include="Views\Inventory\Reports\WastageListingReport.cshtml" />
    <Content Include="Views\Inventory\MenuHeads\MenuHeads.cshtml" />
    <Content Include="Views\Inventory\MenuHeads\index.cshtml" />
    <Content Include="Views\Inventory\Reports\ReceipeDetailReport.cshtml" />
    <Content Include="Views\Inventory\Reports\MenuDetailReport.cshtml" />
    <Content Include="Views\Inventory\Reports\StockValuationReport.cshtml" />
    <Content Include="Views\Inventory\Reports\StockBalanceReport.cshtml" />
    <Content Include="Views\Inventory\Reports\StockVariance.cshtml" />
    <Content Include="Views\Sales\Reports\InvoiceWiseSalesSummary.cshtml" />
    <Content Include="Views\Sales\Reports\InvoiceWiseDiscountSummary.cshtml" />
    <Content Include="Views\Sales\Reports\InvoiceWiseCreditCardDetails.cshtml" />
    <Content Include="Views\Inventory\Reports\InvoiceWiseCreditDetails.cshtml" />
    <Content Include="Views\Inventory\Reports\MenuWiseSale.cshtml" />
    <Content Include="Views\Inventory\Reports\LocationWiseSalesSummary.cshtml" />
    <Content Include="Views\Inventory\Reports\SRNListingReport.cshtml" />
    <Content Include="Views\Sales\Reports\DailySalesSummaryReport.cshtml" />
    <Content Include="Views\Inventory\InternalOrders\PrintPreview.cshtml" />
    <Content Include="Views\Inventory\StockAdjustments\PrintPreview.cshtml" />
    <Content Include="Views\Inventory\Reports\CounterWiseSaleReportDetails.cshtml" />
    <Content Include="Views\Inventory\Reports\InvoiceWiseSaleReportDetails.cshtml" />
    <Content Include="Views\Inventory\Reports\MenuWiseSaleReportSummary.cshtml" />
    <Content Include="Views\Sales\Reports\CreditCardReportDetails.cshtml" />
    <Content Include="Views\Sales\Reports\DeliveryReportDetails.cshtml" />
    <Content Include="Views\Inventory\ProductionNotes\Index.cshtml" />
    <Content Include="Views\Inventory\ProductionNotes\ProductionNoteForm.cshtml" />
    <Content Include="Views\Sales\Reports\DeliveryReportDetail.cshtml" />
    <Content Include="Views\Sales\Reports\DailySalesInvoiceWiseReport.cshtml" />
    <Content Include="Views\Sales\Reports\CounterWiseSaleSummary.cshtml" />
    <Content Include="Views\Sales\Reports\DiscountDetailsReport.cshtml" />
    <Content Include="Views\Inventory\Reports\DailyCancellationSummeryReport.cshtml" />
    <Content Include="Views\Inventory\Reports\BillCancellationSummeryReport.cshtml" />
    <Content Include="Views\Inventory\Reports\BillCancellationDetailReport.cshtml" />
    <Content Include="Views\Sales\Reports\DeliveryReportDetails.cshtml" />
    <Content Include="Views\Inventory\Reports\StockCard.cshtml" />
    <Content Include="Views\Inventory\Reports\GRNListingCentrally.cshtml" />
    <Content Include="Views\Sales\Reports\CounterWiseSaleSummaryCentrally.cshtml" />
    <Content Include="Views\Sales\BankPromotion\index.cshtml" />
    <Content Include="Views\Inventory\goodissuenotes\GoodIssueNoteForm.cshtml" />
    <Content Include="Views\Inventory\goodissuenotes\Index.cshtml" />
    <Content Include="Views\Inventory\MonthEnd\MonthEnd.cshtml" />
    <Content Include="Views\Inventory\MenuHeads\PrintPreview.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="App_Data\" />
    <Folder Include="assets\archiveDocs\" />
    <Folder Include="assets\companyLogo\" />
    <Folder Include="assets\plugins\general\ion-rangeslider\img\" />
    <Folder Include="assets\transRefDocs\" />
    <Folder Include="Reports\Common\" />
    <Folder Include="SSRSTempReports\" />
    <Folder Include="transRefDocs\" />
    <Folder Include="uploadfiles\" />
    <Folder Include="Views\HOReturnStockTransfer\" />
    <Folder Include="Views\Inventory\Palatalizing\" />
    <Folder Include="Views\Inventory\SemiFinishedGoodsQCScannings\" />
    <Folder Include="Views\Sales\NewFolder1\" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-brands-400.eot" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-brands-400.ttf" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-brands-400.woff" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-brands-400.woff2" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-regular-400.eot" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-regular-400.ttf" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-regular-400.woff" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-regular-400.woff2" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-solid-900.eot" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-solid-900.ttf" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-solid-900.woff" />
    <Content Include="assets\plugins\general\fortawesome\fontawesome-free\webfonts\fa-solid-900.woff2" />
    <Content Include="assets\plugins\general\plugins\flaticon2\font\Flaticon2.eot" />
    <Content Include="assets\plugins\general\plugins\flaticon2\font\Flaticon2.ttf" />
    <Content Include="assets\plugins\general\plugins\flaticon2\font\Flaticon2.woff" />
    <Content Include="assets\plugins\general\plugins\flaticon2\font\Flaticon2.woff2" />
    <Content Include="assets\plugins\general\plugins\flaticon\font\Flaticon.eot" />
    <Content Include="assets\plugins\general\plugins\flaticon\font\Flaticon.ttf" />
    <Content Include="assets\plugins\general\plugins\flaticon\font\Flaticon.woff" />
    <Content Include="assets\plugins\general\plugins\line-awesome\fonts\line-awesome.eot" />
    <Content Include="assets\plugins\general\plugins\line-awesome\fonts\line-awesome.ttf" />
    <Content Include="assets\plugins\general\plugins\line-awesome\fonts\line-awesome.woff" />
    <Content Include="assets\plugins\general\plugins\line-awesome\fonts\line-awesome.woff2" />
    <Content Include="assets\plugins\general\socicon\font\socicon.eot" />
    <Content Include="assets\plugins\general\socicon\font\socicon.ttf" />
    <Content Include="assets\plugins\general\socicon\font\socicon.woff" />
    <Content Include="assets\plugins\general\summernote\dist\font\summernote.eot" />
    <Content Include="assets\plugins\general\summernote\dist\font\summernote.ttf" />
    <Content Include="assets\plugins\general\summernote\dist\font\summernote.woff" />
    <Content Include="NemsuERPDataSet2.xsc">
      <DependentUpon>NemsuERPDataSet2.xsd</DependentUpon>
    </Content>
    <None Include="NemsuERPDataSet2.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>NemsuERPDataSet2.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="NemsuERPDataSet2.xss">
      <DependentUpon>NemsuERPDataSet2.xsd</DependentUpon>
    </Content>
    <Content Include="NemsuERPProductDataSet.xsc">
      <DependentUpon>NemsuERPProductDataSet.xsd</DependentUpon>
    </Content>
    <None Include="NemsuERPProductDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>NemsuERPProductDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="NemsuERPProductDataSet.xss">
      <DependentUpon>NemsuERPProductDataSet.xsd</DependentUpon>
    </Content>
    <None Include="packages.config" />
    <Content Include="Views\Account\Login.cshtml" />
    <Content Include="Views\Administration\Currency\Index.cshtml" />
    <Content Include="Views\Administration\Organization\Index.cshtml" />
    <None Include="Properties\PublishProfiles\Default.pubxml" />
    <None Include="Properties\PublishProfiles\FolderProfile.pubxml" />
    <Content Include="Views\Administration\Users\Index.cshtml" />
    <Content Include="Views\Administration\Users\UserForm.cshtml" />
    <Content Include="Views\Common\SupportData\Index.cshtml" />
    <Content Include="Views\Common\SupportData\CreditDays.cshtml" />
    <Content Include="Views\Inventory\Warehouses\Index.cshtml" />
    <Content Include="Views\Procurement\PurchaseReqNotes\PRNForm.cshtml" />
    <Content Include="Views\Inventory\ProductHierarchy\Index.cshtml" />
    <Content Include="Views\Procurement\PurchaseReqNotes\Index.cshtml" />
    <Content Include="Views\Procurement\SupplementaryManufacturers\Index.cshtml" />
    <Content Include="Views\Procurement\Suppliers\Index.cshtml" />
    <Content Include="Views\Procurement\Suppliers\SupplierForm.cshtml" />
    <Content Include="Views\Administration\Users\OperationalUnitMapping.cshtml" />
    <Content Include="Views\Inventory\UnitOfMeasure\Index.cshtml" />
    <Content Include="Views\Inventory\Products\ProductForm.cshtml" />
    <Content Include="Views\Inventory\Products\Index.cshtml" />
    <Content Include="Views\Administration\Users\All.cshtml" />
    <Content Include="Views\Procurement\PurchaseDesk\Index.cshtml" />
    <Content Include="Views\Procurement\Quotations\Index.cshtml" />
    <Content Include="Views\Procurement\PurchaseOrders\Index.cshtml" />
    <Content Include="Views\HR\Employees\Index.cshtml" />
    <Content Include="Views\HR\Employees\EmployeeForm.cshtml" />
    <Content Include="Views\Common\SupportData\TxnReasons.cshtml" />
    <Content Include="Views\Procurement\Quotations\QuotationForm.cshtml" />
    <Content Include="Views\Administration\Authorization\Roles.cshtml" />
    <Content Include="Views\Administration\Authorization\Groups.cshtml" />
    <Content Include="Views\Administration\Authorization\GroupRoleMapping.cshtml" />
    <Content Include="Views\Administration\Authorization\UserGroupMapping.cshtml" />
    <Content Include="Views\Administration\Authorization\UserRoleMapping.cshtml" />
    <Content Include="Views\Administration\Authorization\Permissions.cshtml" />
    <Content Include="Views\Procurement\ServiceOrders\Index.cshtml" />
    <Content Include="Views\Procurement\PurchaseOrders\PurchaseOrderForm.cshtml" />
    <Content Include="Views\Sales\SalesOrders\Index.cshtml" />
    <Content Include="Views\Sales\SalesOrders\SalesOrderForm.cshtml" />
    <Content Include="Views\Inventory\StockTransfer\StockTransferForm.cshtml" />
    <Content Include="Views\Procurement\Quotations\ServiceQuotationForm.cshtml" />
    <Content Include="Views\Sales\SalesDesk\Index.cshtml" />
    <Content Include="Views\Procurement\ServiceOrders\ServiceOrderForm.cshtml" />
    <Content Include="Views\Procurement\ExchangeOrders\ExchangeOrderForm.cshtml" />
    <Content Include="Views\Procurement\ServiceInvoices\Index.cshtml" />
    <Content Include="Views\Procurement\ServiceInvoices\ServiceInvoiceForm.cshtml" />
    <Content Include="Views\Procurement\SubContractOrders\InboundSubContractOrders.cshtml" />
    <Content Include="Views\Procurement\SubContractOrders\OutboundSubContractOrderForm.cshtml" />
    <Content Include="Views\Inventory\StockTransfer\Index.cshtml" />
    <Content Include="Views\Inventory\StockTakes\Index.cshtml" />
    <Content Include="Views\Inventory\StockTakes\StockTakeForm.cshtml" />
    <Content Include="Views\Procurement\ExchangeOrders\Index.cshtml" />
    <Content Include="Views\Inventory\TyreSpecifications\Index.cshtml" />
    <Content Include="Views\Inventory\GRNS\GRNForm.cshtml" />
    <Content Include="Views\Inventory\GRNS\Index.cshtml" />
    <Content Include="Views\Inventory\ShipmentQualityControl\Index.cshtml" />
    <Content Include="Views\Inventory\ShipmentQualityControl\ShipmentQCForm.cshtml" />
    <Content Include="Views\Procurement\InboundLoan\InboundLoanForm.cshtml" />
    <Content Include="Views\Sales\SalesInquiries\Index.cshtml" />
    <Content Include="Views\Sales\SalesInquiries\SalesInquiryForm.cshtml" />
    <Content Include="Views\Procurement\InternalServiceInvoices\Index.cshtml" />
    <Content Include="Views\Procurement\InternalServiceInvoices\InternalServiceInvoiceForm.cshtml" />
    <Content Include="Views\Finance\Accounts\Index.cshtml" />
    <Content Include="Views\Procurement\BankAccounts\Index.cshtml" />
    <Content Include="Views\Sales\Customers\Index.cshtml" />
    <Content Include="Views\Sales\Customers\CustomerForm.cshtml" />
    <Content Include="Views\Inventory\MRNs\Index.cshtml" />
    <Content Include="Views\Inventory\MRNs\MRNForm.cshtml" />
    <Content Include="Views\Procurement\InboundLoan\Index.cshtml" />
    <Content Include="Views\Procurement\OutboundLoan\OutboundLoanForm.cshtml" />
    <Content Include="Views\Procurement\OutboundLoan\Index.cshtml" />
    <Content Include="Views\Procurement\PurchaseReturns\Index.cshtml" />
    <Content Include="Views\Procurement\PurchaseReturns\PurchaseReturnForm.cshtml" />
    <Content Include="Views\Sales\PerformaInvoice\PerformaInvoiceForm.cshtml" />
    <Content Include="Views\Inventory\InternalOrders\Index.cshtml" />
    <Content Include="Views\Inventory\InternalOrders\InternalOrderForm.cshtml" />
    <Content Include="Views\Sales\PackingLists\Index.cshtml" />
    <Content Include="Views\Sales\PackingLists\PackingListForm.cshtml" />
    <Content Include="Views\Inventory\GoodsDispatchNotes\Index.cshtml" />
    <Content Include="Views\Inventory\InternalReturns\Index.cshtml" />
    <Content Include="Views\Inventory\InternalReturns\InternalReturnForm.cshtml" />
    <Content Include="Views\Inventory\GoodsDispatchNotes\GoodsDispatchNoteForm.cshtml" />
    <Content Include="Views\Sales\LoadingPlans\LoadingPlanForm.cshtml" />
    <Content Include="Views\Sales\LoadingPlans\Index.cshtml" />
    <Content Include="Views\Inventory\GoodsDispatchNotes\GatePassReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\StockAdjustments\Index.cshtml" />
    <Content Include="Views\Inventory\StockAdjustments\StockAdjustmentForm.cshtml" />
    <Content Include="Views\Inventory\InternalDispatches\Index.cshtml" />
    <Content Include="Views\Inventory\InternalDispatches\InternalDispatchForm.cshtml" />
    <Content Include="Views\Sales\CommercialInvoices\Index.cshtml" />
    <Content Include="Views\Sales\CommercialInvoices\CommercialInvoiceForm.cshtml" />
    <Content Include="Views\Sales\PerformaInvoice\Index.cshtml" />
    <Content Include="Views\Sales\SalesReturns\SalesReturnForm.cshtml" />
    <Content Include="Views\Sales\SalesReturns\Index.cshtml" />
    <Content Include="Views\Inventory\ShipmentQualityControlReturns\Index.cshtml" />
    <Content Include="Views\Inventory\ShipmentQualityControlReturns\ShipmentQCReturnForm.cshtml" />
    <Content Include="Views\Sales\SalesInvoice\Index.cshtml" />
    <Content Include="Views\Sales\SalesInvoice\SalesInvoiceForm.cshtml" />
    <Content Include="Views\Inventory\StockTransferReceipts\Index.cshtml" />
    <Content Include="Views\Inventory\StockTransferReceipts\StockTransferReceiptForm.cshtml" />
    <Content Include="Views\Finance\ChartOfAccounts\Index.cshtml" />
    <Content Include="Views\Inventory\StockAllocations\Index.cshtml" />
    <Content Include="Views\Finance\TaxTypes\Index.cshtml" />
    <Content Include="Views\Finance\ChargeInformations\Index.cshtml" />
    <Content Include="Views\Finance\CurrencyExchangeRates\Index.cshtml" />
    <Content Include="Views\Finance\TaxGroups\Index.cshtml" />
    <Content Include="Views\Sales\ServiceInvoices\ServiceInvoiceForm.cshtml" />
    <Content Include="Views\Sales\ServiceInvoices\Index.cshtml" />
    <Content Include="Views\Finance\Accounts\AccountForm.cshtml" />
    <Content Include="Views\Finance\BankAdjustments\Index.cshtml" />
    <Content Include="Views\Finance\BankAdjustments\BankAdjustmentForm.cshtml" />
    <Content Include="Views\Finance\ManualJournals\Index.cshtml" />
    <Content Include="Views\Finance\ManualJournals\ManualJournalForm.cshtml" />
    <Content Include="Views\Finance\PayBooks\Index.cshtml" />
    <Content Include="Views\Finance\PaymentAdvices\PaymentAdviceForm.cshtml" />
    <Content Include="Views\Finance\PaymentAdvices\Index.cshtml" />
    <Content Include="Views\Finance\ReceiptAdvices\Index.cshtml" />
    <Content Include="Views\Finance\ReceiptAdvices\ReceiptAdviceForm.cshtml" />
    <Content Include="Views\Finance\OutboundPayments\Index.cshtml" />
    <Content Include="Views\Finance\OutboundPayments\SupplierPaymentVoucherForm.cshtml" />
    <Content Include="Views\Finance\OutboundPayments\OutboundPaymentForm.cshtml" />
    <Content Include="Views\Finance\FinancialStatements\Index.cshtml" />
    <Content Include="Views\Finance\FinancialStatements\FinancialStatementForm.cshtml" />
    <Content Include="Views\Administration\PasswordPolicies\PasswordPolicyForm.cshtml" />
    <Content Include="Views\Common\SupportData\OverheadInformations.cshtml" />
    <Content Include="Views\Manufacturing\Moulds\Index.cshtml" />
    <Content Include="Views\Manufacturing\Machines\Index.cshtml" />
    <Content Include="Views\Manufacturing\Machines\MachineForm.cshtml" />
    <Content Include="Views\Sales\PriceLists\Index.cshtml" />
    <Content Include="Views\Administration\AgingBuckets\Index.cshtml" />
    <Content Include="Views\Finance\InboundReceipts\Index.cshtml" />
    <Content Include="Views\Finance\InboundReceipts\InboundReceiptForm.cshtml" />
    <Content Include="Views\Administration\EmailTemplates\Index.cshtml" />
    <Content Include="Views\Finance\PettyCashOperations\Index.cshtml" />
    <Content Include="Views\Finance\PettyCashOperations\PettyCashForm.cshtml" />
    <Content Include="Views\HR\WorkstationCadres\Index.cshtml" />
    <Content Include="Views\Manufacturing\MouldProducts\Index.cshtml" />
    <Content Include="Views\Manufacturing\MachineMoulds\Index.cshtml" />
    <Content Include="Views\Manufacturing\BillOfMaterials\Index.cshtml" />
    <Content Include="Views\Manufacturing\BillOfMaterials\BillOfMaterialForm.cshtml" />
    <Content Include="Views\Common\WorkFlows\WorkFlowForm.cshtml" />
    <Content Include="Views\Inventory\ShipmentCosts\Index.cshtml" />
    <Content Include="Views\Inventory\ShipmentCosts\ShipmentCostForm.cshtml" />
    <Content Include="Views\Common\WorkFlows\Index.cshtml" />
    <Content Include="Views\Manufacturing\BillOfOperations\BillOfOperationForm.cshtml" />
    <Content Include="Views\Manufacturing\BillOfOperations\Index.cshtml" />
    <Content Include="Views\Procurement\PurchaseOrders\Approval.cshtml" />
    <Content Include="Views\Shared\_TxnWorkFlowStatusLines.cshtml" />
    <Content Include="Views\Procurement\Quotations\Approval.cshtml" />
    <Content Include="Views\Manufacturing\ProductionPlan\Index.cshtml" />
    <Content Include="Views\Procurement\ServiceOrders\Approval.cshtml" />
    <Content Include="Views\Inventory\StockAdjustments\Approval.cshtml" />
    <Content Include="Views\Inventory\StockTakes\Approval.cshtml" />
    <Content Include="Views\Procurement\ExchangeOrders\Approval.cshtml" />
    <Content Include="Views\Inventory\GoodsDispatchNotes\Approval.cshtml" />
    <Content Include="Views\Manufacturing\MachineVisualPlans\Index.cshtml" />
    <Content Include="Views\Manufacturing\MachineVisualPlans\MachineVisualPlan.cshtml" />
    <Content Include="Views\Common\SupportData\OtherActivities.cshtml" />
    <Content Include="Views\Sales\PerformaInvoice\Desk.cshtml" />
    <Content Include="Views\Inventory\QCParameters\Index.cshtml" />
    <Content Include="Views\Inventory\QCParameterMappings\Index.cshtml" />
    <Content Include="Views\HR\AttendenceAllowance\Index.cshtml" />
    <Content Include="Views\HR\HRSettings\Index.cshtml" />
    <Content Include="Views\Manufacturing\DailyProductionPlan\Index.cshtml" />
    <Content Include="Views\Manufacturing\SemiFinishedGoodsProductionPlan\Index.cshtml" />
    <Content Include="Views\Manufacturing\MaterialRequisitionPlanning\Index.cshtml" />
    <Content Include="Views\FixedAssets\Index.cshtml" />
    <Content Include="Views\FixedAssets\FixedAssetForm.cshtml" />
    <Content Include="Views\Manufacturing\ProductionOrders\Index.cshtml" />
    <Content Include="Views\Finance\AccountMappings\Index.cshtml" />
    <Content Include="Views\Finance\AccountMappings\AccountMappingForm.cshtml" />
    <Content Include="Views\Procurement\SubContractOrders\InboundSubContractOrderForm.cshtml" />
    <Content Include="Views\HR\BaseIncentiveRate\Index.cshtml" />
    <Content Include="Views\HR\ScrapPenaltyDeductionRates\Index.cshtml" />
    <Content Include="Views\HR\IncentiveApportionmentRates\Index.cshtml" />
    <Content Include="Views\Manufacturing\TouchScreeens\FGQualityControlTC01.cshtml" />
    <Content Include="Views\Procurement\SubContractOrders\OutboundSubContractOrders.cshtml" />
    <Content Include="Views\Inventory\ProductLicenceRegistration\Index.cshtml" />
    <Content Include="Views\Manufacturing\TouchScreeens\FGQualityControlTC02.cshtml" />
    <Content Include="Views\Sales\ServiceOrders\Index.cshtml" />
    <Content Include="Views\Sales\ServiceOrders\ServiceOrderForm.cshtml" />
    <Content Include="Views\Manufacturing\ProductionOrders\SemiFinishedGoods.cshtml" />
    <Content Include="Views\Sales\ServiceInquiries\Index.cshtml" />
    <Content Include="Views\Sales\ServiceInquiries\ServiceInquiryForm.cshtml" />
    <Content Include="Views\Sales\LoadingBays\Index.cshtml" />
    <Content Include="Views\Sales\ServiceOrderDesk\Index.cshtml" />
    <Content Include="Views\Sales\ServiceOrderJob\ServiceOrderJobForm.cshtml" />
    <Content Include="Views\Sales\ServiceInquiries\PrintPreview.cshtml" />
    <Content Include="Views\Finance\GeneralLedgerLines\Index.cshtml" />
    <Content Include="ReportStorage\ReportData.xsc">
      <DependentUpon>ReportData.xsd</DependentUpon>
    </Content>
    <None Include="Properties\PublishProfiles\MITLERP - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\MITLERP - ReadOnly - FTP.pubxml" />
    <None Include="Properties\PublishProfiles\MITLERP - Web Deploy.pubxml" />
    <Content Include="PurchaseReturns.xsc">
      <DependentUpon>PurchaseReturns.xsd</DependentUpon>
    </Content>
    <Content Include="PurchaseReturnLines.xsc">
      <DependentUpon>PurchaseReturnLines.xsd</DependentUpon>
    </Content>
    <None Include="PurchaseReturnLines.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>PurchaseReturnLines.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="PurchaseReturnLines.xss">
      <DependentUpon>PurchaseReturnLines.xsd</DependentUpon>
    </Content>
    <None Include="PurchaseReturns.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>PurchaseReturns.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="PurchaseReturns.xss">
      <DependentUpon>PurchaseReturns.xsd</DependentUpon>
    </Content>
    <None Include="ReportStorage\ReportData.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>ReportData.Designer.cs</LastGenOutput>
    </None>
    <Content Include="ReportStorage\ReportData.xss">
      <DependentUpon>ReportData.xsd</DependentUpon>
    </Content>
    <Content Include="Views\Administration\ReportDesigner\Index.cshtml" />
    <Content Include="Views\Sales\ServiceOrderJob\Index.cshtml" />
    <Content Include="Views\Sales\SalesInvoiceWalkIn\Index.cshtml" />
    <Content Include="Views\Sales\SalesInvoiceWalkIn\SalesInvoiceWalkInForm.cshtml" />
    <Content Include="Views\Sales\SalesCashier\Index.cshtml" />
    <Content Include="Views\HR\Attendences\Index.cshtml" />
    <Content Include="Views\HR\Attendences\AttendenceUploadForm.cshtml" />
    <Content Include="Views\Sales\FreeIssues\Index.cshtml" />
    <Content Include="Views\Sales\FreeIssues\FreeIssuesForm.cshtml" />
    <Content Include="Views\Manufacturing\TouchScreeens\CompoundIssuingTC.cshtml" />
    <Content Include="Views\Sales\ServiceOrderJob\Approval.cshtml" />
    <Content Include="Views\Administration\ReportDesigner\Viewer.cshtml" />
    <Content Include="Views\Sales\DiscountSchemes\DiscountSchemesForm.cshtml" />
    <Content Include="Views\Sales\DiscountSchemes\Index.cshtml" />
    <Content Include="Views\Administration\AppSettings\Index.cshtml" />
    <Content Include="Views\Inventory\StockCountSheets\Index.cshtml" />
    <Content Include="Views\Inventory\StockCountSheets\StockCountSheetsForm.cshtml" />
    <Content Include="Views\Manufacturing\ProductDies\Index.cshtml" />
    <Content Include="Views\Sales\ConsignmentSalesInvoice\Index.cshtml" />
    <Content Include="Views\Sales\ConsignmentSalesInvoice\ConsignmentSalesInvoiceForm.cshtml" />
    <Content Include="Views\Procurement\PurchaseOrders\PrintPreview.cshtml" />
    <Content Include="Views\Procurement\Reports\Quotations.cshtml" />
    <Content Include="Views\Inventory\GoodsDispatchNotes\AODReceiptTemplate.cshtml" />
    <Content Include="Views\Finance\PettyCashOperations\ReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\StockTransfer\GatePassReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\StockTransfer\AODReceiptTemplate.cshtml" />
    <Content Include="Views\Finance\OutboundPayments\ReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\GRNS\ReceiptTemplate.cshtml" />
    <Content Include="Views\Finance\InboundReceipts\PrintPreview.cshtml" />
    <Content Include="Views\Procurement\ServiceOrders\ReceiptTemplate.cshtml" />
    <Content Include="Views\Procurement\ServiceInvoices\ReceiptTemplate.cshtml" />
    <Content Include="Views\Finance\PettyCashOperations\PettyCashReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\LoadingBays\PrintPreview.cshtml" />
    <Content Include="Views\Inventory\InventoryReports\StockDetailReport.cshtml" />
    <Content Include="Views\Inventory\BundleProducts\Index.cshtml" />
    <Content Include="Views\Inventory\BundleProducts\BundleProductForm.cshtml" />
    <Content Include="Views\Inventory\ProductBrandDiscount\Index.cshtml" />
    <Content Include="Views\Inventory\GoodsDispatchNotes\GatePassAODReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\GoodsDispatchNotes\AODReceiptNewTemplate.cshtml" />
    <Content Include="Views\Procurement\PurchaseOrders\PrintFinalReceiptPreview.cshtml" />
    <Content Include="Views\Sales\PerformaInvoice\ReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\InternalDispatches\Approval.cshtml" />
    <Content Include="Views\Procurement\ServiceOrders\GatePassTemplate.cshtml" />
    <Content Include="Views\Procurement\ServiceOrders\AODTemplate.cshtml" />
    <Content Include="Views\Finance\PaymentAdvices\ReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\CommercialInvoices\ReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\SalesInvoice\ReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\LoadingPlans\ReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\PackingLists\ReceiptTemplate.cshtml" />
    <Content Include="Views\Procurement\PurchaseReturns\PrintPreview.cshtml" />
    <Content Include="Views\Sales\CustomerPayments\CustomerPaymentForm.cshtml" />
    <Content Include="Views\Sales\CustomerPayments\Index.cshtml" />
    <Content Include="Views\Sales\Promotion\Index.cshtml" />
    <Content Include="Views\Sales\Promotion\PromotionForm.cshtml" />
    <Content Include="Views\Manufacturing\ProductionOrders\BarcodePrintPreview.cshtml" />
    <Content Include="Views\Sales\ServiceInvoices\GatePassReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\ServiceInvoices\AODReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\PromotionDesk\Index.cshtml" />
    <Content Include="Views\Sales\ServiceInvoices\ReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\SalesInvoiceWalkIn\ReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\GRNS\BarcodesPreview.cshtml" />
    <Content Include="Views\Sales\CommercialInvoices\BillingInstructionPrint.cshtml" />
    <Content Include="Views\Sales\SalesInvoice\BillingInstructionPrint.cshtml" />
    <Content Include="Views\Finance\BankReconciliations\BankReconciliationForm.cshtml" />
    <Content Include="Views\Finance\BankReconciliations\Index.cshtml" />
    <Content Include="Views\Sales\CommercialInvoices\FumigationPrint.cshtml" />
    <Content Include="Views\Inventory\StockCountSheets\ReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\ConsignmentSalesInvoice\ReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\StockTransferReceipts\ReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\CommercialInvoices\VGMDocumentPrint.cshtml" />
    <Content Include="Views\Sales\SalesInvoice\VGMDocumentPrint.cshtml" />
    <Content Include="Views\Finance\FinancialStatements\FinancialStatementReport.cshtml" />
    <Content Include="Views\Finance\FinancialStatements\_FinStatementTreeListPartial.cshtml" />
    <Content Include="Views\Procurement\PurchaseOrders\PrintPurchaseOrder_SesdermaLaboratories.cshtml" />
    <Content Include="Views\Procurement\PurchaseOrders\PrintPurchaseOrder_SimahAsia.cshtml" />
    <Content Include="Views\Sales\SalesOrders\SalesOrderUploadForm.cshtml" />
    <Content Include="Views\Sales\SalesInvoice\VATReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\Reports\SalesOrderDetails.cshtml" />
    <Content Include="Views\Finance\Reports\SupplierAgingDetails.cshtml" />
    <Content Include="Views\Finance\Reports\CustomerLedger.cshtml" />
    <Content Include="Views\Finance\Reports\SupplierLedger.cshtml" />
    <Content Include="Views\Sales\SalesOrders\ReceiptTemplate.cshtml" />
    <Content Include="Views\Finance\Reports\CustomerAgingDetails.cshtml" />
    <Content Include="Views\Inventory\Reports\StockMovement.cshtml" />
    <Content Include="Views\Finance\Reports\SupplierAgingSummary.cshtml" />
    <Content Include="Views\Finance\Reports\CustomerAgingSummary.cshtml" />
    <Content Include="Views\Finance\Reports\GLDetails.cshtml" />
    <Content Include="Views\Finance\ReceiptAdvices\ReceiptTemplate.cshtml" />
    <Content Include="Views\Inventory\GRNS\Approval.cshtml" />
    <Content Include="Views\Finance\OutboundPayments\ChequeTemplate.cshtml" />
    <Content Include="Views\Sales\SalesOrders\Approval.cshtml" />
    <Content Include="Views\Inventory\GoodsDispatchNotes\ReceiptTemplate.cshtml" />
    <Content Include="Views\Finance\DataDumps\GLDetails.cshtml" />
    <Content Include="Views\Administration\ReportDesigner\ViewerWithModel.cshtml" />
    <Content Include="Views\Sales\DataDumps\LoadingPlanDetails.cshtml" />
    <Content Include="Views\Sales\DataDumps\SalesInvoiceDetails.cshtml" />
    <Content Include="Views\Sales\DataDumps\CommercialInvoiceDetails.cshtml" />
    <Content Include="Views\Sales\DataDumps\CommercialInvoiceSummary.cshtml" />
    <Content Include="Views\Sales\DataDumps\PerformaInvoiceDetails.cshtml" />
    <Content Include="Views\Sales\DataDumps\PerformaInvoiceSummary.cshtml" />
    <Content Include="Views\Finance\Reports\BankReconciliationSummary.cshtml" />
    <Content Include="Views\Finance\Reports\VatAndSVat.cshtml" />
    <Content Include="Views\Inventory\RawMaterialPlannings\Index.cshtml" />
    <Content Include="Views\Inventory\RawMaterialPlannings\RawMaterialPlanningForm.cshtml" />
    <Content Include="Views\Inventory\Reports\StockReconciliationDetail.cshtml" />
    <Content Include="Views\Manufacturing\Reports\CompoundStockReconciliationDetails.cshtml" />
    <Content Include="Views\Manufacturing\Reports\DailyProductionPlanning.cshtml" />
    <Content Include="Views\Finance\Reports\BankReconciliationDetail.cshtml" />
    <Content Include="Views\Procurement\Reports\PendingPOStatus.cshtml" />
    <Content Include="Views\Inventory\Reports\RawMaterialPlanning.cshtml" />
    <Content Include="Views\Inventory\Reports\StockMovementSummary.cshtml" />
    <Content Include="Views\Inventory\Reports\StockSummary.cshtml" />
    <Content Include="Views\Manufacturing\Reports\DailyProduction.cshtml" />
    <Content Include="Views\Manufacturing\Reports\MonthlyProduction.cshtml" />
    <Content Include="Views\Manufacturing\Reports\ChemicalAndCompoundStockReconciliationSummary.cshtml" />
    <Content Include="Views\Manufacturing\Reports\WIPStock.cshtml" />
    <Content Include="Views\Manufacturing\Reports\StockReconciliationSummary.cshtml" />
    <Content Include="Views\Manufacturing\Reports\MonthlyQualitySummary.cshtml" />
    <Content Include="Views\Manufacturing\Reports\MonthlyQualityDetail.cshtml" />
    <Content Include="Views\Sales\Reports\SalesSummary.cshtml" />
    <Content Include="Views\Inventory\Reports\GRNDetailReport_Sothys.cshtml" />
    <Content Include="Views\Sales\SalesReturns\ReceiptTemplate.cshtml" />
    <Content Include="Views\Finance\Reports\TrialBalanceReport_Sothys.cshtml" />
    <Content Include="Views\Inventory\Reports\StockValuation.cshtml" />
    <Content Include="Views\Sales\Reports\SalesServiceInvoice.cshtml" />
    <Content Include="Views\Sales\Reports\TurnoverMargin.cshtml" />
    <Content Include="Views\Sales\Reports\TurnoverMarginService.cshtml" />
    <Content Include="Views\Sales\Reports\CostofSales_Sothys.cshtml" />
    <Content Include="Views\Sales\Reports\SalesInvoiceWalkInDetail.cshtml" />
    <Content Include="Views\Sales\Reports\SalesInvoiceWalkInSummary.cshtml" />
    <Content Include="Views\Sales\Reports\TotalTurnover.cshtml" />
    <Content Include="Views\Sales\Reports\CreditSalesSummary.cshtml" />
    <Content Include="Views\Inventory\ConsignmentStockTransfer\ConsignmentStockTransferForm.cshtml" />
    <Content Include="Views\Inventory\ConsignmentStockTransfer\Index.cshtml" />
    <Content Include="Views\Procurement\Reports\PurchaseOrder_Sothys.cshtml" />
    <Content Include="Views\Procurement\Reports\PurchaseOrderSummary_Sothys.cshtml" />
    <Content Include="Views\Sales\Reports\CreditSalesDetail.cshtml" />
    <Content Include="Views\Sales\Reports\SalesServiceInvoiceSummary.cshtml" />
    <Content Include="Views\Inventory\Reports\StockTransfer.cshtml" />
    <Content Include="Views\Sales\Reports\CustomerLoyality_Sothys.cshtml" />
    <Content Include="Views\Sales\Reports\JobCommissionDetail.cshtml" />
    <Content Include="Views\Sales\Reports\SalesCommissionDetail.cshtml" />
    <Content Include="Views\Sales\SalesReturns\ConsignmentIndex.cshtml" />
    <Content Include="Views\Sales\Reports\JobCostBreakout_Sothys.cshtml" />
    <Content Include="Views\Sales\Reports\ConsignmentSalesInvoiceDetail.cshtml" />
    <Content Include="Views\Inventory\Reports\GRNSummaryReport_Sothys.cshtml" />
    <Content Include="Views\Sales\Reports\ConsignmentSalesInvoiceSummary.cshtml" />
    <Content Include="Views\Inventory\HOReturnStockTransfer\HOReturnStockTransferForm.cshtml" />
    <Content Include="Views\Inventory\HOReturnStockTransfer\Index.cshtml" />
    <Content Include="Views\Sales\Reports\CustomerPaymentDetailReport_Sothys.cshtml" />
    <Content Include="Views\Sales\CustomerPayments\ReceiptTemplate.cshtml" />
    <Content Include="Views\Sales\Reports\StockMovementReport.cshtml" />
    <Content Include="Views\Inventory\StockTransferFIFO\Index.cshtml" />
    <Content Include="Views\Inventory\StockTransferFIFO\StockTransferForm.cshtml" />
    <Content Include="Views\Sales\SalesReturns\PrintTemplate.cshtml" />
    <Content Include="Views\Sales\Reports\CustomerRetentionReport.cshtml" />
    <Content Include="Views\Administration\ReportDesigner\ReceiptViewerWithModel.cshtml" />
    <Content Include="Views\Sales\Reports\SalesInvoiceDetail.cshtml" />
    <Content Include="Views\Sales\Reports\CostOfSalesServiceInvoice_Sothys.cshtml" />
    <Content Include="Views\Finance\BankReconciliations\PrintPreview.cshtml" />
    <Content Include="Views\Procurement\PurchaseReqNotes\PrintPRNReceiptPreview.cshtml" />
    <Content Include="Views\Sales\Reports\CustomerBirthdayDetailReport_Sothys.cshtml" />
    <Content Include="Views\Sales\Reports\CustomerCertainAgeDetailReport_Sothys.cshtml" />
    <Content Include="Views\Sales\Reports\CustomerServiceInfoDetail_Sothys.cshtml" />
    <Content Include="Views\Inventory\Reports\GRNSummaryReport.cshtml" />
    <Content Include="Views\Inventory\Reports\GRNListingReport.cshtml" />
    <Content Include="Views\Finance\Reports\OutboundPaymentDetailReport.cshtml" />
    <Content Include="Views\Finance\Reports\InboundReceiptDetailReport.cshtml" />
    <Content Include="Views\Finance\Reports\PaymentAdviceDetailReport.cshtml" />
    <Content Include="Views\Finance\Reports\ReceiptAdviceDetailReport.cshtml" />
    <Content Include="StockValuationDataSet.xsc">
      <DependentUpon>StockValuationDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="StockDataSet.xsc">
      <DependentUpon>StockDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="SRNDataSet.xsc">
      <DependentUpon>SRNDataSet.xsd</DependentUpon>
    </Content>
    <None Include="SRNDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SRNDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="SRNDataSet.xss">
      <DependentUpon>SRNDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="SRNReportDataset.xsc">
      <DependentUpon>SRNReportDataset.xsd</DependentUpon>
    </Content>
    <None Include="SRNReportDataset.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SRNReportDataset.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="SRNReportDataset.xss">
      <DependentUpon>SRNReportDataset.xsd</DependentUpon>
    </Content>
    <Content Include="stockCardDataSet.xsc">
      <DependentUpon>stockCardDataSet.xsd</DependentUpon>
    </Content>
    <None Include="stockCardDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>stockCardDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="stockCardDataSet.xss">
      <DependentUpon>stockCardDataSet.xsd</DependentUpon>
    </Content>
    <None Include="StockDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>StockDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="StockDataSet.xss">
      <DependentUpon>StockDataSet.xsd</DependentUpon>
    </Content>
    <None Include="StockValuationDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>StockValuationDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="StockValuationDataSet.xss">
      <DependentUpon>StockValuationDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="SupplierReturnNoteItemsDataSet.xsc">
      <DependentUpon>SupplierReturnNoteItemsDataSet.xsd</DependentUpon>
    </Content>
    <None Include="SupplierReturnNoteItemsDataSet.xsd">
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>SupplierReturnNoteItemsDataSet.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </None>
    <Content Include="SupplierReturnNoteItemsDataSet.xss">
      <DependentUpon>SupplierReturnNoteItemsDataSet.xsd</DependentUpon>
    </Content>
    <Content Include="Views\Inventory\Reports\SupplierReturnNote.cshtml" />
  </ItemGroup>
  <ItemGroup>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Receipts\Common\PRN_Receipt.resx">
      <DependentUpon>PRN_Receipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\Cheque_MITL.resx">
      <DependentUpon>Cheque_MITL.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\FumigationCertificateReceipt.resx">
      <DependentUpon>FumigationCertificateReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\BillingInstructionReceipt.resx">
      <DependentUpon>BillingInstructionReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\PackingListReceipt.resx">
      <DependentUpon>PackingListReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\LoadingPlanReceipt.resx">
      <DependentUpon>LoadingPlanReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\PurchaseReturnReceipt.resx">
      <DependentUpon>PurchaseReturnReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\VGMDocumentReceipt.resx">
      <DependentUpon>VGMDocumentReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\SalesInvoiceReceipt.resx">
      <DependentUpon>SalesInvoiceReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\GRNReceipt.resx">
      <DependentUpon>GRNReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\PaymentAdviceReceipt.resx">
      <DependentUpon>PaymentAdviceReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\CommercialInvoiceReceipt.resx">
      <DependentUpon>CommercialInvoiceReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\PettyCashReceipt.resx">
      <DependentUpon>PettyCashReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\PaymentVoucherReceipt.resx">
      <DependentUpon>PaymentVoucherReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\IOUReceipt.resx">
      <DependentUpon>IOUReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\InboundReceipt.resx">
      <DependentUpon>InboundReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\PerformaInvoiceReceipt.resx">
      <DependentUpon>PerformaInvoiceReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\PurchaseOrderFinalReceipt.resx">
      <DependentUpon>PurchaseOrderFinalReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\SalesServiceInvoiceReceipt.resx">
      <DependentUpon>SalesServiceInvoiceReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\ServiceInvoiceReceipt.resx">
      <DependentUpon>ServiceInvoiceReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\ServiceOrderReceipt.resx">
      <DependentUpon>ServiceOrderReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\MITL\PurchaseOrderReceipt.resx">
      <DependentUpon>PurchaseOrderReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\BankReconcilationReceipt_Nemsui.resx">
      <DependentUpon>BankReconcilationReceipt_Nemsui.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\GRNReceipt_Nemsui.resx">
      <DependentUpon>GRNReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\InboundReceipt_Nemsui.resx">
      <DependentUpon>InboundReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\MenuHeadPrint.resx">
      <DependentUpon>MenuHeadPrint.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\ReceiptAdviceReceipt_Nemsui.resx">
      <DependentUpon>ReceiptAdviceReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\PaymentAdviceReceipt_Nemsui.resx">
      <DependentUpon>PaymentAdviceReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\PaymentVoucherReceipt_Nemsui.resx">
      <DependentUpon>PaymentVoucherReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\GDNReceipt_Nemsui.resx">
      <DependentUpon>GDNReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\SalesInvoiceReceiptPP_Nemsui.resx">
      <DependentUpon>SalesInvoiceReceiptPP_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\SalesReturnReceipt_Nemsui.resx">
      <DependentUpon>SalesReturnReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\SalesReturnVATReceipt_Nemsui.resx">
      <DependentUpon>SalesReturnVATReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\SalesOrderReceipt_Nemsui.resx">
      <DependentUpon>SalesOrderReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\PettyCashReceipt_Nemsui.resx">
      <DependentUpon>PettyCashReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\PurchaseOrderFinalReceipt_Nemsui.resx">
      <DependentUpon>PurchaseOrderFinalReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\SalesInvoiceVATReceipt_Nemsui.resx">
      <DependentUpon>SalesInvoiceVATReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\SalesInvoiceReceipt_Nemsui.resx">
      <DependentUpon>SalesInvoiceReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\SalesServiceInvoiceReceipt_Nemsui.resx">
      <DependentUpon>SalesServiceInvoiceReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\ServiceInvoiceReceipt_Nemsui.resx">
      <DependentUpon>ServiceInvoiceReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\ServiceOrderReceipt_Nemsui.resx">
      <DependentUpon>ServiceOrderReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\StockAdjustmentReceipt.resx">
      <DependentUpon>StockAdjustmentReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\StockTransferReceipt_Nemsui.resx">
      <DependentUpon>StockTransferReceipt_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Nemsui\WastageIssueReceipt.resx">
      <DependentUpon>WastageIssueReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\Cheque_Sothys.resx">
      <DependentUpon>Cheque_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\InboundReceipt_Sothys.resx">
      <DependentUpon>InboundReceipt_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\PaymentVoucherReceipt_Sothys.resx">
      <DependentUpon>PaymentVoucherReceipt_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\PurchaseOrder_Sothys_SimahAsia.resx">
      <DependentUpon>PurchaseOrder_Sothys_SimahAsia.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\PurchaseOrder_Sothys_SesdermaLaboratories.resx">
      <DependentUpon>PurchaseOrder_Sothys_SesdermaLaboratories.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\SalesInvoiceReceipt_Sothys.resx">
      <DependentUpon>SalesInvoiceReceipt_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\CustomerPaymentReceipt_Sothys.resx">
      <DependentUpon>CustomerPaymentReceipt_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\StockTransferReceipt.resx">
      <DependentUpon>StockTransferReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\StockCountSheetReceipt.resx">
      <DependentUpon>StockCountSheetReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\SalesInvoiceWalkInReceipt.resx">
      <DependentUpon>SalesInvoiceWalkInReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\SalesServiceInvoiceReceipt_Sothys.resx">
      <DependentUpon>SalesServiceInvoiceReceipt_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Receipts\Sothys\ServiceInquiryReceipt.resx">
      <DependentUpon>ServiceInquiryReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\DefaultReport.resx">
      <DependentUpon>DefaultReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\BarcodeLabelReport.resx">
      <DependentUpon>BarcodeLabelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\GRNBarcodeLabelReport.resx">
      <DependentUpon>GRNBarcodeLabelReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\PalletBarcodeLabelReport.resx">
      <DependentUpon>PalletBarcodeLabelReport.cs</DependentUpon>
    </EmbeddedResource>
    <Content Include="Reports\Nemsui\DailySalesSummaryReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\SRNListingReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\GRNListingReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseCreditCardDetails.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseCreditCardSummary.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseCreditDetails.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseCreditSummary.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseDiscountSummary.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseDiscountSummery.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseSalesSummery.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\InvoiceWiseTotalSalesSummary.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\IssueNote.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\LocationWiseSalesSummery.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\menuDetailReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\MenuWiseSaleReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\OrderTypeWiseDailySummary.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\PODetailReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\POListingReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\ReceipeDetailReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\BillCancellationReportSummery.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="Reports\Nemsui\CommercialCreditCardReportDetail.rdlc" />
    <Content Include="Reports\Nemsui\CounterWiseSaleDetailReport.rdlc" />
    <EmbeddedResource Include="Reports\Nemsui\CounterWiseSaleReportSummary.rdlc" />
    <EmbeddedResource Include="Reports\Nemsui\CounterWiseSaleSummaryCentrallyReport.rdlc" />
    <EmbeddedResource Include="Reports\Nemsui\DeliveryReportDetail.rdlc" />
    <Content Include="Reports\Nemsui\BillCancellationReportDetail.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="Reports\Nemsui\CreditCardReportDetail.rdlc" />
    <EmbeddedResource Include="Reports\Nemsui\DiscountDetailsReport.rdlc" />
    <EmbeddedResource Include="Reports\Nemsui\GRNListingReportCentrally.rdlc" />
    <EmbeddedResource Include="Reports\Nemsui\InvoiceWiseSaleReportDetails.rdlc" />
    <EmbeddedResource Include="Reports\Nemsui\MenuWiseSaleReportSummary.rdlc" />
    <EmbeddedResource Include="Reports\Nemsui\NDBCreditCardReportDetail.rdlc" />
     <Content Include="Reports\Nemsui\PickMeReportDetail.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="Reports\Nemsui\rptAODReceipt_SalesServiceInvoice_Nemsu.resx">
      <DependentUpon>rptAODReceipt_SalesServiceInvoice_Nemsu.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptAODReceipt_StockTransfer_Nemsui.resx">
      <DependentUpon>rptAODReceipt_StockTransfer_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptGatePassAOD_Nemsui.resx">
      <DependentUpon>rptGatePassAOD_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptGatePassReceipt_SalesServiceInvoice_Nemsui.resx">
      <DependentUpon>rptGatePassReceipt_SalesServiceInvoice_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptGatePass_StockTransfer_Nemsui.resx">
      <DependentUpon>rptGatePass_StockTransfer_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptGRNDetailReport.resx">
      <DependentUpon>rptGRNDetailReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptGRNSummaryReport.resx">
      <DependentUpon>rptGRNSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptInboundReceiptDetailReport.resx">
      <DependentUpon>rptInboundReceiptDetailReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptIOUDetails_Nemsui.resx">
      <DependentUpon>rptIOUDetails_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptOutboundPaymentDetailReport.resx">
      <DependentUpon>rptOutboundPaymentDetailReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptPaymentAdviceDetailReport.resx">
      <DependentUpon>rptPaymentAdviceDetailReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptReceiptAdviceDetailReport.resx">
      <DependentUpon>rptReceiptAdviceDetailReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Nemsui\rptSalesInvoiceDetails_Nemsui.resx">
      <DependentUpon>rptSalesInvoiceDetails_Nemsui.cs</DependentUpon>
    </EmbeddedResource>
    <Content Include="Reports\Nemsui\rptStockValuation.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\rptSuplierReturnNote.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\StockAdjustmentReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\StockBalanceReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\StockValuationReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\stockVariance.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <Content Include="Reports\Nemsui\WastageReport.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="Reports\Nemsui\SampathCreditCardReportDetail.rdlc" />
    <Content Include="Reports\Nemsui\stockCard.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
    <EmbeddedResource Include="Reports\Nemsui\UberReportDetail.rdlc" />
    <EmbeddedResource Include="Reports\Sothys\rptConsignmentSalesInvoiceDetail_Sothys.resx">
      <DependentUpon>rptConsignmentSalesInvoiceDetail_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCostOfSales_SalesServiceInvoice.resx">
      <DependentUpon>rptCostOfSales_SalesServiceInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCustomerServiceInfoDetail_Sothys.resx">
      <DependentUpon>rptCustomerServiceInfoDetail_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCustomerCertainAgeDetail_Sothys.resx">
      <DependentUpon>rptCustomerCertainAgeDetail_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCustomerBirthdayDetail_Sothys.resx">
      <DependentUpon>rptCustomerBirthdayDetail_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCustomerRetentionReport_Sothys.resx">
      <DependentUpon>rptCustomerRetentionReport_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptGRNSummaryReport_Sothys.resx">
      <DependentUpon>rptGRNSummaryReport_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCustomerPaymentDetailReport.resx">
      <DependentUpon>rptCustomerPaymentDetailReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptStockMovement_Sothys.resx">
      <DependentUpon>rptStockMovement_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptSalesCommissionDetail_Sothys.resx">
      <DependentUpon>rptSalesCommissionDetail_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptJobCommissionDetail_Sothys.resx">
      <DependentUpon>rptJobCommissionDetail_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptGLDetailNew_Sothys.resx">
      <DependentUpon>rptGLDetailNew_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptConsignmentSalesInvoiceSummary_Sothys.resx">
      <DependentUpon>rptConsignmentSalesInvoiceSummary_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptStockTransfer_Sothys.resx">
      <DependentUpon>rptStockTransfer_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptPurchaseOrderSummary_Sothys.resx">
      <DependentUpon>rptPurchaseOrderSummary_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCreditSalesDetail_Sothys.resx">
      <DependentUpon>rptCreditSalesDetail_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptPurchaseOrder_Sothys.resx">
      <DependentUpon>rptPurchaseOrder_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCreditSalesSummary.resx">
      <DependentUpon>rptCreditSalesSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptSalesServiceInvoiceSummary.resx">
      <DependentUpon>rptSalesServiceInvoiceSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptJobCostBreakout_Sothys.resx">
      <DependentUpon>rptJobCostBreakout_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptTotalTurnover.resx">
      <DependentUpon>rptTotalTurnover.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptSalesInvoiceWalkInDetail.resx">
      <DependentUpon>rptSalesInvoiceWalkInDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptSalesInvoiceWalkInSummary.resx">
      <DependentUpon>rptSalesInvoiceWalkInSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCostOfSales_Sothys.resx">
      <DependentUpon>rptCostOfSales_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptCustomerLoyality_Sothys.resx">
      <DependentUpon>rptCustomerLoyality_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptTurnoverMarginService.resx">
      <DependentUpon>rptTurnoverMarginService.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptTurnoverMargin.resx">
      <DependentUpon>rptTurnoverMargin.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptSalesServiceInvoice.resx">
      <DependentUpon>rptSalesServiceInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptStockValuation.resx">
      <DependentUpon>rptStockValuation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptTrialBalance_Sothys.resx">
      <DependentUpon>rptTrialBalance_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptGLDetail_Sothys.resx">
      <DependentUpon>rptGLDetail_Sothys.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\Sothys\rptGoodsReceiveNoteDetail.resx">
      <DependentUpon>rptGoodsReceiveNoteDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptSalesSummary.resx">
      <DependentUpon>rptSalesSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptBankReconciliationSummary.resx">
      <DependentUpon>rptBankReconciliationSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptChemicalAndCompoundStockReconciliationSummary.resx">
      <DependentUpon>rptChemicalAndCompoundStockReconciliationSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptCompoundStockReconciliationDetails.resx">
      <DependentUpon>rptCompoundStockReconciliationDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptCustomerAgingDetailReport.resx">
      <DependentUpon>rptCustomerAgingDetailReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptCustomerAgingSummaryReport.resx">
      <DependentUpon>rptCustomerAgingSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptDailyProduction.resx">
      <DependentUpon>rptDailyProduction.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptDailyProductionPlanning.resx">
      <DependentUpon>rptDailyProductionPlanning.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptBankReconcilation.resx">
      <DependentUpon>rptBankReconcilation.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptMonthlyQualityDetail.resx">
      <DependentUpon>rptMonthlyQualityDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptMonthlyQualitySummary.resx">
      <DependentUpon>rptMonthlyQualitySummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptRawMaterialPlanning.resx">
      <DependentUpon>rptRawMaterialPlanning.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptPendingPOStatus.resx">
      <DependentUpon>rptPendingPOStatus.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptGLDetail.resx">
      <DependentUpon>rptGLDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptMonthlyProduction.resx">
      <DependentUpon>rptMonthlyProduction.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptStockMovementSummary.resx">
      <DependentUpon>rptStockMovementSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptStockReconciliationSummary.resx">
      <DependentUpon>rptStockReconciliationSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptStockSummary.resx">
      <DependentUpon>rptStockSummary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptStockMovement.resx">
      <DependentUpon>rptStockMovement.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptStockReconciliationDetail.resx">
      <DependentUpon>rptStockReconciliationDetail.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptSupplierAgingSummaryReport.resx">
      <DependentUpon>rptSupplierAgingSummaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptSupplierLedger.resx">
      <DependentUpon>rptSupplierLedger.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptGatePassReceipt_SalesServiceInvoice.resx">
      <DependentUpon>rptGatePassReceipt_SalesServiceInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptAODReceipt_SalesServiceInvoice.resx">
      <DependentUpon>rptAODReceipt_SalesServiceInvoice.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptAODReceipt_StockTransfer2.resx">
      <DependentUpon>rptAODReceipt_StockTransfer2.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptAODReceipt.resx">
      <DependentUpon>rptAODReceipt.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptAODReceiptNew.resx">
      <DependentUpon>rptAODReceiptNew.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptAOD_ServiceOrder.resx">
      <DependentUpon>rptAOD_ServiceOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptGatePass_ServiceOrder.resx">
      <DependentUpon>rptGatePass_ServiceOrder.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptGatePassAOD.resx">
      <DependentUpon>rptGatePassAOD.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptAODReceipt_StockTransfer.resx">
      <DependentUpon>rptAODReceipt_StockTransfer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptGatePass_StockTransfer.resx">
      <DependentUpon>rptGatePass_StockTransfer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptGatePass.resx">
      <DependentUpon>rptGatePass.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptSalesOrderDetails.resx">
      <DependentUpon>rptSalesOrderDetails.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptCustomerLedger.resx">
      <DependentUpon>rptCustomerLedger.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptQuotations.resx">
      <DependentUpon>rptQuotations.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptSupplierAgingDetailReport.resx">
      <DependentUpon>rptSupplierAgingDetailReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptVatAndSVat.resx">
      <DependentUpon>rptVatAndSVat.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Reports\MITL\rptWIPStock.resx">
      <DependentUpon>rptWIPStock.cs</DependentUpon>
    </EmbeddedResource>
    <Content Include="rptSuplierReturnNote.rdlc">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <WCFMetadata Include="Connected Services\" />
  </ItemGroup>
  <PropertyGroup>
    <VisualStudioVersion Condition="'$(VisualStudioVersion)' == ''">10.0</VisualStudioVersion>
    <VSToolsPath Condition="'$(VSToolsPath)' == ''">$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v$(VisualStudioVersion)</VSToolsPath>
  </PropertyGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <Import Project="$(VSToolsPath)\WebApplications\Microsoft.WebApplication.targets" Condition="'$(VSToolsPath)' != ''" />
  <Import Project="$(MSBuildExtensionsPath32)\Microsoft\VisualStudio\v10.0\WebApplications\Microsoft.WebApplication.targets" Condition="false" />
  <Target Name="MvcBuildViews" AfterTargets="AfterBuild" Condition="'$(MvcBuildViews)'=='true'">
    <AspNetCompiler VirtualPath="temp" PhysicalPath="$(WebProjectOutputDir)" />
  </Target>
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{349c5851-65df-11da-9384-00065b846f21}">
        <WebProjectProperties>
          <UseIIS>True</UseIIS>
          <AutoAssignPort>True</AutoAssignPort>
          <DevelopmentServerPort>14722</DevelopmentServerPort>
          <DevelopmentServerVPath>/</DevelopmentServerVPath>
          <IISUrl>https://localhost:44355/</IISUrl>
          <NTLMAuthentication>False</NTLMAuthentication>
          <UseCustomServer>False</UseCustomServer>
          <CustomServerUrl>
          </CustomServerUrl>
          <SaveServerSettingsInUserFile>False</SaveServerSettingsInUserFile>
        </WebProjectProperties>
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets'))" />
    <Error Condition="!Exists('..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.2.0.0\build\net46\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.props'))" />
    <Error Condition="!Exists('..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets'))" />
  </Target>
  <Import Project="..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets" Condition="Exists('..\packages\Microsoft.Data.SqlClient.SNI.6.0.2\build\net462\Microsoft.Data.SqlClient.SNI.targets')" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target> -->
</Project>