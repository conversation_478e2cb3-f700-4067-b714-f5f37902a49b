﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("CostingWarehouses", Schema = "inv")]
    public partial class CostingWarehous
    {
        [Key]
        public int CostingWarehouseId { get; set; }
        public int? ShipmentCostId { get; set; }
        public int? ProductId { get; set; }
        public int? GoodsReceiveNoteLineId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitPrice { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? SubTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? ApportionedPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? ApportionedValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(GoodsReceiveNoteLineId))]
        [InverseProperty("CostingWarehous")]
        public virtual GoodsReceiveNoteLine GoodsReceiveNoteLine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("CostingWarehous")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ShipmentCostId))]
        [InverseProperty("CostingWarehous")]
        public virtual ShipmentCost ShipmentCost { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("CostingWarehous")]
        public virtual TaxGroup TaxGroup { get; set; }
    }
}
