﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GoodsDispatchNotes", Schema = "inv")]
    public partial class GoodsDispatchNote
    {
        public GoodsDispatchNote()
        {
            GoodsDispatchNoteLines = new HashSet<GoodsDispatchNoteLine>();
            PackingLists = new HashSet<PackingList>();
        }

        [Key]
        public int GoodsDispatchNoteId { get; set; }
        public int? CompanyId { get; set; }
        public int? LoanOrderId { get; set; }
        public int? SalesOrderId { get; set; }
        public int? SalesInvoiceId { get; set; }
        public int? LoadingPlanId { get; set; }
        public int? PurchaseReturnId { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        public byte? RefTypeEnum { get; set; }
        public byte? OrderType { get; set; }
        public int? CustomerId { get; set; }
        [StringLength(50)]
        public string CustomerReference { get; set; }
        public int? SupplierId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(50)]
        public string InvoiceNumber { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        [StringLength(255)]
        public string CustomerAddress { get; set; }
        public int? ShipmentPackingMethodId { get; set; }
        [StringLength(255)]
        public string TermsAndConditions { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }
        public int? TaxGroupId { get; set; }
        public int? PaymentTermId { get; set; }
        [StringLength(255)]
        public string PortOfShipment { get; set; }
        [StringLength(50)]
        public string VehicleNo { get; set; }
        [StringLength(50)]
        public string VesselNo { get; set; }
        [StringLength(50)]
        public string ContainerNo { get; set; }
        public int? ShippingTermId { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? DepartmentId { get; set; }
        [Column("AODNumber")]
        [StringLength(50)]
        public string Aodnumber { get; set; }
        [StringLength(50)]
        public string SealNumber { get; set; }
        [StringLength(50)]
        public string Reason { get; set; }
        [StringLength(50)]
        public string GatePassNumber { get; set; }
        public int? TxnWorkFlowId { get; set; }
        [StringLength(20)]
        public string TimeOut { get; set; }
        [StringLength(20)]
        public string DriverName { get; set; }
        [Column("DriverID")]
        [StringLength(20)]
        public string DriverId { get; set; }
        [Column("SFAStatusEnum")]
        public byte? SfastatusEnum { get; set; }
        [Column("SFAMessage")]
        [StringLength(1000)]
        public string Sfamessage { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(LoadingPlanId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual LoadingPlan LoadingPlan { get; set; }
        [ForeignKey(nameof(LoanOrderId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual LoanOrder LoanOrder { get; set; }
        [ForeignKey(nameof(PaymentTermId))]
        [InverseProperty(nameof(SupportData.GoodsDispatchNotePaymentTerms))]
        public virtual SupportData PaymentTerm { get; set; }
        [ForeignKey(nameof(PurchaseReturnId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual PurchaseReturn PurchaseReturn { get; set; }
        [ForeignKey(nameof(SalesInvoiceId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual SalesInvoice SalesInvoice { get; set; }
        [ForeignKey(nameof(SalesOrderId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual SalesOrder SalesOrder { get; set; }
        [ForeignKey(nameof(ShipmentPackingMethodId))]
        [InverseProperty(nameof(SupportData.GoodsDispatchNoteShipmentPackingMethods))]
        public virtual SupportData ShipmentPackingMethod { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual Supplier Supplier { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual TaxGroup TaxGroup { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("GoodsDispatchNotes")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLine.GoodsDispatchNote))]
        public virtual ICollection<GoodsDispatchNoteLine> GoodsDispatchNoteLines { get; set; }
        [InverseProperty(nameof(PackingList.GoodsDispatchNote))]
        public virtual ICollection<PackingList> PackingLists { get; set; }
    }
}
