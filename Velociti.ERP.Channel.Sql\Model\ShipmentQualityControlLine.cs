﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ShipmentQualityControlLines", Schema = "inv")]
    public partial class ShipmentQualityControlLine
    {
        [Key]
        public int ShipmentQualityControlLineId { get; set; }
        public int? ShipmentQualityControlId { get; set; }
        public int? ProductId { get; set; }
        public int? GoodsReceiveNoteLineDetailId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? RejectedQuantity { get; set; }
        public int? RejectReasonId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ProductExpiryDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? WarrantyDate { get; set; }
        [StringLength(32)]
        public string LotNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ShipmentQualityControlLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ShipmentQualityControlLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteLineDetailId))]
        [InverseProperty("ShipmentQualityControlLines")]
        public virtual GoodsReceiveNoteLineDetail GoodsReceiveNoteLineDetail { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ShipmentQualityControlLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(RejectReasonId))]
        [InverseProperty(nameof(SupportData.ShipmentQualityControlLines))]
        public virtual SupportData RejectReason { get; set; }
        [ForeignKey(nameof(ShipmentQualityControlId))]
        [InverseProperty("ShipmentQualityControlLines")]
        public virtual ShipmentQualityControl ShipmentQualityControl { get; set; }
    }
}
