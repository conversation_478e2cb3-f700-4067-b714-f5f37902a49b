﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class StockTakeLinesController : ControllerBase  
    {
        private readonly IStockTakeLineService _stockTakeLineService;    

        public StockTakeLinesController(IStockTakeLineService stockTakeLineService)
        {
            _stockTakeLineService = stockTakeLineService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(int stockTakeId, int companyId)
        {
            return Ok(await _stockTakeLineService.GetAsync(stockTakeId, companyId));
        }
    }
}