﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [ApiController]
    public class PressLinesController : ControllerBase
    {
        private readonly IPressLineService _pressLineService;

        public PressLinesController(IPressLineService pressLineService)
        {
            _pressLineService = pressLineService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetActiveAll(int companyId)
        {
            return Ok(await _pressLineService.GetActiveAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            return Ok(await _pressLineService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("MaxWorkerCount/{pressLineId}")]
        public async Task<IActionResult> GetMaxWorkerCount(int pressLineId)
        {
            return Ok(await _pressLineService.GetMaxWorkerCountAsync(pressLineId));
        }
    }
}