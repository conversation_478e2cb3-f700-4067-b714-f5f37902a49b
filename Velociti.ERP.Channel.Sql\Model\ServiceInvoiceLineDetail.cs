﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ServiceInvoiceLineDetails", Schema = "prc")]
    public partial class ServiceInvoiceLineDetail
    {
        [Key]
        public int ServiceInvoiceLineDetailId { get; set; }
        public int? ServiceInvoiceLineId { get; set; }
        public int? FixedAssetId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }

        [ForeignKey(nameof(FixedAssetId))]
        [InverseProperty("ServiceInvoiceLineDetails")]
        public virtual FixedAsset FixedAsset { get; set; }
        [ForeignKey(nameof(ServiceInvoiceLineId))]
        [InverseProperty("ServiceInvoiceLineDetails")]
        public virtual ServiceInvoiceLine ServiceInvoiceLine { get; set; }
    }
}
