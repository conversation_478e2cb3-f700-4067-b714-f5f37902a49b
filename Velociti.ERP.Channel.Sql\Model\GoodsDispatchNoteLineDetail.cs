﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GoodsDispatchNoteLineDetails", Schema = "inv")]
    public partial class GoodsDispatchNoteLineDetail
    {
        [Key]
        public int GoodsDispatchNoteLineDetailId { get; set; }
        public int? GoodsDispatchNoteLineId { get; set; }
        public int? WarehouseId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ProductExpiryDate { get; set; }
        [StringLength(32)]
        public string LotNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? WarrantyDate { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(GoodsDispatchNoteLineId))]
        [InverseProperty("GoodsDispatchNoteLineDetails")]
        public virtual GoodsDispatchNoteLine GoodsDispatchNoteLine { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.GoodsDispatchNoteLineDetails))]
        public virtual Warehous Warehouse { get; set; }
    }
}
