﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class PasswordPolicyRepository : IPasswordPolicyRepository
    {
        private readonly MarangoniERPContext _context;

        public PasswordPolicyRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task SaveAsync(PasswordPolicy passwordPolicy)
        {
            if(passwordPolicy.PasswordPolicyId == default)
            {
                passwordPolicy.CreationDate = DateTime.Now;

                await _context.PasswordPolicies.AddAsync(passwordPolicy);
            }
            else
            {
                _context.PasswordPolicies.Attach(passwordPolicy);

                var entry = _context.Entry(passwordPolicy);
               
                entry.State = EntityState.Modified;
                entry.Property(p => p.CreatedUserId).IsModified = false;
                entry.Property(p => p.CompanyId).IsModified = false;
                entry.Property(p => p.CreationDate).IsModified = false;
            }

            await _context.SaveChangesAsync();
        }

        public async Task<PasswordPolicy> GetByCompanyIdAsync(int companyId)
        {
            return await _context.PasswordPolicies.FirstOrDefaultAsync(p => p.CompanyId == companyId);
        }
    }
}
