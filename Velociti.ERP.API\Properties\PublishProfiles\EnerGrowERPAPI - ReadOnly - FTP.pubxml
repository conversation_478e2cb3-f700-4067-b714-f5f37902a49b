<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>FTP</WebPublishMethod>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>http://energrowerpapi.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <ProjectGuid>0e96bf2d-a051-4717-91cc-91ef6dd53de3</ProjectGuid>
    <publishUrl>ftp://waws-prod-jnb21-005dr.ftp.azurewebsites.windows.net</publishUrl>
    <DeleteExistingFiles>False</DeleteExistingFiles>
    <FtpPassiveMode>True</FtpPassiveMode>
    <FtpSitePath>site/wwwroot</FtpSitePath>
    <UserName>EnerGrowERPAPI\$EnerGrowERPAPI</UserName>
    <_SavePWD>True</_SavePWD>
  </PropertyGroup>
</Project>