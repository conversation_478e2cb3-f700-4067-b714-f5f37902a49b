﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BillOfMaterialLines", Schema = "man")]
    public partial class BillOfMaterialLine
    {
        [Key]
        public int BillOfMaterialLineId { get; set; }
        public int BillOfMaterialId { get; set; }
        public int? ProductId { get; set; }
        public int? ProductCategoryId { get; set; }
        public decimal? Quantity { get; set; }
        public int? SecondaryUnitOfMeasureId { get; set; }
        [Column(TypeName = "money")]
        public decimal? MaterialCost { get; set; }
        public bool? IsMandatoryProduct { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfMaterialId))]
        [InverseProperty("BillOfMaterialLines")]
        public virtual BillOfMaterial BillOfMaterial { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("BillOfMaterialLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductCategoryId))]
        [InverseProperty(nameof(ProductHierarchy.BillOfMaterialLines))]
        public virtual ProductHierarchy ProductCategory { get; set; }
        [ForeignKey(nameof(SecondaryUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.BillOfMaterialLines))]
        public virtual UnitOfMeasure SecondaryUnitOfMeasure { get; set; }
    }
}
