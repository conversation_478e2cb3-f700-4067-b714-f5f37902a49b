﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class UserSupervisorsRepository : IUserSupervisorsRepository
    {
        private readonly MarangoniERPContext _context;

        public UserSupervisorsRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<UserSupervisor>> GetAsync(int userId, int companyId)
        {
            return await _context.UserSupervisors.Where(p => p.UserId == userId && p.CompanyId == companyId && p.ExpiryDate == null).ToListAsync();
        }

        public async Task SaveAsync(IEnumerable<UserSupervisor> userSupervisors)
        {
            try
            {
                var newRecords = userSupervisors.Where(p => p.UserSupervisorId == 0);
                var userId = 0;
                var modifiedUserId = 0;
                foreach (var row in newRecords)
                {
                    userId = row.UserId;
                    modifiedUserId = row.ModifiedUserId.Value;

                    if (row.UserSupervisorId == default)
                    {
                        row.CreationDate = DateTime.Now;
                        await _context.UserSupervisors.AddAsync(row);
                    }
                }

                //Expire removed supervisors
                var currentUserSupervisorIds = userSupervisors.Where(p => p.UserSupervisorId != 0).Select(p => p.UserSupervisorId).ToList();
                var removedUserSupervisors = _context.UserSupervisors.Where(p => p.UserId == userId && !currentUserSupervisorIds.Contains(p.UserSupervisorId));
                foreach (var row in removedUserSupervisors)
                {
                    row.ExpiryDate = DateTime.Now;
                    row.ModifiedUserId = modifiedUserId;
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }
    }
}
