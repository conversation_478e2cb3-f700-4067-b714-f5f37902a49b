﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ShipmentQualityControlLinesController : ControllerBase
    {
        private readonly IShipmentQualityControlLineService _shipmentQualityControlLineService;

        public ShipmentQualityControlLinesController(IShipmentQualityControlLineService shipmentQualityControlLineService)
        {
            _shipmentQualityControlLineService = shipmentQualityControlLineService;
        }

        [HttpGet]
        [Route("{shipmentQualityControlId}/company/{companyId}")]
        public async Task<IActionResult> Get(int shipmentQualityControlId, int companyId)
        {
            return Ok(await _shipmentQualityControlLineService.GetAsync(shipmentQualityControlId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ShipmentQualityControlLine shipmentQualityControlLine)
        {
            await _shipmentQualityControlLineService.SaveAsync(shipmentQualityControlLine);

            return Ok();
        }

        [HttpPost]
        [Route("List")]
        public async Task<IActionResult> SaveList([FromBody]List<ShipmentQualityControlLine> shipmentQualityControlLines)
        {
            await _shipmentQualityControlLineService.SaveListAsync(shipmentQualityControlLines);

            return Ok();
        }
    }
}