﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [ApiController]
    public class ProfileBuildingWarehouseProductsController : ControllerBase
    {
        private readonly IProfileBuildingService _profileBuildingService;

        public ProfileBuildingWarehouseProductsController(IProfileBuildingService profileBuildingService)
        {
            _profileBuildingService = profileBuildingService;
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] List<ProfileBuildingWarehouseProduct> profileBuildingWarehouseProducts)
        {
            await _profileBuildingService.SaveSemifinishedGoodsSequenceAsync(profileBuildingWarehouseProducts);

            return Ok();
        }
    }
}