﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_QC_Mapping")]
    public partial class IQcMapping
    {
        [Column("Product Name")]
        [StringLength(255)]
        public string ProductName { get; set; }
        [Column("BEVFinal")]
        public double? Bevfinal { get; set; }
        [Column("BEVFinalMAX")]
        public double? BevfinalMax { get; set; }
        [Column("BAPFinal")]
        public double? Bapfinal { get; set; }
        [Column("BAPFinalMAX")]
        public double? BapfinalMax { get; set; }
        [Column("ECODSIFinal")]
        public double? Ecodsifinal { get; set; }
        [Column("ECODSIFinalMAX")]
        public double? EcodsifinalMax { get; set; }
        [Column("ECODSIFinal1")]
        public double? Ecodsifinal1 { get; set; }
        [Column("ECODSIFinal1MAX")]
        public double? Ecodsifinal1Max { get; set; }
        [Column("ECODSIFinal2")]
        public double? Ecodsifinal2 { get; set; }
        [Column("ECODSIFinal2MAX")]
        public double? Ecodsifinal2Max { get; set; }
        [Column("TMTFinal")]
        public double? Tmtfinal { get; set; }
        [Column("TMTFinalMAX")]
        public double? TmtfinalMax { get; set; }
        [Column("TEVFinal")]
        public double? Tevfinal { get; set; }
        [Column("TEVFinalMAX")]
        public double? TevfinalMax { get; set; }
        [Column("GPSFinal")]
        public double? Gpsfinal { get; set; }
        [Column("GPSFinalMAX")]
        public double? GpsfinalMax { get; set; }
        [Column("GPT3Final")]
        public double? Gpt3final { get; set; }
        [Column("GPT3FinalMAX")]
        public double? Gpt3finalMax { get; set; }
        [Column("CSMFinal")]
        public double? Csmfinal { get; set; }
        [Column("CSMFinalMAX")]
        public double? CsmfinalMax { get; set; }
        [Column("MBTFinal")]
        public double? Mbtfinal { get; set; }
        [Column("MBTFinalMAX")]
        public double? MbtfinalMax { get; set; }
        [Column("J2Final")]
        public double? J2final { get; set; }
        [Column("J2FinalMAX")]
        public double? J2finalMax { get; set; }
    }
}
