﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Tokens", Schema = "adm")]
    public partial class Token
    {
        [Key]
        public Guid TokenId { get; set; }
        [Required]
        [StringLength(1500)]
        public string JwtId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime CreatedOn { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime ExpiryOn { get; set; }
        public bool Used { get; set; }
        public bool Invalidated { get; set; }
        public int UserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(UserId))]
        [InverseProperty("Tokens")]
        public virtual User User { get; set; }
    }
}
