﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_FG_BOO")]
    public partial class IFgBoo
    {
        [Column("BOO Description")]
        [StringLength(255)]
        public string BooDescription { get; set; }
        [StringLength(255)]
        public string Category { get; set; }
        [Column("Product Code")]
        [StringLength(255)]
        public string ProductCode { get; set; }
        [Column("BOM Name")]
        [StringLength(255)]
        public string BomName { get; set; }
        [Column("Machine Sequence1")]
        [StringLength(255)]
        public string MachineSequence1 { get; set; }
        [Column("Machine Name1")]
        [StringLength(255)]
        public string MachineName1 { get; set; }
        [Column("Standard Duration1")]
        [StringLength(255)]
        public string StandardDuration1 { get; set; }
        [Column("Handling Time1")]
        [StringLength(255)]
        public string HandlingTime1 { get; set; }
        [Column("Machine Sequence2")]
        [StringLength(255)]
        public string MachineSequence2 { get; set; }
        [Column("Machine Name2")]
        [StringLength(255)]
        public string MachineName2 { get; set; }
        [Column("Standard Duration2")]
        [StringLength(255)]
        public string StandardDuration2 { get; set; }
        [Column("Handling Time2")]
        [StringLength(255)]
        public string HandlingTime2 { get; set; }
        [Column("Machine Sequence3")]
        [StringLength(255)]
        public string MachineSequence3 { get; set; }
        [Column("Machine Name3")]
        [StringLength(255)]
        public string MachineName3 { get; set; }
        [Column("Standard Duration3")]
        [StringLength(255)]
        public string StandardDuration3 { get; set; }
        [Column("Handling Time3")]
        [StringLength(255)]
        public string HandlingTime3 { get; set; }
        [Column("Machine Sequence4")]
        [StringLength(255)]
        public string MachineSequence4 { get; set; }
        [Column("Machine Name4")]
        [StringLength(255)]
        public string MachineName4 { get; set; }
        [Column("Standard Duration4")]
        [StringLength(255)]
        public string StandardDuration4 { get; set; }
        [Column("Handling Time4")]
        [StringLength(255)]
        public string HandlingTime4 { get; set; }
        [Column("Machine Sequence5")]
        [StringLength(255)]
        public string MachineSequence5 { get; set; }
        [Column("Machine Name5")]
        [StringLength(255)]
        public string MachineName5 { get; set; }
        [Column("Standard Duration5")]
        [StringLength(255)]
        public string StandardDuration5 { get; set; }
        [Column("Handling Time5")]
        [StringLength(255)]
        public string HandlingTime5 { get; set; }
    }
}
