﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Mould_Product")]
    public partial class IMouldProduct
    {
        [StringLength(255)]
        public string TyreCode { get; set; }
        [StringLength(255)]
        public string MouldCode1 { get; set; }
        [StringLength(255)]
        public string MouldCode2 { get; set; }
        [StringLength(255)]
        public string MouldCode3 { get; set; }
        [StringLength(255)]
        public string MouldCode4 { get; set; }
        [StringLength(255)]
        public string MouldCode5 { get; set; }
        [StringLength(255)]
        public string MouldCode6 { get; set; }
        [StringLength(255)]
        public string MouldCode7 { get; set; }
    }
}
