2025-12-08 15:48:45,481 [5] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 15:48:45,515 [5] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 15:48:46,053 [5] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 15:48:46,066 [5] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 15:49:01,541 [5] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 15:49:01,541 [5] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-276626faa331774f84a17d87dea794f9-c1ea7fbdd0075748-00"}
2025-12-08 15:49:01,541 [5] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 15:49:11,144 [14] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 15:49:11,144 [14] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 15:49:11,144 [14] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 15:49:11,144 [14] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 15:49:11,255 [14] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 15:49:11,256 [14] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-d9a17804d12de14dae9bda1e023a48cd-585bafaaf0f85a4f-00"}
2025-12-08 15:49:11,256 [14] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 15:52:54,814 [33] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 15:52:54,814 [33] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 15:52:54,814 [33] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 15:52:54,814 [33] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 15:52:54,889 [33] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 15:52:54,889 [33] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-78e2fb1b0289d64398c46c76ea0a9163-7369fde593bef240-00"}
2025-12-08 15:52:54,889 [33] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 15:54:08,306 [33] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 15:54:08,308 [33] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 15:54:08,308 [33] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 15:54:08,308 [33] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 15:54:08,353 [33] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 15:54:08,353 [33] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-68d616f082e7f74fbb4cf952525161c1-c7fea091f772fb48-00"}
2025-12-08 15:54:08,353 [33] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:00:13,572 [43] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:00:13,572 [43] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:00:13,573 [43] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 16:00:13,573 [43] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 16:00:13,655 [43] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 16:00:13,655 [43] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-cb8836eaa251dd489a30343f29e84a84-6d5ed3661be9d948-00"}
2025-12-08 16:00:13,655 [43] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:00:27,497 [50] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:00:27,497 [50] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:00:27,497 [50] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 16:00:27,497 [50] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 16:00:27,528 [50] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 16:00:27,528 [50] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-44dfb70b30736e40ae33b269c58e0bae-0e6cf59121560645-00"}
2025-12-08 16:00:27,528 [50] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:00:39,394 [54] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:00:39,394 [54] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:00:39,394 [54] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 16:00:39,394 [54] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 16:00:39,418 [54] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 16:00:39,418 [54] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-7a2f630c396aac47b504a7563e536756-7f03a9201ea84346-00"}
2025-12-08 16:00:39,418 [54] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:01:00,342 [47] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:01:00,342 [47] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:01:00,342 [47] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 16:01:00,342 [47] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 16:01:00,762 [47] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 16:01:00,762 [47] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-34b22c79f8bda640aded2f3afdb19147-9741dc5bd5a64c48-00"}
2025-12-08 16:01:00,762 [47] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:01:25,472 [60] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:01:25,472 [60] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:01:25,472 [60] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 16:01:25,472 [60] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 16:01:27,965 [60] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 16:01:27,965 [60] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-1fd9b7930712c94bb712d90e1803d4aa-2a6943a63784dd49-00"}
2025-12-08 16:01:27,965 [60] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:01:34,274 [42] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:01:34,276 [42] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:01:34,276 [42] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 16:01:34,276 [42] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 16:01:34,316 [42] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 16:01:34,316 [42] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-12a6b0c330ecb145892a9512c0306513-63a566a37f4e9f49-00"}
2025-12-08 16:01:34,316 [42] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:01:50,839 [51] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:01:50,839 [51] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:01:50,839 [51] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 16:01:50,839 [51] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 16:01:50,892 [51] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 16:01:50,892 [51] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-0844fc87a2d6a4459b55680b2c04bf9a-ffe7876b4cd51d4e-00"}
2025-12-08 16:01:50,892 [51] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:01:55,676 [60] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:01:55,678 [60] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:01:55,678 [60] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"Asanka","password":"12345"}
2025-12-08 16:01:55,678 [60] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: Asanka
2025-12-08 16:01:55,709 [60] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-12-08 16:01:55,711 [60] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-c03bbaac15772f4d9af0d2e7fc8678a5-b638ba1ae4c3d743-00"}
2025-12-08 16:01:55,711 [60] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-12-08 16:02:02,824 [28] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:02:10,129 [42] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:04:45,091 [51] INFO  Web.Controllers.AccountController - Login POST started.
2025-12-08 16:04:45,091 [51] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-12-08 16:04:45,091 [51] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"asanka@laugfs","password":"12345"}
2025-12-08 16:04:45,091 [51] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: asanka@laugfs
2025-12-08 16:04:48,553 [51] DEBUG Web.Controllers.AccountController - Response Status Code: OK
2025-12-08 16:04:48,553 [51] DEBUG Web.Controllers.AccountController - Response Content: {"id":3,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.rr68F9CN2K_ItthTrnQgbrqyMpaagqZyuAMfm3W2C2k","refreshToken":"60e24c5e-02c6-4d52-863a-42f5568ef13c","expiresIn":2073600,"error":null,"user":{"userId":3,"titleId":2,"title":null,"userCode":"U1","fullname":"Pasan Madhawa","displayName":"Navitsa System Admin","email":"<EMAIL>","signature":"","signatureFile":null,"isFilteredByDepartment":false,"employeeId":0,"defaultCompanyId":2,"defaultDepartmentId":0,"creationDate":"2020-08-14T12:00:51.62","expiryDate":null,"modifiedUserId":2,"loginId":0,"username":null,"passwordHash":null,"login":{"loginId":3,"username":"asanka@laugfs","passwordHash":"827ccb0eea8a706c4c34a16891f84e7b","modifiedUserId":2},"designationTypeEnum":254,"designationName":"Administrator","notificationMessage":null},"permissions":[]}
2025-12-08 16:04:48,873 [51] INFO  Web.Controllers.AccountController - Authentication succeeded for user: Navitsa System Admin
2025-12-08 16:04:48,879 [51] DEBUG Web.Controllers.AccountController - Claims created for user Navitsa System Admin
2025-12-08 16:04:48,886 [51] INFO  Web.Controllers.AccountController - User Navitsa System Admin signed in successfully.
