﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class UserRoleRepository : IUserRoleRepository
    {
        private readonly MarangoniERPContext _context;

        public UserRoleRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Domain.Entities.Administration.UserRole>> GetAllAsync(int companyId)
        {
            var result = from ur in _context.UserRoles
                         join r in _context.Roles on ur.RoleId equals r.RoleId
                         join u in _context.Users on ur.UserId equals u.UserId
                         join uc in _context.UserCompanies on u.UserId equals uc.UserId
                         where uc.CompanyId == companyId
                         select new Domain.Entities.Administration.UserRole
                         {
                             UserId = ur.UserId,
                             UserName = u.DisplayName,
                             RoleId = ur.RoleId,
                             RoleName = r.RoleName
                         };

            return await result.ToListAsync();
        }

        public async Task<IEnumerable<UserRole>> GetAllAsync()
        {
            return await _context.UserRoles.Include(p => p.Role).Include(p => p.User).ToListAsync();
        }

        public async Task SaveAsync(UserRole userRole)
        {
            var duplciateRecord = await _context.UserRoles.FirstOrDefaultAsync(p => p.UserId == userRole.UserId && p.RoleId == userRole.RoleId);
            if (duplciateRecord == null)
            {
                await _context.UserRoles.AddAsync(userRole);
                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteAsync(UserRole userRole)
        {
            var record = await _context.UserRoles.FirstAsync(c => c.RoleId == userRole.RoleId && c.UserId == userRole.UserId);

            _context.UserRoles.Remove(record);
            await _context.SaveChangesAsync();
        }
    }
}
