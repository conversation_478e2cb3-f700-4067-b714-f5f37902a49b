﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class QuotationLinesController : ControllerBase
    {
        private readonly IQuotationLineService _quotationLineService;

        public QuotationLinesController(IQuotationLineService quotationLineService)
        {
            _quotationLineService = quotationLineService;
        }

        [HttpGet]
        [Route("{quotationId}")]
        public async Task<IActionResult> Get(int quotationId)
        {
            return Ok(await _quotationLineService.GetAsync(quotationId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]QuotationLine quotationLine)
        {
            await _quotationLineService.SaveAsync(quotationLine);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Remove([FromBody]QuotationLine quotationLine)
        {
            await _quotationLineService.DeleteAsync(quotationLine);

            return Ok();
        }
    }
}