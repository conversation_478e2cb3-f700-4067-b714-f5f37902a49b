﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MenuHeadLines", Schema = "inv")]
    public class MenuHeadLines
    {
        [Key]
        public int MenuHeadLineId { get; set; }
        public int MenuItemId { get; set; }
        public decimal Quantity { get; set; }
        public int StatusEnum { get; set; }
        public int ReceipeId { get; set; }
        public string ReceipeCode { get; set; }
        public string ReceipeName { get; set; }


    }
}
