﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Procurement;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesOrderLinesController : ControllerBase
    {
        private readonly ISalesOrderLineService _salesOrderLineService;
        private readonly ISubContractOrderLineService _subContractOrderLineService;

        public SalesOrderLinesController(ISalesOrderLineService salesOrderLineService, ISubContractOrderLineService subContractOrderLineService)
        {
            _salesOrderLineService = salesOrderLineService;
            _subContractOrderLineService = subContractOrderLineService;
        }

        [HttpGet]
        [Route("{salesOrderId}")]
        public async Task<IActionResult> Get(int salesOrderId)
        {
            return Ok(await _salesOrderLineService.GetAsync(salesOrderId));
        }

        [HttpPost]
        public async Task<IActionResult> GetPurchaseRequisitionNoteLine([FromBody]SalesOrderLine salesOrderLine)
        {
            return Ok(await _salesOrderLineService.AddSalesOrderLineAsync(salesOrderLine));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesOrderLine salesOrderLine)
        {
            await _salesOrderLineService.UpdateAsync(salesOrderLine);

            return Ok();
        }

        [HttpGet]
        [Route("Submitted/{companyId}")]
        public async Task<IActionResult> GetSubmitted(int companyId)
        {
            return Ok(await _salesOrderLineService.GetSubmittedLinesAsync(companyId));
        }

        [HttpGet]
        [Route("Selected/{salesOrderLineIds}/company/{companyId}")]
        public async Task<IActionResult> GetSelected(string salesOrderLineIds, int companyId)
        {
            string[] orderlineIdArray = salesOrderLineIds.Split(',');
            var list = orderlineIdArray.Select(p => int.Parse(p)).ToList();

            return Ok(await _salesOrderLineService.GetSelectedLinesAsync(list, companyId));
        }
    }
}