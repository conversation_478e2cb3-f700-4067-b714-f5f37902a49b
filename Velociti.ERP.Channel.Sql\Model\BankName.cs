﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("CardBanks", Schema = "dbo")]
    public class BankName
    {
        [Key]
        [Column(TypeName = "CardBanksId")]
        public int CardBanksId { get; set; }
        [Column(TypeName = "CardBanksName")]
        [StringLength(255)]
        public string CardBanksName { get; set; }

    }
}
