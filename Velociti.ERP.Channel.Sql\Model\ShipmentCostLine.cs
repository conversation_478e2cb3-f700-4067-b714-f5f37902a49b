﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ShipmentCostLines", Schema = "inv")]
    public partial class ShipmentCostLine
    {
        public ShipmentCostLine()
        {
            PaymentAdviceLines = new HashSet<PaymentAdviceLine>();
        }

        [Key]
        public int ShipmentCostLineId { get; set; }
        public int? ShipmentCostId { get; set; }
        public int? ChargeGroupId { get; set; }
        public byte? NatureEnum { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [Column(TypeName = "money")]
        public decimal? ChargeValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? ChargeValueInBaseCurrency { get; set; }
        public bool? IsAllocated { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChargeGroupId))]
        [InverseProperty(nameof(SupportData.ShipmentCostLines))]
        public virtual SupportData ChargeGroup { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("ShipmentCostLines")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(ShipmentCostId))]
        [InverseProperty("ShipmentCostLines")]
        public virtual ShipmentCost ShipmentCost { get; set; }
        [InverseProperty(nameof(PaymentAdviceLine.ShipmentCostLine))]
        public virtual ICollection<PaymentAdviceLine> PaymentAdviceLines { get; set; }
    }
}
