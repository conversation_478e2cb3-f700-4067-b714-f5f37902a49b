﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ChequeBooks", Schema = "fin")]
    public partial class ChequeBook
    {
        public ChequeBook()
        {
            ChequeBookLines = new HashSet<ChequeBookLine>();
        }

        [Key]
        public int ChequeBookId { get; set; }
        public int? PayBookId { get; set; }
        [StringLength(50)]
        public string ChequeBookNumber { get; set; }
        public int? StartingCheckNumber { get; set; }
        public int? NoOfCheques { get; set; }
        public int? RemainderChequeCount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(PayBookId))]
        [InverseProperty("ChequeBooks")]
        public virtual PayBook PayBook { get; set; }
        [InverseProperty(nameof(ChequeBookLine.ChequeBook))]
        public virtual ICollection<ChequeBookLine> ChequeBookLines { get; set; }
    }
}
