﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class SubContractOrderLinesController : ControllerBase
    {
        private readonly ISubContractOrderLineService _subContractOrderLineService;

        public SubContractOrderLinesController(ISubContractOrderLineService subContractOrderLineService)
        {
            _subContractOrderLineService = subContractOrderLineService;
        }

        [HttpGet]
        [Route("{subContractOrderId}/Company/{companyId}")]
        public async Task<IActionResult> GetAll(int subContractOrderId, int companyId)
        {
            return Ok(await _subContractOrderLineService.GetByHeaderIdAsync(companyId, subContractOrderId));
        }
    }
}