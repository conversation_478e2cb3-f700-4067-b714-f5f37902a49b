﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing  
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]  
    public class BillOfMaterialLinesController : ControllerBase
    {
        private readonly IBillOfMaterialLineService _billOfMaterialLineService;  

        public BillOfMaterialLinesController(IBillOfMaterialLineService billOfMaterialLineService)  
        {
            _billOfMaterialLineService = billOfMaterialLineService;
        }

        [HttpGet]
        [Route("{billOfMaterialId}")]
        public async Task<IActionResult> Get(int billOfMaterialId)
        {
            return Ok(await _billOfMaterialLineService.GetAsync(billOfMaterialId));
        }

        [HttpGet]
        [Route("GetRawMaterialsByBOM/{billOfMaterialId}/Product/{productId}")]
        public async Task<IActionResult> GetRawMaterialsByBOM(int billOfMaterialId, int productId)
        {
            var list = await _billOfMaterialLineService.GetRawMaterialsByBOMAsync(billOfMaterialId, productId);

            return Ok(list);
        }

        [HttpPost]
        public async Task<IActionResult> GetBillOfMaterialLine([FromBody]BillOfMaterialLine billOfMaterialLine)
        {
            return Ok(await _billOfMaterialLineService.AddBillOfMaterialLineAsync(billOfMaterialLine));
        }
    }
}