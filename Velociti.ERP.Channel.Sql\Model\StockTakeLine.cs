﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockTakeLines", Schema = "inv")]
    public partial class StockTakeLine
    {
        [Key]
        public int StockTakeLineId { get; set; }
        public int? StockTakeId { get; set; }
        public int? ProductId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int UnitOfMeasureId { get; set; }
        public int? AllocatedQuantity { get; set; }
        public int? OnHandQuantity { get; set; }
        public int? PhysicalQuantity { get; set; }
        [StringLength(200)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ProductId))]
        [InverseProperty("StockTakeLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(StockTakeId))]
        [InverseProperty("StockTakeLines")]
        public virtual StockTake StockTake { get; set; }
    }
}
