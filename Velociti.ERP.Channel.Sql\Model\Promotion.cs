﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Promotions", Schema = "sales")]
    public partial class Promotion
    {
        public Promotion()
        {
            PromotionLines = new HashSet<PromotionLine>();
        }

        [Key]
        public int PromotionId { get; set; }
        [Required]
        [StringLength(50)]
        public string Code { get; set; }
        [StringLength(1000)]
        public string Description { get; set; }
        public int? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        public int? CompanyId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [InverseProperty(nameof(PromotionLine.Promotion))]
        public virtual ICollection<PromotionLine> PromotionLines { get; set; }
    }
}
