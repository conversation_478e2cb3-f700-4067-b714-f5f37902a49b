﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [ApiController]
    public class PerformaInvoiceLineController : ControllerBase
    {
        private readonly IPerformaInvoiceLineService performaInvoiceLineService;

        public PerformaInvoiceLineController(IPerformaInvoiceLineService performaInvoiceLineService)
        {
            this.performaInvoiceLineService = performaInvoiceLineService;
        }

        [HttpGet]
        [Route("{salesInvoiceId}")]
        public async Task<IActionResult> Get(int salesInvoiceId)
        {
            return Ok(await this.performaInvoiceLineService.GetAsync(salesInvoiceId));
        }

        [HttpGet]
        [Route("Submitted/{companyId}/userId/{userId}/startDate/{startDate}/endDate/{endDate}")]
        public async Task<IActionResult> GetSubmitted(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await performaInvoiceLineService.GetSubmittedLinesAsync(companyId, userId, startDate, endDate));
        }
    }
}