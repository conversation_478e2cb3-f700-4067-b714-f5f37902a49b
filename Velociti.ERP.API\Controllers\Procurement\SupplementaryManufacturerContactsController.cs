﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class SupplementaryManufacturerContactsController : ControllerBase
    {
        private readonly ISupplementaryManufacturerContactService _supplementaryManufacturerContactService;

        public SupplementaryManufacturerContactsController(ISupplementaryManufacturerContactService supplementaryManufacturerContactService)
        {
            _supplementaryManufacturerContactService = supplementaryManufacturerContactService;
        }

        [HttpGet]
        [Route("{supplementaryManufacturerId}")]
        public async Task<IActionResult> Get(int supplementaryManufacturerId)
        {
            return Ok(await _supplementaryManufacturerContactService.GetBySupplementaryManufacturerIdAsync(supplementaryManufacturerId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]SupplementaryManufacturerContact supplementaryManufacturerContact)
        {
            if (supplementaryManufacturerContact == null)
                return BadRequest();

            await _supplementaryManufacturerContactService.SaveAsync(supplementaryManufacturerContact);

            return Ok();
        }

        [HttpDelete]
        [Route("{supplementaryManufacturerId}/Contact/{contactId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int supplementaryManufacturerId, int contactId, int userId)
        {
            await _supplementaryManufacturerContactService.ToggleActivationAsync(supplementaryManufacturerId, contactId, userId);

            return Ok();
        }
    }
}