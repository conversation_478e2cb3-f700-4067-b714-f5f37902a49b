﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Production;
using Velociti.ERP.Domain.Services.Production;

namespace Velociti.ERP.API.Controllers.Production
{
    [Route("api/Production/[controller]")]
    [Authorize]
    [ApiController]
    public class DashboardController : ControllerBase
    {
        private readonly IDashboardService _dashboardService;

        public DashboardController(IDashboardService dashboardService)
        {
            this._dashboardService = dashboardService;
        }

        [HttpGet]
        [Route("Schedule/{year}/{month}/{day}")]
        public IActionResult GetDailySchedule(int day,int month,int year)
        {
            return Ok(_dashboardService.GetDailyScheduleAsync(day,month,year));
        }

        [HttpPost]
        [Route("Schedule/Job")]
        public IActionResult GetDailySchedule([FromForm] Job job)
        {
            return Ok(_dashboardService.UpdateJobAsync(job));
        }
    }
}