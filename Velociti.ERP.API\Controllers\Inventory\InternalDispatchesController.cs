﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class InternalDispatchesController : ControllerBase
    {
        private readonly IInternalDispatchService _internalDispatchService;

        public InternalDispatchesController(IInternalDispatchService internalDispatchService)
        {
            _internalDispatchService = internalDispatchService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _internalDispatchService.GetAllAsync(companyId, userId, startDate, endDate));
        }
        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            return Ok(await _internalDispatchService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("{internalDispatchId}")]
        public async Task<IActionResult> GetByIdAsync(int internalDispatchId)
        {
            return Ok(await _internalDispatchService.GetByIdAsync(internalDispatchId));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]InternalDispatch internalDispatch)
        {
            switch (internalDispatch.Action)
            {
                case "submit": await _internalDispatchService.SubmitAsync(internalDispatch); break;
                case "accept": await _internalDispatchService.AcceptAsync(internalDispatch); break;
                case "cancel": await _internalDispatchService.CancelAsync(internalDispatch); break;
                case "update": await _internalDispatchService.SaveAsync(internalDispatch); break;
                case "reverse": await _internalDispatchService.ReverseAsync(internalDispatch); break;
                case "send for approval": await _internalDispatchService.SendForApprovalAsync(internalDispatch.InternalDispatchId, internalDispatch.CompanyId.Value, internalDispatch.ModifiedUserId.Value); break;
                case "update work flow": await _internalDispatchService.UpdateWorkFlowStatusAsync(internalDispatch); break;
            }

            return Ok();
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate, byte typeEnum)
        {
            return Ok(await _internalDispatchService.GetForApprovalAsync(companyId, userId, startDate, endDate, typeEnum));
        }
    }
}