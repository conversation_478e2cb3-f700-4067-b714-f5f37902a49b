﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ChequeBookLines", Schema = "fin")]
    public partial class ChequeBookLine
    {
        [Key]
        public int ChequeBookLineId { get; set; }
        public int? ChequeBookId { get; set; }
        public int? ChequeNumber { get; set; }
        public byte? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChequeBookId))]
        [InverseProperty("ChequeBookLines")]
        public virtual ChequeBook ChequeBook { get; set; }
    }
}
