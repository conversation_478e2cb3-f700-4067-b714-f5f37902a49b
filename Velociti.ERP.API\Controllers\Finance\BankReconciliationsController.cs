﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class BankReconciliationsController : ControllerBase
    {
        private readonly IBankReconciliationService _bankReconciliationService;

        public BankReconciliationsController(IBankReconciliationService bankReconciliationService)
        {
            _bankReconciliationService = bankReconciliationService;
        }

        [HttpGet]
        [Route("Single/{bankReconciliationId}")]
        public async Task<IActionResult> GetById(int bankReconciliationId)
        {
            return Ok(await _bankReconciliationService.GetByIdAsync(bankReconciliationId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _bankReconciliationService.GetAllAsync(companyId, startDate, endDate));
        }

        [HttpGet]
        [Route("LastStatementBalance/{accountId}")]
        public async Task<IActionResult> GetLastStatementBalance(int accountId)
        {
            return Ok(await _bankReconciliationService.GetLastStatementBalanceAsync(accountId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]BankReconciliation bankReconciliation)
        {
            await _bankReconciliationService.SaveAsync(bankReconciliation);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]BankReconciliation bankReconciliation)
        {
            switch (bankReconciliation.Action)
            {
                case "submit":
                    await _bankReconciliationService.SubmitAsync(bankReconciliation);
                    break;
                case "cancel":
                    await _bankReconciliationService.CancelAsync(bankReconciliation);
                    break;
            }

            return Ok();
        }
    }
}