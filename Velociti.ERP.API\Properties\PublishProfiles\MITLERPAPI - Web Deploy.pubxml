<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <ResourceId>/subscriptions/f652297f-ef01-4cc9-9e3f-5285fc2dbc6e/resourcegroups/SandBoxRG/providers/Microsoft.Web/sites/MITLERPAPI</ResourceId>
    <ResourceGroup>SandBoxRG</ResourceGroup>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <SiteUrlToLaunchAfterPublish>https://mitlerpapi.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <TargetFramework>netcoreapp3.1</TargetFramework>
    <ProjectGuid>0e96bf2d-a051-4717-91cc-91ef6dd53de3</ProjectGuid>
    <SelfContained>false</SelfContained>
    <MSDeployServiceURL>mitlerpapi.scm.azurewebsites.net:443</MSDeployServiceURL>
    <DeployIisAppPath>MITLERPAPI</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <UserName>$MITLERPAPI</UserName>
    <_SavePWD>True</_SavePWD>
    <_DestinationType>AzureWebSite</_DestinationType>
    <InstallAspNetCoreSiteExtension>False</InstallAspNetCoreSiteExtension>
  </PropertyGroup>
</Project>