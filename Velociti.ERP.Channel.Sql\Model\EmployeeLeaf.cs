﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("EmployeeLeaves", Schema = "hr")]
    public partial class EmployeeLeaf
    {
        [Key]
        public int EmployeeLeaveId { get; set; }
        public int? EmployeeId { get; set; }
        public byte? LeaveTypeEnum { get; set; }
        public byte? TotalNoOfLeavesAllocated { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("EmployeeLeaves")]
        public virtual Employee Employee { get; set; }
    }
}
