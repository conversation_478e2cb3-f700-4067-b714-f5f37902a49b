﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductionOrderLines", Schema = "man")]
    public partial class ProductionOrderLine
    {
        public ProductionOrderLine()
        {
            ProductionOrderLineDetails = new HashSet<ProductionOrderLineDetail>();
            ProfileBuildingLines = new HashSet<ProfileBuildingLine>();
        }

        [Key]
        public int ProductionOrderLineId { get; set; }
        public int? ProductionOrderId { get; set; }
        public int? SequenceNo { get; set; }
        public int? MachineId { get; set; }
        public int? CavityId { get; set; }
        public byte? DayLightNo { get; set; }
        public int? EmployeeId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BarcodeScannedStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualEndDateTime { get; set; }
        [Column(TypeName = "money")]
        public decimal? MachineCost { get; set; }
        [Column(TypeName = "money")]
        public decimal? OverheadCost { get; set; }
        [Column(TypeName = "money")]
        public decimal? LabourCost { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Weight { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CavityId))]
        [InverseProperty(nameof(MouldCavity.ProductionOrderLines))]
        public virtual MouldCavity Cavity { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("ProductionOrderLines")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("ProductionOrderLines")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(ProductionOrderId))]
        [InverseProperty("ProductionOrderLines")]
        public virtual ProductionOrder ProductionOrder { get; set; }
        [InverseProperty(nameof(ProductionOrderLineDetail.ProductionOrderLine))]
        public virtual ICollection<ProductionOrderLineDetail> ProductionOrderLineDetails { get; set; }
        [InverseProperty(nameof(ProfileBuildingLine.ProductionOrderLine))]
        public virtual ICollection<ProfileBuildingLine> ProfileBuildingLines { get; set; }
    }
}
