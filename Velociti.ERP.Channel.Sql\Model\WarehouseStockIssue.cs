﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("WarehouseStockIssues", Schema = "inv")]
    public partial class WarehouseStockIssue
    {
        [Key]
        public int WarehouseStockIssueId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string DocNumber { get; set; }
        public int? SrcWarehouseId { get; set; }
        public int? DstWarehouseId { get; set; }
        public int? ProductId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int? UnitOfMeasureId { get; set; }
        public long? Quantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("WarehouseStockIssues")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DstWarehouseId))]
        [InverseProperty(nameof(Warehous.WarehouseStockIssueDstWarehouses))]
        public virtual Warehous DstWarehouse { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("WarehouseStockIssues")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SrcWarehouseId))]
        [InverseProperty(nameof(Warehous.WarehouseStockIssueSrcWarehouses))]
        public virtual Warehous SrcWarehouse { get; set; }
        [ForeignKey(nameof(UnitOfMeasureId))]
        [InverseProperty("WarehouseStockIssues")]
        public virtual UnitOfMeasure UnitOfMeasure { get; set; }
    }
}
