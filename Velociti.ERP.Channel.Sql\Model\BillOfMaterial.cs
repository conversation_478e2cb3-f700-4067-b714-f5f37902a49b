﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BillOfMaterials", Schema = "man")]
    public partial class BillOfMaterial
    {
        public BillOfMaterial()
        {
            BillOfMaterialLines = new HashSet<BillOfMaterialLine>();
            BillOfOperations = new HashSet<BillOfOperation>();
            InternalOrderLines = new HashSet<InternalOrderLine>();
            ProductionOrders = new HashSet<ProductionOrder>();
            SalesServiceOrderLines = new HashSet<SalesServiceOrderLine>();
            SubContractOrderLines = new HashSet<SubContractOrderLine>();
            SubContractOrders = new HashSet<SubContractOrder>();
        }

        [Key]
        public int BillOfMaterialId { get; set; }
        public int? CompanyId { get; set; }
        [Column("BOMCode")]
        [StringLength(50)]
        public string Bomcode { get; set; }
        [Column("BOMDescription")]
        [StringLength(255)]
        public string Bomdescription { get; set; }
        public byte? CategoryEnum { get; set; }
        public int? ProductId { get; set; }
        public decimal? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Scrap { get; set; }
        public int? BrandId { get; set; }
        [Column(TypeName = "money")]
        public decimal? PackagePrice { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Commission { get; set; }
        public int? Duration { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalMaterialCost { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
        public bool Status { get; set; }

        [ForeignKey(nameof(BrandId))]
        [InverseProperty(nameof(SupportData.BillOfMaterials))]
        public virtual SupportData Brand { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("BillOfMaterials")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(BillOfMaterialLine.BillOfMaterial))]
        public virtual ICollection<BillOfMaterialLine> BillOfMaterialLines { get; set; }
        [InverseProperty(nameof(BillOfOperation.BillOfMaterial))]
        public virtual ICollection<BillOfOperation> BillOfOperations { get; set; }
        [InverseProperty(nameof(InternalOrderLine.BillOfMaterial))]
        public virtual ICollection<InternalOrderLine> InternalOrderLines { get; set; }
        [InverseProperty(nameof(ProductionOrder.BillOfMaterial))]
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; }
        [InverseProperty(nameof(SalesServiceOrderLine.BillOfMaterial))]
        public virtual ICollection<SalesServiceOrderLine> SalesServiceOrderLines { get; set; }
        [InverseProperty(nameof(SubContractOrderLine.BillOfMaterial))]
        public virtual ICollection<SubContractOrderLine> SubContractOrderLines { get; set; }
        [InverseProperty(nameof(SubContractOrder.BillOfMaterial))]
        public virtual ICollection<SubContractOrder> SubContractOrders { get; set; }
    }
}
