﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalDispatches", Schema = "inv")]
    public partial class InternalDispatch
    {
        public InternalDispatch()
        {
            InternalDispatchLines = new HashSet<InternalDispatchLine>();
        }

        [Key]
        public int InternalDispatchId { get; set; }
        public int? MaterialRequisitionNoteId { get; set; }
        public int? CompanyId { get; set; }
        public int? EmployeeId { get; set; }
        public int? DepartmentId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [Required]
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? RequestedDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? PostDate { get; set; }
        public byte? StatusEnum { get; set; }
        public int? DeliveryWarehouseId { get; set; }
        public int? RequestReasonId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TxnWorkFlowId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("InternalDispatches")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DeliveryWarehouseId))]
        [InverseProperty(nameof(Warehous.InternalDispatches))]
        public virtual Warehous DeliveryWarehouse { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("InternalDispatches")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("InternalDispatches")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(MaterialRequisitionNoteId))]
        [InverseProperty("InternalDispatches")]
        public virtual MaterialRequisitionNote MaterialRequisitionNote { get; set; }
        [ForeignKey(nameof(RequestReasonId))]
        [InverseProperty(nameof(SupportData.InternalDispatches))]
        public virtual SupportData RequestReason { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("InternalDispatches")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [InverseProperty(nameof(InternalDispatchLine.InternalDispatch))]
        public virtual ICollection<InternalDispatchLine> InternalDispatchLines { get; set; }
    }
}
