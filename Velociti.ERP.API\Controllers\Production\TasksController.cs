﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Production;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Production
{
    [Route("api/Production/[controller]")]
    [ApiController]
    public class TasksController : ControllerBase
    {
        private readonly IProductionPlanLineService _productionPlanLineService;
        private readonly IProductionPlanService _productionPlanService;

        public TasksController(IProductionPlanLineService productionPlanLineService, IProductionPlanService productionPlanService)
        {
            _productionPlanLineService = productionPlanLineService;
            _productionPlanService = productionPlanService;
        }

        [HttpGet]
        [Route("BarcodeScanned/{taskSerialNumber}/SerialNumber/{serialNumber}")]
        public async Task<IActionResult> GetDailySchedule(string taskSerialNumber, int serialNumber)
        {
            bool allBarcodesScanned = await _productionPlanLineService.CheckAllBarcodesScannedAsyc(serialNumber);

            var jsonObject = new
            {
                status = allBarcodesScanned ? "success" : "fail"
            };

            return Ok(jsonObject);
        }

        [HttpPut]
        public async Task<IActionResult> Put([FromBody]ProductionPlanLine productionPlanLine)
        {
            try
            {
                switch (productionPlanLine.Action)
                {
                    case "update actual start":
                        await _productionPlanService.UpdateActualStartDateAsync(Convert.ToInt32(productionPlanLine.serial_number)
                            , Convert.ToDateTime(productionPlanLine.actual_start_time)); break;

                    case "update completed time":
                        await _productionPlanService.UpdateCompletedTimeAsync(Convert.ToInt32(productionPlanLine.serial_number)
                        , Convert.ToDateTime(productionPlanLine.actual_completed_time)); break;

                    case "update finished time":
                        await _productionPlanService.UpdateActualEndDateAsync(Convert.ToInt32(productionPlanLine.serial_number)
                        , Convert.ToDateTime(productionPlanLine.actual_completed_time)); break;

                   
                }

                var jsonObject = new
                {
                    code = 200,
                    status = "success"
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {

                var jsonObject = new
                {
                    code = 500,
                    status = ex.Message
                };

                return Ok(jsonObject);
            }
        }

        [HttpPut]
        [Route("ScheduleChange")]
        public async Task<IActionResult> ScheduleChange([FromBody]List<Job> productionPlanLines)
        {

            try
            {
                await _productionPlanService.ChangePlannedTimeAsync(productionPlanLines);

                var jsonObject = new
                {
                    code = 200,
                    status = "success"
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    code = 500,
                    status = ex.Message
                };

                return Ok(jsonObject);
            }
        }
    }
}