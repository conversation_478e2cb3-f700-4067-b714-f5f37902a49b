﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("AgingBuckets", Schema = "adm")]
    public partial class AgingBucket
    {
        [Key]
        public int AgingBucketId { get; set; }
        public int CompanyId { get; set; }
        public byte TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(50)]
        public string Description { get; set; }
        public int StartDays { get; set; }
        public int EndDays { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("AgingBuckets")]
        public virtual Company Company { get; set; }
    }
}
