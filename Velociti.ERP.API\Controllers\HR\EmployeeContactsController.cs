﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Services.HR;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class EmployeeContactsController : ControllerBase    
    {
        private readonly IEmployeeContactService _employeeContactService;    

        public EmployeeContactsController(IEmployeeContactService employeeContactService)    
        {
            _employeeContactService = employeeContactService;
        }

        [HttpGet]
        [Route("{employeeId}")]
        public async Task<IActionResult> Get(int employeeId)  
        {
            return Ok(await _employeeContactService.GetByEmployeeIdAsync(employeeId));    
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]EmployeeContact employeeContact)    
        {
            if (employeeContact == null)
                return BadRequest();

            await _employeeContactService.SaveAsync(employeeContact);

            return Ok();
        }

        [HttpDelete]
        [Route("{employeeId}/Contact/{contactId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int employeeId, int contactId, int userId)
        {
            await _employeeContactService.ToggleActivationAsync(employeeId, contactId, userId);

            return Ok();
        }
    }
}