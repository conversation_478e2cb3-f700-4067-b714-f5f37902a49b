﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Attendances", Schema = "hr")]
    public partial class IAttendance
    {
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string EmployeeCode { get; set; }
        [StringLength(255)]
        public string EmployeeName { get; set; }
        [Column(TypeName = "date")]
        public DateTime Date { get; set; }
        [Column(TypeName = "time(0)")]
        public TimeSpan? TimeIn { get; set; }
        [Column(TypeName = "time(0)")]
        public TimeSpan? TimeOut { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [StringLength(250)]
        public string Error { get; set; }
    }
}
