﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class UserGroupsRepository : IUserGroupsRepository
    {
        private readonly MarangoniERPContext _context;

        public UserGroupsRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<UserGroup>> GetAllAsync()
        {
            return await _context.UserGroups.Include(p => p.Group).Include(p => p.User).ToListAsync();
        }

        public async Task SaveAsync(UserGroup userGroup)
        {
            var duplciateRecord = await _context.UserGroups.FirstOrDefaultAsync(p => p.GroupId == userGroup.GroupId && p.UserId == userGroup.UserId);
            if (duplciateRecord == null)
            {
                await _context.UserGroups.AddAsync(userGroup);
                await _context.SaveChangesAsync();
            }
        }

        public async Task DeleteAsync(UserGroup userGroup)
        {
            var record = await _context.UserGroups.FirstAsync(c => c.GroupId == userGroup.GroupId && c.UserId == userGroup.UserId);

            _context.UserGroups.Remove(record);
            await _context.SaveChangesAsync();
        }
    }
}
