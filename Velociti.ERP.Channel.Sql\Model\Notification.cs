﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Notifications", Schema = "adm")]
    public partial class Notification
    {
        [Key]
        public int NotificationId { get; set; }
        public int CompanyId { get; set; }
        public byte TypeEnum { get; set; }
        [StringLength(200)]
        public string Description { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ViewedDateTime { get; set; }
        public int AssignedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Notifications")]
        public virtual Company Company { get; set; }
    }
}
