﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseOrderAdditionalChargeLines", Schema = "prc")]
    public partial class PurchaseOrderAdditionalChargeLine
    {
        [Key]
        public int PurchaseOrderAdditionalChargeLineId { get; set; }
        public int? PurchaseOrderAdditionalChargeId { get; set; }
        public int? ChargeGroupId { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? ChargeValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalChargeValue { get; set; }
        public bool? IsAllocated { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChargeGroupId))]
        [InverseProperty(nameof(SupportData.PurchaseOrderAdditionalChargeLines))]
        public virtual SupportData ChargeGroup { get; set; }
        [ForeignKey(nameof(PurchaseOrderAdditionalChargeId))]
        [InverseProperty("PurchaseOrderAdditionalChargeLines")]
        public virtual PurchaseOrderAdditionalCharge PurchaseOrderAdditionalCharge { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("PurchaseOrderAdditionalChargeLines")]
        public virtual TaxGroup TaxGroup { get; set; }
    }
}
