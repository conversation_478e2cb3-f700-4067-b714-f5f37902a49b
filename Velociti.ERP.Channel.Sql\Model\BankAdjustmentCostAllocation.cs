﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BankAdjustmentCostAllocations", Schema = "fin")]
    public partial class BankAdjustmentCostAllocation
    {
        [Key]
        public int BankAdjustmentCostAllocationId { get; set; }
        public int? BankAdjustmentLineId { get; set; }
        public int? DepartmentId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? ApportionPct { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }

        [ForeignKey(nameof(BankAdjustmentLineId))]
        [InverseProperty("BankAdjustmentCostAllocations")]
        public virtual BankAdjustmentLine BankAdjustmentLine { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("BankAdjustmentCostAllocations")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("BankAdjustmentCostAllocations")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
    }
}
