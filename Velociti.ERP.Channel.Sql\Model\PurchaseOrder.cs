﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseOrders", Schema = "prc")]
    public partial class PurchaseOrder
    {
        public PurchaseOrder()
        {
            GoodsReceiveNotes = new HashSet<GoodsReceiveNote>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            PurchaseOrderAdditionalCharges = new HashSet<PurchaseOrderAdditionalCharge>();
            PurchaseOrderLines = new HashSet<PurchaseOrderLine>();
            PurchaseOrderSalesInvoices = new HashSet<PurchaseOrderSalesInvoice>();
        }

        [Key]
        public int PurchaseOrderId { get; set; }
        public int? CompanyId { get; set; }
        public int? QuotationId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        public string VATNo { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? OrderTypeEnum { get; set; }
        public byte? AssetTypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? VersionNo { get; set; }
        [StringLength(50)]
        public string SupplierReference { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        public int? SupplierId { get; set; }
        [StringLength(255)]
        public string SupplierAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        public int? PaymentTermId { get; set; }
        [StringLength(50)]
        public string PortOfShipment { get; set; }
        public int? ShippingTermId { get; set; }
        public int? AccountId { get; set; }
        public int? ParentId { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        public int? ShipmentPackingMethodId { get; set; }
        [StringLength(255)]
        public string OrderTerms { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? TxnWorkFlowId { get; set; }
        public int? DepartmentId { get; set; }
        public int? EmployeeId { get; set; }
        public int? PriceListId { get; set; }
        [Column("AODNumber")]
        [StringLength(50)]
        public string Aodnumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpectedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("PurchaseOrders")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PurchaseOrders")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("PurchaseOrders")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("PurchaseOrders")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("PurchaseOrders")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(PriceListId))]
        [InverseProperty(nameof(SupportData.PurchaseOrders))]
        public virtual SupportData PriceList { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("PurchaseOrders")]
        public virtual Supplier Supplier { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("PurchaseOrders")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [InverseProperty(nameof(GoodsReceiveNote.PurchaseOrder))]
        public virtual ICollection<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        [InverseProperty(nameof(PaymentAdvice.PurchaseOrder))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(PurchaseOrderAdditionalCharge.PurchaseOrder))]
        public virtual ICollection<PurchaseOrderAdditionalCharge> PurchaseOrderAdditionalCharges { get; set; }
        [InverseProperty(nameof(PurchaseOrderLine.PurchaseOrder))]
        public virtual ICollection<PurchaseOrderLine> PurchaseOrderLines { get; set; }
        [InverseProperty(nameof(PurchaseOrderSalesInvoice.PurchaseOrder))]
        public virtual ICollection<PurchaseOrderSalesInvoice> PurchaseOrderSalesInvoices { get; set; }
    }
}
