﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Authorize]
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class PurchaseOrderAdditionalChargesController : ControllerBase
    {
        private readonly IPurchaseOrderAdditionalChargeService _purchaseOrderAdditionalChargeService;

        public PurchaseOrderAdditionalChargesController(IPurchaseOrderAdditionalChargeService purchaseOrderAdditionalChargeService)
        {
            _purchaseOrderAdditionalChargeService = purchaseOrderAdditionalChargeService;
        }

        [HttpGet]
        [Route("PurchaseOrder/{purchaseOrderId}")]
        public async Task<IActionResult> GetByPurchaseOrderAsync(int purchaseOrderId)
        {
            return Ok(await _purchaseOrderAdditionalChargeService.GetByPurchaseOrderAsync(purchaseOrderId));
        }
    }
}