﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class ManualJournalsController : ControllerBase
    {
        private readonly IManualJournalService _manualJournalService;

        public ManualJournalsController(IManualJournalService manualJournalService)
        {
            _manualJournalService = manualJournalService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _manualJournalService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("{manualJournalId}")]
        public async Task<IActionResult> GetById(int manualJournalId)
        {
            return Ok(await _manualJournalService.GetByIdAsync(manualJournalId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]ManualJournal manualJournal)
        {
            await _manualJournalService.SaveAsync(manualJournal);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]ManualJournal manualJournal)
        {
            switch (manualJournal.Action)
            {
                case "submit":
                    await _manualJournalService.SubmitAsync(manualJournal);
                    break;
                case "cancel":
                    await _manualJournalService.CancelAsync(manualJournal);
                    break;
                case "copy":
                    await _manualJournalService.CopyAsync(manualJournal.ManualJournalId);
                    break;
                default:
                    throw new InvalidOperationException();
            };

            return Ok();
        }
    }
}