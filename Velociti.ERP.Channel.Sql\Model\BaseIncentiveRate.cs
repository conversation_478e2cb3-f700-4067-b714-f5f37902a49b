﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BaseIncentiveRate", Schema = "hr")]
    public partial class BaseIncentiveRate
    {
        [Key]
        public int BaseIncentiveRateId { get; set; }
        public int? LowerLimit { get; set; }
        public int? UpperLimit { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? Rate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
    }
}
