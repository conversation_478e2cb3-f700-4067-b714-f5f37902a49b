﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Groups", Schema = "adm")]
    public partial class Group
    {
        public Group()
        {
            GroupRoles = new HashSet<GroupRole>();
            Permissions = new HashSet<Permission>();
            UserGroups = new HashSet<UserGroup>();
        }

        [Key]
        public int GroupId { get; set; }
        [Required]
        [StringLength(100)]
        public string GroupName { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [InverseProperty(nameof(GroupRole.Group))]
        public virtual ICollection<GroupRole> GroupRoles { get; set; }
        [InverseProperty(nameof(Permission.Group))]
        public virtual ICollection<Permission> Permissions { get; set; }
        [InverseProperty(nameof(UserGroup.Group))]
        public virtual ICollection<UserGroup> UserGroups { get; set; }
    }
}
