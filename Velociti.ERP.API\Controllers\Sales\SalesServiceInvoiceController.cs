﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [ApiController]
    public class SalesServiceInvoiceController : ControllerBase
    {
        private readonly ISalesServiceInvoiceService _serviceInvoiceService;
        public SalesServiceInvoiceController(ISalesServiceInvoiceService serviceInvoiceService)
        {
            _serviceInvoiceService = serviceInvoiceService;
        }

        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]SalesInvoice salesInvoice)
        {
            SalesInvoice salesInv = await _serviceInvoiceService.SaveAsync(salesInvoice);

            return Ok(salesInv);
        }
        [HttpGet]
        [Route("Single/{salesInvoiceId}")]
        public async Task<IActionResult> GetById(int salesInvoiceId)
        {

            var result = await _serviceInvoiceService.GetByIdAsync(salesInvoiceId);
            return Ok(result);

        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            return Ok(await _serviceInvoiceService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("SettlementDocuments/{serviceInvoiceId}")]
        public async Task<IActionResult> GetSettlemetDocumentsAsync(int serviceInvoiceId)
        {
            return Ok(await _serviceInvoiceService.GetSettlementDocumentsAsync(serviceInvoiceId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte orderTypeEnum)
        {
            return Ok(await _serviceInvoiceService.GetAllAsync(companyId, userId, startDate, endDate, (int)Module.DocumentType.InboundLoanRequest));
        }
        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesInvoice salesInvoice)
        {
            switch (salesInvoice.Action)
            {
                case "submit":
                    await _serviceInvoiceService.SubmitAsync(salesInvoice);
                    break;
                case "reverse": 
                    await _serviceInvoiceService.ReverseAsync(salesInvoice); 
                    break;
                case "cancel":
                    await _serviceInvoiceService.CancelAsync(salesInvoice);
                    break;
               
            }

            return Ok();
        }
    }
}