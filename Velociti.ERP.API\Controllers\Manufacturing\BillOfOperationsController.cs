﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class BillOfOperationsController : ControllerBase
    {
        private readonly IBillOfOperationService _billOfOperationService;

        public BillOfOperationsController(IBillOfOperationService billOfOperationService)
        {
            _billOfOperationService = billOfOperationService;
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _billOfOperationService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("Single/{billOfOperationId}")]
        public async Task<IActionResult> FindById(int billOfOperationId)
        {
            return Ok(await _billOfOperationService.FindByIdAsync(billOfOperationId));
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> Get(string action, int companyId)
        {
            return action switch
            {
                "Active" => Ok(await _billOfOperationService.GetActiveAllAsync(companyId)),

                _ => BadRequest(),
            };
        }

        [HttpGet]
        [Route("Company/{companyId}/User/{userId}")]
        public async Task<IActionResult> Get(int companyId, int userId)  
        {            
            return Ok(await _billOfOperationService.GetAllAsync(companyId, userId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]BillOfOperation billOfOperation)  
        {
            await _billOfOperationService.SaveAsync(billOfOperation);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]BillOfOperation billOfOperation)
        {
            await _billOfOperationService.SubmitAsync(billOfOperation.BillOfOperationId, billOfOperation.ModifiedUserId.Value); 

            return Ok();
        }

        [HttpDelete]
        [Route("{billOfOperationId}/User/{userId}/Company/{companyId}")]
        public async Task<IActionResult> ToggleActivation(int billOfOperationId, int userId, int companyId)
        {
            await _billOfOperationService.ToggleActivationAsync(billOfOperationId, userId, companyId);

            return Ok();
        }
    }
}