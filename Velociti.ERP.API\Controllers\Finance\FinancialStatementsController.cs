﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class FinancialStatementsController : ControllerBase
    {
        private readonly IFinancialStatementService _financialStatementService;

        public FinancialStatementsController(IFinancialStatementService financialStatementService)  
        {
            _financialStatementService = financialStatementService;
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("ShortList/{companyId}/Type/{typeEnum}")]
        public async Task<IActionResult> GetByTypeAsync(int companyId, byte typeEnum)
        {
            return Ok(await _financialStatementService.GetStatementsByTypeShortList(companyId, typeEnum));
        }

        [HttpGet]
        [Route("Hierarchy/{companyId}/{financialStatementId}")]
        public async Task<IActionResult> GetHierarchy(int companyId, int financialStatementId)
        {
            return Ok(await _financialStatementService.GetHierarchyAsync(companyId, financialStatementId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Hierarchy/Company/{companyId}/StartDate/{startDate}/EndDate/{endDate}/Id/{financialStatementId}")]
        public async Task<IActionResult> GetFinancialStatement(int? companyId, DateTime? startDate, DateTime? endDate, int? financialStatementId)
        {
            return Ok(await _financialStatementService.GetFinancialStatementAsync(companyId.Value, startDate.Value, endDate.Value, financialStatementId.Value));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]FinancialStatement financialStatement)  
        {
            if (financialStatement == null)
                return BadRequest();

            await _financialStatementService.SaveAsync(financialStatement);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivation(int id)
        {
            await _financialStatementService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}