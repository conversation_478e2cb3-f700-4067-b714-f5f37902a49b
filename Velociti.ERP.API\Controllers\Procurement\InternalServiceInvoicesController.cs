﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class InternalServiceInvoicesController : ControllerBase  
    {
        private readonly IInternalServiceInvoiceService _internalServiceInvoiceService;  

        public InternalServiceInvoicesController(IInternalServiceInvoiceService internalServiceInvoiceService)    
        {
            _internalServiceInvoiceService = internalServiceInvoiceService;  
        }

        [HttpGet]
        [Route("Single/{internalServiceInvoiceId}")]
        public async Task<IActionResult> FindById(int internalServiceInvoiceId)  
        {
            return Ok(await _internalServiceInvoiceService.FindByIdAsync(internalServiceInvoiceId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)  
        {
            return Ok(await _internalServiceInvoiceService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]InternalServiceInvoice internalServiceInvoice)  
        {
            int id = 0;
            switch (internalServiceInvoice.Action)
            {
                case "save": 
                    id = await _internalServiceInvoiceService.SaveAsync(internalServiceInvoice); 
                    break;
            }
            return Ok(id);
        }

        [HttpDelete]
        [Route("{internalServiceInvoiceId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int internalServiceInvoiceId, int userId)
        {
            await _internalServiceInvoiceService.CancelAsync(internalServiceInvoiceId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]InternalServiceInvoice internalServiceInvoice)  
        {
            switch (internalServiceInvoice.Action)
            {
                case "submit": await _internalServiceInvoiceService.SubmitAsync(internalServiceInvoice.InternalServiceInvoiceId, internalServiceInvoice.ModifiedUserId.Value); break;
                case "reverse": await _internalServiceInvoiceService.ReverseAsync(internalServiceInvoice.InternalServiceInvoiceId, internalServiceInvoice.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}