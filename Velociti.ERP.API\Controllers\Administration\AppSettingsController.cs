﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [ApiController]
    public class AppSettingsController : ControllerBase
    {
        private readonly IAppSettingsService _appSettingsService;

        public AppSettingsController(IAppSettingsService appSettingsService)
        {
            _appSettingsService = appSettingsService;
        }

        [HttpGet]
        [Route("ValidateTouchScreenPIN/company/{companyId}/pin/{pin}")]
        public async Task<IActionResult> ValidateTouchScreenPIN(int companyId, string pin)
        {
            var isValid = await _appSettingsService.ValidateTouchScreenPINAsync(pin, companyId);

            return Ok(isValid);
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _appSettingsService.GetAllAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]AppSetting appSetting)
        {
            if (appSetting == null)
                return BadRequest();

            await _appSettingsService.SaveAsync(appSetting);

            return Ok();
        }
    }
}