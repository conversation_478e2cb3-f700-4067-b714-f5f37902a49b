﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_SalesOrders", Schema = "sales")]
    public partial class ISalesOrder
    {
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime DocDate { get; set; }
        [StringLength(50)]
        public string CustomerName { get; set; }
        [StringLength(50)]
        public string ProductCode { get; set; }
        [StringLength(100)]
        public string ProductName { get; set; }
        [Column("RequestingUOM")]
        [StringLength(50)]
        public string RequestingUom { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [StringLength(250)]
        public string Error { get; set; }
    }
}
