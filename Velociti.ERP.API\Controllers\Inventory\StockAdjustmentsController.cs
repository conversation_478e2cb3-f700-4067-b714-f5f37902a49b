﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{ 
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class StockAdjustmentsController : ControllerBase  
    {
        private readonly IStockAdjustmentService _stockAdjustmentService;    

        public StockAdjustmentsController(IStockAdjustmentService stockAdjustmentService)  
        {
            _stockAdjustmentService = stockAdjustmentService;
        }

        [HttpGet]
        [Route("Single/{stockAdjustmentId}")]
        public async Task<IActionResult> FindById(int stockAdjustmentId) 
        {
            return Ok(await _stockAdjustmentService.FindByIdAsync(stockAdjustmentId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _stockAdjustmentService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _stockAdjustmentService.GetForApprovalAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]StockAdjustment stockAdjustment)
        {
            await _stockAdjustmentService.SaveAsync(stockAdjustment);

            return Ok();
        }

        [HttpDelete]
        [Route("{stockAdjustmentId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int stockAdjustmentId, int userId)  
        {
            await _stockAdjustmentService.CancelAsync(stockAdjustmentId, userId);  

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]StockAdjustment stockAdjustment)
        {
            switch (stockAdjustment.Action)
            {
                case "submit": await _stockAdjustmentService.SubmitAsync(stockAdjustment.StockAdjustmentId, stockAdjustment.ModifiedUserId.Value); break;
                case "send for approval": await _stockAdjustmentService.SendForApprovalAsync(stockAdjustment.StockAdjustmentId, stockAdjustment.CompanyId.Value, stockAdjustment.ModifiedUserId.Value); break;
                case "update work flow": await _stockAdjustmentService.UpdateWorkFlowStatusAsync(stockAdjustment); break;
            }

            return Ok();
        }
    }
}