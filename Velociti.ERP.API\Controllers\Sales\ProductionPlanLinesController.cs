﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductionPlanLinesController : ControllerBase
    {
        private readonly IProductionPlanLineService _productionPlanLineService;
        private readonly IProductionPlanService _productionPlanService;

        public ProductionPlanLinesController(IProductionPlanLineService productionPlanLineService, IProductionPlanService productionPlanService)
        {
            _productionPlanLineService = productionPlanLineService;
            _productionPlanService = productionPlanService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(string action, DateTime startDate, DateTime endDate, int companyId)
        {
            return action switch
            {
                "Active" => Ok(await _productionPlanLineService.GetActiveAllAsync(startDate, endDate, companyId)),
                "Headers" => Ok(await _productionPlanLineService.GetHeadersAsync(companyId, startDate, endDate)),

                _ => BadRequest(),
            };
        }

        [HttpGet]
        [Route("SendDailyPlan")]
        public async Task<IActionResult> SendDailyPlan(DateTime startDate, int companyId, int userId)
        {
            DateTime endDate = startDate.AddHours(24);

            var result = await _productionPlanLineService.GetProductionPlanLinesAsync(companyId, userId, startDate, endDate, false);

            //send new records
            string serviceUrl = "http://203.115.11.66:3001/activeschedules/create";
            using (var httpClient = new HttpClient())
            {
                foreach (var dailyPlan in result)
                {
                    var content = new StringContent(JsonConvert.SerializeObject(dailyPlan), System.Text.Encoding.UTF8, "application/json");

                    HttpResponseMessage httpResult = httpClient.PostAsync(serviceUrl, content).Result;

                    string resultContent = httpResult.Content.ReadAsStringAsync().Result;

                    if (!httpResult.IsSuccessStatusCode)
                    {
                        throw new Exception(resultContent);
                    }

                    var authResponse = JsonConvert.DeserializeObject<ResponseDTO>(resultContent);
                    if (authResponse.Status.Equals("success"))
                    {
                        await _productionPlanService.UpdateSentDateAsync(Convert.ToInt32(authResponse.Serieal), userId);
                    }
                    else
                    {
                        throw new Exception("Failed to send:" + authResponse.Serieal + ", Error:" + authResponse.Status);
                    }

                }
            }


            //send changed records
            result = await _productionPlanLineService.GetUpdatedProductionPlanLinesAsync(companyId, userId, startDate, endDate);

            serviceUrl = "http://203.115.11.66:3001/activeschedules/update";
            using (var httpClient = new HttpClient())
            {
                foreach (var dailyPlan in result)
                {
                    var content = new StringContent(JsonConvert.SerializeObject(dailyPlan), System.Text.Encoding.UTF8, "application/json");

                    HttpResponseMessage httpResult = httpClient.PutAsync(serviceUrl, content).Result;

                    string resultContent = httpResult.Content.ReadAsStringAsync().Result;

                    if (!httpResult.IsSuccessStatusCode)
                    {
                        throw new Exception(resultContent);
                    }

                    var authResponse = JsonConvert.DeserializeObject<ResponseDTO>(resultContent);
                    if (authResponse.Status.Equals("success"))
                    {
                        await _productionPlanService.UpdateSentDateAsync(Convert.ToInt32(authResponse.Serieal), userId);
                    }
                    else
                    {
                        throw new Exception("Failed to send:" + authResponse.Serieal + ", Error:" + authResponse.Status);
                    }

                }
            }

            return Ok();
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("GetDailyPlanByMachineAndProduct/machine/{machineId}/product/{productId}")]
        public async Task<IActionResult> GetDailyPlanByMachineAndProduct(int machineId, int productId)
        {
            //DateTime startDate = DateTime.Today.AddMonths(-1);
            //DateTime endDate = DateTime.Today.AddHours(24);

            DateTime startDate = DateTime.Today;
            DateTime endDate = startDate.AddHours(24);

            var result = await _productionPlanLineService.GetByMachineAndProductAsync(startDate, endDate, machineId, productId);

            return Ok(result);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("GetDailyPlanByMachine/machine/{machineId}")]
        public async Task<IActionResult> GetDailyPlanByMachine(int machineId)
        {
            DateTime startDate = DateTime.Today;

            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, DateTime.Now.Hour, DateTime.Now.Minute, 0);
            startDate = startDate.AddHours(-12);
            DateTime endDate = startDate.AddHours(24);

            var result = await _productionPlanLineService.GetByMachineForBarcodePrintAsync(startDate, endDate, machineId);

            return Ok(result);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("GetDailyPlanForBarcodeScan/company/{companyId}")]
        public async Task<IActionResult> GetDailyPlanForBarcodeScan(int companyId)
        {
            DateTime startDate = DateTime.Today;

            startDate = new DateTime(startDate.Year, startDate.Month, startDate.Day, DateTime.Now.Hour, DateTime.Now.Minute, 0);
            startDate = startDate.AddHours(-12);
            DateTime endDate = startDate.AddHours(24);

            var result = await _productionPlanLineService.GetForBarcodeScanAsync(startDate, endDate, companyId);

            return Ok(result);
        }

        [HttpGet]
        [Route("OrderLine/{salesOrderLineId}")]
        public async Task<IActionResult> GetByOrderLine(int salesOrderLineId)
        {
            return Ok(await _productionPlanLineService.GetByOrderLineIdAsync(salesOrderLineId));
        }

        [HttpGet]
        [Route("SalesOrderLines/{salesOrderLineIds}/company/{companyId}")]
        public async Task<IActionResult> GetSelected(string salesOrderLineIds, int companyId)
        {
            string[] orderlineIdArray = salesOrderLineIds.Split(',');
            var list = orderlineIdArray.Select(p => int.Parse(p)).ToList();

            return Ok(await _productionPlanLineService.GetBySalesOrderLineIdsAsync(list, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save(ProductionPlanLine productionPlanLine)
        {
            //this had to be done cause the time returned from scheduler was GMT time
            productionPlanLine.StartDate = DateTime.Parse(productionPlanLine.StartDateString);

            if (productionPlanLine.CopyNo == default)
                await _productionPlanLineService.SaveAsync(productionPlanLine);
            else
                await _productionPlanLineService.SaveMultipleCopiesAsync(productionPlanLine);

            return Ok();
        }

        [HttpPut]
        [AllowAnonymous]
        public async Task<IActionResult> Update(ProductionPlanLine productionPlanLine)
        {
            try
            {
                switch (productionPlanLine.Action)
                {
                    case "Update Barcode Printed Date": await _productionPlanLineService.UpdateBarcodePrintedDateAsync(productionPlanLine); break;
                    case "Update Actual End Date": await _productionPlanLineService.UpdateActualEndDateAsync(productionPlanLine); break;
                }

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpDelete]
        [Route("{id}/User/{userId}")]
        public async Task<IActionResult> Delete(int id, int userId)
        {
            var productionPlan = await _productionPlanLineService.DeleteAsync(id, userId);

            //send update to POMS
            //if (productionPlan != null && productionPlan.ExpiryDate.HasValue)
            //{
            //    var serviceUrl = "http://203.115.11.66:3001/activeschedules/delete";
            //    using (var httpClient = new HttpClient())
            //    {
            //        var jsonObject = new
            //        {
            //            task_serial_no = productionPlan.ProductionPlanNumber,
            //            serial_number = productionPlan.ProductionPlanId.ToString()
            //        };

            //        var content = new StringContent(JsonConvert.SerializeObject(jsonObject), System.Text.Encoding.UTF8, "application/json");

            //        HttpResponseMessage httpResult = httpClient.SendAsync(
            //           new HttpRequestMessage(HttpMethod.Delete, serviceUrl)
            //           {
            //               Content = content
            //           }).Result;

            //        string resultContent = httpResult.Content.ReadAsStringAsync().Result;

            //        if (!httpResult.IsSuccessStatusCode)
            //        {
            //            throw new Exception(resultContent);
            //        }

            //        var authResponse = JsonConvert.DeserializeObject<ResponseDTO>(resultContent);
            //        if (authResponse.Status.Equals("success"))
            //        {
            //            await _productionPlanService.UpdateSentDateAsync(Convert.ToInt32(authResponse.Serieal), userId);
            //        }
            //        else
            //        {
            //            throw new Exception("Failed to delete:" + authResponse.Serieal + ", Error:" + authResponse.Status);
            //        }

            //    }
            //}

            return Ok();
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Barcode/{barcode}/Company/{companyId}")]
        public async Task<IActionResult> GetByBarcode(string barcode, int companyId)
        {
            return Ok(await _productionPlanLineService.GetByBarcodeAsync(barcode, companyId));
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("UpdateQualityControlStatus")]
        public async Task<IActionResult> UpdateQualityControlStatus([FromBody]Domain.Entities.Production.QualityControl qualityControl)
        {
            try
            {
                await _productionPlanLineService.UpdateQualityControlStatusTC01Async(qualityControl);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {

                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("UpdateQualityControlStatus02")]
        public async Task<IActionResult> UpdateQualityControlStatus02([FromBody]Domain.Entities.Production.QualityControl qualityControl)
        {
            try
            {
                await _productionPlanLineService.UpdateQualityControlStatusTC02Async(qualityControl);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {

                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpPost]
        [AllowAnonymous]
        [Route("CheckIfApprovalIsNeeded")]
        public async Task<IActionResult> CheckIfApprovalIsNeeded([FromBody]Domain.Entities.Production.QualityControl qualityControl)
        {
            var isApprovalNeeded = await _productionPlanLineService.CheckIfApprovalIsNeededAsync(qualityControl);

            return Ok(isApprovalNeeded);
        }
    }
}