﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("TxnWorkFlowLines", Schema = "cmn")]
    public partial class TxnWorkFlowLine
    {
        [Key]
        public int TxnWorkFlowLineId { get; set; }
        public int TxnWorkFlowId { get; set; }
        public int WorkFlowLineId { get; set; }
        public int SequenceNumber { get; set; }
        public int? DesignationId { get; set; }
        public int? UserId { get; set; }
        public byte? CombinationEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DesignationId))]
        [InverseProperty("TxnWorkFlowLines")]
        public virtual Designation Designation { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("TxnWorkFlowLines")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [ForeignKey(nameof(UserId))]
        [InverseProperty("TxnWorkFlowLines")]
        public virtual User User { get; set; }
        [ForeignKey(nameof(WorkFlowLineId))]
        [InverseProperty("TxnWorkFlowLines")]
        public virtual WorkFlowLine WorkFlowLine { get; set; }
    }
}
