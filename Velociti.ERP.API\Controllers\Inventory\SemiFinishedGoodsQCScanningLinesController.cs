﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]  
    public class SemiFinishedGoodsQCScanningLinesController : ControllerBase
    {
        private readonly ISemiFinishedGoodsQCScanningLineService _semiFinishedGoodsQCScanningLineService;  

        public SemiFinishedGoodsQCScanningLinesController(ISemiFinishedGoodsQCScanningLineService semiFinishedGoodsQCScanningLineService)      
        {
            _semiFinishedGoodsQCScanningLineService = semiFinishedGoodsQCScanningLineService;
        }

        [HttpGet]
        [Route("{semiFinishedGoodsQCScanningId}/company/{companyId}")]
        public async Task<IActionResult> Get(int semiFinishedGoodsQCScanningId, int companyId)
        {
            return Ok(await _semiFinishedGoodsQCScanningLineService.GetAsync(semiFinishedGoodsQCScanningId, companyId));
        }

        [HttpGet]
        [Route("{semiFinishedGoodsQCScanningId}/Product/{productId}/Company/{companyId}")]
        public async Task<IActionResult> Get(int semiFinishedGoodsQCScanningId, int productId, int companyId)
        {
            return Ok(await _semiFinishedGoodsQCScanningLineService.GetAllAsync(semiFinishedGoodsQCScanningId, productId, companyId));
        }
    }
}