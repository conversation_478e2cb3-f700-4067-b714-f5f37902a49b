﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("WorkstationCadres", Schema = "hr")]
    public partial class WorkstationCadre
    {
        [Key]
        public int WorkstationCadreId { get; set; }
        public int? CompanyId { get; set; }
        public int? MachineId { get; set; }
        public int? PressLineId { get; set; }
        public int EmployeeId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date { get; set; }
        public byte? ShiftEnum { get; set; }
        public byte? CategoryEnum { get; set; }
        public byte? TypeEnum { get; set; }
        public int? CurrentNumberOfWorkers { get; set; }
        public int? MaxNumberWorkers { get; set; }
        [StringLength(50)]
        public string WorkStationCadreCode { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("WorkstationCadres")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("WorkstationCadres")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(PressLineId))]
        [InverseProperty("WorkstationCadres")]
        public virtual PressLine PressLine { get; set; }
    }
}
