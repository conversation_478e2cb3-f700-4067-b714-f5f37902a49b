﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

using Microsoft.Extensions.Options;
using Velociti.ERP.Domain.Utils;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ShipmentQualityControlsController : ControllerBase
    {
        private readonly AppSettings _appSettings;
        private readonly IShipmentQualityControlService _shipmentQualityControlService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public ShipmentQualityControlsController(IOptions<AppSettings> appSettings,IShipmentQualityControlService shipmentQualityControlService, IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _appSettings = appSettings.Value;
            _shipmentQualityControlService = shipmentQualityControlService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }

        [HttpGet]
        [Route("Single/{shipmentQCId}")]
        public async Task<IActionResult> FindById(int shipmentQCId)
        {
            return Ok(await _shipmentQualityControlService.FindByIdAsync(shipmentQCId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _shipmentQualityControlService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]ShipmentQualityControl shipmentQualityControl)
        {
            switch (shipmentQualityControl.Action)
            {
                case "save": await _shipmentQualityControlService.SaveAsync(shipmentQualityControl); break;
                case "submit": await _shipmentQualityControlService.SubmitAsync(shipmentQualityControl.ShipmentQualityControlId, shipmentQualityControl.ModifiedUserId.Value); break;
                case "reverse": await _shipmentQualityControlService.ReverseAsync(shipmentQualityControl.ShipmentQualityControlId, shipmentQualityControl.ModifiedUserId.Value); break;
                case "reverseCosting": await _shipmentQualityControlService.ReverseForCostingAsync(shipmentQualityControl.ShipmentQualityControlId, shipmentQualityControl.ModifiedUserId.Value); break;
                case "convert": return Ok(await _goodsDispatchNoteService.ConvertAsync(shipmentQualityControl.ShipmentQualityControlId
                                                                                     , Domain.Entities.Administration.Module.DocumentType.ShipmentQC
                                                                                     , shipmentQualityControl.ModifiedUserId));
                case "submitForCosting": return Ok(await _shipmentQualityControlService.SubmitForCostingAsync(shipmentQualityControl.ShipmentQualityControlId
                                                                                                             , shipmentQualityControl.ModifiedUserId.Value));
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/User/{userId}")]
        public async Task<IActionResult> Cancel(int id, int userId)
        {
            await _shipmentQualityControlService.CancelAsync(id, userId);

            return Ok();
        }
    }
}