﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class QCParameterMappingsController : ControllerBase
    {
        private readonly IQCParameterMappingService _qCParameterMappingService;

        public QCParameterMappingsController(IQCParameterMappingService qCParameterMappingService)
        {
            _qCParameterMappingService = qCParameterMappingService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> Get(int companyId)
        {
            return Ok(await _qCParameterMappingService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/Product/{productId}/{companyId}")]
        public async Task<IActionResult> GetShortListByProduct(int productId, int companyId)
        {
            var list = await _qCParameterMappingService.GetShortListByProductAsync(productId, companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Parameter/{parameterId}/Product/{productId}/{companyId}")]
        public async Task<IActionResult> GetShortList(int parameterId, int productId, int companyId)
        {
            var list = await _qCParameterMappingService.GetByParameterAndProductAsync(parameterId, productId);

            return Ok(list);
        }

        [HttpDelete]
        [Route("{parameterId}/Product/{productId}/User/{modifiedUserId}")]
        public async Task<IActionResult> ToggleActivation(int parameterId, int productId, int modifiedUserId)
        {
            await _qCParameterMappingService.ToggleActivationAsync(parameterId, productId, modifiedUserId);

            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]QcparameterMapping qcparameterMapping)  
        {
            if (qcparameterMapping == null)
                return BadRequest();

            await _qCParameterMappingService.SaveAsync(qcparameterMapping);

            return Ok();
        }
    }
}