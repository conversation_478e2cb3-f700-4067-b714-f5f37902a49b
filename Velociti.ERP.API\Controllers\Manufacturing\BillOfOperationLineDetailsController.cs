﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class BillOfOperationLineDetailsController : ControllerBase  
    {
        private readonly IBillOfOperationLineDetailService _billOfOperationLineDetailService;

        public BillOfOperationLineDetailsController(IBillOfOperationLineDetailService billOfOperationLineDetailService)
        {
            _billOfOperationLineDetailService = billOfOperationLineDetailService;
        }

        [HttpGet]
        [Route("{billOfOperationLineId}")]
        public async Task<IActionResult> Get(int billOfOperationLineId)  
        {
            return Ok(await _billOfOperationLineDetailService.GetAsync(billOfOperationLineId));
        }

        [HttpPost]
        public async Task<IActionResult> GetBillOfOperationLineDetails([FromBody]BillOfOperationLineDetail billOfOperationLineDetail)
        {
            await _billOfOperationLineDetailService.SaveAsync(billOfOperationLineDetail);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _billOfOperationLineDetailService.DeleteAsync(id);

            return Ok();
        }
    }
}