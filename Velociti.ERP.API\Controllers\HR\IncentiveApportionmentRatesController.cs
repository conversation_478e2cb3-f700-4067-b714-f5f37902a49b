﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.HR;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Entities.Sales;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class IncentiveApportionmentRatesController : ControllerBase
    {
        private readonly IIncentiveApportionmentRatesService _incentiveApportionmentRatesService;

        public IncentiveApportionmentRatesController(IIncentiveApportionmentRatesService incentiveApportionmentRatesService)
        {
            _incentiveApportionmentRatesService = incentiveApportionmentRatesService;
        }

        [HttpGet]
        [Route("Single/{incentiveApportionmentRatesId}")]
        public async Task<IActionResult> FindById(int incentiveApportionmentRatesId)
        {
            return Ok(await _incentiveApportionmentRatesService.FindByIdAsync(incentiveApportionmentRatesId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            return Ok(await _incentiveApportionmentRatesService.GetAllAsync());
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]IncentiveApportionmentRates incentiveApportionmentRates)
        {
            switch (incentiveApportionmentRates.Action)
            {
                case "save": await _incentiveApportionmentRatesService.SaveAsync(incentiveApportionmentRates); break;
                case "submit": await _incentiveApportionmentRatesService.UpdateAsync(incentiveApportionmentRates); break;
            }

            return Ok();
        }
    }
}