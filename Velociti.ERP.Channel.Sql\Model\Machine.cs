﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Machines", Schema = "man")]
    public partial class Machine
    {
        public Machine()
        {
            BillOfOperationLines = new HashSet<BillOfOperationLine>();
            FixedAssets = new HashSet<FixedAsset>();
            MachineLines = new HashSet<MachineLine>();
            MachineMoulds = new HashSet<MachineMould>();
            MachineProductComponents = new HashSet<MachineProductComponent>();
            MachineVisualPlanLines = new HashSet<MachineVisualPlanLine>();
            ProductionOrderLines = new HashSet<ProductionOrderLine>();
            ProductionPlanLines = new HashSet<ProductionPlanLine>();
            ProductionPlans = new HashSet<ProductionPlan>();
            ProfileBuildingWarehouseProducts = new HashSet<ProfileBuildingWarehouseProduct>();
            ProfileBuildings = new HashSet<ProfileBuilding>();
            TouchScreenHistories = new HashSet<TouchScreenHistory>();
            WorkstationCadres = new HashSet<WorkstationCadre>();
        }

        [Key]
        public int MachineId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string MachineCode { get; set; }
        [StringLength(255)]
        public string MachineName { get; set; }
        public byte? CategoryEnum { get; set; }
        [Column(TypeName = "money")]
        public decimal? StandardRate { get; set; }
        [Column(TypeName = "money")]
        public decimal? LabourRate { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? HandlingTimePerHour { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? HandlingTimePerUnit { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? DayLightCount { get; set; }
        public int? PressLineId { get; set; }
        public int? MaxNumberWorkers { get; set; }
        public bool? IsBarcodePrinting { get; set; }
        public bool? IsProfile { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Machines")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(PressLineId))]
        [InverseProperty("Machines")]
        public virtual PressLine PressLine { get; set; }
        [InverseProperty(nameof(BillOfOperationLine.Machine))]
        public virtual ICollection<BillOfOperationLine> BillOfOperationLines { get; set; }
        [InverseProperty(nameof(FixedAsset.Machine))]
        public virtual ICollection<FixedAsset> FixedAssets { get; set; }
        [InverseProperty(nameof(MachineLine.Machine))]
        public virtual ICollection<MachineLine> MachineLines { get; set; }
        [InverseProperty(nameof(MachineMould.Machine))]
        public virtual ICollection<MachineMould> MachineMoulds { get; set; }
        [InverseProperty(nameof(MachineProductComponent.Machine))]
        public virtual ICollection<MachineProductComponent> MachineProductComponents { get; set; }
        [InverseProperty(nameof(MachineVisualPlanLine.Machine))]
        public virtual ICollection<MachineVisualPlanLine> MachineVisualPlanLines { get; set; }
        [InverseProperty(nameof(ProductionOrderLine.Machine))]
        public virtual ICollection<ProductionOrderLine> ProductionOrderLines { get; set; }
        [InverseProperty(nameof(ProductionPlanLine.Machine))]
        public virtual ICollection<ProductionPlanLine> ProductionPlanLines { get; set; }
        [InverseProperty(nameof(ProductionPlan.Machine))]
        public virtual ICollection<ProductionPlan> ProductionPlans { get; set; }
        [InverseProperty(nameof(ProfileBuildingWarehouseProduct.Machine))]
        public virtual ICollection<ProfileBuildingWarehouseProduct> ProfileBuildingWarehouseProducts { get; set; }
        [InverseProperty(nameof(ProfileBuilding.Machine))]
        public virtual ICollection<ProfileBuilding> ProfileBuildings { get; set; }
        [InverseProperty(nameof(TouchScreenHistory.Machine))]
        public virtual ICollection<TouchScreenHistory> TouchScreenHistories { get; set; }
        [InverseProperty(nameof(WorkstationCadre.Machine))]
        public virtual ICollection<WorkstationCadre> WorkstationCadres { get; set; }
    }
}
