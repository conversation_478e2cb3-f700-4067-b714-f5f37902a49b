﻿using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Data;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{

    public class TransactionTypesRepository : ITransactionTypesRepository
    {
        private readonly MarangoniERPContext _context;

        public TransactionTypesRepository(MarangoniERPContext context)
        {
            _context = context;
        }
         
        public async Task<IEnumerable<Domain.Entities.Common.ShortList>> GetShortListAsync(bool isAll)
        {
            int isAllSupported = isAll ? 1 : 0;

            using (SqlConnection connection = new SqlConnection(_context.Database.GetDbConnection().ConnectionString))
            {
                var result = await connection.QueryAsync<Domain.Entities.Common.ShortList>("GetTransactionTypes",
                        new { IsAllSupported = isAllSupported },
                        commandType: CommandType.StoredProcedure);

                return result;
            }
        }
    }

}