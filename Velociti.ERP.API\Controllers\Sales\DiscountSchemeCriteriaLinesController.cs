﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class DiscountSchemeCriteriaLinesController : ControllerBase
    {
        private readonly IDiscountSchemeCriteriaLinesService _discountSchemeCriteriaLinesService;

        public DiscountSchemeCriteriaLinesController(IDiscountSchemeCriteriaLinesService discountSchemeCriteriaLinesService)
        {
            _discountSchemeCriteriaLinesService = discountSchemeCriteriaLinesService;
        }

        [HttpGet]
        [Route("{discountSchemeId}")]
        public async Task<IActionResult> Get(int discountSchemeId)
        {
            return Ok(await _discountSchemeCriteriaLinesService.GetAsync(discountSchemeId));
        }
    }
}