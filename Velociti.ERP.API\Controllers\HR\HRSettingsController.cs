﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.HR;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Entities.Sales;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class HRSettingsController : ControllerBase
    {
        private readonly IHRSettingsService _hrSettingsService;

        public HRSettingsController(IHRSettingsService hrSettingsService)
        {
            _hrSettingsService = hrSettingsService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _hrSettingsService.GetAllAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]HRSettings HRSettings)
        {
            switch (HRSettings.Action)
            {
                case "submit": await _hrSettingsService.UpdateAsync(HRSettings); break;
            }

            return Ok();
        }
    }
}