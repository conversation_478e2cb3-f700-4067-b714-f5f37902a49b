﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net5.0</TargetFramework>
    <UserSecretsId>870399eb-732d-4d7b-985b-f3545edf460e</UserSecretsId>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Controllers\Administration\TaxGroupsController.cs" />
    <Compile Remove="Controllers\Sales\CustomerController.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.102.2" />
    <PackageReference Include="DevExpress.Xpo" Version="25.1.3" />
    <PackageReference Include="DevExtreme.AspNet.Data" Version="2.5.1" />
    <PackageReference Include="EPPlus" Version="4.5.3.2" />
    <PackageReference Include="log4net" Version="3.0.4" />
    <PackageReference Include="Microsoft.AspNet.Identity.Owin" Version="2.2.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="2.2.0" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.3">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="3.0.0-rc1.19456.10" />
    <PackageReference Include="Microsoft.Owin" Version="4.2.2" />
    <PackageReference Include="Microsoft.Owin.Security" Version="4.2.2" />
    <PackageReference Include="Microsoft.Owin.Security.Cookies" Version="4.2.2" />
    <PackageReference Include="Microsoft.ReportViewer.Runtime.Common" Version="12.0.2402.15" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="3.1.4" />
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="NLog.Web.AspNetCore" Version="4.9.0" />
    <PackageReference Include="NLog" Version="4.6.7" />
    <PackageReference Include="SixLabors.Fonts" Version="1.0.0" />
    <PackageReference Include="System.Data.SqlClient" Version="4.8.6" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Velociti.ERP.Channel.Mock\Velociti.ERP.Channel.Mock.csproj" />
    <ProjectReference Include="..\Velociti.ERP.Channel.Sql\Velociti.ERP.Channel.Sql.csproj" />
    <ProjectReference Include="..\Velociti.ERP.Domain\Velociti.ERP.Domain.csproj" />
  </ItemGroup>

</Project>