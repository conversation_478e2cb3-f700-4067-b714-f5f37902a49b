﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PettyCashes", Schema = "fin")]
    public partial class PettyCash
    {
        public PettyCash()
        {
            InversePettyCashIou = new HashSet<PettyCash>();
            PettyCashLines = new HashSet<PettyCashLine>();
        }

        [Key]
        public int PettyCashId { get; set; }
        public byte? TypeEnum { get; set; }
        public int? CompanyId { get; set; }
        public int? AccountId { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        public int? EmployeeId { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }
        [Column("PettyCashIOUId")]
        public int? PettyCashIouid { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DocDate { get; set; }
        public int? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [StringLength(32)]
        public string DocNumber { get; set; }
        [StringLength(32)]
        public string RefDocNumber { get; set; }
        public int? SupplierId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("PettyCashes")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PettyCashes")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("PettyCashes")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("PettyCashes")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(PettyCashIouid))]
        [InverseProperty(nameof(PettyCash.InversePettyCashIou))]
        public virtual PettyCash PettyCashIou { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("PettyCashes")]
        public virtual Supplier Supplier { get; set; }
        [InverseProperty(nameof(PettyCash.PettyCashIou))]
        public virtual ICollection<PettyCash> InversePettyCashIou { get; set; }
        [InverseProperty(nameof(PettyCashLine.PettyCash))]
        public virtual ICollection<PettyCashLine> PettyCashLines { get; set; }
    }
}
