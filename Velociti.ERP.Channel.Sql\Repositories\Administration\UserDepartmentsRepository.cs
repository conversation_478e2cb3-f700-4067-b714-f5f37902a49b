﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using System.Linq;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class UserDepartmentsRepository : IUserDepartmentsRepository
    {
        private readonly MarangoniERPContext _context;

        public UserDepartmentsRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<UserDepartment>> GetAssignedDepartmentsAsync(int userId)
        {
            return await _context.UserDepartments.Include(p => p.Department).Include(p => p.Department.Company).Where(p => p.UserId == userId).ToListAsync();
        }

        public async Task<UserDepartment> GetDefaultDepartmentAsync(int userId)
        {
            return await _context.UserDepartments.Include(p => p.Department)
                                               .Where(p => p.UserId == userId && p.IsDefault == true).FirstOrDefaultAsync();
        }

        public async Task SaveAsync(int companyId, IEnumerable<UserDepartment> userDepartments)
        {
            try
            {
                foreach(var row in userDepartments)
                {
                    if (row.IsDefault == true)
                    {
                        var defaultDepartment = await _context.UserDepartments.SingleOrDefaultAsync(p => p.UserId == row.UserId && p.Department.CompanyId == companyId && p.IsDefault == true);
                        if(defaultDepartment != null)
                        {
                            defaultDepartment.IsDefault = false;
                            defaultDepartment.ModifiedUserId = row.ModifiedUserId;
                        }
                    }

                    row.CreationDate = DateTime.Now;
                    await _context.UserDepartments.AddAsync(row);
                }

                await _context.SaveChangesAsync();
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        public async Task ToggleDefaultAsync(int userDepartmentId)
        {
            var record = await _context.UserDepartments.Include(p => p.Department).FirstAsync(c => c.UserDepartmentId == userDepartmentId);

            record.IsDefault = !record.IsDefault;

            if(record.IsDefault == true)
            {
                var defaultDepartment = await _context.UserDepartments.SingleOrDefaultAsync(p => p.UserId == record.UserId && p.Department.CompanyId == record.Department.CompanyId && p.IsDefault == true);
                if (defaultDepartment != null)
                {
                    defaultDepartment.IsDefault = false;
                }
            }
            await _context.SaveChangesAsync();
        }

        public async Task ToggleActivationAsync(int userDepartmentId)
        {
            var record = await _context.UserDepartments.FirstAsync(c => c.UserDepartmentId == userDepartmentId);
            if (record.ExpiryDate == null && record.IsDefault == true)
                throw new Exception("Cannot deactivate default record.");

            record.ExpiryDate = record.ExpiryDate == null ? DateTime.Now : (DateTime?)null;

            await _context.SaveChangesAsync();
        }
    }
}
