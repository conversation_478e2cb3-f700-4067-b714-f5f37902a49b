﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductionPlanBarcodes", Schema = "sales")]
    public partial class ProductionPlanBarcode
    {
        [Key]
        public int Id { get; set; }
        public int? CompanyId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Date { get; set; }
        public int? SequenceNumber { get; set; }
        [StringLength(50)]
        public string Barcode { get; set; }
        public bool? IsPrinted { get; set; }
        public bool? IsSecondGrade { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ProductionPlanBarcodes")]
        public virtual Company Company { get; set; }
    }
}
