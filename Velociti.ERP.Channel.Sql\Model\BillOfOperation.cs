﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BillOfOperations", Schema = "man")]
    public partial class BillOfOperation
    {
        public BillOfOperation()
        {
            BillOfOperationLines = new HashSet<BillOfOperationLine>();
            MachineVisualPlanLines = new HashSet<MachineVisualPlanLine>();
            ProductionOrders = new HashSet<ProductionOrder>();
            ProductionPlanLines = new HashSet<ProductionPlanLine>();
        }

        [Key]
        public int BillOfOperationId { get; set; }
        public int? CompanyId { get; set; }
        [Column("BOOCode")]
        [StringLength(50)]
        public string Boocode { get; set; }
        [Column("BOODescription")]
        [StringLength(255)]
        public string Boodescription { get; set; }
        public byte? CategoryEnum { get; set; }
        public int? ProductId { get; set; }
        public int? BillOfMaterialId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfMaterialId))]
        [InverseProperty("BillOfOperations")]
        public virtual BillOfMaterial BillOfMaterial { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("BillOfOperations")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("BillOfOperations")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(BillOfOperationLine.BillOfOperation))]
        public virtual ICollection<BillOfOperationLine> BillOfOperationLines { get; set; }
        [InverseProperty(nameof(MachineVisualPlanLine.BillOfOperation))]
        public virtual ICollection<MachineVisualPlanLine> MachineVisualPlanLines { get; set; }
        [InverseProperty(nameof(ProductionOrder.BillOfOperation))]
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; }
        [InverseProperty(nameof(ProductionPlanLine.BillOfOperation))]
        public virtual ICollection<ProductionPlanLine> ProductionPlanLines { get; set; }
    }
}
