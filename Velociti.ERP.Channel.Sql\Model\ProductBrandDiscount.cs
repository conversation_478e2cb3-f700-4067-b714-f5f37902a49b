﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductBrandDiscounts", Schema = "inv")]
    public partial class ProductBrandDiscount
    {
        [Key]
        public int ProductBrandDiscountId { get; set; }
        public int CompanyId { get; set; }
        public int? ProductBrandId { get; set; }
        [Column("StockCountSheetDiscountPCT", TypeName = "decimal(18, 4)")]
        public decimal StockCountSheetDiscountPct { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ProductBrandDiscounts")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductBrandId))]
        [InverseProperty(nameof(SupportData.ProductBrandDiscounts))]
        public virtual SupportData ProductBrand { get; set; }
    }
}
