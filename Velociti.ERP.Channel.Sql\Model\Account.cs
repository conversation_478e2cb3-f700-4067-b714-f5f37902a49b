﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Accounts", Schema = "fin")]
    public partial class Account
    {
        public Account()
        {
            BankAdjustments = new HashSet<BankAdjustment>();
            BankReconciliations = new HashSet<BankReconciliation>();
            CustomerPaymentBankAccounts = new HashSet<CustomerPayment>();
            CustomerPaymentCashAccounts = new HashSet<CustomerPayment>();
            EmployeeAccounts = new HashSet<EmployeeAccount>();
            GoodsReceiveNotes = new HashSet<GoodsReceiveNote>();
            InboundReceipts = new HashSet<InboundReceipt>();
            LoadingPlans = new HashSet<LoadingPlan>();
            OutboundPayments = new HashSet<OutboundPayment>();
            PayBooks = new HashSet<PayBook>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            PettyCashes = new HashSet<PettyCash>();
            PurchaseOrders = new HashSet<PurchaseOrder>();
            Quotations = new HashSet<Quotation>();
            SalesInvoices = new HashSet<SalesInvoice>();
            SalesReturns = new HashSet<SalesReturn>();
        }

        [Key]
        public int AccountId { get; set; }
        public int? CompanyId { get; set; }
        public int? EmployeeId { get; set; }
        public bool? IsGeneral { get; set; }
        public byte? TypeEnum { get; set; }
        [StringLength(50)]
        public string AccountName { get; set; }
        public int? CurrencyId { get; set; }
        [Column("IOUEnabled")]
        public bool? Iouenabled { get; set; }
        [StringLength(50)]
        public string BankCode { get; set; }
        [StringLength(50)]
        public string BankAccountNumber { get; set; }
        [StringLength(50)]
        public string BankBranch { get; set; }
        public byte? AccountTypeEnum { get; set; }
        public bool? MultiCurrencyEnabled { get; set; }
        [Column("ODLimit", TypeName = "money")]
        public decimal? Odlimit { get; set; }
        [Column("SWIFTCode")]
        [StringLength(50)]
        public string Swiftcode { get; set; }
        [StringLength(255)]
        public string StreetAddress { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Accounts")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("Accounts")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("Accounts")]
        public virtual Employee Employee { get; set; }
        [InverseProperty(nameof(BankAdjustment.Account))]
        public virtual ICollection<BankAdjustment> BankAdjustments { get; set; }
        [InverseProperty(nameof(BankReconciliation.Account))]
        public virtual ICollection<BankReconciliation> BankReconciliations { get; set; }
        [InverseProperty(nameof(CustomerPayment.BankAccount))]
        public virtual ICollection<CustomerPayment> CustomerPaymentBankAccounts { get; set; }
        [InverseProperty(nameof(CustomerPayment.CashAccount))]
        public virtual ICollection<CustomerPayment> CustomerPaymentCashAccounts { get; set; }
        [InverseProperty(nameof(EmployeeAccount.Account))]
        public virtual ICollection<EmployeeAccount> EmployeeAccounts { get; set; }
        [InverseProperty(nameof(GoodsReceiveNote.Account))]
        public virtual ICollection<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        [InverseProperty(nameof(InboundReceipt.Account))]
        public virtual ICollection<InboundReceipt> InboundReceipts { get; set; }
        [InverseProperty(nameof(LoadingPlan.Account))]
        public virtual ICollection<LoadingPlan> LoadingPlans { get; set; }
        [InverseProperty(nameof(OutboundPayment.Account))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
        [InverseProperty(nameof(PayBook.Account))]
        public virtual ICollection<PayBook> PayBooks { get; set; }
        [InverseProperty(nameof(PaymentAdvice.Account))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(PettyCash.Account))]
        public virtual ICollection<PettyCash> PettyCashes { get; set; }
        [InverseProperty(nameof(PurchaseOrder.Account))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        [InverseProperty(nameof(Quotation.Account))]
        public virtual ICollection<Quotation> Quotations { get; set; }
        [InverseProperty(nameof(SalesInvoice.Account))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
        [InverseProperty(nameof(SalesReturn.Account))]
        public virtual ICollection<SalesReturn> SalesReturns { get; set; }
    }
}
