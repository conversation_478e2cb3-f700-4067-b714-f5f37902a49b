﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class MaterialRequisitionNoteLinesController : ControllerBase
    {
        private readonly IMaterialRequisitionNoteLineService _materialRequisitionNoteLineService;

        public MaterialRequisitionNoteLinesController(IMaterialRequisitionNoteLineService materialRequisitionNoteLineService)
        {
            _materialRequisitionNoteLineService = materialRequisitionNoteLineService;
        }

        [HttpGet]
        [Route("{mrnId}/company/{companyId}")]
        public async Task<IActionResult> Get(int mrnId, int companyId)
        {
            return Ok(await _materialRequisitionNoteLineService.GetAsync(mrnId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> GetPurchaseRequisitionNoteLine([FromBody]MaterialRequisitionNoteLine materialRequisitionNoteLine)
        {
            return Ok(await _materialRequisitionNoteLineService.AddMaterialRequisitionNoteLineAsync(materialRequisitionNoteLine));
        }
    }
}