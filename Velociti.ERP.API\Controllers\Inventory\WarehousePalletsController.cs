﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class WarehousePalletsController : ControllerBase
    {
        private readonly IWarehousePalletService _warehousePalletService;

        public WarehousePalletsController(IWarehousePalletService warehousePalletService)
        {
            _warehousePalletService = warehousePalletService;
        }

        [HttpGet]
        [Route("Barcode/{barcode}/Company/{companyId}")]
        public async Task<IActionResult> GetByBarcode(string barcode, int companyId)
        {
            return Ok(await _warehousePalletService.GetByBarcodeAsync(barcode, companyId));
        }

        [HttpGet]
        [Route("GetBarcodes/Company/{companyId}/quantity/{quantity}/location/{location}")]
        public async Task<IActionResult> GetBarcodes(int companyId, int quantity, string location)
        {
            return Ok(await _warehousePalletService.GenerateBarcodesAsync(companyId, quantity, location));
        }

        [HttpPost]
        [Route("Qty/{qty}/Company/{companyId}/User/{userId}")]
        public async Task<IActionResult> GeneratePalletBarcodesAsync(int qty, int companyId, int userId)
        {
            await _warehousePalletService.GeneratePalletBarcodesAsync(qty, companyId, userId);

            return Ok();
        }
    }
}