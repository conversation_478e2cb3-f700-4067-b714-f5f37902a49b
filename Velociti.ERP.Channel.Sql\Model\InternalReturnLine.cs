﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalReturnLines", Schema = "inv")]
    public partial class InternalReturnLine
    {
        [Key]
        public int InternalReturnLineId { get; set; }
        public int? InternalReturnId { get; set; }
        public int? ProductId { get; set; }
        public int? WarehouseId { get; set; }
        public int? FromWarehouseId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        public int? OnHandQuantity { get; set; }
        public int? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        public int? ReturnQuantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public bool? AllowStockAllocation { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.InternalReturnLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.InternalReturnLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(FromWarehouseId))]
        [InverseProperty(nameof(Warehous.InternalReturnLineFromWarehouses))]
        public virtual Warehous FromWarehouse { get; set; }
        [ForeignKey(nameof(InternalReturnId))]
        [InverseProperty("InternalReturnLines")]
        public virtual InternalReturn InternalReturn { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("InternalReturnLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.InternalReturnLineWarehouses))]
        public virtual Warehous Warehouse { get; set; }
    }
}
