﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class UserGroupsController : ControllerBase
    {
        private readonly IUserGroupService _userGroupService;

        public UserGroupsController(IUserGroupService userGroupService)
        {
            _userGroupService = userGroupService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            return Ok(await _userGroupService.GetAllAsync());
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]List<UserGroup> userGroups)
        {
            if (userGroups == null)
                return BadRequest();

            await _userGroupService.SaveAsync(userGroups);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Delete([FromBody]UserGroup userGroup)
        {
            await _userGroupService.DeleteAsync(userGroup);

            return Ok();
        }
    }
}