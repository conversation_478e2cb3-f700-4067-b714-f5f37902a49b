﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ManualJournalLines", Schema = "fin")]
    public partial class ManualJournalLine
    {
        public ManualJournalLine()
        {
            ManualJournalCostAllocations = new HashSet<ManualJournalCostAllocation>();
        }

        [Key]
        public int ManualJournalLineId { get; set; }
        public int? ManualJournalId { get; set; }
        public int? ChartOfAccountId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }
        [StringLength(255)]
        public string Narration { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChartOfAccountId))]
        [InverseProperty("ManualJournalLines")]
        public virtual ChartOfAccount ChartOfAccount { get; set; }
        [ForeignKey(nameof(ManualJournalId))]
        [InverseProperty("ManualJournalLines")]
        public virtual ManualJournal ManualJournal { get; set; }
        [InverseProperty(nameof(ManualJournalCostAllocation.ManualJournalLine))]
        public virtual ICollection<ManualJournalCostAllocation> ManualJournalCostAllocations { get; set; }
    }
}
