﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Resources", Schema = "adm")]
    public partial class Resource
    {
        public Resource()
        {
            Permissions = new HashSet<Permission>();
        }

        [Key]
        public int ResourceId { get; set; }
        public int ModuleId { get; set; }
        [Required]
        [StringLength(100)]
        public string ResourceName { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        public int PermissionValue { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ModuleId))]
        [InverseProperty("Resources")]
        public virtual Module Module { get; set; }
        [InverseProperty(nameof(Permission.Resource))]
        public virtual ICollection<Permission> Permissions { get; set; }
    }
}
