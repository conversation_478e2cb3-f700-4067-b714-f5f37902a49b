﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_SFG_BOO")]
    public partial class ISfgBoo
    {
        [StringLength(255)]
        public string Header { get; set; }
        [Column("BOO Description")]
        [StringLength(255)]
        public string BooDescription { get; set; }
        [Column("P1350 - TMT  - Master")]
        [StringLength(255)]
        public string P1350TmtMaster { get; set; }
        [Column("P1351 - TEV - Master")]
        [StringLength(255)]
        public string P1351TevMaster { get; set; }
        [Column("P1352 - GPT-3 - Master")]
        [StringLength(255)]
        public string P1352Gpt3Master { get; set; }
        [Column("P1353 - GPS - Master")]
        [StringLength(255)]
        public string P1353GpsMaster { get; set; }
        [Column("P1354 - BEV - Master")]
        [StringLength(255)]
        public string P1354BevMaster { get; set; }
        [Column("P1355 - MBT - Master")]
        [StringLength(255)]
        public string P1355MbtMaster { get; set; }
        [Column("P1356 - BAP - Master")]
        [StringLength(255)]
        public string P1356BapMaster { get; set; }
        [Column("P1357 - J2 - Master")]
        [StringLength(255)]
        public string P1357J2Master { get; set; }
        [Column("P1358 - CSM - Master")]
        [StringLength(255)]
        public string P1358CsmMaster { get; set; }
        [Column("P1359 - ECO - DSI - Master")]
        [StringLength(255)]
        public string P1359EcoDsiMaster { get; set; }
        [Column("P1360 - ECO - TWW - Master")]
        [StringLength(255)]
        public string P1360EcoTwwMaster { get; set; }
        [Column("P1361 - ECO - ARP - Master")]
        [StringLength(255)]
        public string P1361EcoArpMaster { get; set; }
        [Column("ECO NM Gray - Master")]
        [StringLength(255)]
        public string EcoNmGrayMaster { get; set; }
        [Column("P1362 - TMT - Final")]
        [StringLength(255)]
        public string P1362TmtFinal { get; set; }
        [Column("P1363 - TEV - Final")]
        [StringLength(255)]
        public string P1363TevFinal { get; set; }
        [Column("P1364 - GPT-3 - Final")]
        [StringLength(255)]
        public string P1364Gpt3Final { get; set; }
        [Column("P1365 - GPS - Final")]
        [StringLength(255)]
        public string P1365GpsFinal { get; set; }
        [Column("P1366 - BEV - Final")]
        [StringLength(255)]
        public string P1366BevFinal { get; set; }
        [Column("P1367 - MBT - Final")]
        [StringLength(255)]
        public string P1367MbtFinal { get; set; }
        [Column("P1368 - BAP - Final")]
        [StringLength(255)]
        public string P1368BapFinal { get; set; }
        [Column("P1369 - J2 - Final")]
        [StringLength(255)]
        public string P1369J2Final { get; set; }
        [Column("P1370 - CSM - Final")]
        [StringLength(255)]
        public string P1370CsmFinal { get; set; }
        [Column("P1371 - ECO - DSI - Final")]
        [StringLength(255)]
        public string P1371EcoDsiFinal { get; set; }
        [Column("P1372 - ECO - TWW - Final")]
        [StringLength(255)]
        public string P1372EcoTwwFinal { get; set; }
        [Column("P1373 - ECO - ARP - Final")]
        [StringLength(255)]
        public string P1373EcoArpFinal { get; set; }
        [Column("BFP - Master")]
        [StringLength(255)]
        public string BfpMaster { get; set; }
        [Column("GPT 2 - Final")]
        [StringLength(255)]
        public string Gpt2Final { get; set; }
        [Column("GPT 1 - Final")]
        [StringLength(255)]
        public string Gpt1Final { get; set; }
        [Column("BFP - Final")]
        [StringLength(255)]
        public string BfpFinal { get; set; }
        [Column("ECO NM Gray - Final")]
        [StringLength(255)]
        public string EcoNmGrayFinal { get; set; }
        [Column("Yellow - Master Batch")]
        [StringLength(255)]
        public string YellowMasterBatch { get; set; }
    }
}
