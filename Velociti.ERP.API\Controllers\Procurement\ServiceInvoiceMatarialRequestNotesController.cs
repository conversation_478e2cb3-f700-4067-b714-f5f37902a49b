﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class ServiceInvoiceMatarialRequestNotesController : ControllerBase
    {
        private readonly IServiceInvoiceMatarialRequestNotesService serviceInvoiceMatarialRequestNotesService;

        public ServiceInvoiceMatarialRequestNotesController(IServiceInvoiceMatarialRequestNotesService serviceInvoiceMatarialRequestNotesService)
        {
            this.serviceInvoiceMatarialRequestNotesService = serviceInvoiceMatarialRequestNotesService;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody]IEnumerable<ServiceInvoiceMatarialRequestNote> serviceInvoiceMatarialRequestNote)
        {
            await serviceInvoiceMatarialRequestNotesService.SaveAsync(serviceInvoiceMatarialRequestNote);

            return Ok();
        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int internalServiceInvoiceId)
        {
            return Ok(await serviceInvoiceMatarialRequestNotesService.GetAll(internalServiceInvoiceId));
        }
    }
}