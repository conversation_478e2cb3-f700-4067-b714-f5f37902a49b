﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MachineVisualPlanLines", Schema = "man")]
    public partial class MachineVisualPlanLine
    {
        [Key]
        public int MachineVisualPlanLineId { get; set; }
        public int? CompanyId { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        public int? RefDocLineId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDateTime { get; set; }
        public int? ProductionPlanLineId { get; set; }
        public int? BillOfOperationId { get; set; }
        public int? MachineId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfOperationId))]
        [InverseProperty("MachineVisualPlanLines")]
        public virtual BillOfOperation BillOfOperation { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("MachineVisualPlanLines")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("MachineVisualPlanLines")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("MachineVisualPlanLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductionPlanLineId))]
        [InverseProperty("MachineVisualPlanLines")]
        public virtual ProductionPlanLine ProductionPlanLine { get; set; }
    }
}
