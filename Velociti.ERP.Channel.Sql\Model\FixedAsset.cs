﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FixedAssets", Schema = "fa")]
    public partial class FixedAsset
    {
        public FixedAsset()
        {
            FixedAssetRegistries = new HashSet<FixedAssetRegistry>();
            MaterialRequisitionNotes = new HashSet<MaterialRequisitionNote>();
            PurchaseOrderLineDetails = new HashSet<PurchaseOrderLineDetail>();
            PurchaseOrderLines = new HashSet<PurchaseOrderLine>();
        }

        [Key]
        public int FixedAssetId { get; set; }
        public int CompanyId { get; set; }
        [StringLength(50)]
        public string FixedAssetCode { get; set; }
        [StringLength(255)]
        public string FixedAssetName { get; set; }
        [StringLength(255)]
        public string FixedAssetDescription { get; set; }
        public int? MachineId { get; set; }
        public int? AssetGroupId { get; set; }
        public int? AssetTypeId { get; set; }
        public int? AssetLocationId { get; set; }
        public int? UserId { get; set; }
        public int? EmployeeId { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? PerUnitCost { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string TagNumber { get; set; }
        [StringLength(255)]
        public string AssetManufacturer { get; set; }
        public int? StandardPeriod { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? WarrantyExpirationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AssetGroupId))]
        [InverseProperty(nameof(SupportData.FixedAssetAssetGroups))]
        public virtual SupportData AssetGroup { get; set; }
        [ForeignKey(nameof(AssetLocationId))]
        [InverseProperty(nameof(SupportData.FixedAssetAssetLocations))]
        public virtual SupportData AssetLocation { get; set; }
        [ForeignKey(nameof(AssetTypeId))]
        [InverseProperty(nameof(SupportData.FixedAssetAssetTypes))]
        public virtual SupportData AssetType { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("FixedAssets")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("FixedAssets")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("FixedAssets")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(UserId))]
        [InverseProperty("FixedAssets")]
        public virtual User User { get; set; }
        [InverseProperty(nameof(FixedAssetRegistry.FixedAsset))]
        public virtual ICollection<FixedAssetRegistry> FixedAssetRegistries { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNote.FixedAsset))]
        public virtual ICollection<MaterialRequisitionNote> MaterialRequisitionNotes { get; set; }
        [InverseProperty(nameof(PurchaseOrderLineDetail.FixedAsset))]
        public virtual ICollection<PurchaseOrderLineDetail> PurchaseOrderLineDetails { get; set; }
        [InverseProperty(nameof(PurchaseOrderLine.FixedAsset))]
        public virtual ICollection<PurchaseOrderLine> PurchaseOrderLines { get; set; }
    }
}
