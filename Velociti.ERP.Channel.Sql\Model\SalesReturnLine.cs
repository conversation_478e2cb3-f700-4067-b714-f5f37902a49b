﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesReturnLines", Schema = "sales")]
    public partial class SalesReturnLine
    {
        public SalesReturnLine()
        {
            SalesReturnLineDetails = new HashSet<SalesReturnLineDetail>();
        }

        [Key]
        public int SalesReturnLineId { get; set; }
        public int SalesReturnId { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? DispatchWarehouseId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? ReturnReasonId { get; set; }
        public int? TyreSpecificationId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DispatchWarehouseId))]
        [InverseProperty(nameof(Warehous.SalesReturnLines))]
        public virtual Warehous DispatchWarehouse { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.SalesReturnLines))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SalesReturnLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ReturnReasonId))]
        [InverseProperty(nameof(SupportData.SalesReturnLines))]
        public virtual SupportData ReturnReason { get; set; }
        [ForeignKey(nameof(SalesReturnId))]
        [InverseProperty("SalesReturnLines")]
        public virtual SalesReturn SalesReturn { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("SalesReturnLines")]
        public virtual TaxGroup TaxGroup { get; set; }
        [ForeignKey(nameof(TyreSpecificationId))]
        [InverseProperty("SalesReturnLines")]
        public virtual TyreSpecification TyreSpecification { get; set; }
        [InverseProperty(nameof(SalesReturnLineDetail.SalesReturnLine))]
        public virtual ICollection<SalesReturnLineDetail> SalesReturnLineDetails { get; set; }
    }
}
