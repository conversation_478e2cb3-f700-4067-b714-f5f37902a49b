﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductionPlans", Schema = "sales")]
    public partial class ProductionPlan
    {
        public ProductionPlan()
        {
            ProductionPlanLines = new HashSet<ProductionPlanLine>();
        }

        [Key]
        public int ProductionPlanId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string ProductionPlanNumber { get; set; }
        public int? ProductId { get; set; }
        public int? MachineId { get; set; }
        public byte? DayLightNo { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? BarcodeScannedStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualStartDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CompletedDateTime { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ActualEndDateTime { get; set; }
        public byte? CavityCount { get; set; }
        public byte? UsedCavityCount { get; set; }
        public byte? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SentDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ProductionPlans")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("ProductionPlans")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ProductionPlans")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(ProductionPlanLine.ProductionPlan))]
        public virtual ICollection<ProductionPlanLine> ProductionPlanLines { get; set; }
    }
}
