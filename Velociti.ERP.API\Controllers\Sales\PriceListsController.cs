﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using OfficeOpenXml;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class PriceListsController : ControllerBase
    {
        private readonly IPriceListService _priceListService;

        public PriceListsController(IPriceListService priceListService)
        {
            _priceListService = priceListService;
        }

        [HttpGet]
        [Route("Company/{companyId}/PriceList/{priceListId}")]
        public async Task<IActionResult> GetAllByPriceList(int companyId, int priceListId)
        {
            return Ok(await _priceListService.GetByPriceListIdAsync(priceListId, companyId));
        }

        [HttpGet]
        [Route("{priceListId}/Product/{productId}")]
        public async Task<IActionResult> GetByProduct(int priceListId, int productId)
        {
            return Ok(await _priceListService.GetByProductIdAsync(priceListId, productId));
        }

        [HttpGet]
        [Route("Customer/{customerId}")]
        public async Task<IActionResult> GetByCustomerId(int customerId)
        {
            return Ok(await _priceListService.GetByCustomerIdAsync(customerId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]IEnumerable<PriceList> priceLists)
        {
            await _priceListService.SaveAsync(priceLists);

            return Ok();
        }

        [HttpPost]
        [Route("Upload")]
        public async Task<IActionResult> Upload([FromForm]PriceList priceList)
        {
            var priceLists = new List<PriceList>();
            var headers = new List<string> { "ProductCode", "MRP", "Provision" };
            var currentHeaders = new List<string>();

            if (priceList.UploadedFile == null && priceList.UploadedFile.Length == 0)
                throw new Exception("Please select a file to upload.");

            string extension = Path.GetExtension(priceList.UploadedFile.FileName);

            if (!extension.Equals(".xlsx") && !extension.Equals(".xls"))
                throw new Exception("Invalid file type.");

            using var ms = new MemoryStream();
            await priceList.UploadedFile.CopyToAsync(ms);
            using var package = new ExcelPackage(ms);

            var worksheet = package.Workbook.Worksheets["PriceList"];

            if (worksheet == null)
                throw new Exception("Invalid excel sheet.");

            foreach (var firstRowCell in worksheet.Cells[worksheet.Dimension.Start.Row, worksheet.Dimension.Start.Column, 1, worksheet.Dimension.End.Column])
                currentHeaders.Add(firstRowCell.Text);

            if (headers.Count() != currentHeaders.Count() || !headers.SequenceEqual(currentHeaders))
                throw new Exception("Invalid headers.");

            // get number of rows and columns in the sheet
            int rows = worksheet.Dimension.Rows;

            // loop through the worksheet rows and columns 
            for (int i = 2; i <= rows; i++)
            {
                if (worksheet.Cells[i, 1].Value == default) break;

                var priceListDto = new PriceList()
                {
                    ProductCode = worksheet.Cells[i, 1].Value.ToString()
                };

                if (decimal.TryParse(worksheet.Cells[i, 2].Value?.ToString(), out decimal price) && price >= 0)
                    priceListDto.Price = price;
                else
                    throw new Exception("Uploaded file contains invalid MRP values.");

                if (worksheet.Cells[i, 3].Value == null)
                    worksheet.Cells[i, 3].Value = 0;

                if (decimal.TryParse(worksheet.Cells[i, 3].Value?.ToString(), out decimal provision) && provision >= 0)
                    priceListDto.Provision = provision;
                else
                    throw new Exception("Uploaded file contains invalid provision values.");

                priceListDto.ModifiedUserId = priceList.ModifiedUserId;
                priceListDto.PriceListId = priceList.PriceListId;

                priceLists.Add(priceListDto);
            }

            if (priceLists.Count() == 0)
                throw new Exception("Please enter values to upload.");

            await _priceListService.UploadAsync(priceLists, priceList.CompanyId);

            return Ok();
        }
    }
}