﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesInvoices", Schema = "sales")]
    public partial class SalesInvoice
    {
        public SalesInvoice()
        {
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            LoadingPlans = new HashSet<LoadingPlan>();
            PurchaseOrderSalesInvoices = new HashSet<PurchaseOrderSalesInvoice>();
            SalesInvoiceChargeGroups = new HashSet<SalesInvoiceChargeGroup>();
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SalesReturns = new HashSet<SalesReturn>();
            ServiceInvoiceSalesInvoices = new HashSet<ServiceInvoiceSalesInvoice>();
        }

        [Key]
        public int SalesInvoiceId { get; set; }
        public int? SalesOrderId { get; set; }
        public int? LoadingPlanId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? OrderType { get; set; }
        public byte? StatusEnum { get; set; }
        public int? VersionNo { get; set; }
        public int? CustomerId { get; set; }
        [StringLength(500)]
        public string CustomerReferenceNo { get; set; }
        [StringLength(255)]
        public string CustomerAddress { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        [StringLength(255)]
        public string PayingParty { get; set; }
        public int? DispatchWarehouseId { get; set; }
        public int? SalesRepEmployeeId { get; set; }
        public int? RecommendedPersonId { get; set; }
        public int? PriceListId { get; set; }
        public int? CurrencyId { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [StringLength(255)]
        public string InsuranceNo { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ExpectedDispatchDate { get; set; }
        [StringLength(255)]
        public string CustomsDeclarationRefNo { get; set; }
        [StringLength(255)]
        public string ContainerReleaseOrderRefNo { get; set; }
        public int? AccountId { get; set; }
        public int? PaymentTermId { get; set; }
        [StringLength(255)]
        public string ShippingPort { get; set; }
        [StringLength(255)]
        public string DestinationPort { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ContainerSize { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? CreditCardPayment { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? CommissionTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? AdditionalCharges { get; set; }
        [StringLength(1000)]
        public string AdditionalChargeNote { get; set; }
        [StringLength(50)]
        public string Measurement { get; set; }
        public byte? ShippingModeEnum { get; set; }
        public byte? ShippingTermEnum { get; set; }
        public int? ShipmentPackingMethodId { get; set; }
        [StringLength(50)]
        public string ContainerNo { get; set; }
        [StringLength(50)]
        public string TrailerNo { get; set; }
        [StringLength(50)]
        public string SealNo { get; set; }
        [StringLength(50)]
        public string VesselName { get; set; }
        [StringLength(50)]
        public string Consignee { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? GrossWeight { get; set; }
        [Column("AODNumber")]
        [StringLength(50)]
        public string Aodnumber { get; set; }
        public int? DiscountSchemeId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [StringLength(255)]
        public string CourierName { get; set; }
        [StringLength(255)]
        public string CourierReferenceNo { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DestinationReceivedDate { get; set; }
        public int? ParentId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("SalesInvoices")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SalesInvoices")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("SalesInvoices")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("SalesInvoices")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("SalesInvoices")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(DiscountSchemeId))]
        [InverseProperty("SalesInvoices")]
        public virtual DiscountScheme DiscountScheme { get; set; }
        [ForeignKey(nameof(DispatchWarehouseId))]
        [InverseProperty(nameof(Warehous.SalesInvoices))]
        public virtual Warehous DispatchWarehouse { get; set; }
        [ForeignKey(nameof(PriceListId))]
        [InverseProperty(nameof(SupportData.SalesInvoices))]
        public virtual SupportData PriceList { get; set; }
        [ForeignKey(nameof(RecommendedPersonId))]
        [InverseProperty(nameof(Employee.SalesInvoiceRecommendedPersons))]
        public virtual Employee RecommendedPerson { get; set; }
        [ForeignKey(nameof(SalesOrderId))]
        [InverseProperty("SalesInvoices")]
        public virtual SalesOrder SalesOrder { get; set; }
        [ForeignKey(nameof(SalesRepEmployeeId))]
        [InverseProperty(nameof(Employee.SalesInvoiceSalesRepEmployees))]
        public virtual Employee SalesRepEmployee { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.SalesInvoice))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(LoadingPlan.SalesInvoice))]
        public virtual ICollection<LoadingPlan> LoadingPlans { get; set; }
        [InverseProperty(nameof(PurchaseOrderSalesInvoice.SalesInvoice))]
        public virtual ICollection<PurchaseOrderSalesInvoice> PurchaseOrderSalesInvoices { get; set; }
        [InverseProperty(nameof(SalesInvoiceChargeGroup.SalesInvoice))]
        public virtual ICollection<SalesInvoiceChargeGroup> SalesInvoiceChargeGroups { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.SalesInvoice))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SalesReturn.SalesInvoice))]
        public virtual ICollection<SalesReturn> SalesReturns { get; set; }
        [InverseProperty(nameof(ServiceInvoiceSalesInvoice.SalesInvoice))]
        public virtual ICollection<ServiceInvoiceSalesInvoice> ServiceInvoiceSalesInvoices { get; set; }
    }
}
