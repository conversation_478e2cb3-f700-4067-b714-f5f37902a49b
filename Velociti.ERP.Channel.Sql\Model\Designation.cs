﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Designations", Schema = "adm")]
    public partial class Designation
    {
        public Designation()
        {
            Employees = new HashSet<Employee>();
            InverseParentDesignation = new HashSet<Designation>();
            TxnWorkFlowLines = new HashSet<TxnWorkFlowLine>();
            UserCompanies = new HashSet<UserCompany>();
            WorkFlowLines = new HashSet<WorkFlowLine>();
            WorkFlows = new HashSet<WorkFlow>();
        }

        [Key]
        public int DesignationId { get; set; }
        public int CompanyId { get; set; }
        [StringLength(50)]
        public string DesignationCode { get; set; }
        [StringLength(255)]
        public string DesignationName { get; set; }
        public int? ParentDesignationId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public byte? TypeEnum { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Designations")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ParentDesignationId))]
        [InverseProperty(nameof(Designation.InverseParentDesignation))]
        public virtual Designation ParentDesignation { get; set; }
        [InverseProperty(nameof(Employee.Designation))]
        public virtual ICollection<Employee> Employees { get; set; }
        [InverseProperty(nameof(Designation.ParentDesignation))]
        public virtual ICollection<Designation> InverseParentDesignation { get; set; }
        [InverseProperty(nameof(TxnWorkFlowLine.Designation))]
        public virtual ICollection<TxnWorkFlowLine> TxnWorkFlowLines { get; set; }
        [InverseProperty(nameof(UserCompany.Designation))]
        public virtual ICollection<UserCompany> UserCompanies { get; set; }
        [InverseProperty(nameof(WorkFlowLine.Designation))]
        public virtual ICollection<WorkFlowLine> WorkFlowLines { get; set; }
        [InverseProperty(nameof(WorkFlow.Designation))]
        public virtual ICollection<WorkFlow> WorkFlows { get; set; }
    }
}
