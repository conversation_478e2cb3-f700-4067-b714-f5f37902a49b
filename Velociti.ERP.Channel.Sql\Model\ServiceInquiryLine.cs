﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ServiceInquiryLines", Schema = "sales")]
    public partial class ServiceInquiryLine
    {
        [Key]
        public int ServiceInquiryLineId { get; set; }
        public int? ServiceInquiryId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitPrice { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date { get; set; }
        [StringLength(500)]
        public string Remarks { get; set; }
        public int? TherapistId { get; set; }
        public string FrequencyofUseProductId { get; set; }
        public string FrequencyofUseServiceId { get; set; }
        public int? MethodOfUseId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(MethodOfUseId))]
        [InverseProperty(nameof(SupportData.ServiceInquiryLines))]
        public virtual SupportData MethodOfUse { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ServiceInquiryLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ServiceInquiryId))]
        [InverseProperty("ServiceInquiryLines")]
        public virtual ServiceInquiry ServiceInquiry { get; set; }
        [ForeignKey(nameof(TherapistId))]
        [InverseProperty(nameof(Employee.ServiceInquiryLines))]
        public virtual Employee Therapist { get; set; }
    }
}
