﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class DivisionsController : ControllerBase
    {
        private readonly IDivisionService _divisionService;

        public DivisionsController(IDivisionService divisionService)
        {
            _divisionService = divisionService;
        }

        [HttpGet]
        [Route("ShortList/User/{userId}/department/{departmentId}")]
        public async Task<IActionResult> GetShortListForAssignUser(int userId, int departmentId)
        {
            var list = await _divisionService.GetShortListForAssignUserAsync(userId, departmentId);

            return Ok(list);
        }

        [HttpGet]
        [Route("{divisionId}")]
        public async Task<IActionResult> GetSingleDivisionAsync(int divisionId)
        {
            var list = await _divisionService.GetSingleDivisionAsync(divisionId);

            return Ok(list);
        }
    }
}