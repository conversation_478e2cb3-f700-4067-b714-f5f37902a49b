﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class DocTypeConfigRepository : IDocTypeConfigRepository
    {
        private readonly MarangoniERPContext _context;

        public DocTypeConfigRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<DocTypeConfig>> GetActiveAllAsync()
        {
            return await _context.DocTypeConfigs.Where(dtc => dtc.ExpiryDate == null).AsNoTracking().ToListAsync();
        }
    }
}
