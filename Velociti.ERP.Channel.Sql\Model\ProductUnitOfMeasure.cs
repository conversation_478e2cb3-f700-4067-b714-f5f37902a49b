﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductUnitOfMeasures", Schema = "inv")]
    public partial class ProductUnitOfMeasure
    {
        [Key]
        public int ProductId { get; set; }
        [Key]
        public int UnitOfMeasureId { get; set; }
        public int? Conversion { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ProductUnitOfMeasures")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(UnitOfMeasureId))]
        [InverseProperty("ProductUnitOfMeasures")]
        public virtual UnitOfMeasure UnitOfMeasure { get; set; }
    }
}
