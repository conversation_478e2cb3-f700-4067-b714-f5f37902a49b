﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesInvoiceLinesController : ControllerBase
    {
        private readonly ISalesInvoiceLineService _salesInvoiceLineService;

        public SalesInvoiceLinesController(ISalesInvoiceLineService salesInvoiceLineService)
        {
            _salesInvoiceLineService = salesInvoiceLineService;
        }

        [HttpGet]
        [Route("{salesInvoiceId}")]
        public async Task<IActionResult> Get(int salesInvoiceId)
        {
            return Ok(await _salesInvoiceLineService.GetAsync(salesInvoiceId));
        }

        [HttpGet]
        [Route("ReturnLines/{salesInvoiceId}")]
        public async Task<IActionResult> GetLinesForReturnAsync(int salesInvoiceId)
        {
            return Ok(await _salesInvoiceLineService.GetLinesForReturnAsync(salesInvoiceId));
        }

    }
}