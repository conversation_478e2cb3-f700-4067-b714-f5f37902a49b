﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PressLines", Schema = "man")]
    public partial class PressLine
    {
        public PressLine()
        {
            Machines = new HashSet<Machine>();
            WorkstationCadres = new HashSet<WorkstationCadre>();
        }

        [Key]
        public int PressLineId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string Description { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PressLines")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(Machine.PressLine))]
        public virtual ICollection<Machine> Machines { get; set; }
        [InverseProperty(nameof(WorkstationCadre.PressLine))]
        public virtual ICollection<WorkstationCadre> WorkstationCadres { get; set; }
    }
}
