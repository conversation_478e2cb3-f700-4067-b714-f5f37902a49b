﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Entities.Inventory;
using Microsoft.AspNetCore.Authorization;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class HOReturnStockTransferController : ControllerBase
    {
        private readonly IHOReturnStockTransferService _hoReturnStockTranserService;
        public HOReturnStockTransferController(IHOReturnStockTransferService hoReturnStockTranserService)
        {

            _hoReturnStockTranserService = hoReturnStockTranserService;

        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte typeEnum)
        {
            return Ok(await _hoReturnStockTranserService.GetAll(companyId, userId, startDate, endDate, typeEnum));
        }
        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/{productId}")]
        public async Task<IActionResult> GetProducts(int fromWarehouseId, int toWarehouseId, int productId)
        {
            var result = await _hoReturnStockTranserService.GetStockByWarehouses(fromWarehouseId, toWarehouseId, productId);
            return Ok(result);
        }

        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/Barcode/{barcode}")]
        public async Task<IActionResult> GetProductByBarcode(int fromWarehouseId, int toWarehouseId, string barcode)
        {
            var result = await _hoReturnStockTranserService.GetStockByBarcodeAsync(fromWarehouseId, toWarehouseId, barcode);
            return Ok(result);
        }

        [HttpGet]
        [Route("FromStore/{fromWarehouseId}/ToStore/{toWarehouseId}/Pallet/{palletCode}")]
        public async Task<IActionResult> GetProductsByPallet(int fromWarehouseId, int toWarehouseId, string palletCode)
        {
            var result = await _hoReturnStockTranserService.GetStockByPalletAsync(fromWarehouseId, toWarehouseId, palletCode);
            return Ok(result);
        }
        [HttpGet]
        [Route("{transferOrderId}")]
        public async Task<IActionResult> GetById(int transferOrderId)
        {
            return Ok(await _hoReturnStockTranserService.FindByIdAsync(transferOrderId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _hoReturnStockTranserService.GetShortListAsync(companyId);

            return Ok(list);
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody] HOReturnStockTransfer stockTransfer)
        {
            return Ok(await _hoReturnStockTranserService.SaveAsync(stockTransfer));
        }
        [HttpGet]
        [Route("Single/{hoReturnStockTransferId}")]
        public async Task<IActionResult> GetStockTransfer(int hoReturnStockTransferId)
        {
            return Ok(await _hoReturnStockTranserService.GetStockTransfer(hoReturnStockTransferId));
        }
        [HttpPut]
        public async Task<IActionResult> Update([FromBody] HOReturnStockTransfer stockTransfer)
        {
            switch (stockTransfer.Action)
            {
                case "submit":
                    await _hoReturnStockTranserService.SubmitStockTransfer(stockTransfer.HOReturnStockTransferId, stockTransfer.ModifiedUserId.Value);
                    break;
                case "reject":
                    await _hoReturnStockTranserService.RejectStockTransfer(stockTransfer.HOReturnStockTransferId, stockTransfer.ModifiedUserId.Value);
                    break;
                case "convert":
                    return Ok(await _hoReturnStockTranserService.ConvertAsync(stockTransfer));
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{stockTransferId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int stockTransferId, int userId)
        {
            await _hoReturnStockTranserService.CancelAsync(stockTransferId, userId);
            return Ok();
        }
    }
}
