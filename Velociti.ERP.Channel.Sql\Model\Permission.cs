﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Permissions", Schema = "adm")]
    public partial class Permission
    {
        [Key]
        public int PermissionId { get; set; }
        public int? CompanyId { get; set; }
        public int ModuleId { get; set; }
        public int ResourceId { get; set; }
        public int? GroupId { get; set; }
        public int? RoleId { get; set; }
        public int CrudPermission { get; set; }
        [StringLength(100)]
        public string PermissionName { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Permissions")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(GroupId))]
        [InverseProperty("Permissions")]
        public virtual Group Group { get; set; }
        [ForeignKey(nameof(ModuleId))]
        [InverseProperty("Permissions")]
        public virtual Module Module { get; set; }
        [ForeignKey(nameof(ResourceId))]
        [InverseProperty("Permissions")]
        public virtual Resource Resource { get; set; }
        [ForeignKey(nameof(RoleId))]
        [InverseProperty("Permissions")]
        public virtual Role Role { get; set; }
    }
}
