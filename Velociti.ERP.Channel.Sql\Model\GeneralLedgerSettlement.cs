﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GeneralLedgerSettlements", Schema = "fin")]
    public partial class GeneralLedgerSettlement
    {
        public GeneralLedgerSettlement()
        {
            GeneralLedgerSettlementLines = new HashSet<GeneralLedgerSettlementLine>();
        }

        [Key]
        public int GeneralLedgerSettlementId { get; set; }
        public int CompanyId { get; set; }
        public int GeneralLedgerLineId { get; set; }
        [Column(TypeName = "money")]
        public decimal? BalanceAmount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifiedDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("GeneralLedgerSettlements")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(GeneralLedgerLineId))]
        [InverseProperty("GeneralLedgerSettlements")]
        public virtual GeneralLedgerLine GeneralLedgerLine { get; set; }
        [InverseProperty(nameof(GeneralLedgerSettlementLine.GeneralLedgerSettlement))]
        public virtual ICollection<GeneralLedgerSettlementLine> GeneralLedgerSettlementLines { get; set; }
    }
}
