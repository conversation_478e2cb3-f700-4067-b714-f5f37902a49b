﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class ServiceInvoiceSalesInvoicesController : ControllerBase
    {
        private readonly IServiceInvoiceSalesInvoiceService serviceInvoiceSalesInvoiceService;  

        public ServiceInvoiceSalesInvoicesController(IServiceInvoiceSalesInvoiceService serviceInvoiceSalesInvoiceService)  
        {
            this.serviceInvoiceSalesInvoiceService = serviceInvoiceSalesInvoiceService;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody]IEnumerable<ServiceInvoiceSalesInvoice> serviceInvoiceSalesInvoice)  
        {
            await serviceInvoiceSalesInvoiceService.SaveAsync(serviceInvoiceSalesInvoice);

            return Ok();
        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int serviceInvoiceId)  
        {
            return Ok(await serviceInvoiceSalesInvoiceService.GetAll(serviceInvoiceId));
        }
    }
}