﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MaterialRequisitionNoteLines", Schema = "inv")]
    public partial class MaterialRequisitionNoteLine
    {
        [Key]
        public int MaterialRequisitionNoteLineId { get; set; }
        public int? MaterialRequisitionNoteId { get; set; }
        public int? DeliveryWarehouseId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public bool? AllowStockAllocation { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.MaterialRequisitionNoteLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DeliveryWarehouseId))]
        [InverseProperty(nameof(Warehous.MaterialRequisitionNoteLines))]
        public virtual Warehous DeliveryWarehouse { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.MaterialRequisitionNoteLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(MaterialRequisitionNoteId))]
        [InverseProperty("MaterialRequisitionNoteLines")]
        public virtual MaterialRequisitionNote MaterialRequisitionNote { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("MaterialRequisitionNoteLines")]
        public virtual Product Product { get; set; }
    }
}
