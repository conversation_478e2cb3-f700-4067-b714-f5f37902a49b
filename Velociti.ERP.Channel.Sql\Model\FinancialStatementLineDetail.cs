﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FinancialStatementLineDetails", Schema = "fin")]
    public partial class FinancialStatementLineDetail
    {
        [Key]
        public int FinancialStatementLineDetailId { get; set; }
        public int? FinancialStatementLineId { get; set; }
        public int? ChartOfAccountId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChartOfAccountId))]
        [InverseProperty("FinancialStatementLineDetails")]
        public virtual ChartOfAccount ChartOfAccount { get; set; }
        [ForeignKey(nameof(FinancialStatementLineId))]
        [InverseProperty("FinancialStatementLineDetails")]
        public virtual FinancialStatementLine FinancialStatementLine { get; set; }
    }
}
