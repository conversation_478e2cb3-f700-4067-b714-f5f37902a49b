﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class InboundReceiptLinesController : ControllerBase
    {
        private readonly IInboundReceiptLineService _inboundReceiptLineService;

        public InboundReceiptLinesController(IInboundReceiptLineService inboundReceiptLineService)
        {
            _inboundReceiptLineService = inboundReceiptLineService;
        }

        [HttpGet]
        [Route("{inboundReceiptId}")]
        public async Task<IActionResult> GetByHeader(int inboundReceiptId)
        {
            return Ok(await _inboundReceiptLineService.GetByHeaderIdAsync(inboundReceiptId));
        }

        [HttpGet]
        [Route("SettlementDocuments/{inboundReceiptId}")]
        public async Task<IActionResult> GetSettlementDocumentsAsync(int inboundReceiptId)
        {
            return Ok(await _inboundReceiptLineService.GetSettlementDocumentsAsync(inboundReceiptId));
        }

        [HttpGet]
        [Route("{inboundReceiptId}/Supplier/{supplierId}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetSupplierReceiptVoucherLines(int inboundReceiptId, int supplierId, int currencyId, int companyId)
        {
            return Ok(await _inboundReceiptLineService.GetSupplierReceiptVoucherLinesAsync(inboundReceiptId, supplierId, currencyId, companyId));
        }

        [HttpGet]
        [Route("{inboundReceiptId}/Customer/{customerId}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetCustomerReceiptVoucherLines(int inboundReceiptId, int customerId, int currencyId, int companyId)
        {
            return Ok(await _inboundReceiptLineService.GetCustomerReceiptVoucherLinesAsync(inboundReceiptId, customerId, currencyId, companyId));
        }

        [HttpGet]
        [Route("{inboundReceiptId}/Employee/{employeeId}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetEmployeeReceiptVoucherLines(int inboundReceiptId, int employeeId, int currencyId, int companyId)
        {
            return Ok(await _inboundReceiptLineService.GetEmployeeReceiptVoucherLinesAsync(inboundReceiptId, employeeId, currencyId, companyId));
        }

        //[HttpGet]
        //[Route("{inboundReceiptId}/Type/{typeEnum}/Currency/{currencyId}/Company/{companyId}")]
        //public async Task<IActionResult> GetReceiptVoucherLines(int inboundReceiptId, byte typeEnum, int currencyId, int companyId)
        //{
        //    return Ok(await _inboundReceiptLineService.GetReceiptVoucherLinesByTypeAsync(inboundReceiptId, currencyId, typeEnum, companyId));
        //}

        [HttpGet]
        [Route("{inboundReceiptId}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetCustomReceiptVoucherLines(int inboundReceiptId, int currencyId, int companyId)
        {
            return Ok(await _inboundReceiptLineService.GetCustomReceiptVoucherLinesAsync(inboundReceiptId, currencyId, companyId));
        }

        [HttpGet]
        [Route("{inboundReceiptId}/Type/{typeEnum}/SubType/{subType}/Currency/{currencyId}/Company/{companyId}")]
        public async Task<IActionResult> GetReceiptVoucherLines(int inboundReceiptId, byte typeEnum, byte subType, int currencyId, int companyId)
        {
            return Ok(await _inboundReceiptLineService.GetReceiptVoucherLinesByTypeAsync(inboundReceiptId, currencyId, typeEnum, subType, companyId));
        }
    }
}