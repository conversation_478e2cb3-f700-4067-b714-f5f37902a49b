﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class MachinesController : ControllerBase
    {
        private readonly IMachineService _machineService;

        public MachinesController(IMachineService machineService)
        {
            _machineService = machineService;
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _machineService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("MachinesShortList/{companyId}")]
        public async Task<IActionResult> GetMachinesShortList(int companyId)
        {
            var list = await _machineService.GetMachinesShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ProcessShortList/{companyId}")]
        public async Task<IActionResult> ProcessShortList(int companyId)  
        {
            var list = await _machineService.GetProcessShortListAsync(companyId);  

            return Ok(list);
        }

        [HttpGet]
        [Route("MachinesMoulds")]
        public async Task<IActionResult> GetMachinesMoulds()
        {
            var list = await _machineService.GetMachinesMouldsListAsync();

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/type/{type}/{companyId}")]
        public async Task<IActionResult> GetMachinesShortListByType(int companyId, byte type)
        {
            var list = await _machineService.GetMachinesByTypeShortListAsync(companyId, type);

            return Ok(list);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("ProfileShortList/company/{companyId}")]
        public async Task<IActionResult> GetProfileMachinesShortList(int companyId)
        {
            var list = await _machineService.GetProfileMachinesShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("type/{type}/{companyId}")]
        public async Task<IActionResult> GetMachinesByType(int companyId, byte type)
        {
            var list = await _machineService.GetMachinesByTypeAsync(companyId, type);

            return Ok(list);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("type/{type}/company/{companyId}/barcodePrinting/{isBarcodePrinting}")]
        public async Task<IActionResult> GetMachinesByType(int companyId, byte type, bool isBarcodePrinting)
        {
            var list = await _machineService.GetMachinesByTypeAsync(companyId, type, isBarcodePrinting);

            return Ok(list);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("withoutProfile/type/{type}/company/{companyId}")]
        public async Task<IActionResult> GetWithoutProfile(int companyId, byte type)
        {
            var list = await _machineService.GetMachinesByTypeWithoutProfileAsync(companyId, type);

            return Ok(list);
        }

        [HttpGet]
        [Route("{companyId}/pressLine/{pressLineId}")]
        public async Task<IActionResult> GetMachinesByPressLine(int companyId, int pressLineId)    
        {
            var list = await _machineService.GetMachinesByPressLineAsync(companyId, pressLineId);

            return Ok(list);
        }

        [HttpGet]
        [Route("Single/{machineId}")]
        public async Task<IActionResult> FindById(int machineId)
        {
            return Ok(await _machineService.FindByIdAsync(machineId));
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> Get(string action, int companyId)
        {
            return action switch
            {
                "Active" => Ok(await _machineService.GetActiveAllAsync(companyId)),

                _ => BadRequest(),
            };
        }

        [HttpGet]
        [Route("Company/{companyId}/User/{userId}")]
        public async Task<IActionResult> Get(int companyId, int userId)  
        {            
            return Ok(await _machineService.GetAllAsync(companyId, userId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]Machine machine)  
        {
            await _machineService.SaveAsync(machine);

            return Ok();
        }

        [HttpDelete]
        [Route("{machineId}/User/{userId}/Company/{companyId}")]
        public async Task<IActionResult> ToggleActivation(int machineId, int userId, int companyId)
        {
            await _machineService.ToggleActivationAsync(machineId, userId, companyId);

            return Ok();
        }
    }
}