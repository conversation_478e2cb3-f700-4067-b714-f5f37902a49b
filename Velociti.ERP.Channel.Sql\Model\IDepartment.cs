﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Department")]
    public partial class IDepartment
    {
        [StringLength(255)]
        public string Type { get; set; }
        [StringLength(255)]
        public string Parent { get; set; }
        [StringLength(255)]
        public string Code { get; set; }
        [StringLength(255)]
        public string Name { get; set; }
        [StringLength(255)]
        public string StreetAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        [StringLength(255)]
        public string TaxRegistrationNumber { get; set; }
        [StringLength(255)]
        public string Telephone { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string Website { get; set; }
        [StringLength(255)]
        public string Warehouse { get; set; }
        [StringLength(255)]
        public string NatureOfOperation { get; set; }
    }
}
