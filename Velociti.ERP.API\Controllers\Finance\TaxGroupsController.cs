﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class TaxGroupsController : ControllerBase
    {
        private readonly ITaxGroupService _taxGroupsService;

        public TaxGroupsController(ITaxGroupService taxGroupsService)
        {
            _taxGroupsService = taxGroupsService;
        }

        [HttpGet]
        [Route("TaxType/{taxTypeId}")]
        public async Task<IActionResult> GetByTaxTypeId(int taxTypeId)
        {
            return Ok(await _taxGroupsService.GetByTaxTypeIdAsync(taxTypeId));
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _taxGroupsService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GeShortList(int companyId)
        {
            return Ok(await _taxGroupsService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("IsDefault/{companyId}")]
        public async Task<IActionResult> GeDefaultTaxGroup(int companyId)  
        {
            return Ok(await _taxGroupsService.GetDefaultAsync(companyId));
        }

        [HttpGet]
        [Route("ReceivableShortList/{companyId}")]
        public async Task<IActionResult> GeReceivableShortList(int companyId)  
        {
            return Ok(await _taxGroupsService.GeReceivableShortListAsync(companyId));
        }

        [HttpGet]
        [Route("PayableShortList/{companyId}")]
        public async Task<IActionResult> GePayableShortList(int companyId)
        {
            return Ok(await _taxGroupsService.GePayableShortListAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]TaxGroup taxGroup)
        {
            await _taxGroupsService.SaveAsync(taxGroup);

            return Ok();
        }

        [HttpPost]
        [Route("TestResult")]
        public async Task<IActionResult> TestResult([FromBody]TaxGroup taxGroup)
        {
            return Ok(await _taxGroupsService.GetTestResultAsync(taxGroup));
        }

        [HttpPost]
        [Route("Calculate")]
        public async Task<IActionResult> CalculateResult([FromBody]TaxGroup taxGroup)
        {
            return Ok(await _taxGroupsService.CalculateTaxValueAsync(taxGroup.TaxGroupId, taxGroup.SampleValue));
        }

        [HttpDelete]
        public async Task<IActionResult> ToggleActivation([FromBody]TaxGroup taxGroup)
        {
            await _taxGroupsService.ToggleActivationAsync(taxGroup.TaxGroupId.Value, taxGroup.ModifiedUserId);

            return Ok();
        }
    }
}