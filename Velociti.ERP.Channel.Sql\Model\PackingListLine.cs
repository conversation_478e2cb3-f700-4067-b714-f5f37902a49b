﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PackingListLines", Schema = "sales")]
    public partial class PackingListLine
    {
        [Key]
        public int PackingListLineId { get; set; }
        public int PackingListId { get; set; }
        public int? SalesOrderLineId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? TotalNetWeight { get; set; }
        public int? TyresPerPallet { get; set; }
        [StringLength(500)]
        public string PalletNumbers { get; set; }
        public int? PalletCount { get; set; }
        public int? TyresPerBox { get; set; }
        public int? BoxCount { get; set; }
        public int? LooseTyresCount { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? UnitWeight { get; set; }
        [Column(TypeName = "decimal(18, 3)")]
        public decimal? GrossWeight { get; set; }
        [StringLength(500)]
        public string CustomerReferenceNo { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(PackingListId))]
        [InverseProperty("PackingListLines")]
        public virtual PackingList PackingList { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("PackingListLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SalesOrderLineId))]
        [InverseProperty("PackingListLines")]
        public virtual SalesOrderLine SalesOrderLine { get; set; }
    }
}
