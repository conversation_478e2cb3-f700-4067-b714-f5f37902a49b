﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class PromotionController : ControllerBase
    {
        private readonly IPromotionService _promotionService;

        public PromotionController(IPromotionService promotionService)
        {
            _promotionService = promotionService;
        }

        [HttpGet]
        [Route("Single/{promotionId}")]
        public async Task<IActionResult> FindById(int promotionId)
        {
            return Ok(await _promotionService.FindByIdAsync(promotionId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _promotionService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] Promotion promotion)
        {
            switch (promotion.Action)
            {
                case "save": await _promotionService.SaveAsync(promotion); break;
                //case "update": await _promotionService.UpdateAsync(promotion); break;
            }

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody] Promotion promotion)
        {
            switch (promotion.Action)
            {
                case "submit": await _promotionService.SubmitAsync(promotion.PromotionId, promotion.ModifiedUserId.Value); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{promotionId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int promotionId, int userId)
        {
            await _promotionService.CancelAsync(promotionId, userId);

            return Ok();
        }
    }
}
