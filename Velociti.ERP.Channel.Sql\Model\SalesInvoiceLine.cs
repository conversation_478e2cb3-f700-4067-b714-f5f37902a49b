﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesInvoiceLines", Schema = "sales")]
    public partial class SalesInvoiceLine
    {
        public SalesInvoiceLine()
        {
            LoadingPlanLines = new HashSet<LoadingPlanLine>();
        }

        [Key]
        public int SalesInvoiceLineId { get; set; }
        public int SalesInvoiceId { get; set; }
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? DispatchWarehouseId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        public int? TherapistId { get; set; }
        public int? TherapyRoomId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Commission { get; set; }
        [Column(TypeName = "money")]
        public decimal? CommissionValue { get; set; }
        public int? SalesServiceOrderLineId { get; set; }
        [StringLength(50)]
        public string Measurement { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [StringLength(500)]
        public string CustomerReferenceNo { get; set; }
        public int? DiscountSchemeId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EffectiveDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? SalesOrderLineId { get; set; }
        public int? PerformaInvoiceLineId { get; set; }
        public int? PromotionId { get; set; }
        public int? FreeIssueId { get; set; }
        public int? OpeningStockQuantity { get; set; }
        public int? TransferredQuantity { get; set; }
        public int? TotalStockQuantity { get; set; }
        public int? RemainingQuantity { get; set; }
        public int? VarianceQuantity { get; set; }
        public int? FreeIssuesQuantity { get; set; }
        public int? SoldQuantity { get; set; }
        public int? TempId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        public int? SalesServiceOrderJobId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.SalesInvoiceLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DiscountSchemeId))]
        [InverseProperty("SalesInvoiceLines")]
        public virtual DiscountScheme DiscountScheme { get; set; }
        [ForeignKey(nameof(DispatchWarehouseId))]
        [InverseProperty(nameof(Warehous.SalesInvoiceLines))]
        public virtual Warehous DispatchWarehouse { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.SalesInvoiceLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(FreeIssueId))]
        [InverseProperty("SalesInvoiceLines")]
        public virtual FreeIssue FreeIssue { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SalesInvoiceLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SalesInvoiceId))]
        [InverseProperty("SalesInvoiceLines")]
        public virtual SalesInvoice SalesInvoice { get; set; }
        [ForeignKey(nameof(SalesOrderLineId))]
        [InverseProperty("SalesInvoiceLines")]
        public virtual SalesOrderLine SalesOrderLine { get; set; }
        [ForeignKey(nameof(SalesServiceOrderJobId))]
        [InverseProperty("SalesInvoiceLines")]
        public virtual SalesServiceOrderJob SalesServiceOrderJob { get; set; }
        [ForeignKey(nameof(SalesServiceOrderLineId))]
        [InverseProperty("SalesInvoiceLines")]
        public virtual SalesServiceOrderLine SalesServiceOrderLine { get; set; }
        [ForeignKey(nameof(TherapistId))]
        [InverseProperty(nameof(Employee.SalesInvoiceLines))]
        public virtual Employee Therapist { get; set; }
        [ForeignKey(nameof(TherapyRoomId))]
        [InverseProperty(nameof(Division.SalesInvoiceLines))]
        public virtual Division TherapyRoom { get; set; }
        [InverseProperty(nameof(LoadingPlanLine.SalesInvoiceLine))]
        public virtual ICollection<LoadingPlanLine> LoadingPlanLines { get; set; }
    }
}
