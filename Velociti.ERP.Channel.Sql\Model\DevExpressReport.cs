﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    public partial class DevExpressReport
    {
        [Key]
        public int Id { get; set; }
        [StringLength(50)]
        public string DisplayName { get; set; }
        public byte[] LayoutData { get; set; }
        public int? TempId { get; set; }
    }
}
