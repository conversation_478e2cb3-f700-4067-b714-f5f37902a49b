﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ShipmentQualityControlReturns", Schema = "inv")]
    public partial class ShipmentQualityControlReturn
    {
        public ShipmentQualityControlReturn()
        {
            ShipmentQualityControlReturnLines = new HashSet<ShipmentQualityControlReturnLine>();
        }

        [Key]
        public int ShipmentQualityControlReturnId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        public int? ShipmentQualityControlId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        public int? PurchaseOrderId { get; set; }
        public int? LoanOrderId { get; set; }
        public int? SupplierId { get; set; }
        public int? SupplementaryManufacturerId { get; set; }
        public int? CustomerId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [StringLength(500)]
        public string Reference { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ShipmentQualityControlReturns")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("ShipmentQualityControlReturns")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("ShipmentQualityControlReturns")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("ShipmentQualityControlReturns")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(ShipmentQualityControlId))]
        [InverseProperty("ShipmentQualityControlReturns")]
        public virtual ShipmentQualityControl ShipmentQualityControl { get; set; }
        [ForeignKey(nameof(SupplementaryManufacturerId))]
        [InverseProperty("ShipmentQualityControlReturns")]
        public virtual SupplementaryManufacturer SupplementaryManufacturer { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("ShipmentQualityControlReturns")]
        public virtual Supplier Supplier { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturnLine.ShipmentQualityControlReturn))]
        public virtual ICollection<ShipmentQualityControlReturnLine> ShipmentQualityControlReturnLines { get; set; }
    }
}
