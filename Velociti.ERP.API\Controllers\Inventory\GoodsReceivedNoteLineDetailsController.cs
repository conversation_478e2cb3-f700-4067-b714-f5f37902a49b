﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class GoodsReceivedNoteLineDetailsController : ControllerBase
    {
        private readonly IGoodsReceiveNoteLineDetailService _goodsReceiveNoteLineDetailService;

        public GoodsReceivedNoteLineDetailsController(IGoodsReceiveNoteLineDetailService goodsReceiveNoteLineDetailService)
        {
            _goodsReceiveNoteLineDetailService = goodsReceiveNoteLineDetailService;
        }

        [HttpGet]
        [Route("{grnLineId}")]
        public async Task<IActionResult> Get(int grnLineId)
        {
            return Ok(await _goodsReceiveNoteLineDetailService.GetAsync(grnLineId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]GRNLineDetailSummary gRNLineDetailSummary)
        {
            return Ok(await _goodsReceiveNoteLineDetailService.SaveListAsync(gRNLineDetailSummary));
        }

        [HttpDelete]
        public async Task<IActionResult> Delete([FromBody]GoodsReceiveNoteLineDetail goodsReceiveNoteLineDetail)
        {
            switch(goodsReceiveNoteLineDetail.Action)
            {
                case "delete single": await _goodsReceiveNoteLineDetailService.DeleteAsync(goodsReceiveNoteLineDetail.GoodsReceiveNoteLineDetailId);break;
                case "delete all": await _goodsReceiveNoteLineDetailService.DeleteByGRNLineIdAsync(goodsReceiveNoteLineDetail.GoodsReceiveNoteLineId.Value);break;
            }
            
            return Ok();
        }
    }
}