﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("QCParameters", Schema = "inv")]
    public partial class Qcparameter
    {
        public Qcparameter()
        {
            QcparameterMappings = new HashSet<QcparameterMapping>();
            SemiFinishedGoodsQcscanningLines = new HashSet<SemiFinishedGoodsQcscanningLine>();
        }

        [Key]
        public int ParameterId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(255)]
        public string ParameterDescription { get; set; }
        [StringLength(15)]
        public string UnitOfMeasure { get; set; }
        public int? ParameterSequence { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [InverseProperty(nameof(QcparameterMapping.Parameter))]
        public virtual ICollection<QcparameterMapping> QcparameterMappings { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsQcscanningLine.Parameter))]
        public virtual ICollection<SemiFinishedGoodsQcscanningLine> SemiFinishedGoodsQcscanningLines { get; set; }
    }
}
