﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using System.Linq;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class UsersRepository : IUsersRepository
    {
        private readonly MarangoniERPContext _context;

        public UsersRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<User> FindByIdAsync(int id)
        {
            return await _context.Users.Include(p => p.Login).AsNoTracking().FirstOrDefaultAsync(c => c.UserId == id);
        }

        public async Task<User> GetIsDepartmentFilterByUserIdAsync(int userId)
        {
            var user = await _context.Users.Include(p => p.UserDepartments).FirstAsync(p => p.UserId == userId);

            return user;
        }

        public async Task<User> FindByCodeAsync(string code)
        {
            return await _context.Users.FirstOrDefaultAsync(c => c.UserCode == code);
        }

        public async Task<IEnumerable<User>> GetAllAsync()
        {
            return await _context.Users.ToListAsync();
        }

        public async Task<IEnumerable<User>> GetAllWithLoginAsync() // New method implementation
        {
            return await _context.Users
                .Include(u => u.Login) // Include the Login navigation property
                .AsNoTracking()
                .ToListAsync();
        }

        public async Task<IEnumerable<User>> GetActiveAllAsync(int companyId)
        {
            var result = from u in _context.Users
                         join uc in _context.UserCompanies on u.UserId equals uc.UserId
                         where uc.CompanyId == companyId
                            && u.ExpiryDate == null
                         select u;

            return await result.ToListAsync();
        }

        public async Task<IEnumerable<User>> GetAllByDesignationAsync(int designationId, int userId, int companyId)
        {
            return await _context.UserCompanies.Where(p => p.DesignationId == designationId && p.CompanyId == companyId).Select(p => p.User).ToListAsync();
        }

        public async Task<IEnumerable<User>> GetParentUsersOfDesignationAsync(int designationId)
        {
            var parentDesignationIds = new List<int>();

            var designation = await _context.Designations.FindAsync(designationId);
            while (designation != null && designation.ParentDesignationId.HasValue)
            {
                parentDesignationIds.Add(designation.ParentDesignationId.Value);

                designation = await _context.Designations.Where(p => p.DesignationId == designation.ParentDesignationId.Value && p.ExpiryDate == null).FirstOrDefaultAsync();
            }

            var result = from u in _context.Users
                         join uc in _context.UserCompanies on u.UserId equals uc.UserId
                         where parentDesignationIds.Contains(uc.DesignationId)
                            && uc.ExpiryDate == null
                            && u.ExpiryDate == null
                         select u;

            return await result.ToListAsync();
        }

        public async Task<User> SaveAsync(User user)
        {
            try
            {
                if (user.UserId == default)
                {
                    user.CreationDate = DateTime.Now;
                    await _context.Users.AddAsync(user);
                }
                else
                {
                    _context.Users.Attach(user);
                    _context.Entry(user).State = EntityState.Modified;

                    var login = user.Login;
                    if(!String.IsNullOrEmpty(login.PasswordHash))
                    {
                        _context.Logins.Attach(login);
                        var entry = _context.Entry(login);
                        entry.Property(p => p.PasswordHash).IsModified = true;
                        entry.Property(p => p.ModifiedUserId).IsModified = true;
                    }
                }

                await _context.SaveChangesAsync();

                return user;
            }
            catch (Exception ex)
            {

                throw ex;
            }
        }

        public async Task ToggleActivationAsync(int id)
        {
            var user = await _context.Users.FirstAsync(c => c.UserId == id);

            var logins = await _context.Logins.FirstAsync(l => l.LoginId == id);

            user.ExpiryDate = user.ExpiryDate == null ? DateTime.Now : (DateTime?)null;

            logins.ExpiryDate = logins.ExpiryDate == null ? DateTime.Now : (DateTime?)null;

            await _context.SaveChangesAsync();
        }
    }
}
