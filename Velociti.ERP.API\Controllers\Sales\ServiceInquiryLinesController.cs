﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class ServiceInquiryLinesController : ControllerBase
    {
        private readonly IServiceInquiryLineService _serviceInquiryLineService;

        public ServiceInquiryLinesController(IServiceInquiryLineService serviceInquiryLineService)
        {
            _serviceInquiryLineService = serviceInquiryLineService;
        }

        [HttpGet]
        [Route("{serviceInquiryId}")]
        public async Task<IActionResult> Get(int serviceInquiryId)
        {
            return Ok(await _serviceInquiryLineService.GetAsync(serviceInquiryId));
        }
    }
}