﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalOrderLines", Schema = "inv")]
    public partial class InternalOrderLine
    {
        [Key]
        public int InternalOrderLineId { get; set; }
        public int? InternalOrderId { get; set; }
        public int? ProductId { get; set; }
        public int? WarehouseId { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        public int? BillOfMaterialId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public decimal? OnHandQuantity { get; set; }
        public decimal? Quantity { get; set; }
        public decimal UnitCost { get; set; }
        public decimal LastCost { get; set; }
        public decimal? QuantityInBase { get; set; }
        public decimal? BalanceQuantity { get; set; }
        public bool? AllowStockAllocation { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? ProductionOrderIdLinkedToRework { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.InternalOrderLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(BillOfMaterialId))]
        [InverseProperty("InternalOrderLines")]
        public virtual BillOfMaterial BillOfMaterial { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.InternalOrderLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(InternalOrderId))]
        [InverseProperty("InternalOrderLines")]
        public virtual InternalOrder InternalOrder { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("InternalOrderLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ProductionOrderIdLinkedToRework))]
        [InverseProperty(nameof(ProductionOrder.InternalOrderLines))]
        public virtual ProductionOrder ProductionOrderIdLinkedToReworkNavigation { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.InternalOrderLines))]
        public virtual Warehous Warehouse { get; set; }
    }
}
