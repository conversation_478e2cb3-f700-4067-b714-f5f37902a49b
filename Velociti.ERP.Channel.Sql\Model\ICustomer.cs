﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Customers")]
    public partial class ICustomer
    {
        [StringLength(255)]
        public string CustomerCode { get; set; }
        [StringLength(255)]
        public string CustomerName { get; set; }
        [StringLength(255)]
        public string Category { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        [StringLength(255)]
        public string CountryOfOrigin { get; set; }
        [StringLength(255)]
        public string PayingParty { get; set; }
        [StringLength(255)]
        public string LocalAgent { get; set; }
        [StringLength(255)]
        public string BillingCurrency { get; set; }
        [StringLength(255)]
        public string ContactPersonName { get; set; }
        [StringLength(255)]
        public string ContactNoPhone { get; set; }
        [StringLength(255)]
        public string ContactNoMobile { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string TaxRegistrationNo { get; set; }
        [StringLength(255)]
        public string DeliveryMode { get; set; }
        [StringLength(255)]
        public string DeliveryTerm { get; set; }
        public int? CreditDays { get; set; }
        [Column(TypeName = "money")]
        public decimal? CreditLimit { get; set; }
        [StringLength(255)]
        public string PayeeName { get; set; }
        [StringLength(255)]
        public string CustomerGroup { get; set; }
        [StringLength(255)]
        public string CustomerType { get; set; }
        [StringLength(50)]
        public string PriceList { get; set; }
    }
}
