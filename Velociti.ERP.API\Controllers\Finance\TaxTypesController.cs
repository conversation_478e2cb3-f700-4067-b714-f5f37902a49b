﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class TaxTypesController : ControllerBase
    {
        private readonly ITaxTypeService _taxTypeService;

        public TaxTypesController(ITaxTypeService taxTypeService)
        {
            _taxTypeService = taxTypeService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _taxTypeService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("Active/{companyId}")]
        public async Task<IActionResult> GetActiveAll(int companyId)
        {
            return Ok(await _taxTypeService.GetActiveAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}/Category/{categoryEnum}")]
        public async Task<IActionResult> GetShortListAsync(int companyId, byte categoryEnum)
        {
            return Ok(await _taxTypeService.GetShortListAsync(companyId, categoryEnum));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]TaxType taxType)
        {
            await _taxTypeService.SaveAsync(taxType);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Delete([FromBody]TaxType taxType)
        {
            await _taxTypeService.ToggleActivationAsync(taxType.TaxTypeId, taxType.ModifiedUserId);

            return Ok();
        }
    }
}