﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Administration;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class GoodsReceivedNotesController : ControllerBase
    {
        private readonly IAppSettingsService _appSettingsService;
        private readonly IGoodsReceivedNoteService _goodsReceivedNoteService;

        public GoodsReceivedNotesController(IGoodsReceivedNoteService goodsReceivedNoteService, IAppSettingsService appSettingsService)
        {
            _goodsReceivedNoteService = goodsReceivedNoteService;
            _appSettingsService = appSettingsService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            var appSettings = await _appSettingsService.GetAllAsync(companyId);
            if (appSettings.EffectiveDate.HasValue && appSettings.EffectiveDate < DateTime.Now)
                return BadRequest();

            return Ok(await _goodsReceivedNoteService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("Single/{grnId}")]
        public async Task<IActionResult> FindById(int grnId)
        {
            return Ok(await _goodsReceivedNoteService.FindByIdAsync(grnId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _goodsReceivedNoteService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Supplier/{supplierId}")]
        public async Task<IActionResult> GetShortListBySupplier(int supplierId)
        {
            var list = await _goodsReceivedNoteService.GetShortListBySupplierAsync(supplierId, new byte[] { (byte)GoodsReceiveNote.Status.Submitted });

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Supplier/{supplierId}/StatusEnums/{statusEnums}")]
        public async Task<IActionResult> GetShortListBySupplierAsync(int supplierId, string statusEnums)
        {
            var list = await _goodsReceivedNoteService.GetShortListBySupplierAsync(supplierId, Array.ConvertAll(statusEnums.Split(','), byte.Parse));

            return Ok(list);
        }

        [HttpGet]
        [Route("SubmittedShortList/Supplier/{supplierId}")]
        public async Task<IActionResult> GetSubmittedShortListBySupplier(int supplierId)
        {
            var list = await _goodsReceivedNoteService.GetSubmittedShortListBySupplierAsync(supplierId);  

            return Ok(list);
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _goodsReceivedNoteService.GetForApprovalAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("SettlementDocuments/{grnId}")]
        public async Task<IActionResult> GetSettlementDocumentsAsync(int grnId)
        {
            return Ok(await _goodsReceivedNoteService.GetSettlementDocumentsAsync(grnId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]GoodsReceiveNote goodsReceiveNote)
        {
            switch (goodsReceiveNote.Action)
            {
                case "update": await _goodsReceivedNoteService.SaveAsync(goodsReceiveNote); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/User/{userId}")]
        public async Task<IActionResult> Cancel(int id, int userId)
        {
            await _goodsReceivedNoteService.CancelAsync(id, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]GoodsReceiveNote goodsReceiveNote)
        {
            switch (goodsReceiveNote.Action)
            {
                case "submit": return Ok(await _goodsReceivedNoteService.SubmitAsync(goodsReceiveNote.GoodsReceiveNoteId, goodsReceiveNote.ModifiedUserId.Value));
                case "reverse": await _goodsReceivedNoteService.ReverseAsync(goodsReceiveNote.GoodsReceiveNoteId, goodsReceiveNote.ModifiedUserId.Value); break;
                case "convert to SQC": return Ok(await _goodsReceivedNoteService.ConvertToShipmentQCAsync(goodsReceiveNote.GoodsReceiveNoteId, goodsReceiveNote.ModifiedUserId.Value));
                case "send for approval": await _goodsReceivedNoteService.SendForApprovalAsync(goodsReceiveNote.GoodsReceiveNoteId, goodsReceiveNote.CompanyId.Value, goodsReceiveNote.ModifiedUserId.Value); break;
                case "update work flow": await _goodsReceivedNoteService.UpdateWorkFlowStatusAsync(goodsReceiveNote); break;
            }

            return Ok();
        }
    }
}