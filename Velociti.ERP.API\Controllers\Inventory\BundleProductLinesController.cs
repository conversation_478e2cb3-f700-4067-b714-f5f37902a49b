﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Authorize]
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class BundleProductLinesController : ControllerBase
    {
        private readonly IBundleProductLineService _bundleProductLineService;

        public BundleProductLinesController(IBundleProductLineService bundleProductLineService)
        {
            _bundleProductLineService = bundleProductLineService;
        }

        [HttpGet]
        [Route("{bundleProductId}")]
        public async Task<IActionResult> GetByBundleProductIdAsync(int bundleProductId)
        {
            return Ok(await _bundleProductLineService.GetByBundleProductIdAsync(bundleProductId));
        }
    }
}