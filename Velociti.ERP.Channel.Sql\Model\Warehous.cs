﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Warehouses", Schema = "inv")]
    public partial class Warehous
    {
        public Warehous()
        {
            AppSettingConsignmentHoreturnWarehouses = new HashSet<AppSetting>();
            AppSettingConsignmentStockTransferWarehouses = new HashSet<AppSetting>();
            BundleProducts = new HashSet<BundleProduct>();
            ConsignmentStockTransferFromWarehouses = new HashSet<ConsignmentStockTransfer>();
            ConsignmentStockTransferToWarehouses = new HashSet<ConsignmentStockTransfer>();
            Customers = new HashSet<Customer>();
            ExchangeOrderLines = new HashSet<ExchangeOrderLine>();
            GoodsDispatchNoteLineDetails = new HashSet<GoodsDispatchNoteLineDetail>();
            GoodsDispatchNoteLines = new HashSet<GoodsDispatchNoteLine>();
            GoodsReceiveNoteLineDetails = new HashSet<GoodsReceiveNoteLineDetail>();
            HoreturnStockTransferFromWarehouses = new HashSet<HoreturnStockTransfer>();
            HoreturnStockTransferToWarehouses = new HashSet<HoreturnStockTransfer>();
            InternalDispatchLineDeliveryWarehouses = new HashSet<InternalDispatchLine>();
            InternalDispatchLineDestinationWarehouses = new HashSet<InternalDispatchLine>();
            InternalDispatches = new HashSet<InternalDispatch>();
            InternalOrderLines = new HashSet<InternalOrderLine>();
            InternalReturnLineFromWarehouses = new HashSet<InternalReturnLine>();
            InternalReturnLineWarehouses = new HashSet<InternalReturnLine>();
            LoadingPlanLineDetails = new HashSet<LoadingPlanLineDetail>();
            MaterialRequisitionNoteLines = new HashSet<MaterialRequisitionNoteLine>();
            MaterialRequisitionNotes = new HashSet<MaterialRequisitionNote>();
            Products = new HashSet<Product>();
            PurchaseRequisitionNoteLines = new HashSet<PurchaseRequisitionNoteLine>();
            PurchaseReturnLines = new HashSet<PurchaseReturnLine>();
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SalesInvoices = new HashSet<SalesInvoice>();
            SalesOrderLines = new HashSet<SalesOrderLine>();
            SalesOrders = new HashSet<SalesOrder>();
            SalesReturnLines = new HashSet<SalesReturnLine>();
            SemiFinishedGoodsQcscannings = new HashSet<SemiFinishedGoodsQcscanning>();
            StockAdjustments = new HashSet<StockAdjustment>();
            StockCountSheets = new HashSet<StockCountSheet>();
            StockTakes = new HashSet<StockTake>();
            StockTransferFromWarehouses = new HashSet<StockTransfer>();
            StockTransferReceiptFromWarehouses = new HashSet<StockTransferReceipt>();
            StockTransferReceiptToWarehouses = new HashSet<StockTransferReceipt>();
            StockTransferToWarehouses = new HashSet<StockTransfer>();
            TouchScreenHistories = new HashSet<TouchScreenHistory>();
            TxnLineHistoryDstWarehouses = new HashSet<TxnLineHistory>();
            TxnLineHistorySrcWarehouses = new HashSet<TxnLineHistory>();
            WarehouseProducts = new HashSet<WarehouseProduct>();
            WarehouseStockIssueDstWarehouses = new HashSet<WarehouseStockIssue>();
            WarehouseStockIssueSrcWarehouses = new HashSet<WarehouseStockIssue>();
            WarehouseStockQualityControlDstWarehouses = new HashSet<WarehouseStockQualityControl>();
            WarehouseStockQualityControlSrcWarehouses = new HashSet<WarehouseStockQualityControl>();
        }

        [Key]
        public int WarehouseId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string WarehouseCode { get; set; }
        [StringLength(255)]
        public string WarehouseName { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        public bool? IsAutoGenerateLotNumbers { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? NatureEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Warehous")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(AppSetting.ConsignmentHoreturnWarehouse))]
        public virtual ICollection<AppSetting> AppSettingConsignmentHoreturnWarehouses { get; set; }
        [InverseProperty(nameof(AppSetting.ConsignmentStockTransferWarehouse))]
        public virtual ICollection<AppSetting> AppSettingConsignmentStockTransferWarehouses { get; set; }
        [InverseProperty(nameof(BundleProduct.Warehouse))]
        public virtual ICollection<BundleProduct> BundleProducts { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransfer.FromWarehouse))]
        public virtual ICollection<ConsignmentStockTransfer> ConsignmentStockTransferFromWarehouses { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransfer.ToWarehouse))]
        public virtual ICollection<ConsignmentStockTransfer> ConsignmentStockTransferToWarehouses { get; set; }
        [InverseProperty(nameof(Customer.Warehouse))]
        public virtual ICollection<Customer> Customers { get; set; }
        [InverseProperty(nameof(ExchangeOrderLine.Warehouse))]
        public virtual ICollection<ExchangeOrderLine> ExchangeOrderLines { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLineDetail.Warehouse))]
        public virtual ICollection<GoodsDispatchNoteLineDetail> GoodsDispatchNoteLineDetails { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLine.Warehouse))]
        public virtual ICollection<GoodsDispatchNoteLine> GoodsDispatchNoteLines { get; set; }
        [InverseProperty(nameof(GoodsReceiveNoteLineDetail.Warehouse))]
        public virtual ICollection<GoodsReceiveNoteLineDetail> GoodsReceiveNoteLineDetails { get; set; }
        [InverseProperty(nameof(HoreturnStockTransfer.FromWarehouse))]
        public virtual ICollection<HoreturnStockTransfer> HoreturnStockTransferFromWarehouses { get; set; }
        [InverseProperty(nameof(HoreturnStockTransfer.ToWarehouse))]
        public virtual ICollection<HoreturnStockTransfer> HoreturnStockTransferToWarehouses { get; set; }
        [InverseProperty(nameof(InternalDispatchLine.DeliveryWarehouse))]
        public virtual ICollection<InternalDispatchLine> InternalDispatchLineDeliveryWarehouses { get; set; }
        [InverseProperty(nameof(InternalDispatchLine.DestinationWarehouse))]
        public virtual ICollection<InternalDispatchLine> InternalDispatchLineDestinationWarehouses { get; set; }
        [InverseProperty(nameof(InternalDispatch.DeliveryWarehouse))]
        public virtual ICollection<InternalDispatch> InternalDispatches { get; set; }
        [InverseProperty(nameof(InternalOrderLine.Warehouse))]
        public virtual ICollection<InternalOrderLine> InternalOrderLines { get; set; }
        [InverseProperty(nameof(InternalReturnLine.FromWarehouse))]
        public virtual ICollection<InternalReturnLine> InternalReturnLineFromWarehouses { get; set; }
        [InverseProperty(nameof(InternalReturnLine.Warehouse))]
        public virtual ICollection<InternalReturnLine> InternalReturnLineWarehouses { get; set; }
        [InverseProperty(nameof(LoadingPlanLineDetail.Warehouse))]
        public virtual ICollection<LoadingPlanLineDetail> LoadingPlanLineDetails { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNoteLine.DeliveryWarehouse))]
        public virtual ICollection<MaterialRequisitionNoteLine> MaterialRequisitionNoteLines { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNote.DeliveryWarehouse))]
        public virtual ICollection<MaterialRequisitionNote> MaterialRequisitionNotes { get; set; }
        [InverseProperty(nameof(Product.DefaultWarehouse))]
        public virtual ICollection<Product> Products { get; set; }
        [InverseProperty(nameof(PurchaseRequisitionNoteLine.Warehouse))]
        public virtual ICollection<PurchaseRequisitionNoteLine> PurchaseRequisitionNoteLines { get; set; }
        [InverseProperty(nameof(PurchaseReturnLine.Warehouse))]
        public virtual ICollection<PurchaseReturnLine> PurchaseReturnLines { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.DispatchWarehouse))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SalesInvoice.DispatchWarehouse))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
        [InverseProperty(nameof(SalesOrderLine.DispatchWarehouse))]
        public virtual ICollection<SalesOrderLine> SalesOrderLines { get; set; }
        [InverseProperty(nameof(SalesOrder.DispatchWarehouse))]
        public virtual ICollection<SalesOrder> SalesOrders { get; set; }
        [InverseProperty(nameof(SalesReturnLine.DispatchWarehouse))]
        public virtual ICollection<SalesReturnLine> SalesReturnLines { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsQcscanning.Warehouse))]
        public virtual ICollection<SemiFinishedGoodsQcscanning> SemiFinishedGoodsQcscannings { get; set; }
        [InverseProperty(nameof(StockAdjustment.Warehouse))]
        public virtual ICollection<StockAdjustment> StockAdjustments { get; set; }
        [InverseProperty(nameof(StockCountSheet.Warehouse))]
        public virtual ICollection<StockCountSheet> StockCountSheets { get; set; }
        [InverseProperty(nameof(StockTake.Warehouse))]
        public virtual ICollection<StockTake> StockTakes { get; set; }
        [InverseProperty(nameof(StockTransfer.FromWarehouse))]
        public virtual ICollection<StockTransfer> StockTransferFromWarehouses { get; set; }
        [InverseProperty(nameof(StockTransferReceipt.FromWarehouse))]
        public virtual ICollection<StockTransferReceipt> StockTransferReceiptFromWarehouses { get; set; }
        [InverseProperty(nameof(StockTransferReceipt.ToWarehouse))]
        public virtual ICollection<StockTransferReceipt> StockTransferReceiptToWarehouses { get; set; }
        [InverseProperty(nameof(StockTransfer.ToWarehouse))]
        public virtual ICollection<StockTransfer> StockTransferToWarehouses { get; set; }
        [InverseProperty(nameof(TouchScreenHistory.Warehouse))]
        public virtual ICollection<TouchScreenHistory> TouchScreenHistories { get; set; }
        [InverseProperty(nameof(TxnLineHistory.DstWarehouse))]
        public virtual ICollection<TxnLineHistory> TxnLineHistoryDstWarehouses { get; set; }
        [InverseProperty(nameof(TxnLineHistory.SrcWarehouse))]
        public virtual ICollection<TxnLineHistory> TxnLineHistorySrcWarehouses { get; set; }
        [InverseProperty(nameof(WarehouseProduct.Warehouse))]
        public virtual ICollection<WarehouseProduct> WarehouseProducts { get; set; }
        [InverseProperty(nameof(WarehouseStockIssue.DstWarehouse))]
        public virtual ICollection<WarehouseStockIssue> WarehouseStockIssueDstWarehouses { get; set; }
        [InverseProperty(nameof(WarehouseStockIssue.SrcWarehouse))]
        public virtual ICollection<WarehouseStockIssue> WarehouseStockIssueSrcWarehouses { get; set; }
        [InverseProperty(nameof(WarehouseStockQualityControl.DstWarehouse))]
        public virtual ICollection<WarehouseStockQualityControl> WarehouseStockQualityControlDstWarehouses { get; set; }
        [InverseProperty(nameof(WarehouseStockQualityControl.SrcWarehouse))]
        public virtual ICollection<WarehouseStockQualityControl> WarehouseStockQualityControlSrcWarehouses { get; set; }
    }
}
