﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MachineLines", Schema = "man")]
    public partial class MachineLine
    {
        [Key]
        public int MachineLineId { get; set; }
        public int MachineId { get; set; }
        public int? OverheadInformationId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }
        [Column(TypeName = "money")]
        public decimal? AmountPerUnit { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(MachineId))]
        [InverseProperty("MachineLines")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(OverheadInformationId))]
        [InverseProperty(nameof(SupportData.MachineLines))]
        public virtual SupportData OverheadInformation { get; set; }
    }
}
