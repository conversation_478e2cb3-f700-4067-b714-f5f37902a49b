﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProfileBuilding", Schema = "man")]
    public partial class ProfileBuilding
    {
        public ProfileBuilding()
        {
            ProfileBuildingLines = new HashSet<ProfileBuildingLine>();
        }

        [Key]
        public int ProfileBuildingId { get; set; }
        public int? CompanyId { get; set; }
        public int? MachineId { get; set; }
        public int? DieId { get; set; }
        public int? RequiredQty { get; set; }
        public int? ProducedQty { get; set; }
        [Column(TypeName = "date")]
        public DateTime? PlannedStartDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? StartDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EndDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ProfileBuildings")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DieId))]
        [InverseProperty(nameof(SupportData.ProfileBuildings))]
        public virtual SupportData Die { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("ProfileBuildings")]
        public virtual Machine Machine { get; set; }
        [InverseProperty(nameof(ProfileBuildingLine.ProfileBuilding))]
        public virtual ICollection<ProfileBuildingLine> ProfileBuildingLines { get; set; }
    }
}
