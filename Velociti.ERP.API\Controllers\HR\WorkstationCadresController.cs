﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Services.HR;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class WorkstationCadresController : ControllerBase  
    {
        private readonly IWorkstationCadreService _workstationCadreService;

        public WorkstationCadresController(IWorkstationCadreService workstationCadreService)
        {
            _workstationCadreService = workstationCadreService;
        }

        [HttpGet]
        public async Task<ActionResult<Response<IEnumerable<WorkstationCadre>>>> Get(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _workstationCadreService.GetAllAsync(companyId, startDate, endDate));
        }

        [HttpGet]
        [Route("Single")]
        public async Task<ActionResult<Response<bool>>> GetExistingWorkStationCadreByEmployee(int workstationCadreId, int companyId, DateTime cadreDate, string employeeList, int shiftEnum, int? machineId, int? pressLineId)
        {
            bool isExist = await _workstationCadreService.GetExistingWorkStationCadreByEmployee(workstationCadreId, companyId, cadreDate, employeeList, shiftEnum, machineId?? 0, pressLineId ?? 0);

            return Ok(isExist);
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]WorkstationCadre[] workstationCadres)  
        {
            if (workstationCadres == null)
                return BadRequest();

            await _workstationCadreService.SaveAsync(workstationCadres);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/loggedInUser/{loggedInUserId}")]
        public async Task<IActionResult> ToggleActivation(int id, int loggedInUserId)
        {
            await _workstationCadreService.ToggleActivationAsync(id, loggedInUserId);

            return Ok();
        }
    }
}