﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class GoodsDispatchNoteLinesController : ControllerBase
    {
        private readonly IGoodsDispatchNoteLineService _goodsDispatchNoteLineService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public GoodsDispatchNoteLinesController(IGoodsDispatchNoteLineService goodsDispatchNoteLineService, IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _goodsDispatchNoteLineService = goodsDispatchNoteLineService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }

        [HttpGet]
        [Route("{goodsDispatchNoteId}")]
        public async Task<IActionResult> GetByHeaderId(int goodsDispatchNoteId)
        {
            return Ok(await _goodsDispatchNoteLineService.GetByHeaderIdAsync(goodsDispatchNoteId));
        }

        [HttpGet]
        [Route("GDN/{goodsDispatchNoteId}")]
        public async Task<IActionResult> GetByHeader(int goodsDispatchNoteId)
        {
            return Ok(await _goodsDispatchNoteLineService.GetByHeaderAsync(goodsDispatchNoteId));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]GoodsDispatchNoteLine goodsDispatchNoteLine)
        {
            await _goodsDispatchNoteLineService.SaveAsync(goodsDispatchNoteLine);

            return Ok(await _goodsDispatchNoteService.CalculateHeaderValuesAsync(goodsDispatchNoteLine.GoodsDispatchNoteId.Value));
        }

        [HttpDelete]
        public async Task<IActionResult> Delete([FromBody]GoodsDispatchNoteLine goodsDispatchNoteLine)
        {
            await _goodsDispatchNoteLineService.RemoveAsync(goodsDispatchNoteLine.GoodsDispatchNoteLineId);

            return Ok(await _goodsDispatchNoteService.CalculateHeaderValuesAsync(goodsDispatchNoteLine.GoodsDispatchNoteId.Value));
        }
    }
}