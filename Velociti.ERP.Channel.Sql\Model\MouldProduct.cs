﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MouldProducts", Schema = "man")]
    public partial class MouldProduct
    {
        [Key]
        public int MouldId { get; set; }
        [Key]
        public int ProductId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(MouldId))]
        [InverseProperty("MouldProducts")]
        public virtual Mould Mould { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("MouldProducts")]
        public virtual Product Product { get; set; }
    }
}
