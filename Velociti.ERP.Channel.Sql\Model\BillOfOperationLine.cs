﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BillOfOperationLines", Schema = "man")]
    public partial class BillOfOperationLine
    {
        public BillOfOperationLine()
        {
            BillOfOperationLineDetails = new HashSet<BillOfOperationLineDetail>();
            BillOfOperationLineOverheads = new HashSet<BillOfOperationLineOverhead>();
        }

        [Key]
        public int BillOfOperationLineId { get; set; }
        public int BillOfOperationId { get; set; }
        public int? SequenceNo { get; set; }
        public int? MachineId { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? StandardDuration { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? HandlingTime { get; set; }
        public int? SecondaryMachineId { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? SecondaryMachineStandardDuration { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? SecondaryMachineHandlingTime { get; set; }
        [Column(TypeName = "decimal(10, 4)")]
        public decimal? CumWeight { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfOperationId))]
        [InverseProperty("BillOfOperationLines")]
        public virtual BillOfOperation BillOfOperation { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("BillOfOperationLines")]
        public virtual Machine Machine { get; set; }
        [InverseProperty(nameof(BillOfOperationLineDetail.BillOfOperationLine))]
        public virtual ICollection<BillOfOperationLineDetail> BillOfOperationLineDetails { get; set; }
        [InverseProperty(nameof(BillOfOperationLineOverhead.BillOfOperationLine))]
        public virtual ICollection<BillOfOperationLineOverhead> BillOfOperationLineOverheads { get; set; }
    }
}
