﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ShipmentQualityControlReturnsController : ControllerBase
    {
        private readonly IShipmentQualityControlReturnService _shipmentQualityControlReturnService;

        public ShipmentQualityControlReturnsController(IShipmentQualityControlReturnService shipmentQualityControlReturnService)
        {
            _shipmentQualityControlReturnService = shipmentQualityControlReturnService;
        }

        [HttpGet]
        [Route("Single/{shipmentQCReturnId}")]
        public async Task<IActionResult> FindById(int shipmentQCReturnId)
        {
            return Ok(await _shipmentQualityControlReturnService.FindByIdAsync(shipmentQCReturnId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _shipmentQualityControlReturnService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]ShipmentQualityControlReturn shipmentQualityControlReturn)
        {
            switch (shipmentQualityControlReturn.Action)
            {
                case "submit": await _shipmentQualityControlReturnService.SubmitAsync(shipmentQualityControlReturn.ShipmentQualityControlReturnId, shipmentQualityControlReturn.ModifiedUserId.Value); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/User/{userId}")]
        public async Task<IActionResult> Cancel(int id, int userId)
        {
            await _shipmentQualityControlReturnService.CancelAsync(id, userId);

            return Ok();
        }
    }
}