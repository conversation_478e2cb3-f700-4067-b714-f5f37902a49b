﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductsController : ControllerBase
    {
        private readonly IProductService _productService;

        public ProductsController(IProductService productService)
        {
            _productService = productService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _productService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("ActiveAll/Company/{companyId}")]
        public async Task<IActionResult> GetActiveAll(int companyId)
        {
            return Ok(await _productService.GetActiveAllAsync(companyId));
        }

        [HttpGet]
        [Route("{productId}")]
        public async Task<IActionResult> GetById(int productId)
        {
            return Ok(await _productService.FindByIdAsync(productId));
        }

        [HttpGet]
        [Route("{productId}/StockTransfer/{stockTransferId}")]
        public async Task<IActionResult> GetStockTransferProductById(int productId, int stockTransferId)  
        {
            return Ok(await _productService.FindStockTransferProductByIdAsync(stockTransferId, productId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _productService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Category/{categoryId}/Brand/{brandId}")]
        public async Task<IActionResult> GetShortList(int companyId, int categoryId, int brandId)
        {
            var list = await _productService.GetShortListAsync(companyId, categoryId, brandId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Category/{categoryId}/Brand/{brandId}/ProductHierarchy2/{ProductHierarchyId2}")]
        public async Task<IActionResult> GetShortList(int companyId, int categoryId, int brandId, int productHierarchyId2)
        {
            var list = await _productService.GetShortListAsync(companyId, categoryId, brandId, productHierarchyId2);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Supplier/{supplierId}")]
        public async Task<IActionResult> GetShortListBySupplier(int companyId, int supplierId)
        {
            var list = await _productService.GetShortListBySupplierAsync(companyId, supplierId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/LicensedAndRegistered/{companyId}")]
        public async Task<IActionResult> GetShortListLicensedAndRegistered(int companyId)
        {
            var list = await _productService.GetLicensedAndRegisteredShortListAsync(companyId);

            return Ok(list);
        }

        [HttpPost]
        [Route("Filtered")]
        public async Task<IActionResult> GetByCategoriesAsync(ViewForm viewForm)
        {
            var list = await _productService.GetFilteredProductsAsync(viewForm.ParameterOne, viewForm.IntegerList);

            return Ok(list);
        }

        [HttpGet]
        [Route("NonServiceProductShortList/Company/{companyId}")]
        public async Task<IActionResult> GetNonServiceProductShortList(int companyId)
        {
            var list = await _productService.GetNonServiceProductShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("NonServiceProductShortListByGRN/Company/{companyId}/GRN/{grnId}")]
        public async Task<IActionResult> GetNonServiceProductShortListByGRN(int companyId, int grnId)
        {
            var list = await _productService.GetNonServiceProductShortListByGRN(companyId, grnId);

            return Ok(list);
        }

        [HttpGet]
        [Route("FinishedAndSemiFinishedProductShortList/Company/{companyId}")]
        public async Task<IActionResult> FinishedAndSemiFinishedProductShortList(int companyId)  
        {
            var list = await _productService.GetNonServiceProductShortList(companyId);

            return Ok(list);
        }


        [HttpGet]
        [Route("NonServiceProductsByWarehouseShortList/Company/{companyId}/Warehouse/{warehouseId}")]
        public async Task<IActionResult> GetNonServiceProductsByWarehouseShortList(int companyId, int warehouseId)
        {
            var list = await _productService.GetNonServiceProductsByWarehouseShortList(companyId, warehouseId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ServiceProductShortList/Company/{companyId}")]
        public async Task<IActionResult> GetServiceProductShortList(int companyId)  
        {
            var list = await _productService.GetServiceProductShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ComponentProductShortList/Company/{companyId}")]
        public async Task<IActionResult> GetComponentProductShortList(int companyId)
        {
            var list = await _productService.GetComponentProductShortList(companyId);  

            return Ok(list);
        }

        [HttpGet]
        [Route("GetFinishedGoodsShortList/Company/{companyId}/Category/{categoryEnum}")]
        public async Task<IActionResult> GetFinishedGoodsShortList(int companyId, int categoryEnum)
        {
            var list = await _productService.GetFinishedGoodsShortList(companyId, categoryEnum);  

            return Ok(list);
        }

        [HttpGet]
        [Route("GetManufacturedGoodsShortList/Company/{companyId}")]
        public async Task<IActionResult> GetManufacturedGoodsShortList(int companyId)
        {
            var list = await _productService.GetManufacturedGoodsShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("GetRawMaterialsShortList/Company/{companyId}")]
        public async Task<IActionResult> GetRawMaterialsShortList(int companyId)
        {
            var list = await _productService.GetRawMaterialsShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("GetRawMaterialsByBOMShortList/Company/{companyId}/BOM/{billOfMaterialId}")]
        public async Task<IActionResult> GetRawMaterialsByBOMShortList(int companyId, int billOfMaterialId)
        {
            var list = await _productService.GetRawMaterialsByBOMShortList(companyId, billOfMaterialId);

            return Ok(list);
        }

        [HttpGet]
        [Route("StockTransfer/{stockTransferId}")]
        public async Task<IActionResult> GetStockTranserProductsShortList(int stockTransferId)  
        {
            var list = await _productService.GetStockTransferProductsShortListAsync(stockTransferId);  

            return Ok(list);
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Barcode/{barcode}/Company/{companyId}")]
        public async Task<IActionResult> GetByBarcode(string barcode, int companyId)
        {
            try
            {
                var record = await _productService.GetByBarcodeAsync(barcode, companyId);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200,
                    data = record
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Category/{categoryEnum}")]
        public async Task<IActionResult> GetShortListByCategoryAsync(int companyId, byte categoryEnum)
        {
            return Ok(await _productService.GetShortListByCategoryAsync(companyId, categoryEnum));
        }

        [HttpGet]
        [Route("ShortListByCategoryAndBrand")]
        public async Task<IActionResult> GetShortListByCategoryAndBrandAsync(int companyId, byte categoryEnum, int brandId)
        {
            return Ok(await _productService.GetShortListByCategoryAndBrandAsync(companyId, categoryEnum, brandId));
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Category/{categoryEnum}/Division/{divisionId}")]
        public async Task<IActionResult> GetShortListByCategoryAndDivisionAsync(int companyId, byte categoryEnum, int divisionId)
        {
            return Ok(await _productService.GetShortListByCategoryAndDivisionAsync(companyId, categoryEnum, divisionId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Product product)
        {
            //await _productService.SaveAsync(product);
            return Ok(await _productService.SaveProduct(product));
        }

        [HttpDelete]
        [Route("{productId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int productId, int userId)
        {
            await _productService.ToggleActivationAsync(productId, userId);

            return Ok();
        }


        [HttpGet]
        [Route("FinishedProductShortList/Company/{companyId}")]
        public async Task<IActionResult> FinishedProductShortList(int companyId)
        {
            var list = await _productService.FinishedProductShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("SemiFinishedProductShortList/Company/{companyId}")]
        public async Task<IActionResult> SemiFinishedProductShortList(int companyId)
        {
            var list = await _productService.SemiFinishedProductShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("FinishedNonCabinProductShortList/Company/{companyId}")]
        public async Task<IActionResult> FinishedNonCabinProductShortList(int companyId)
        {
            var list = await _productService.FinishedProductNonCabinTypeShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("NonFinishedNonServiceProductShortList/Company/{companyId}")]
        public async Task<IActionResult> NonFinishedNonServiceProductShortList(int companyId)
        {
            var list = await _productService.NonFinishedNonServiceProductShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("FinishedAndServiceProductShortList/Company/{companyId}")]
        public async Task<IActionResult> FinishedAndServiceProductShortList(int companyId)
        {
            var list = await _productService.FinishedAndServiceProductShortList(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortListForDieAssign/Company/{companyId}")]
        public async Task<IActionResult> GetShortListForDieAssign(int companyId)
        {
            var list = await _productService.GetForDieAssignAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}/Nature/{natureEnum}")]
        public async Task<IActionResult> GetShortListForDieAssign(int companyId, byte natureEnum)
        {
            return Ok(await _productService.GetShortListByProductNatureAsync(companyId, natureEnum));
        }

        [HttpGet]
        [Route("CategoryShortList/IsAll/{isAll}")]
        public async Task<IActionResult> GetCategoryShortList(byte isAll)
        {
            var list = await _productService.GetProductCategoriesShortListAsync(isAll == 1);

            return Ok(list);
        }

        [HttpPost]
        [Route("ShortListByCategory")]
        public async Task<IActionResult> GetCategoryShortList([FromBody]ViewForm viewForm)
        {
            var list = await _productService.GetProductShortListByCategoriesAsync(viewForm.ParameterOne == 1, viewForm.StringArray);

            return Ok(list);
        }
        [HttpGet("VatValue")]
        public async Task<IActionResult> GetVatValue(int productId,int supplierId,decimal cost)
        {
            var vat = await _productService.GetVatAsync(productId, supplierId, cost);
            return Ok(new
            {
                Vat = vat
            });
        }

        [HttpGet]
        [Route("SupplierProducts/Company/{companyId}/Warehouse/{warehouseId}/Supplier/{supplierId}/Product/{productId}")]
        public async Task<IActionResult> GetSupplierProducts(int companyId, int warehouseId, int supplierId, int productId)
        {
            var products = await _productService.GetSupplierProductsAsync(companyId, warehouseId, supplierId, productId);
            return Ok(products);
        }
    }
}