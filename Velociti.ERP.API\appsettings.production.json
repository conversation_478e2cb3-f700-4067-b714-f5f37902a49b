{"Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}}, "ConnectionStrings": {"DefaultConnection": "Server=DESKTOP-NRUK56G\\SQLEXPRESS01;Database=NemsuERP;User Id=sa;Password=password1234; MultipleActiveResultSets=true"}, "AllowedHosts": "*", "AppSettings": {"Secret": "a-very-long-random-string-at-least-32-characters-long", "TokenExpiresIn": "24:00:00", "RefreshTokenExpiresIn": "24:00:00", "Client": "<PERSON><PERSON><PERSON>", "TransImagesFolderPath": "C:\\inetpub\\wwwroot\\NemsuiERP\\assets\\transRefDocs", "TransImagesViewFolderPath": "/NemsuiERP/assets/transRefDocs", "TransImagesArchiveFolderPath": "C:\\inetpub\\wwwroot\\NemsuiERP\\assets\\archiveDocs", "CompanyLogoImagesFolderPath": "C:\\inetpub\\wwwroot\\NemsuiERP\\assets\\companyLogo", "CompanyLogoImagesViewFolderPath": "/assets/companyLogo", "CompanyLogoArchiveFolderPath": "C:\\inetpub\\wwwroot\\NemsuiERP\\assets\\archiveDocs\\companyLogo"}}