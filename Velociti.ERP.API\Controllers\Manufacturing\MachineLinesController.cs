﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing  
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]  
    public class MachineLinesController : ControllerBase
    {
        private readonly IMachineLineService _machineLineService;  

        public MachineLinesController(IMachineLineService machineLineService)  
        {
            _machineLineService = machineLineService;
        }

        [HttpGet]
        [Route("{machineId}")]
        public async Task<IActionResult> Get(int machineId)
        {
            return Ok(await _machineLineService.GetAsync(machineId));
        }

        [HttpPost]
        public async Task<IActionResult> GetMachineLine([FromBody]MachineLine machineLine)  
        {
            return Ok(await _machineLineService.AddMachineLineAsync(machineLine));
        }
    }
}