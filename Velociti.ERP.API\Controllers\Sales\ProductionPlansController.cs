﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductionPlansController : ControllerBase
    {
        private readonly IProductionPlanService _productionPlanService;

        public ProductionPlansController(IProductionPlanService productionPlanService)
        {
            _productionPlanService = productionPlanService;
        }

        [HttpPut]
        [Route("Update")]
        public async Task<IActionResult> Update([FromBody]List<Velociti.ERP.Domain.Entities.Production.ProductionPlanLine> productionPlanLines, int companyId, int userId)
        {
            await _productionPlanService.UpdateFromPOMSAsync(productionPlanLines, userId);

            return Ok();
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("UpdateStartBarcodeScanned")]
        public async Task<IActionResult> UpdateStartBarcodeScanned(ProductionPlan productionPlan)
        {
            try
            {
                await _productionPlanService.UpdateStartBarcodeScannedAsync(productionPlan);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpGet]
        [Route("Selected/{salesOrderLineIds}/company/{companyId}/docTypeEnum/{docTypeEnum}")]
        public async Task<IActionResult> GetSelected(string salesOrderLineIds, int companyId, byte docTypeEnum)
        {
            string[] orderlineIdArray = salesOrderLineIds.Split(',');
            var list = orderlineIdArray.Select(p => int.Parse(p)).ToList();

            return Ok(await _productionPlanService.GetSelectedLinesAsync(list, companyId, docTypeEnum));
        }
    }
}