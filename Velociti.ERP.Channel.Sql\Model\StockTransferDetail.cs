﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockTransferDetails", Schema = "inv")]
    public partial class StockTransferDetail
    {
        [Key]
        public int StockTransferDetailId { get; set; }
        public int? StockTransferLineId { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? TransferCost { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }

        [ForeignKey(nameof(StockTransferLineId))]
        [InverseProperty("StockTransferDetails")]
        public virtual StockTransferLine StockTransferLine { get; set; }
    }
}
