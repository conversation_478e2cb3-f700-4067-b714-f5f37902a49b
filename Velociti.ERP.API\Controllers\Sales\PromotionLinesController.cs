﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class PromotionLinesController : ControllerBase
    {
        private readonly IPromotionLinesService _promotionLinesService;

        public PromotionLinesController(IPromotionLinesService promotionLinesService)
        {
            _promotionLinesService = promotionLinesService;
        }

        [HttpGet]
        [Route("{promotionId}")]
        public async Task<IActionResult> Get(int promotionId)
        {
            return Ok(await _promotionLinesService.GetAsync(promotionId));
        }
    }
}
