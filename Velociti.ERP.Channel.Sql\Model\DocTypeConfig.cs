﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("DocTypeConfig", Schema = "adm")]
    public partial class DocTypeConfig
    {
        [Key]
        public int DocTypeConfigId { get; set; }
        public int CompanyId { get; set; }
        public byte ModuleEnum { get; set; }
        [StringLength(255)]
        public string ModuleName { get; set; }
        public byte? DocTypeEnum { get; set; }
        [StringLength(255)]
        public string DocTypeName { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("DocTypeConfigs")]
        public virtual Company Company { get; set; }
    }
}
