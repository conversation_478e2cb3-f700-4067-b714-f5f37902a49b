﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class PaymentAdviceLinesController : ControllerBase  
    {
        private readonly IPaymentAdviceLineService _paymentAdviceLineService;    

        public PaymentAdviceLinesController(IPaymentAdviceLineService paymentAdviceLineService)      
        {
            _paymentAdviceLineService = paymentAdviceLineService;
        }

        [HttpGet]
        [Route("{paymentAdviceId}")]  
        public async Task<IActionResult> Get(int paymentAdviceId)  
        {
            return Ok(await _paymentAdviceLineService.GetAsync(paymentAdviceId));
        }

        [HttpGet]
        [Route("ForEdit/{paymentAdviceId}")]
        public async Task<IActionResult> GetForEdit(int paymentAdviceId)
        {
            return Ok(await _paymentAdviceLineService.GetForEditAsync(paymentAdviceId));
        }

        [HttpPost]
        public async Task<IActionResult> GetInternalServiceInvoiceLine([FromBody]PaymentAdviceLine paymentAdviceLine)    
        {
            return Ok(await _paymentAdviceLineService.AddPaymentAdviceLineAsync(paymentAdviceLine));
        }

        [HttpGet]
        [Route("Submitted/{companyId}")]
        public async Task<IActionResult> GetSubmitted(int companyId)
        {
            return Ok(await _paymentAdviceLineService.GetSubmittedLinesAsync(companyId));
        }

        [HttpGet]
        [Route("Selected/{paymentAdviceLineIds}/company/{companyId}")]
        public async Task<IActionResult> GetSelected(string paymentAdviceLineIds, int companyId)    
        {
            string[] orderlineIdArray = paymentAdviceLineIds.Split(',');
            var list = orderlineIdArray.Select(p => int.Parse(p)).ToList();

            return Ok(await _paymentAdviceLineService.GetSelectedLinesAsync(list, companyId));
        }

        [HttpGet]
        [Route("Customer/{customerId}/Type/{type}")]
        public async Task<IActionResult> GetByCustomerIdAsync(int customerId, int type)
        {
            return Ok(await _paymentAdviceLineService.GetByCustomerIdAsync(customerId, type));
        }

    }
}