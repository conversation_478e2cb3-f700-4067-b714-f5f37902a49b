﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("TransactionDocuments", Schema = "cmn")]
    public partial class TransactionDocument
    {
        [Key]
        public int TransactionDocumentId { get; set; }
        public int CompanyId { get; set; }
        public byte DocTypeEnum { get; set; }
        public int TransactionId { get; set; }
        public string FileName { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public bool? IsDeleted { get; set; }
        public int? LastModifiedUser { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifeDate { get; set; }
        [StringLength(50)]
        public string Remarks { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("TransactionDocuments")]
        public virtual Company Company { get; set; }
    }
}
