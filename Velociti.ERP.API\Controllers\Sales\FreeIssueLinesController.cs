﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class FreeIssueLinesController : ControllerBase
    {
        private readonly IFreeIssueLinesService _freeIssueLinesService;

        public FreeIssueLinesController(IFreeIssueLinesService freeIssueLinesService)
        {
            _freeIssueLinesService = freeIssueLinesService;
        }

        [HttpGet]
        [Route("{freeIssueId}")]
        public async Task<IActionResult> Get(int freeIssueId)
        {
            return Ok(await _freeIssueLinesService.GetAsync(freeIssueId));
        }
    }
}