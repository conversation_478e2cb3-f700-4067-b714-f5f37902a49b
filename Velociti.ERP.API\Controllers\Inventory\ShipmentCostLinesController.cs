﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ShipmentCostLinesController : ControllerBase
    {
        private readonly IShipmentCostLineService _shipmentCostLineService;

        public ShipmentCostLinesController(IShipmentCostLineService shipmentCostLineService)
        {
            _shipmentCostLineService = shipmentCostLineService;
        }

        [HttpGet]
        [Route("{shipmentCostId}")]
        public async Task<IActionResult> GetByHeaderAsync(int shipmentCostId)
        {
            return Ok(await _shipmentCostLineService.GetByHeaderAsync(shipmentCostId));
        }

        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]ShipmentCostLine shipmentCostLine)
        {
            return Ok(await _shipmentCostLineService.SaveAsync(shipmentCostLine));
        }

        [HttpDelete]
        public async Task<IActionResult> RemoveAsync([FromBody]ShipmentCostLine shipmentCostLine)
        {
            return Ok(await _shipmentCostLineService.RemoveAsync(shipmentCostLine));
        }
    }
}