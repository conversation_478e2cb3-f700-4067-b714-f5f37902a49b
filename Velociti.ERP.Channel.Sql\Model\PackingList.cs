﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PackingLists", Schema = "sales")]
    public partial class PackingList
    {
        public PackingList()
        {
            PackingListLines = new HashSet<PackingListLine>();
        }

        [Key]
        public int PackingListId { get; set; }
        public int CompanyId { get; set; }
        public int? GoodsDispatchNoteId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public int? CustomerId { get; set; }
        public int? SalesOrderId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? PlannedShipmentDate { get; set; }
        [StringLength(50)]
        public string ContainerEngineNo { get; set; }
        [StringLength(50)]
        public string ContainerNo { get; set; }
        [StringLength(50)]
        public string TrailerNo { get; set; }
        [StringLength(50)]
        public string SealNo { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(100)]
        public string Remark { get; set; }
        [StringLength(100)]
        public string Measurement { get; set; }
        [StringLength(100)]
        public string DestinationPort { get; set; }
        [Column(TypeName = "decimal(18, 2)")]
        public decimal? GrossWeight { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PackingLists")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("PackingLists")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(GoodsDispatchNoteId))]
        [InverseProperty("PackingLists")]
        public virtual GoodsDispatchNote GoodsDispatchNote { get; set; }
        [ForeignKey(nameof(SalesOrderId))]
        [InverseProperty("PackingLists")]
        public virtual SalesOrder SalesOrder { get; set; }
        [InverseProperty(nameof(PackingListLine.PackingList))]
        public virtual ICollection<PackingListLine> PackingListLines { get; set; }
    }
}
