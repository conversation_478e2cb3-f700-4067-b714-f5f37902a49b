﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseReturnLines", Schema = "prc")]
    public partial class PurchaseReturnLine
    {
        [Key]
        public int PurchaseReturnLineId { get; set; }
        public int PurchaseReturnId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? ReturnReasonId { get; set; }
        public int? WarehouseId { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "money")]
        public decimal? SupplierDiscount { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? AdditionalCharges { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.PurchaseReturnLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.PurchaseReturnLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("PurchaseReturnLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(PurchaseReturnId))]
        [InverseProperty("PurchaseReturnLines")]
        public virtual PurchaseReturn PurchaseReturn { get; set; }
        [ForeignKey(nameof(ReturnReasonId))]
        [InverseProperty(nameof(SupportData.PurchaseReturnLines))]
        public virtual SupportData ReturnReason { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("PurchaseReturnLines")]
        public virtual TaxGroup TaxGroup { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.PurchaseReturnLines))]
        public virtual Warehous Warehouse { get; set; }
        public decimal? TaxValue { get; set; }


    }
}
