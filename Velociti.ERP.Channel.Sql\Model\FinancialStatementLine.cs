﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FinancialStatementLines", Schema = "fin")]
    public partial class FinancialStatementLine
    {
        public FinancialStatementLine()
        {
            FinancialStatementLineDetails = new HashSet<FinancialStatementLineDetail>();
            InverseParent = new HashSet<FinancialStatementLine>();
        }

        [Key]
        public int FinancialStatementLineId { get; set; }
        public int? FinancialStatementId { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? LevelEnum { get; set; }
        public int? ParentId { get; set; }
        public byte? SequenceNumber { get; set; }
        [StringLength(50)]
        public string Label { get; set; }
        [StringLength(500)]
        public string Description { get; set; }
        [Column("GLAccountId")]
        public int? GlaccountId { get; set; }
        [StringLength(500)]
        public string Formula { get; set; }
        public bool? IsVisibleInReport { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }
        public bool? IsExternal { get; set; }
        public int? ExFinancialStatementId { get; set; }
        public bool? IsCumulative { get; set; }

        [ForeignKey(nameof(FinancialStatementId))]
        [InverseProperty("FinancialStatementLines")]
        public virtual FinancialStatement FinancialStatement { get; set; }
        [ForeignKey(nameof(ParentId))]
        [InverseProperty(nameof(FinancialStatementLine.InverseParent))]
        public virtual FinancialStatementLine Parent { get; set; }
        [InverseProperty(nameof(FinancialStatementLineDetail.FinancialStatementLine))]
        public virtual ICollection<FinancialStatementLineDetail> FinancialStatementLineDetails { get; set; }
        [InverseProperty(nameof(FinancialStatementLine.Parent))]
        public virtual ICollection<FinancialStatementLine> InverseParent { get; set; }
    }
}
