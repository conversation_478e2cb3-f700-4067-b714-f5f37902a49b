﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Supplier")]
    public partial class ISupplier
    {
        [StringLength(255)]
        public string SupplierCode { get; set; }
        [StringLength(255)]
        public string SupplierName { get; set; }
        [StringLength(255)]
        public string Category { get; set; }
        [StringLength(255)]
        public string CountryOfOrigin { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        [StringLength(255)]
        public string BillingCurrency { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        [StringLength(255)]
        public string LocalAgent { get; set; }
        [StringLength(255)]
        public string TaxRegistrationNo { get; set; }
        [StringLength(255)]
        public string DeliveryMode { get; set; }
        [StringLength(255)]
        public string DeliveryTerm { get; set; }
        [StringLength(255)]
        public string CreditDays { get; set; }
        [StringLength(255)]
        public string CreditLimit { get; set; }
        [StringLength(255)]
        public string SupplierGroup { get; set; }
        [StringLength(255)]
        public string SupplierType { get; set; }
        [StringLength(255)]
        public string PayeeName { get; set; }
        [StringLength(255)]
        public string ContactPersonName { get; set; }
        [StringLength(255)]
        public string ContactNoPhone { get; set; }
        [StringLength(255)]
        public string ContactNoMobile { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
    }
}
