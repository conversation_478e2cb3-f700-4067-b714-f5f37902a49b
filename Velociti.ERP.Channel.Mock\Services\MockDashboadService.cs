﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Domain.Entities.Production;
using Velociti.ERP.Domain.Services.Production;
using System.Linq;

namespace Velociti.ERP.Channel.Mock.Services
{
    public class MockDashboadService : IDashboardService
    {
        public IEnumerable<Job> GetDailyScheduleAsync(int day, int month, int year)
        {
            List<Job> dailySchedule = new List<Job>();

            Job job1 = new Job();
            job1.JobNo = 1;
            job1.SerialNo = "1234567890";
            job1.StartTime = DateTime.Now;
            job1.PressNo = "1M11";
            job1.Tray = 3;
            job1.Cavity = 2;
            job1.Status = JobStatus.RUN;

            job1.Item = new Tyre()
            {
                Code = "220101020",
                Brand = "ER BLICKLE",
                Description = "ELTOR 3.00-4 (2.10) ER - BLICKLE 164749",
                RimSize = "-2.10",
                Strcuture = "S4B",
                Design = "BLICKLE",
                Messure = " 3.00-4 ",
                Weight = 123.45,
                CuringTime = 15,
                EndTime = 120,
                Tolerance = 5
            };

            Job job2 = new Job();
            job2.JobNo = 2;
            job2.SerialNo = "123432152";
            job2.StartTime = DateTime.Now;
            job2.PressNo = "1M12";
            job2.Tray = 1;
            job2.Cavity = 3;
            job2.Status = JobStatus.SCRAP;

            job2.Item = new Tyre()
            {
                Code = "220101030",
                Brand = "ES",
                Description = "ELTOR 3.00-4 (2.10) ES",
                RimSize = "-2.10",
                Strcuture = "S10",
                Design = "ELTOR",
                Messure = " 3.00-4 ",
                Weight = 123.45,
                CuringTime = 15,
                EndTime = 120,
                Tolerance = 5
            };

            dailySchedule.Add(job1);
            dailySchedule.Add(job2);

            return dailySchedule.AsEnumerable<Job>();
        }

        public Job UpdateJobAsync(Job job)
        {
            Job job1 = new Job();
            job1.JobNo = job.JobNo;
            job1.SerialNo = "1234567890";
            job1.StartTime = DateTime.Now;
            job1.PressNo = "1M11";
            job1.Tray = 3;
            job1.Cavity = 2;
            job1.Status = JobStatus.RUN;
            job1.ActualStartTime = job.ActualStartTime;
            job1.ActualStopTime = job.ActualStopTime;

            job1.Item = new Tyre()
            {
                Code = "220101020",
                Brand = "ER BLICKLE",
                Description = "ELTOR 3.00-4 (2.10) ER - BLICKLE 164749",
                RimSize = "-2.10",
                Strcuture = "S4B",
                Design = "BLICKLE",
                Messure = " 3.00-4 ",
                Weight = 123.45,
                CuringTime = 15,
                EndTime = 120,
                Tolerance = 5
            };

            return job1;
        }
    }
}
