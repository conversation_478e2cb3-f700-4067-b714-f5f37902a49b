﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [ApiController]
    public class TouchScreenHistoryController : ControllerBase
    {
        private readonly ITouchScreenHistoryService _touchScreenHistoryService;

        public TouchScreenHistoryController(ITouchScreenHistoryService touchScreenHistoryService)
        {
            _touchScreenHistoryService = touchScreenHistoryService;
        }

        [HttpGet]
        [Route("Type/{typeEnum}/Company/{companyId}")]
        public async Task<IActionResult> GetByType(byte typeEnum, int companyId)
        {
            var records = await _touchScreenHistoryService.GetByTypeAsync((TouchScreenHistory.TypeEnum)typeEnum, companyId);

            return Ok(records);
        }
    }
}