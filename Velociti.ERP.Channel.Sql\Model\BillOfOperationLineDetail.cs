﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BillOfOperationLineDetails", Schema = "man")]
    public partial class BillOfOperationLineDetail
    {
        [Key]
        public int BillOfOperationLineDetailId { get; set; }
        public int BillOfOperationLineId { get; set; }
        public byte? TypeEnum { get; set; }
        public int? ProductId { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "decimal(5, 2)")]
        public decimal? AllocatedPct { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfOperationLineId))]
        [InverseProperty("BillOfOperationLineDetails")]
        public virtual BillOfOperationLine BillOfOperationLine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("BillOfOperationLineDetails")]
        public virtual Product Product { get; set; }
    }
}
