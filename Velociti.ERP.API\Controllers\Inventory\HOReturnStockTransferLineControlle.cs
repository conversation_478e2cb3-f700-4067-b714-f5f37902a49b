﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Entities.Inventory;
using Microsoft.AspNetCore.Authorization;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class HOReturnStockTransferLineController : ControllerBase
    {
        private readonly IHOReturnStockTransferLineService _hoReturnStocktockTranserLineService;
        public HOReturnStockTransferLineController(IHOReturnStockTransferLineService hoReturnStockTranserLineService)
        {

            _hoReturnStocktockTranserLineService = hoReturnStockTranserLineService;

        }
        [HttpGet]
        [Route("Products/{hoReturnStockTransferId}")]
        public async Task<IActionResult> LoadProducts(int hoReturnStockTransferId)
        {
            return Ok(await _hoReturnStocktockTranserLineService.LoadProducts(hoReturnStockTransferId));
        }

        [HttpGet]
        [Route("PUrchaseOrder/{hoReturnStockTransferId}")]
        public async Task<IActionResult> LoadPOLines(int hoReturnStockTransferId)
        {
            return Ok(await _hoReturnStocktockTranserLineService.LoadPOLines(hoReturnStockTransferId));
        }

        [HttpGet]
        [Route("{stockTransferId}/company/{defaultCompanyId}")]
        public async Task<IActionResult> Get(int stockTransferId, int defaultCompanyId)
        {
            return Ok(await _hoReturnStocktockTranserLineService.GetAsync(stockTransferId, defaultCompanyId));
        }

        [HttpGet]
        [Route("Details/{stockTransferLineId}")]
        public async Task<IActionResult> LineDetails(int stockTransferLineId)
        {
            return Ok(await _hoReturnStocktockTranserLineService.GetLineDetailsAsync(stockTransferLineId));
        }
    }
}
