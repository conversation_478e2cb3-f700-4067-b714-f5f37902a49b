﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class ChargeInformationsController : ControllerBase  
    {
        private readonly IChargeInformationService _chargeInformationService;

        public ChargeInformationsController(IChargeInformationService chargeInformationService)
        {
            _chargeInformationService = chargeInformationService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<ChargeInformation>>>> GetChargeInformations(int companyId)
        {
            return Ok(await _chargeInformationService.GetActiveAllChargeInformationsAsync(companyId));
        }

        [HttpDelete]
        [Route("{id}/loggedInUser/{loggedInUserId}")]
        public async Task<IActionResult> ToggleActivation(int id, int loggedInUserId)
        {
            await _chargeInformationService.ToggleActivationAsync(id, loggedInUserId);

            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ChargeInformation chargeInformation)  
        {
            if (chargeInformation == null)
                return BadRequest();

            await _chargeInformationService.SaveAsync(chargeInformation);

            return Ok();
        }

    }
}