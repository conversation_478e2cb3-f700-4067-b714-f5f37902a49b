﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;
using Microsoft.EntityFrameworkCore;
using System.Linq;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class SubGroupsRepository : ISubGroupsRepository
    {
        private readonly MarangoniERPContext _context;

        public SubGroupsRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<SubGroup> FindByCodeAsync(string code)
        {
            return await _context.SubGroups.FirstOrDefaultAsync(c => c.SubGroupCode == code);
        }

        public async Task<SubGroup> FindByIdAsync(int subGroupId)
        {
            return await _context.SubGroups.FirstOrDefaultAsync(c => c.SubGroupId == subGroupId);
        }

        public async Task<IEnumerable<SubGroup>> GetActiveAllAsync()
        {
            return await _context.SubGroups.Where(p => p.ExpiryDate == null && p.SubGroupName != "Sandbox").ToListAsync();
        }

        public async Task SaveAsync(SubGroup subGroup)
        {
            if (subGroup.SubGroupId == default)
            {
                subGroup.CreationDate = DateTime.Now;
                await _context.SubGroups.AddAsync(subGroup);
            }
            else
            {
                _context.SubGroups.Attach(subGroup);
                _context.Entry(subGroup).State = EntityState.Modified;
            }

            await _context.SaveChangesAsync();
        }
    }
}
