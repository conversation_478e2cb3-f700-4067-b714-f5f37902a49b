﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class LoadingPlansController : ControllerBase
    {
        private readonly ILoadingPlanService _loadingPlanService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public LoadingPlansController(ILoadingPlanService loadingPlanService, IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _loadingPlanService = loadingPlanService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }

        [HttpGet]
        [Route("Single/{loadingPlanId}")]
        public async Task<IActionResult> FindById(int loadingPlanId)
        {
            return Ok(await _loadingPlanService.FindByIdAsync(loadingPlanId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _loadingPlanService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]LoadingPlan loadingPlan)
        {
            switch (loadingPlan.Action)
            {
                case "generate": return Ok(await _loadingPlanService.GenerateLoadingPlanAsync(loadingPlan));
                case "save": await _loadingPlanService.SaveAsync(loadingPlan); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{loadingPlanId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int loadingPlanId, int userId)
        {
            await _loadingPlanService.CancelAsync(loadingPlanId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]LoadingPlan loadingPlan)
        {
            switch (loadingPlan.Action)
            {
                case "copy": await _loadingPlanService.CreateNewVersionAsync(loadingPlan.LoadingPlanId, loadingPlan.ModifiedUserId.Value); break;
                case "submit": await _loadingPlanService.SubmitAsync(loadingPlan.LoadingPlanId, loadingPlan.ModifiedUserId.Value); break;
                case "convert to CI": return Ok(await _loadingPlanService.ConvertToCommercialInvoiceAsync(loadingPlan.LoadingPlanId, loadingPlan.ModifiedUserId.Value));
                case "confirm": await _loadingPlanService.ConfirmAsync(loadingPlan.LoadingPlanId, loadingPlan.ModifiedUserId.Value); break;
                case "create SI": return Ok(await _loadingPlanService.ConvertToSalesInvoiceAsync(loadingPlan.LoadingPlanId, loadingPlan.ModifiedUserId.Value));
                case "convertToGDN": return Ok(await _goodsDispatchNoteService.ConvertAsync(loadingPlan.LoadingPlanId, Domain.Entities.Administration.Module.DocumentType.LoadingPlan, loadingPlan.ModifiedUserId));
            }

            return Ok();
        }
    }
}