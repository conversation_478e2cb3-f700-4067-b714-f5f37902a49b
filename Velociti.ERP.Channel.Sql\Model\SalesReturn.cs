﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesReturns", Schema = "sales")]
    public partial class SalesReturn
    {
        public SalesReturn()
        {
            SalesReturnLines = new HashSet<SalesReturnLine>();
        }

        [Key]
        public int SalesReturnId { get; set; }
        public int? SalesInvoiceId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? CustomerId { get; set; }
        [StringLength(50)]
        public string CustomerReferenceNo { get; set; }
        [StringLength(255)]
        public string CustomerAddress { get; set; }
        [StringLength(255)]
        public string DeliveryAddress { get; set; }
        [StringLength(255)]
        public string BilingAddress { get; set; }
        [StringLength(255)]
        public string PayingParty { get; set; }
        public int? PriceListId { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DeliveryDate { get; set; }
        public int? PaymentTermId { get; set; }
        public int? AccountId { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public byte? RefDocTypeEnum { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("SalesReturns")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SalesReturns")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("SalesReturns")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("SalesReturns")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("SalesReturns")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(PriceListId))]
        [InverseProperty(nameof(SupportData.SalesReturns))]
        public virtual SupportData PriceList { get; set; }
        [ForeignKey(nameof(SalesInvoiceId))]
        [InverseProperty("SalesReturns")]
        public virtual SalesInvoice SalesInvoice { get; set; }
        [InverseProperty(nameof(SalesReturnLine.SalesReturn))]
        public virtual ICollection<SalesReturnLine> SalesReturnLines { get; set; }
    }
}
