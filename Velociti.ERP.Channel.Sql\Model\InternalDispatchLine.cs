﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalDispatchLines", Schema = "inv")]
    public partial class InternalDispatchLine
    {
        public InternalDispatchLine()
        {
            InternalDispatchLineDetails = new HashSet<InternalDispatchLineDetail>();
        }

        [Key]
        public int InternalDispatchLineId { get; set; }
        public int? InternalDispatchId { get; set; }
        public int? DeliveryWarehouseId { get; set; }
        public int? DestinationWarehouseId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? RequestedQuantity { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.InternalDispatchLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DeliveryWarehouseId))]
        [InverseProperty(nameof(Warehous.InternalDispatchLineDeliveryWarehouses))]
        public virtual Warehous DeliveryWarehouse { get; set; }
        [ForeignKey(nameof(DestinationWarehouseId))]
        [InverseProperty(nameof(Warehous.InternalDispatchLineDestinationWarehouses))]
        public virtual Warehous DestinationWarehouse { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.InternalDispatchLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(InternalDispatchId))]
        [InverseProperty("InternalDispatchLines")]
        public virtual InternalDispatch InternalDispatch { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("InternalDispatchLines")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(InternalDispatchLineDetail.InternalDispatchLine))]
        public virtual ICollection<InternalDispatchLineDetail> InternalDispatchLineDetails { get; set; }
    }
}
