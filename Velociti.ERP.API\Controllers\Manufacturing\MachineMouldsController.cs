﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class MachineMouldsController : ControllerBase  
    {
        private readonly IMachineMouldService _machineMouldService;

        public MachineMouldsController(IMachineMouldService machineMouldService)
        {
            _machineMouldService = machineMouldService;
        }

        [HttpGet]
        public async Task<ActionResult<Response<IEnumerable<MachineMould>>>> Get(int companyId, int machineId)  
        {
            return Ok(await _machineMouldService.GetAllAsync(companyId, machineId));
        }

        [HttpPost]
        [Route("{loggedInUserId}")]
        public async Task<IActionResult> Save([FromBody]List<MachineMould> machineMoulds, int loggedInUserId)
        {
            await _machineMouldService.SaveAsync(machineMoulds, loggedInUserId);

            return Ok();
        }

        [HttpDelete]
        [Route("{machineMouldId}/User/{userId}")]
        public async Task<IActionResult> RemoveMould(int machineMouldId, int userId)
        {
            await _machineMouldService.RemoveMouldAsync(machineMouldId, userId);

            return Ok();
        }
    }
}