﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ShipmentQualityControls", Schema = "inv")]
    public partial class ShipmentQualityControl
    {
        public ShipmentQualityControl()
        {
            ShipmentCosts = new HashSet<ShipmentCost>();
            ShipmentQualityControlLines = new HashSet<ShipmentQualityControlLine>();
            ShipmentQualityControlReturns = new HashSet<ShipmentQualityControlReturn>();
        }

        [Key]
        public int ShipmentQualityControlId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        public int? PurchaseOrderId { get; set; }
        public int? LoanOrderId { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public int? SupplierId { get; set; }
        public int? SupplementaryManufacturerId { get; set; }
        public int? CustomerId { get; set; }
        public byte? AssetTypeEnum { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ShipmentQualityControls")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("ShipmentQualityControls")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("ShipmentQualityControls")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("ShipmentQualityControls")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
        [ForeignKey(nameof(SupplementaryManufacturerId))]
        [InverseProperty("ShipmentQualityControls")]
        public virtual SupplementaryManufacturer SupplementaryManufacturer { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("ShipmentQualityControls")]
        public virtual Supplier Supplier { get; set; }
        [InverseProperty(nameof(ShipmentCost.ShipmentQualityControl))]
        public virtual ICollection<ShipmentCost> ShipmentCosts { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlLine.ShipmentQualityControl))]
        public virtual ICollection<ShipmentQualityControlLine> ShipmentQualityControlLines { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturn.ShipmentQualityControl))]
        public virtual ICollection<ShipmentQualityControlReturn> ShipmentQualityControlReturns { get; set; }
    }
}
