﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Suppliers", Schema = "prc")]
    public partial class Supplier
    {
        public Supplier()
        {
            ExchangeOrders = new HashSet<ExchangeOrder>();
            FixedAssetMaintenances = new HashSet<FixedAssetMaintenance>();
            GeneralLedgerLines = new HashSet<GeneralLedgerLine>();
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            GoodsReceiveNotes = new HashSet<GoodsReceiveNote>();
            InboundReceipts = new HashSet<InboundReceipt>();
            OutboundPayments = new HashSet<OutboundPayment>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            PettyCashes = new HashSet<PettyCash>();
            PurchaseOrders = new HashSet<PurchaseOrder>();
            PurchaseReturns = new HashSet<PurchaseReturn>();
            Quotations = new HashSet<Quotation>();
            ServiceInvoices = new HashSet<ServiceInvoice>();
            ShipmentQualityControlReturns = new HashSet<ShipmentQualityControlReturn>();
            ShipmentQualityControls = new HashSet<ShipmentQualityControl>();
            SubContractOrders = new HashSet<SubContractOrder>();
            SupplierContacts = new HashSet<SupplierContact>();
            SupplierProducts = new HashSet<SupplierProduct>();
        }

        [Key]
        public int SupplierId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string SupplierCode { get; set; }
        [StringLength(255)]
        public string SupplierName { get; set; }
        public byte? CategoryEnum { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        public int? CountryId { get; set; }
        public int? PriceListId { get; set; }
        [StringLength(255)]
        public string LocalAgent { get; set; }
        public int? BillingCurrencyId { get; set; }
        [StringLength(50)]
        public string TaxRegistrationNo { get; set; }
        public byte? ShippingModeEnum { get; set; }
        public byte? ShippingTermEnum { get; set; }
        public int? PaymentTermId { get; set; }
        public int? ShippingTermId { get; set; }
        public int? CreditPeriodId { get; set; }
        [Column(TypeName = "money")]
        public decimal? CreditLimit { get; set; }
        [StringLength(255)]
        public string PayeeName { get; set; }
        public int? SupplierGroupId { get; set; }
        public int? SupplierTypeId { get; set; }
        [StringLength(255)]
        public string Notes { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }
        public string VATNo { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Suppliers")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CreditPeriodId))]
        [InverseProperty(nameof(SupportData.SupplierCreditPeriods))]
        public virtual SupportData CreditPeriod { get; set; }
        [ForeignKey(nameof(PaymentTermId))]
        [InverseProperty(nameof(SupportData.SupplierPaymentTerms))]
        public virtual SupportData PaymentTerm { get; set; }
        [ForeignKey(nameof(PriceListId))]
        [InverseProperty(nameof(SupportData.SupplierPriceLists))]
        public virtual SupportData PriceList { get; set; }
        [ForeignKey(nameof(ShippingTermId))]
        [InverseProperty(nameof(SupportData.SupplierShippingTerms))]
        public virtual SupportData ShippingTerm { get; set; }
        [InverseProperty(nameof(ExchangeOrder.Supplier))]
        public virtual ICollection<ExchangeOrder> ExchangeOrders { get; set; }
        [InverseProperty(nameof(FixedAssetMaintenance.Supplier))]
        public virtual ICollection<FixedAssetMaintenance> FixedAssetMaintenances { get; set; }
        [InverseProperty(nameof(GeneralLedgerLine.Supplier))]
        public virtual ICollection<GeneralLedgerLine> GeneralLedgerLines { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.Supplier))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(GoodsReceiveNote.Supplier))]
        public virtual ICollection<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        [InverseProperty(nameof(InboundReceipt.Supplier))]
        public virtual ICollection<InboundReceipt> InboundReceipts { get; set; }
        [InverseProperty(nameof(OutboundPayment.Supplier))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
        [InverseProperty(nameof(PaymentAdvice.Supplier))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(PettyCash.Supplier))]
        public virtual ICollection<PettyCash> PettyCashes { get; set; }
        [InverseProperty(nameof(PurchaseOrder.Supplier))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        [InverseProperty(nameof(PurchaseReturn.Supplier))]
        public virtual ICollection<PurchaseReturn> PurchaseReturns { get; set; }
        [InverseProperty(nameof(Quotation.Supplier))]
        public virtual ICollection<Quotation> Quotations { get; set; }
        [InverseProperty(nameof(ServiceInvoice.Supplier))]
        public virtual ICollection<ServiceInvoice> ServiceInvoices { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturn.Supplier))]
        public virtual ICollection<ShipmentQualityControlReturn> ShipmentQualityControlReturns { get; set; }
        [InverseProperty(nameof(ShipmentQualityControl.Supplier))]
        public virtual ICollection<ShipmentQualityControl> ShipmentQualityControls { get; set; }
        [InverseProperty(nameof(SubContractOrder.Supplier))]
        public virtual ICollection<SubContractOrder> SubContractOrders { get; set; }
        [InverseProperty(nameof(SupplierContact.Supplier))]
        public virtual ICollection<SupplierContact> SupplierContacts { get; set; }
        [InverseProperty(nameof(SupplierProduct.Supplier))]
        public virtual ICollection<SupplierProduct> SupplierProducts { get; set; }
        
    }
}
