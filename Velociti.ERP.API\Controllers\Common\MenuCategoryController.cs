﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using System.Collections.Generic;
using System.Threading.Tasks;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Common;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Common
{
    [Route("api/Common/[controller]")]
    [Authorize]
    [ApiController]
    public class MenuCategoryController : ControllerBase
    {
        private readonly IMenuCategoryService _menuCategoryService;

        public MenuCategoryController(IMenuCategoryService menuCategoryService)
        {
            _menuCategoryService = menuCategoryService;
        }

        [HttpGet]
        [Route("cat")]
        public async Task<ActionResult<IEnumerable<MenuCategory>>> GetAll()
        {
            var data = await _menuCategoryService.GetAll();
            return Ok(data);
        }
    }
}
