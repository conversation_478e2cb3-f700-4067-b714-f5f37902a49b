﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class UserSupervisorsController : ControllerBase
    {
        private readonly IUserSupervisorService _userSupervisorService;

        public UserSupervisorsController(IUserSupervisorService userSupervisorService)
        {
            _userSupervisorService = userSupervisorService;
        }

        [HttpGet]
        [Route("user/{userId}/company/{companyId}")]
        public async Task<IActionResult> Get(int userId, int companyId)
        {
            return Ok(await _userSupervisorService.GetAsync(userId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]IEnumerable<UserSupervisor> userSupervisors)
        {
            if (userSupervisors == null)
                return BadRequest();

            await _userSupervisorService.SaveAsync(userSupervisors);

            return Ok();
        }
    }
}