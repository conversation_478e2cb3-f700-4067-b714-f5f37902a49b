﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("QCParameterMappings", Schema = "inv")]
    public partial class QcparameterMapping
    {
        [Key]
        public int ParameterId { get; set; }
        [Key]
        public int ProductId { get; set; }
        public byte? TypeEnum { get; set; }
        [Column(TypeName = "decimal(10, 3)")]
        public decimal? Min { get; set; }
        [Column(TypeName = "decimal(10, 3)")]
        public decimal? Max { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ParameterId))]
        [InverseProperty(nameof(Qcparameter.QcparameterMappings))]
        public virtual Qcparameter Parameter { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("QcparameterMappings")]
        public virtual Product Product { get; set; }
    }
}
