﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class PurchaseOrderLinesController : ControllerBase
    {
        private readonly IPurchaseOrderLineService _purchaseOrderLineService;

        public PurchaseOrderLinesController(IPurchaseOrderLineService purchaseOrderLineService)
        {
            _purchaseOrderLineService = purchaseOrderLineService;
        }

        [HttpGet]
        [Route("{purchaseOrderId}/company/{companyId}")]
        public async Task<IActionResult> Get(int purchaseOrderId, int companyId)
        {
            return Ok(await _purchaseOrderLineService.GetAsync(purchaseOrderId, companyId));
        }

        [HttpGet]
        [Route("LastPurchaseDetails/Product/{productId}")]
        public async Task<IActionResult> GetLastPurchaseDetails(int productId)
        {
            return Ok(await _purchaseOrderLineService.GetLastPurchaseDetailsByProduct(productId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]PurchaseOrderLine purchaseOrderLine)
        {
            await _purchaseOrderLineService.SaveAsync(purchaseOrderLine);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _purchaseOrderLineService.DeleteAsync(id);

            return Ok();
        }
    }
}