﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("LoadingPlanLines", Schema = "sales")]
    public partial class LoadingPlanLine
    {
        public LoadingPlanLine()
        {
            LoadingPlanLineDetails = new HashSet<LoadingPlanLineDetail>();
        }

        [Key]
        public int LoadingPlanLineId { get; set; }
        public int LoadingPlanId { get; set; }
        public int? ProductId { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? TotalNetWeight { get; set; }
        public int? TyresPerPallet { get; set; }
        public int? PalletCount { get; set; }
        public int? TyresPerBox { get; set; }
        public int? BoxCount { get; set; }
        public int? LooseTyresCount { get; set; }
        [StringLength(500)]
        public string CustomerReferenceNo { get; set; }
        public int? SalesInvoiceLineId { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(LoadingPlanId))]
        [InverseProperty("LoadingPlanLines")]
        public virtual LoadingPlan LoadingPlan { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("LoadingPlanLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SalesInvoiceLineId))]
        [InverseProperty("LoadingPlanLines")]
        public virtual SalesInvoiceLine SalesInvoiceLine { get; set; }
        [InverseProperty(nameof(LoadingPlanLineDetail.LoadingPlanLine))]
        public virtual ICollection<LoadingPlanLineDetail> LoadingPlanLineDetails { get; set; }
    }
}
