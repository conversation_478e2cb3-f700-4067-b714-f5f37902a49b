﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class SubContractOrdersController : ControllerBase
    {
        private readonly ISubContractOrderService _subContractOrderService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;
        private readonly IGoodsReceivedNoteService _goodsReceivedNoteService;

        public SubContractOrdersController(ISubContractOrderService subContractOrderService
            , IGoodsReceivedNoteService goodsReceivedNoteService
            , IGoodsDispatchNoteService goodsDispatchNoteService)
        {
            _subContractOrderService = subContractOrderService;
            _goodsReceivedNoteService = goodsReceivedNoteService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte typeEnum)
        {
            return Ok(await _subContractOrderService.GetAllAsync(companyId, userId, startDate, endDate, typeEnum));
        }

        [HttpGet]
        [Route("Single/{subContractOrderId}")]
        public async Task<IActionResult> GetById(int subContractOrderId)
        {
            return Ok(await _subContractOrderService.GetByIdAsync(subContractOrderId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]SubContractOrder subContractOrder)
        {
            await _subContractOrderService.SaveAsync(subContractOrder);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SubContractOrder subContractOrder)
        {
            switch (subContractOrder.Action)
            {
                case "submit":
                    await _subContractOrderService.SubmitAsync(subContractOrder);
                    break;
                case "cancel":
                    await _subContractOrderService.CancelAsync(subContractOrder);
                    break;
                case "convert":
                    {
                        var documentType =  Module.DocumentType.InboundSubContractOrder;
                        return Ok(await _goodsDispatchNoteService.ConvertAsync(subContractOrder.SubContractOrderId, documentType, subContractOrder.ModifiedUserId));
                    }
                case "convertToGRN":
                    {
                        var documentType = Module.DocumentType.OutboundSubContractOrder;
                        return Ok(await _goodsReceivedNoteService.ConvertSubContractOrderAsync(subContractOrder.SubContractOrderId, subContractOrder.ModifiedUserId, documentType));
                    }
            }

            return Ok();
        }
    }
}