﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Auth;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [ApiController]
    public class IdentityController : ControllerBase
    {
        private IAuthService authService;

        public IdentityController(IAuthService userService)
        {
            this.authService = userService;
        }

        [AllowAnonymous]
        [HttpPost("authenticate")]
        public async Task<IActionResult> Authenticate([FromBody]AuthRequest login)
        {
            var response = await authService.Authenticate(login.Username, login.Password);

            if (response == null)
                return new UnauthorizedResult();

            return Ok(response);
        }

        [AllowAnonymous]
        [HttpPost("refresh")]
        public IActionResult Refresh([FromBody]TokenRequest request)
        {
            var response = this.authService.RefreshToken(request.Token, request.RefreshToken);

            if (response == null)
            {
                return BadRequest(new { message = "Access token or Refresh token is invalid" });
            }
            else if (!String.IsNullOrEmpty(response.Error))
            {
                return BadRequest(new { message = response.Error });
            }

            return Ok(response);
        }
    }
}