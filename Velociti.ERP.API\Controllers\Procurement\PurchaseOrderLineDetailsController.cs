﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class PurchaseOrderLineDetailsController : ControllerBase    
    {
        private readonly IPurchaseOrderLineDetailService _purchaseOrderLineDetailService;

        public PurchaseOrderLineDetailsController(IPurchaseOrderLineDetailService purchaseOrderLineDetailService)  
        {
            _purchaseOrderLineDetailService = purchaseOrderLineDetailService;
        }

        [HttpGet]
        [Route("{purchaseOrderLineId}")]
        public async Task<IActionResult> Get(int purchaseOrderLineId)  
        {
            return Ok(await _purchaseOrderLineDetailService.GetAsync(purchaseOrderLineId));
        }

        [HttpPost]
        public async Task<IActionResult> GetPurchaseOrderLineDetails([FromBody]PurchaseOrderLineDetail purchaseOrderLineDetail)
        {
            await _purchaseOrderLineDetailService.SaveAsync(purchaseOrderLineDetail);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _purchaseOrderLineDetailService.DeleteAsync(id);

            return Ok();
        }
    }
}