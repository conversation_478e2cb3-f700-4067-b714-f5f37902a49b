﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Logins", Schema = "adm")]
    public partial class Login
    {
        [Key]
        public int LoginId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string Username { get; set; }
        [Required]
        [StringLength(1500)]
        public string PasswordHash { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LockoutEndDate { get; set; }
        public bool? LockoutEnabled { get; set; }
        public int? AccessFailedCount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? PasswordExpiryDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Logins")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(LoginId))]
        [InverseProperty(nameof(User.Login))]
        public virtual User LoginNavigation { get; set; }
    }
}
