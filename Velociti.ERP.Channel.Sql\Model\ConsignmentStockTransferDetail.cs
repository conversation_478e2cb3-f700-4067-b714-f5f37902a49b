﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ConsignmentStockTransferDetails", Schema = "inv")]
    public partial class ConsignmentStockTransferDetail
    {
        [Key]
        public int ConsignmentStockTransferDetailId { get; set; }
        public int? ConsignmentStockTransferLineId { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? TransferCost { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }

        [ForeignKey(nameof(ConsignmentStockTransferLineId))]
        [InverseProperty("ConsignmentStockTransferDetails")]
        public virtual ConsignmentStockTransferLine ConsignmentStockTransferLine { get; set; }
    }
}
