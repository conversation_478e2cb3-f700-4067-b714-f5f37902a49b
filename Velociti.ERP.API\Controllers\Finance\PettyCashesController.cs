﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class PettyCashesController : ControllerBase
    {
        private readonly IPettyCashService _pettyCashService;

        public PettyCashesController(IPettyCashService pettyCashService)
        {
            _pettyCashService = pettyCashService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAllAsync(int companyId, int userId, DateTime start, DateTime end)
        {
            return Ok(await _pettyCashService.GetAllAsync(companyId, userId, start, end));
        }

        [HttpGet]
        [Route("Single/{pettyCashId}")]
        public async Task<IActionResult> GetByIdAsync(int pettyCashId)
        {
            return Ok(await _pettyCashService.GetByIdAsync(pettyCashId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}/Type/{type}")]
        public async Task<IActionResult> GetShortListAsync(int companyId, byte type)
        {
            return Ok(await _pettyCashService.GetShortListAsync(companyId, type));
        }

        [HttpGet]
        [Route("IOUShortList/{companyId}")]
        public async Task<IActionResult> GetIOUShortList(int companyId)
        {
            var list = await _pettyCashService.GetIOUShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortListByEmployee/{employeeId}/Type/{type}")]
        public async Task<IActionResult> GetShortListByEmployeeAsync(int employeeId, byte type, int currentIOUId = default)
        {
            return Ok(await _pettyCashService.GetShortListByEmployeeAsync(employeeId, type, currentIOUId));
        }

        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]PettyCash pettyCash)
        {
            await _pettyCashService.SaveAsync(pettyCash);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> ToggleActivationAsync([FromBody]PettyCash pettyCash)
        {
            await _pettyCashService.ToggleActivationAsync(pettyCash);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]PettyCash pettyCash)
        {
            switch (pettyCash.Action)
            {
                case "submit": await _pettyCashService.SubmitAsync(pettyCash.PettyCashId, pettyCash.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}