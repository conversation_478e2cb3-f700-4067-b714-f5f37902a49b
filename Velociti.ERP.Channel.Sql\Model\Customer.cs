﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Customers", Schema = "sales")]
    public partial class Customer
    {
        public Customer()
        {
            ConsignmentStockTransfers = new HashSet<ConsignmentStockTransfer>();
            CustomerContacts = new HashSet<CustomerContact>();
            CustomerPayments = new HashSet<CustomerPayment>();
            DiscountSchemeCustomers = new HashSet<DiscountSchemeCustomer>();
            FreeIssueCustomers = new HashSet<FreeIssueCustomer>();
            GeneralLedgerLines = new HashSet<GeneralLedgerLine>();
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            GoodsReceiveNotes = new HashSet<GoodsReceiveNote>();
            HoreturnStockTransfers = new HashSet<HoreturnStockTransfer>();
            InboundReceipts = new HashSet<InboundReceipt>();
            LoadingPlans = new HashSet<LoadingPlan>();
            OutboundPayments = new HashSet<OutboundPayment>();
            PackingLists = new HashSet<PackingList>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            SalesInvoices = new HashSet<SalesInvoice>();
            SalesOrders = new HashSet<SalesOrder>();
            SalesReturns = new HashSet<SalesReturn>();
            SalesServiceOrders = new HashSet<SalesServiceOrder>();
            ServiceInquiries = new HashSet<ServiceInquiry>();
            ShipmentQualityControlReturns = new HashSet<ShipmentQualityControlReturn>();
            ShipmentQualityControls = new HashSet<ShipmentQualityControl>();
            StockCountSheets = new HashSet<StockCountSheet>();
            SubContractOrders = new HashSet<SubContractOrder>();
        }

        [Key]
        public int CustomerId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string CustomerCode { get; set; }
        [StringLength(255)]
        public string CustomerName { get; set; }
        public byte? CategoryEnum { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        [StringLength(255)]
        public string PayingParty { get; set; }
        [StringLength(100)]
        public string CountryOfOrigin { get; set; }
        public int? BillingCurrencyId { get; set; }
        [StringLength(50)]
        public string TaxRegistrationNo { get; set; }
        public byte? ShippingModeEnum { get; set; }
        public byte? ShippingTermEnum { get; set; }
        public int? CreditPeriodId { get; set; }
        [Column(TypeName = "money")]
        public decimal? CreditLimit { get; set; }
        public int? CustomerGroupId { get; set; }
        public int? CustomerTypeId { get; set; }
        public int? PriceListId { get; set; }
        [StringLength(150)]
        public string ContactNumber { get; set; }
        public int? WarehouseId { get; set; }
        [Column("NIC")]
        [StringLength(12)]
        public string Nic { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DateOfBirth { get; set; }
        public byte? GenderEnum { get; set; }
        public int? SourceTypeId { get; set; }
        [StringLength(150)]
        public string SourceDescription { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ModifiedDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CountryId { get; set; }
        public int? TempId { get; set; }
        [Column(TypeName = "money")]
        public decimal? ConsignmentCreditLimit { get; set; }

        [ForeignKey(nameof(BillingCurrencyId))]
        [InverseProperty(nameof(Currency.Customers))]
        public virtual Currency BillingCurrency { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Customers")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CreditPeriodId))]
        [InverseProperty(nameof(SupportData.CustomerCreditPeriods))]
        public virtual SupportData CreditPeriod { get; set; }
        [ForeignKey(nameof(PriceListId))]
        [InverseProperty(nameof(SupportData.CustomerPriceLists))]
        public virtual SupportData PriceList { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.Customers))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransfer.Customer))]
        public virtual ICollection<ConsignmentStockTransfer> ConsignmentStockTransfers { get; set; }
        [InverseProperty(nameof(CustomerContact.Customer))]
        public virtual ICollection<CustomerContact> CustomerContacts { get; set; }
        [InverseProperty(nameof(CustomerPayment.Customer))]
        public virtual ICollection<CustomerPayment> CustomerPayments { get; set; }
        [InverseProperty(nameof(DiscountSchemeCustomer.Customer))]
        public virtual ICollection<DiscountSchemeCustomer> DiscountSchemeCustomers { get; set; }
        [InverseProperty(nameof(FreeIssueCustomer.Customer))]
        public virtual ICollection<FreeIssueCustomer> FreeIssueCustomers { get; set; }
        [InverseProperty(nameof(GeneralLedgerLine.Customer))]
        public virtual ICollection<GeneralLedgerLine> GeneralLedgerLines { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.Customer))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(GoodsReceiveNote.Customer))]
        public virtual ICollection<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        [InverseProperty(nameof(HoreturnStockTransfer.Customer))]
        public virtual ICollection<HoreturnStockTransfer> HoreturnStockTransfers { get; set; }
        [InverseProperty(nameof(InboundReceipt.Customer))]
        public virtual ICollection<InboundReceipt> InboundReceipts { get; set; }
        [InverseProperty(nameof(LoadingPlan.Customer))]
        public virtual ICollection<LoadingPlan> LoadingPlans { get; set; }
        [InverseProperty(nameof(OutboundPayment.Customer))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
        [InverseProperty(nameof(PackingList.Customer))]
        public virtual ICollection<PackingList> PackingLists { get; set; }
        [InverseProperty(nameof(PaymentAdvice.Customer))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(SalesInvoice.Customer))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
        [InverseProperty(nameof(SalesOrder.Customer))]
        public virtual ICollection<SalesOrder> SalesOrders { get; set; }
        [InverseProperty(nameof(SalesReturn.Customer))]
        public virtual ICollection<SalesReturn> SalesReturns { get; set; }
        [InverseProperty(nameof(SalesServiceOrder.Customer))]
        public virtual ICollection<SalesServiceOrder> SalesServiceOrders { get; set; }
        [InverseProperty(nameof(ServiceInquiry.Customer))]
        public virtual ICollection<ServiceInquiry> ServiceInquiries { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturn.Customer))]
        public virtual ICollection<ShipmentQualityControlReturn> ShipmentQualityControlReturns { get; set; }
        [InverseProperty(nameof(ShipmentQualityControl.Customer))]
        public virtual ICollection<ShipmentQualityControl> ShipmentQualityControls { get; set; }
        [InverseProperty(nameof(StockCountSheet.Customer))]
        public virtual ICollection<StockCountSheet> StockCountSheets { get; set; }
        [InverseProperty(nameof(SubContractOrder.Customer))]
        public virtual ICollection<SubContractOrder> SubContractOrders { get; set; }
    }
}
