﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [ApiController]
    public class ProductionOrderLinesController : ControllerBase
    {
        private readonly IProductionOrderLineService _productionOrderLineService;

        public ProductionOrderLinesController(IProductionOrderLineService productionOrderLineService)
        {
            _productionOrderLineService = productionOrderLineService;
        }

        [HttpGet]
        [Route("{productionOrderId}")]
        public async Task<IActionResult> Get(int productionOrderId)
        {
            return Ok(await _productionOrderLineService.GetAsync(productionOrderId));
        }

        [HttpGet]
        [Route("ForSemiFinished/{productionOrderId}")]
        public async Task<IActionResult> GetForSemiFinished(int productionOrderId)
        {
            return Ok(await _productionOrderLineService.GetForSemiFinishedAsync(productionOrderId));
        }

        [HttpPut]
        [AllowAnonymous]
        public async Task<IActionResult> Update(ProductionOrderLine productionOrderLine)
        {
            try
            {
                switch (productionOrderLine.Action)
                {
                    case "Update Actual Start Date": await _productionOrderLineService.UpdateActualStartDateAsync(productionOrderLine.ProductionOrderId.Value, productionOrderLine.MachineId.Value); break;
                    case "Update Actual Start Date And Warehouse": await _productionOrderLineService.UpdateActualStartDateWithWarehouseAsync(
                        productionOrderLine.ProductionOrderId.Value, productionOrderLine.MachineId.Value, productionOrderLine.Barcode); break;
                    case "Update Actual End Date": await _productionOrderLineService.UpdateActualEndDateAsync(productionOrderLine); break;
                    case "Update Actual End Date TS01": await _productionOrderLineService.UpdateActualEndDateTS01Async(productionOrderLine); break;
                    case "Update Press Machine Actual End Date - Web": await _productionOrderLineService.UpdateActualEndDateAsync(productionOrderLine.ProductionOrderId.Value); break;
                }

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }
    }
}