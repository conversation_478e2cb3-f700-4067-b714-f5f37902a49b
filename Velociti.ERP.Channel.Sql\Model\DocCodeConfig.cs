﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("DocCodeConfig", Schema = "adm")]
    public partial class DocCodeConfig
    {
        [Key]
        public int DocCodeConfigId { get; set; }
        public int CompanyId { get; set; }
        public byte? DocTypeEnum { get; set; }
        public int? CodeStartingNumber { get; set; }
        [StringLength(10)]
        public string CodePrefix { get; set; }
        [StringLength(10)]
        public string CodeSufix { get; set; }
        public int? CodeLength { get; set; }
        [StringLength(10)]
        public string CodePrefixSeperator { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        [Required]
        [StringLength(10)]
        public string BookNumber { get; set; }
        public bool IsDefault { get; set; }
        [StringLength(10)]
        public string CodeSufixSeperator { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("DocCodeConfigs")]
        public virtual Company Company { get; set; }
    }
}
