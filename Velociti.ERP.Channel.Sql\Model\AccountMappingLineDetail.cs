﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("AccountMappingLineDetails", Schema = "fin")]
    public partial class AccountMappingLineDetail
    {
        [Key]
        public int AccountMappingLineDetailId { get; set; }
        public int? AccountMappingLineId { get; set; }
        public byte? SubDivisionEnum { get; set; }
        [StringLength(255)]
        public string SubDivisionName { get; set; }
        public int? SubDivisionValue { get; set; }
        [StringLength(255)]
        public string SubDivisionValueText { get; set; }
        public int? ChartOfAccountId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountMappingLineId))]
        [InverseProperty("AccountMappingLineDetails")]
        public virtual AccountMappingLine AccountMappingLine { get; set; }
        [ForeignKey(nameof(ChartOfAccountId))]
        [InverseProperty("AccountMappingLineDetails")]
        public virtual ChartOfAccount ChartOfAccount { get; set; }
    }
}
