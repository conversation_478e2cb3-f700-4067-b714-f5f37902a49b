﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PurchaseRequisitionNoteProducts", Schema = "inv")]
    public partial class PurchaseRequisitionNoteProduct
    {
        [Key]
        public int PurchaseRequisitionNoteProductId { get; set; }
        public int? PurchaseRequisitionNoteId { get; set; }
        public int? ProductId { get; set; }
    }
}
