﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class InternalDispatchLineDetailsController : ControllerBase
    {
        private readonly IInternalDispatchLineDetailService _internalDispatchLineDetailService;

        public InternalDispatchLineDetailsController(IInternalDispatchLineDetailService internalDispatchLineDetailService)
        {
            _internalDispatchLineDetailService = internalDispatchLineDetailService;
        }

        [HttpGet]
        [Route("{internalDispatchLineId}")]
        public async Task<IActionResult> GetById(int internalDispatchLineId)
        {
            return Ok(await _internalDispatchLineDetailService.GetByHeaderIdAsync(internalDispatchLineId));
        }
    }
}