﻿using System;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata;

namespace Velociti.ERP.Channel.Sql.Model
{
    public partial class MarangoniERPContext : DbContext
    {
        public MarangoniERPContext(DbContextOptions<MarangoniERPContext> options)
            : base(options)
        {
        }

        public virtual DbSet<Account> Accounts { get; set; }
        public virtual DbSet<AccountMapping> AccountMappings { get; set; }
        public virtual DbSet<AccountMappingLine> AccountMappingLines { get; set; }
        public virtual DbSet<AccountMappingLineDetail> AccountMappingLineDetails { get; set; }
        public virtual DbSet<AgingBucket> AgingBuckets { get; set; }
        public virtual DbSet<AppSetting> AppSettings { get; set; }
        public virtual DbSet<Attendance> Attendances { get; set; }
        public virtual DbSet<AttendanceAllowance> AttendanceAllowances { get; set; }
        public virtual DbSet<AuditLog> AuditLogs { get; set; }
        public virtual DbSet<BankAdjustment> BankAdjustments { get; set; }
        public virtual DbSet<BankAdjustmentCostAllocation> BankAdjustmentCostAllocations { get; set; }
        public virtual DbSet<BankAdjustmentLine> BankAdjustmentLines { get; set; }
        public virtual DbSet<BankReconciliation> BankReconciliations { get; set; }
        public virtual DbSet<BankReconciliationLine> BankReconciliationLines { get; set; }
        public virtual DbSet<Barcode> Barcodes { get; set; }
        public virtual DbSet<BaseIncentiveRate> BaseIncentiveRates { get; set; }
        public virtual DbSet<BillOfMaterial> BillOfMaterials { get; set; }
        public virtual DbSet<BillOfMaterialLine> BillOfMaterialLines { get; set; }
        public virtual DbSet<BillOfOperation> BillOfOperations { get; set; }
        public virtual DbSet<BillOfOperationLine> BillOfOperationLines { get; set; }
        public virtual DbSet<BillOfOperationLineDetail> BillOfOperationLineDetails { get; set; }
        public virtual DbSet<BillOfOperationLineOverhead> BillOfOperationLineOverheads { get; set; }
        public virtual DbSet<BundleProduct> BundleProducts { get; set; }
        public virtual DbSet<BundleProductLine> BundleProductLines { get; set; }
        public virtual DbSet<ChargeInfoChargeGroup> ChargeInfoChargeGroups { get; set; }
        public virtual DbSet<ChargeInformation> ChargeInformations { get; set; }
        public virtual DbSet<ChartOfAccount> ChartOfAccounts { get; set; }
        public virtual DbSet<ChequeBook> ChequeBooks { get; set; }
        public virtual DbSet<ChequeBookLine> ChequeBookLines { get; set; }
        public virtual DbSet<Coamapping> Coamappings { get; set; }
        public virtual DbSet<Company> Companies { get; set; }
        public virtual DbSet<ConsignmentPurchaseOrderLine> ConsignmentPurchaseOrderLines { get; set; }
        public virtual DbSet<ConsignmentStockTransfer> ConsignmentStockTransfers { get; set; }
        public virtual DbSet<ConsignmentStockTransferDetail> ConsignmentStockTransferDetails { get; set; }
        public virtual DbSet<ConsignmentStockTransferLine> ConsignmentStockTransferLines { get; set; }
        public virtual DbSet<Contact> Contacts { get; set; }
        public virtual DbSet<CostingWarehous> CostingWarehouses { get; set; }
        public virtual DbSet<Currency> Currencies { get; set; }
        public virtual DbSet<CurrencyExchangeRate> CurrencyExchangeRates { get; set; }
        public virtual DbSet<Customer> Customers { get; set; }
        public virtual DbSet<CustomerContact> CustomerContacts { get; set; }
        public virtual DbSet<CustomerPayment> CustomerPayments { get; set; }
        public virtual DbSet<CustomerPaymentLine> CustomerPaymentLines { get; set; }
        public virtual DbSet<CustomerPaymentVoucher> CustomerPaymentVouchers { get; set; }
        public virtual DbSet<DateFormat> DateFormats { get; set; }
        public virtual DbSet<Department> Departments { get; set; }
        public virtual DbSet<Designation> Designations { get; set; }
        public virtual DbSet<DevExpressReport> DevExpressReports { get; set; }
        public virtual DbSet<DiscountScheme> DiscountSchemes { get; set; }
        public virtual DbSet<DiscountSchemeCriteriaLine> DiscountSchemeCriteriaLines { get; set; }
        public virtual DbSet<DiscountSchemeCustomer> DiscountSchemeCustomers { get; set; }
        public virtual DbSet<DiscountSchemeLine> DiscountSchemeLines { get; set; }
        public virtual DbSet<Division> Divisions { get; set; }
        public virtual DbSet<DocCodeConfig> DocCodeConfigs { get; set; }
        public virtual DbSet<DocTypeConfig> DocTypeConfigs { get; set; }
        public virtual DbSet<EmailTemplate> EmailTemplates { get; set; }
        public virtual DbSet<EmailTemplateLine> EmailTemplateLines { get; set; }
        public virtual DbSet<Employee> Employees { get; set; }
        public virtual DbSet<EmployeeAccount> EmployeeAccounts { get; set; }
        public virtual DbSet<EmployeeContact> EmployeeContacts { get; set; }
        public virtual DbSet<EmployeeIncrement> EmployeeIncrements { get; set; }
        public virtual DbSet<EmployeeLeaf> EmployeeLeaves { get; set; }
        public virtual DbSet<ExchangeOrder> ExchangeOrders { get; set; }
        public virtual DbSet<ExchangeOrderLine> ExchangeOrderLines { get; set; }
        public virtual DbSet<FinancialStatement> FinancialStatements { get; set; }
        public virtual DbSet<FinancialStatementLine> FinancialStatementLines { get; set; }
        public virtual DbSet<FinancialStatementLineDetail> FinancialStatementLineDetails { get; set; }
        public virtual DbSet<FixedAsset> FixedAssets { get; set; }
        public virtual DbSet<FixedAssetMaintenance> FixedAssetMaintenances { get; set; }
        public virtual DbSet<FixedAssetRegistry> FixedAssetRegistries { get; set; }
        public virtual DbSet<FreeIssue> FreeIssues { get; set; }
        public virtual DbSet<FreeIssueCriteriaLine> FreeIssueCriteriaLines { get; set; }
        public virtual DbSet<FreeIssueCustomer> FreeIssueCustomers { get; set; }
        public virtual DbSet<FreeIssueLine> FreeIssueLines { get; set; }
        public virtual DbSet<GeneralLedgerLine> GeneralLedgerLines { get; set; }
        public virtual DbSet<GeneralLedgerSettlement> GeneralLedgerSettlements { get; set; }
        public virtual DbSet<GeneralLedgerSettlementLine> GeneralLedgerSettlementLines { get; set; }
        public virtual DbSet<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        public virtual DbSet<GoodsDispatchNoteLine> GoodsDispatchNoteLines { get; set; }
        public virtual DbSet<GoodsDispatchNoteLineDetail> GoodsDispatchNoteLineDetails { get; set; }
        public virtual DbSet<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        public virtual DbSet<GoodsReceiveNoteLine> GoodsReceiveNoteLines { get; set; }
        public virtual DbSet<GoodsReceiveNoteLineDetail> GoodsReceiveNoteLineDetails { get; set; }
        public virtual DbSet<Group> Groups { get; set; }
        public virtual DbSet<GroupRole> GroupRoles { get; set; }
        public virtual DbSet<HierarchyTemplate> HierarchyTemplates { get; set; }
        public virtual DbSet<Holding> Holdings { get; set; }
        public virtual DbSet<HoreturnStockTransfer> HoreturnStockTransfers { get; set; }
        public virtual DbSet<HoreturnStockTransferDetail> HoreturnStockTransferDetails { get; set; }
        public virtual DbSet<HoreturnStockTransferIntegrationLine> HoreturnStockTransferIntegrationLines { get; set; }
        public virtual DbSet<HoreturnStockTransferLine> HoreturnStockTransferLines { get; set; }
        public virtual DbSet<Hrsetting> Hrsettings { get; set; }
        public virtual DbSet<IAttendance> IAttendances { get; set; }
        public virtual DbSet<IBom> IBoms { get; set; }
        public virtual DbSet<IChartOfAccount> IChartOfAccounts { get; set; }
        public virtual DbSet<ICustomer> ICustomers { get; set; }
        public virtual DbSet<IDepartment> IDepartments { get; set; }
        public virtual DbSet<IEmployee> IEmployees { get; set; }
        public virtual DbSet<IFgBom> IFgBoms { get; set; }
        public virtual DbSet<IFgBoo> IFgBoos { get; set; }
        public virtual DbSet<IFgBooLineDetail> IFgBooLineDetails { get; set; }
        public virtual DbSet<IFgBooLineDetails2> IFgBooLineDetails2 { get; set; }
        public virtual DbSet<IMachine> IMachines { get; set; }
        public virtual DbSet<IMould> IMoulds { get; set; }
        public virtual DbSet<IMouldProduct> IMouldProducts { get; set; }
        public virtual DbSet<IPricelist> IPricelists { get; set; }
        public virtual DbSet<IProduct> IProducts { get; set; }
        public virtual DbSet<IProductDie> IProductDies { get; set; }
        public virtual DbSet<IQcMapping> IQcMappings { get; set; }
        public virtual DbSet<IRawMaterialPlanning> IRawMaterialPlannings { get; set; }
        public virtual DbSet<IRolePermission> IRolePermissions { get; set; }
        public virtual DbSet<ISalesOrder> ISalesOrders { get; set; }
        public virtual DbSet<ISfgBoo> ISfgBoos { get; set; }
        public virtual DbSet<ISupplier> ISuppliers { get; set; }
        public virtual DbSet<ISupplierProduct> ISupplierProducts { get; set; }
        public virtual DbSet<ISupplymentaryManufacture> ISupplymentaryManufactures { get; set; }
        public virtual DbSet<ISupportData> ISupportDatas { get; set; }
        public virtual DbSet<ITyreSpecification> ITyreSpecifications { get; set; }
        public virtual DbSet<IUser> IUsers { get; set; }
        public virtual DbSet<IUserDepartment> IUserDepartments { get; set; }
        public virtual DbSet<IUserRole> IUserRoles { get; set; }
        public virtual DbSet<IWarehouseProduct> IWarehouseProducts { get; set; }
        public virtual DbSet<IWorkStationCadre> IWorkStationCadres { get; set; }
        public virtual DbSet<InboundReceipt> InboundReceipts { get; set; }
        public virtual DbSet<InboundReceiptCostAllocation> InboundReceiptCostAllocations { get; set; }
        public virtual DbSet<InboundReceiptLine> InboundReceiptLines { get; set; }
        public virtual DbSet<IncentiveApportionmentRate> IncentiveApportionmentRates { get; set; }
        public virtual DbSet<IntegrationStatu> IntegrationStatus { get; set; }
        public virtual DbSet<InternalDispatch> InternalDispatches { get; set; }
        public virtual DbSet<InternalDispatchLine> InternalDispatchLines { get; set; }
        public virtual DbSet<InternalDispatchLineDetail> InternalDispatchLineDetails { get; set; }
        public virtual DbSet<InternalOrder> InternalOrders { get; set; }
        public virtual DbSet<InternalOrderLine> InternalOrderLines { get; set; }
        public virtual DbSet<InternalReturn> InternalReturns { get; set; }
        public virtual DbSet<InternalReturnLine> InternalReturnLines { get; set; }
        public virtual DbSet<InternalServiceInvoice> InternalServiceInvoices { get; set; }
        public virtual DbSet<InternalServiceInvoiceLine> InternalServiceInvoiceLines { get; set; }
        public virtual DbSet<InternalServiceInvoiceLineDetail> InternalServiceInvoiceLineDetails { get; set; }
        public virtual DbSet<LeaveApplicationForm> LeaveApplicationForms { get; set; }
        public virtual DbSet<LeaveConfiguration> LeaveConfigurations { get; set; }
        public virtual DbSet<LoadingPlan> LoadingPlans { get; set; }
        public virtual DbSet<LoadingPlanLine> LoadingPlanLines { get; set; }
        public virtual DbSet<LoadingPlanLineDetail> LoadingPlanLineDetails { get; set; }
        public virtual DbSet<LoanOrder> LoanOrders { get; set; }
        public virtual DbSet<LoanOrderLine> LoanOrderLines { get; set; }
        public virtual DbSet<Log> Logs { get; set; }
        public virtual DbSet<Login> Logins { get; set; }
        public virtual DbSet<Machine> Machines { get; set; }
        public virtual DbSet<MachineLine> MachineLines { get; set; }
        public virtual DbSet<MachineMould> MachineMoulds { get; set; }
        public virtual DbSet<MachineProductComponent> MachineProductComponents { get; set; }
        public virtual DbSet<MachineVisualPlanLine> MachineVisualPlanLines { get; set; }
        public virtual DbSet<ManualJournal> ManualJournals { get; set; }
        public virtual DbSet<ManualJournalCostAllocation> ManualJournalCostAllocations { get; set; }
        public virtual DbSet<ManualJournalLine> ManualJournalLines { get; set; }
        public virtual DbSet<MaterialRequisitionNote> MaterialRequisitionNotes { get; set; }
        public virtual DbSet<MaterialRequisitionNoteLine> MaterialRequisitionNoteLines { get; set; }
        public virtual DbSet<MaterialRequisitionPlan> MaterialRequisitionPlans { get; set; }
        public virtual DbSet<MaterialRequisitionPlanLine> MaterialRequisitionPlanLines { get; set; }
        public virtual DbSet<MenuHeadLines> MenuHeadLines { get; set; }
        public virtual DbSet<MenuItem> MenuItems { get; set; }
        public virtual DbSet<Module> Modules { get; set; }
        public virtual DbSet<MonthEndWarehouseProduct> MonthEndWarehouseProducts { get; set; }
        public virtual DbSet<Mould> Moulds { get; set; }
        public virtual DbSet<MouldCavity> MouldCavities { get; set; }
        public virtual DbSet<MouldProduct> MouldProducts { get; set; }
        public virtual DbSet<Notification> Notifications { get; set; }
        public virtual DbSet<OutboundPayment> OutboundPayments { get; set; }
        public virtual DbSet<OutboundPaymentCostAllocation> OutboundPaymentCostAllocations { get; set; }
        public virtual DbSet<OutboundPaymentLine> OutboundPaymentLines { get; set; }
        public virtual DbSet<PackingList> PackingLists { get; set; }
        public virtual DbSet<PackingListLine> PackingListLines { get; set; }
        public virtual DbSet<PalletBarcode> PalletBarcodes { get; set; }
        public virtual DbSet<PasswordPolicy> PasswordPolicies { get; set; }
        public virtual DbSet<PayBook> PayBooks { get; set; }
        public virtual DbSet<PaymentAdvice> PaymentAdvices { get; set; }
        public virtual DbSet<PaymentAdviceCostAllocation> PaymentAdviceCostAllocations { get; set; }
        public virtual DbSet<PaymentAdviceLine> PaymentAdviceLines { get; set; }
        public virtual DbSet<Permission> Permissions { get; set; }
        public virtual DbSet<PettyCash> PettyCashes { get; set; }
        public virtual DbSet<PettyCashCostAllocation> PettyCashCostAllocations { get; set; }
        public virtual DbSet<PettyCashLine> PettyCashLines { get; set; }
        public virtual DbSet<PressLine> PressLines { get; set; }
        public virtual DbSet<PriceList> PriceLists { get; set; }
        public virtual DbSet<MenuCategory> MenuCategorys { get; set; }
        public virtual DbSet<Printers> Print { get; set; }
        public virtual DbSet<Product> Products { get; set; }
        public virtual DbSet<ProductBrandDiscount> ProductBrandDiscounts { get; set; }
        public virtual DbSet<ProductDy> ProductDies { get; set; }
        public virtual DbSet<ProductHierarchy> ProductHierarchies { get; set; }
        public virtual DbSet<ProductLicenceRegistration> ProductLicenceRegistrations { get; set; }
        public virtual DbSet<ProductList> ProductLists { get; set; }
        public virtual DbSet<ProductionNote> ProductionNotes { get; set; }
        public virtual DbSet<ProductionNoteLine> ProductionNoteLines { get; set; }
        public virtual DbSet<ProductPackingMethod> ProductPackingMethods { get; set; }
        public virtual DbSet<ProductSale> ProductSales { get; set; }
        public virtual DbSet<ProductStock> ProductStocks { get; set; }
        public virtual DbSet<ProductStockCount> ProductStockCounts { get; set; }
        public virtual DbSet<ProductUnitOfMeasure> ProductUnitOfMeasures { get; set; }
        public virtual DbSet<ProductionOrder> ProductionOrders { get; set; }
        public virtual DbSet<ProductionOrderLine> ProductionOrderLines { get; set; }
        public virtual DbSet<ProductionOrderLineDetail> ProductionOrderLineDetails { get; set; }
        public virtual DbSet<ProductionPlan> ProductionPlans { get; set; }
        public virtual DbSet<ProductionPlanBarcode> ProductionPlanBarcodes { get; set; }
        public virtual DbSet<ProductionPlanLine> ProductionPlanLines { get; set; }
        public virtual DbSet<ProductionWarehouseProduct> ProductionWarehouseProducts { get; set; }
        public virtual DbSet<ProfileBuilding> ProfileBuildings { get; set; }
        public virtual DbSet<ProfileBuildingLine> ProfileBuildingLines { get; set; }
        public virtual DbSet<ProfileBuildingWarehouseProduct> ProfileBuildingWarehouseProducts { get; set; }
        public virtual DbSet<Promotion> Promotions { get; set; }
        public virtual DbSet<PromotionLine> PromotionLines { get; set; }
        public virtual DbSet<PurchaseOrder> PurchaseOrders { get; set; }
        public virtual DbSet<PurchaseOrderAdditionalCharge> PurchaseOrderAdditionalCharges { get; set; }
        public virtual DbSet<PurchaseOrderAdditionalChargeLine> PurchaseOrderAdditionalChargeLines { get; set; }
        public virtual DbSet<PurchaseOrderLine> PurchaseOrderLines { get; set; }
        public virtual DbSet<PurchaseOrderLineDetail> PurchaseOrderLineDetails { get; set; }
        public virtual DbSet<PurchaseOrderSalesInvoice> PurchaseOrderSalesInvoices { get; set; }
        public virtual DbSet<PurchaseRequisitionNote> PurchaseRequisitionNotes { get; set; }
        public virtual DbSet<PurchaseRequisitionNoteLine> PurchaseRequisitionNoteLines { get; set; }
        public virtual DbSet<PurchaseReturn> PurchaseReturns { get; set; }
        public virtual DbSet<PurchaseReturnLine> PurchaseReturnLines { get; set; }
        public virtual DbSet<Qcparameter> Qcparameters { get; set; }
        public virtual DbSet<QcparameterMapping> QcparameterMappings { get; set; }
        public virtual DbSet<Quotation> Quotations { get; set; }
        public virtual DbSet<QuotationLine> QuotationLines { get; set; }
        public virtual DbSet<RawMaterialPlanning> RawMaterialPlannings { get; set; }
        public virtual DbSet<Resource> Resources { get; set; }
        public virtual DbSet<Role> Roles { get; set; }
        public virtual DbSet<SalesInvoice> SalesInvoices { get; set; }
        public virtual DbSet<SalesInvoiceChargeGroup> SalesInvoiceChargeGroups { get; set; }
        public virtual DbSet<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        public virtual DbSet<SalesOrder> SalesOrders { get; set; }
        public virtual DbSet<SalesOrderLine> SalesOrderLines { get; set; }
        public virtual DbSet<SalesReturn> SalesReturns { get; set; }
        public virtual DbSet<SalesReturnLine> SalesReturnLines { get; set; }
        public virtual DbSet<SalesReturnLineDetail> SalesReturnLineDetails { get; set; }
        public virtual DbSet<SalesServiceOrder> SalesServiceOrders { get; set; }
        public virtual DbSet<SalesServiceOrderJob> SalesServiceOrderJobs { get; set; }
        public virtual DbSet<SalesServiceOrderJobLine> SalesServiceOrderJobLines { get; set; }
        public virtual DbSet<SalesServiceOrderLine> SalesServiceOrderLines { get; set; }
        public virtual DbSet<ScrapPenaltyDeductionRate> ScrapPenaltyDeductionRates { get; set; }
        public virtual DbSet<SemiFinishedGoodsProductionPlan> SemiFinishedGoodsProductionPlans { get; set; }
        public virtual DbSet<SemiFinishedGoodsProductionPlanSummary> SemiFinishedGoodsProductionPlanSummaries { get; set; }
        public virtual DbSet<SemiFinishedGoodsQcscanning> SemiFinishedGoodsQcscannings { get; set; }
        public virtual DbSet<SemiFinishedGoodsQcscanningLine> SemiFinishedGoodsQcscanningLines { get; set; }
        public virtual DbSet<ServiceInquiry> ServiceInquiries { get; set; }
        public virtual DbSet<ServiceInquiryLine> ServiceInquiryLines { get; set; }
        public virtual DbSet<ServiceInvoice> ServiceInvoices { get; set; }
        public virtual DbSet<ServiceInvoiceLine> ServiceInvoiceLines { get; set; }
        public virtual DbSet<ServiceInvoiceMatarialRequestNote> ServiceInvoiceMatarialRequestNotes { get; set; }
        public virtual DbSet<ServiceInvoiceSalesInvoice> ServiceInvoiceSalesInvoices { get; set; }
        public virtual DbSet<ShipmentCost> ShipmentCosts { get; set; }
        public virtual DbSet<ShipmentCostLine> ShipmentCostLines { get; set; }
        public virtual DbSet<ShipmentQualityControl> ShipmentQualityControls { get; set; }
        public virtual DbSet<ShipmentQualityControlLine> ShipmentQualityControlLines { get; set; }
        public virtual DbSet<ShipmentQualityControlReturn> ShipmentQualityControlReturns { get; set; }
        public virtual DbSet<ShipmentQualityControlReturnLine> ShipmentQualityControlReturnLines { get; set; }
        public virtual DbSet<StockAdjustment> StockAdjustments { get; set; }
        public virtual DbSet<StockAdjustmentLine> StockAdjustmentLines { get; set; }
        public virtual DbSet<StockAllocation> StockAllocations { get; set; }
        public virtual DbSet<StockCountSheet> StockCountSheets { get; set; }
        public virtual DbSet<StockCountSheetLine> StockCountSheetLines { get; set; }
        public virtual DbSet<StockTake> StockTakes { get; set; }
        public virtual DbSet<StockTakeLine> StockTakeLines { get; set; }
        public virtual DbSet<StockTransfer> StockTransfers { get; set; }
        public virtual DbSet<StockTransferDetail> StockTransferDetails { get; set; }
        public virtual DbSet<StockTransferLine> StockTransferLines { get; set; }
        public virtual DbSet<StockTransferReceipt> StockTransferReceipts { get; set; }
        public virtual DbSet<StockTransferReceiptLine> StockTransferReceiptLines { get; set; }
        public virtual DbSet<SubContractOrder> SubContractOrders { get; set; }
        public virtual DbSet<SubContractOrderLine> SubContractOrderLines { get; set; }
        public virtual DbSet<SubGroup> SubGroups { get; set; }
        public virtual DbSet<SupplementaryManufacturer> SupplementaryManufacturers { get; set; }
        public virtual DbSet<SupplementaryManufacturerContact> SupplementaryManufacturerContacts { get; set; }
        public virtual DbSet<Supplier> Suppliers { get; set; }
        public virtual DbSet<SupplierContact> SupplierContacts { get; set; }
        public virtual DbSet<SupplierProduct> SupplierProducts { get; set; }
        public virtual DbSet<SupportData> SupportDatas { get; set; }
        public virtual DbSet<TaxGroup> TaxGroups { get; set; }
        public virtual DbSet<TaxGroupTaxType> TaxGroupTaxTypes { get; set; }
        public virtual DbSet<TaxType> TaxTypes { get; set; }
        public virtual DbSet<Token> Tokens { get; set; }
        public virtual DbSet<TouchScreenHistory> TouchScreenHistories { get; set; }
        public virtual DbSet<TransactionDocument> TransactionDocuments { get; set; }
        //public virtual DbSet<TransactionType> TransactionTypes { get; set; }
        public virtual DbSet<TxnLineHistory> TxnLineHistories { get; set; }
        public virtual DbSet<TxnWorkFlow> TxnWorkFlows { get; set; }
        public virtual DbSet<TxnWorkFlowLine> TxnWorkFlowLines { get; set; }
        public virtual DbSet<TyreSpecification> TyreSpecifications { get; set; }
        public virtual DbSet<UnitOfMeasure> UnitOfMeasures { get; set; }
        public virtual DbSet<User> Users { get; set; }
        public virtual DbSet<UserCompany> UserCompanies { get; set; }
        public virtual DbSet<UserDepartment> UserDepartments { get; set; }
        public virtual DbSet<UserDivision> UserDivisions { get; set; }
        public virtual DbSet<UserGroup> UserGroups { get; set; }
        public virtual DbSet<UserRole> UserRoles { get; set; }
        public virtual DbSet<UserSupervisor> UserSupervisors { get; set; }
        public virtual DbSet<Warehous> Warehouses { get; set; }
        public virtual DbSet<WarehousePallet> WarehousePallets { get; set; }
        public virtual DbSet<WarehouseProduct> WarehouseProducts { get; set; }
        public virtual DbSet<WarehouseStockIssue> WarehouseStockIssues { get; set; }
        public virtual DbSet<WarehouseStockQualityControl> WarehouseStockQualityControls { get; set; }
        public virtual DbSet<WorkFlow> WorkFlows { get; set; }
        public virtual DbSet<WorkFlowLine> WorkFlowLines { get; set; }
        public virtual DbSet<WorkstationCadre> WorkstationCadres { get; set; }
        public virtual DbSet<BankPromotion> bankpromotion { get; set; }
        public virtual DbSet<BankName> bankname { get; set; }
        public virtual DbSet<CardType> cardtype { get; set; }
        protected override void OnConfiguring(DbContextOptionsBuilder optionsBuilder)
        {
            if (!optionsBuilder.IsConfigured)
            {
#warning To protect potentially sensitive information in your connection string, you should move it out of source code. See http://go.microsoft.com/fwlink/?LinkId=723263 for guidance on storing connection strings.
                optionsBuilder.UseSqlServer("Server=.;Database=MarangoniERP;integrated security=True;");
            }
        }

        protected override void OnModelCreating(ModelBuilder modelBuilder)
        {
            modelBuilder.Entity<Account>(entity =>
            {
                entity.Property(e => e.BankAccountNumber).IsUnicode(false);

                entity.Property(e => e.BankBranch).IsUnicode(false);

                entity.Property(e => e.BankCode).IsUnicode(false);

                entity.Property(e => e.Swiftcode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Accounts)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Accounts_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.Accounts)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_Accounts_Currencies");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.Accounts)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_Accounts_Employees");
            });

            modelBuilder.Entity<AccountMapping>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.AccountMappings)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_AccountMappings_Companies");
            });

            modelBuilder.Entity<AccountMappingLine>(entity =>
            {
                entity.HasOne(d => d.AccountMapping)
                    .WithMany(p => p.AccountMappingLines)
                    .HasForeignKey(d => d.AccountMappingId)
                    .HasConstraintName("FK_AccountMappingLines_AccountMappings");

                entity.HasOne(d => d.ChartOfAccount)
                    .WithMany(p => p.AccountMappingLines)
                    .HasForeignKey(d => d.ChartOfAccountId)
                    .HasConstraintName("FK_AccountMappingLines_ChartOfAccounts");
            });

            modelBuilder.Entity<AccountMappingLineDetail>(entity =>
            {
                entity.HasOne(d => d.AccountMappingLine)
                    .WithMany(p => p.AccountMappingLineDetails)
                    .HasForeignKey(d => d.AccountMappingLineId)
                    .HasConstraintName("FK_AccountMappingLineDetails_AccountMappingLines");

                entity.HasOne(d => d.ChartOfAccount)
                    .WithMany(p => p.AccountMappingLineDetails)
                    .HasForeignKey(d => d.ChartOfAccountId)
                    .HasConstraintName("FK_AccountMappingLineDetails_ChartOfAccounts");
            });

            modelBuilder.Entity<AgingBucket>(entity =>
            {
                entity.Property(e => e.Description).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.AgingBuckets)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AgingBuckets_Companies");
            });

            modelBuilder.Entity<AppSetting>(entity =>
            {
                entity.Property(e => e.TouchScreenPin).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.AppSettings)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_AppSettings_Companies");

                entity.HasOne(d => d.ConsignmentHoreturnWarehouse)
                    .WithMany(p => p.AppSettingConsignmentHoreturnWarehouses)
                    .HasForeignKey(d => d.ConsignmentHoreturnWarehouseId)
                    .HasConstraintName("FK_AppSettings_ConsignmentHOReturnWarehouse");

                entity.HasOne(d => d.ConsignmentStockTransferWarehouse)
                    .WithMany(p => p.AppSettingConsignmentStockTransferWarehouses)
                    .HasForeignKey(d => d.ConsignmentStockTransferWarehouseId)
                    .HasConstraintName("FK_AppSettings_ConsignmentStockTransferWarehouse");
            });

            modelBuilder.Entity<Attendance>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Attendances)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Attendances_Companies");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.Attendances)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_Attendances_Employees");
            });

            modelBuilder.Entity<AttendanceAllowance>(entity =>
            {
                entity.Property(e => e.Description).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.AttendanceAllowances)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_AttendanceAllowance_Companies");
            });

            modelBuilder.Entity<AuditLog>(entity =>
            {
                entity.Property(e => e.EventDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.AuditLogs)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_AuditLogs_Companies");
            });

            modelBuilder.Entity<BankAdjustment>(entity =>
            {
                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.BankAdjustments)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_BankAdjustments_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.BankAdjustments)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_BankAdjustments_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.BankAdjustments)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_BankAdjustments_Departments");
            });

            modelBuilder.Entity<BankAdjustmentCostAllocation>(entity =>
            {
                entity.HasOne(d => d.BankAdjustmentLine)
                    .WithMany(p => p.BankAdjustmentCostAllocations)
                    .HasForeignKey(d => d.BankAdjustmentLineId)
                    .HasConstraintName("FK_BankAdjustmentCostAllocations_BankAdjustmentLines");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.BankAdjustmentCostAllocations)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_BankAdjustmentCostAllocations_Departments");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.BankAdjustmentCostAllocations)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_BankAdjustmentCostAllocations_GoodsReceiveNotes");
            });

            modelBuilder.Entity<BankAdjustmentLine>(entity =>
            {
                entity.HasOne(d => d.BankAdjustment)
                    .WithMany(p => p.BankAdjustmentLines)
                    .HasForeignKey(d => d.BankAdjustmentId)
                    .HasConstraintName("FK_BankAdjustmentLines_BankAdjustments");

                entity.HasOne(d => d.ChartOfAccount)
                    .WithMany(p => p.BankAdjustmentLines)
                    .HasForeignKey(d => d.ChartOfAccountId)
                    .HasConstraintName("FK_BankAdjustmentLines_ChartOfAccounts");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.BankAdjustmentLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_BankAdjustmentLines_TaxGroups");
            });

            modelBuilder.Entity<BankReconciliation>(entity =>
            {
                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.BankReconciliations)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_BankReconciliations_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.BankReconciliations)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_BankReconciliations_Companies");
            });

            modelBuilder.Entity<BankReconciliationLine>(entity =>
            {
                entity.HasOne(d => d.BankReconciliation)
                    .WithMany(p => p.BankReconciliationLines)
                    .HasForeignKey(d => d.BankReconciliationId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BankReconciliationLines_BankReconciliations");

                entity.HasOne(d => d.GeneralLedgerLine)
                    .WithMany(p => p.BankReconciliationLines)
                    .HasForeignKey(d => d.GeneralLedgerLineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BankReconciliationLines_GeneralLedgerLines");
            });

            modelBuilder.Entity<Barcode>(entity =>
            {
                entity.HasIndex(e => new { e.Date, e.SequenceNumber })
                    .HasName("IX_Barcodes_Column")
                    .IsUnique();

                entity.Property(e => e.Barcode1).IsUnicode(false);
            });

            modelBuilder.Entity<BillOfMaterial>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.Bomcode })
                    .HasName("IX_BillOfMaterials_Code")
                    .IsUnique();

                entity.Property(e => e.Bomcode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.BillOfMaterials)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_BillOfMaterials_Companies");
            });

            modelBuilder.Entity<BillOfMaterialLine>(entity =>
            {
                entity.HasOne(d => d.BillOfMaterial)
                    .WithMany(p => p.BillOfMaterialLines)
                    .HasForeignKey(d => d.BillOfMaterialId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BillOfMaterialLines_BillOfMaterials");

                entity.HasOne(d => d.ProductCategory)
                    .WithMany(p => p.BillOfMaterialLines)
                    .HasForeignKey(d => d.ProductCategoryId)
                    .HasConstraintName("FK_BillOfMaterialLines_ProductHierarchy");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.BillOfMaterialLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_BillOfMaterialLines_Products");

                entity.HasOne(d => d.SecondaryUnitOfMeasure)
                    .WithMany(p => p.BillOfMaterialLines)
                    .HasForeignKey(d => d.SecondaryUnitOfMeasureId)
                    .HasConstraintName("FK_BillOfMaterialLines_UnitOfMeasures");
            });

            modelBuilder.Entity<BillOfOperation>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.Boocode })
                    .HasName("IX_BillOfOperations_Code")
                    .IsUnique();

                entity.Property(e => e.Boocode).IsUnicode(false);

                entity.HasOne(d => d.BillOfMaterial)
                    .WithMany(p => p.BillOfOperations)
                    .HasForeignKey(d => d.BillOfMaterialId)
                    .HasConstraintName("FK_BillOfOperations_BillOfMaterials");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.BillOfOperations)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_BillOfOperations_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.BillOfOperations)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_BillOfOperations_Products");
            });

            modelBuilder.Entity<BillOfOperationLine>(entity =>
            {
                entity.HasOne(d => d.BillOfOperation)
                    .WithMany(p => p.BillOfOperationLines)
                    .HasForeignKey(d => d.BillOfOperationId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BillOfOperationLines_BillOfOperations");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.BillOfOperationLines)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_BillOfOperationLines_Machines");
            });

            modelBuilder.Entity<BillOfOperationLineDetail>(entity =>
            {
                entity.HasOne(d => d.BillOfOperationLine)
                    .WithMany(p => p.BillOfOperationLineDetails)
                    .HasForeignKey(d => d.BillOfOperationLineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BillOfOperationLineDetails_BillOfOperationLines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.BillOfOperationLineDetails)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_BillOfOperationLineDetails_Products");
            });

            modelBuilder.Entity<BillOfOperationLineOverhead>(entity =>
            {
                entity.HasKey(e => new { e.BillOfOperationLineId, e.SupportDataId });

                entity.HasOne(d => d.BillOfOperationLine)
                    .WithMany(p => p.BillOfOperationLineOverheads)
                    .HasForeignKey(d => d.BillOfOperationLineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BillOfOperationLineOverheads_BillOfOperationLines");

                entity.HasOne(d => d.SupportData)
                    .WithMany(p => p.BillOfOperationLineOverheads)
                    .HasForeignKey(d => d.SupportDataId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BillOfOperationLineOverheads_SupportDatas");
            });

            modelBuilder.Entity<BundleProduct>(entity =>
            {
                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.BundleProducts)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_BundleProducts_Companies");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.BundleProducts)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_BundleProducts_UnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.BundleProducts)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_BundleProducts_Products");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.BundleProducts)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_BundleProducts_Warehouses");
            });

            modelBuilder.Entity<BundleProductLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BundleProduct)
                    .WithMany(p => p.BundleProductLines)
                    .HasForeignKey(d => d.BundleProductId)
                    .HasConstraintName("FK_BundleProductLines_BundleProducts");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.BundleProductLines)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_BundleProductLines_UnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.BundleProductLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_BundleProductLines_Products");
            });

            modelBuilder.Entity<ChargeInfoChargeGroup>(entity =>
            {
                entity.HasKey(e => new { e.ChargeInformationId, e.ChargeGroupId });

                entity.HasOne(d => d.ChargeGroup)
                    .WithMany(p => p.ChargeInfoChargeGroups)
                    .HasForeignKey(d => d.ChargeGroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChargeInfoChargeGroups_ChargeGroups");

                entity.HasOne(d => d.ChargeInformation)
                    .WithMany(p => p.ChargeInfoChargeGroups)
                    .HasForeignKey(d => d.ChargeInformationId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChargeInfoChargeGroups_ChargeInformations");
            });

            modelBuilder.Entity<ChargeInformation>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ChargeInformations)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ChargeInformations_Companies");
            });

            modelBuilder.Entity<ChartOfAccount>(entity =>
            {
                entity.Property(e => e.ChartOfAccountCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ChartOfAccounts)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ChartOfAccounts_Companies");
            });

            modelBuilder.Entity<ChequeBook>(entity =>
            {
                entity.Property(e => e.ChequeBookNumber).IsUnicode(false);

                entity.HasOne(d => d.PayBook)
                    .WithMany(p => p.ChequeBooks)
                    .HasForeignKey(d => d.PayBookId)
                    .HasConstraintName("FK_ChequeBooks_PayBooks");
            });

            modelBuilder.Entity<ChequeBookLine>(entity =>
            {
                entity.HasOne(d => d.ChequeBook)
                    .WithMany(p => p.ChequeBookLines)
                    .HasForeignKey(d => d.ChequeBookId)
                    .HasConstraintName("FK_ChequeBookLines_ChequeBooks");
            });

            modelBuilder.Entity<Coamapping>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Coamappings)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_COAMappings_Companies");
            });

            modelBuilder.Entity<Company>(entity =>
            {
                entity.Property(e => e.Email).IsUnicode(false);

                entity.Property(e => e.FilePath1).IsUnicode(false);

                entity.Property(e => e.FilePath2).IsUnicode(false);

                entity.Property(e => e.FilePath3).IsUnicode(false);

                entity.Property(e => e.ReportHeader1).IsUnicode(false);

                entity.Property(e => e.ReportHeader2).IsUnicode(false);

                entity.Property(e => e.ReportHeader3).IsUnicode(false);

                entity.Property(e => e.ReportHeader4).IsUnicode(false);

                entity.Property(e => e.Telephone).IsUnicode(false);

                entity.Property(e => e.Website).IsUnicode(false);

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.Companies)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_Companies_Currencies");

                entity.HasOne(d => d.SubGroup)
                    .WithMany(p => p.Companies)
                    .HasForeignKey(d => d.SubGroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Companies_SubGroups");
            });

            modelBuilder.Entity<ConsignmentPurchaseOrderLine>(entity =>
            {
                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.ConsignmentPurchaseOrderLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_ConsignmentPurchaseOrderLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.ConsignmentStockTransfer)
                    .WithMany(p => p.ConsignmentPurchaseOrderLines)
                    .HasForeignKey(d => d.ConsignmentStockTransferId)
                    .HasConstraintName("FK_ConsignmentPurchaseOrderLines_ConsignmentStockTransfers");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.ConsignmentPurchaseOrderLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_ConsignmentPurchaseOrderLines_DocUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ConsignmentPurchaseOrderLines)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ConsignmentPurchaseOrderLines_Products");
            });

            modelBuilder.Entity<ConsignmentStockTransfer>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_ConsignmentStockTransfers_Code")
                    .IsUnique();

                entity.Property(e => e.Aodnumber).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.Sfamessage).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ConsignmentStockTransfers)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ConsignmentStockTransfers_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.ConsignmentStockTransfers)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_ConsignmentStockTransfers_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.ConsignmentStockTransfers)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_ConsignmentStockTransfers_Departments");

                entity.HasOne(d => d.FromWarehouse)
                    .WithMany(p => p.ConsignmentStockTransferFromWarehouses)
                    .HasForeignKey(d => d.FromWarehouseId)
                    .HasConstraintName("FK_ConsignmentStockTransfers_Warehouses_From");

                entity.HasOne(d => d.ToWarehouse)
                    .WithMany(p => p.ConsignmentStockTransferToWarehouses)
                    .HasForeignKey(d => d.ToWarehouseId)
                    .HasConstraintName("FK_ConsignmentStockTransfers_Warehouses_To");
            });

            modelBuilder.Entity<ConsignmentStockTransferDetail>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.ConsignmentStockTransferLine)
                    .WithMany(p => p.ConsignmentStockTransferDetails)
                    .HasForeignKey(d => d.ConsignmentStockTransferLineId)
                    .HasConstraintName("FK_ConsignmentStockTransferDetails_ConsignmentStockTransferLines");
            });

            modelBuilder.Entity<ConsignmentStockTransferLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.ConsignmentStockTransferLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_ConsignmentStockTransferLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.ConsignmentStockTransfer)
                    .WithMany(p => p.ConsignmentStockTransferLines)
                    .HasForeignKey(d => d.ConsignmentStockTransferId)
                    .HasConstraintName("FK_ConsignmentStockTransferLines_ConsignmentStockTransfers");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.ConsignmentStockTransferLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_ConsignmentStockTransferLines_DocUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ConsignmentStockTransferLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ConsignmentStockTransferLines_Products");
            });

            modelBuilder.Entity<Contact>(entity =>
            {
                entity.Property(e => e.Email).IsUnicode(false);

                entity.Property(e => e.Mobile).IsUnicode(false);

                entity.Property(e => e.Telephone1).IsUnicode(false);

                entity.Property(e => e.Telephone2).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Contacts)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Products_Contacts");
            });

            modelBuilder.Entity<CostingWarehous>(entity =>
            {
                entity.HasOne(d => d.GoodsReceiveNoteLine)
                    .WithMany(p => p.CostingWarehous)
                    .HasForeignKey(d => d.GoodsReceiveNoteLineId)
                    .HasConstraintName("FK_CostingWarehouses_GoodsReceiveNoteLines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.CostingWarehous)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ConsingWarehouses_Products");

                entity.HasOne(d => d.ShipmentCost)
                    .WithMany(p => p.CostingWarehous)
                    .HasForeignKey(d => d.ShipmentCostId)
                    .HasConstraintName("FK_CostingWarehouses_ShipmentCosts");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.CostingWarehous)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_ConsingWarehouses_TaxGroups");
            });

            modelBuilder.Entity<CurrencyExchangeRate>(entity =>
            {
                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.CurrencyExchangeRates)
                    .HasForeignKey(d => d.CurrencyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CurrencyExchangeRates_Currencies");
            });

            modelBuilder.Entity<Customer>(entity =>
            {
                entity.Property(e => e.ContactNumber).IsUnicode(false);

                entity.Property(e => e.CountryOfOrigin).IsUnicode(false);

                entity.Property(e => e.CustomerCode).IsUnicode(false);

                entity.Property(e => e.Nic).IsUnicode(false);

                entity.Property(e => e.PayingParty).IsUnicode(false);

                entity.Property(e => e.SourceDescription).IsUnicode(false);

                entity.Property(e => e.TaxRegistrationNo).IsUnicode(false);

                entity.HasOne(d => d.BillingCurrency)
                    .WithMany(p => p.Customers)
                    .HasForeignKey(d => d.BillingCurrencyId)
                    .HasConstraintName("FK_Customers_Currencies");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Customers)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Customers_Companies");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.Customers)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_Customers_Warehouse");
            });

            modelBuilder.Entity<CustomerContact>(entity =>
            {
                entity.HasKey(e => new { e.CustomerId, e.ContactId });

                entity.HasOne(d => d.Contact)
                    .WithMany(p => p.CustomerContacts)
                    .HasForeignKey(d => d.ContactId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerContacts_Contacts");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.CustomerContacts)
                    .HasForeignKey(d => d.CustomerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerContacts_Customers");
            });

            modelBuilder.Entity<CustomerPayment>(entity =>
            {
                entity.Property(e => e.BillingAddress).IsUnicode(false);

                entity.Property(e => e.ChequeDescription).IsUnicode(false);

                entity.Property(e => e.ChequeNumber).IsUnicode(false);

                entity.Property(e => e.CreditCardNumber).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.BankAccount)
                    .WithMany(p => p.CustomerPaymentBankAccounts)
                    .HasForeignKey(d => d.BankAccountId)
                    .HasConstraintName("FK_CustomerPayments_Accounts_Bank");

                entity.HasOne(d => d.CashAccount)
                    .WithMany(p => p.CustomerPaymentCashAccounts)
                    .HasForeignKey(d => d.CashAccountId)
                    .HasConstraintName("FK_CustomerPayments_Accounts_Cash");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.CustomerPayments)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerPayments_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.CustomerPayments)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_CustomerPayments_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.CustomerPayments)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_CustomerPayments_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.CustomerPayments)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_CustomerPayments_Departments");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.CustomerPayments)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_CustomerPayments_TaxGroup");
            });

            modelBuilder.Entity<CustomerPaymentLine>(entity =>
            {
                entity.HasOne(d => d.CustomerPayment)
                    .WithMany(p => p.CustomerPaymentLines)
                    .HasForeignKey(d => d.CustomerPaymentId)
                    .HasConstraintName("FK_CustomerPaymentLines_CustomerPayments");

                entity.HasOne(d => d.GeneralLedgerLine)
                    .WithMany(p => p.CustomerPaymentLines)
                    .HasForeignKey(d => d.GeneralLedgerLineId)
                    .HasConstraintName("FK_CustomerPaymentLines_GeneralLedgerLines");
            });

            modelBuilder.Entity<CustomerPaymentVoucher>(entity =>
            {
                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.CustomerPayment)
                    .WithMany(p => p.CustomerPaymentVouchers)
                    .HasForeignKey(d => d.CustomerPaymentId)
                    .HasConstraintName("FK_CustomerPaymenVouchers_CustomerPayments");
            });

            modelBuilder.Entity<DateFormat>(entity =>
            {
                entity.Property(e => e.Format).IsUnicode(false);
            });

            modelBuilder.Entity<Department>(entity =>
            {
                entity.Property(e => e.Email).IsUnicode(false);

                entity.Property(e => e.Telephone).IsUnicode(false);

                entity.Property(e => e.Website).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Departments)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Departments_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.Departments)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_Departments_Currencies");
            });

            modelBuilder.Entity<Designation>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Designations)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Designations_Companies");

                entity.HasOne(d => d.ParentDesignation)
                    .WithMany(p => p.InverseParentDesignation)
                    .HasForeignKey(d => d.ParentDesignationId)
                    .HasConstraintName("FK_Designations_Designations");
            });

            modelBuilder.Entity<DiscountScheme>(entity =>
            {
                entity.Property(e => e.DiscountCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.DiscountSchemes)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DiscountScheme_Companies");

                entity.HasOne(d => d.CustomerGroup)
                    .WithMany(p => p.DiscountSchemeCustomerGroups)
                    .HasForeignKey(d => d.CustomerGroupId)
                    .HasConstraintName("FK_DiscountScheme_SupportData_CustomerGroupId");

                entity.HasOne(d => d.CustomerType)
                    .WithMany(p => p.DiscountSchemeCustomerTypes)
                    .HasForeignKey(d => d.CustomerTypeId)
                    .HasConstraintName("FK_DiscountScheme_SupportData_CustomerTypeId");
            });

            modelBuilder.Entity<DiscountSchemeCriteriaLine>(entity =>
            {
                entity.HasOne(d => d.DiscountScheme)
                    .WithMany(p => p.DiscountSchemeCriteriaLines)
                    .HasForeignKey(d => d.DiscountSchemeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DiscountSchemeCriteriaLines_DiscountSchemes");

                entity.HasOne(d => d.ProductHierarchyId2Navigation)
                    .WithMany(p => p.DiscountSchemeCriteriaLines)
                    .HasForeignKey(d => d.ProductHierarchyId2)
                    .HasConstraintName("FK_DiscountSchemeCriteriaLines_ProductHierarchy2");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.DiscountSchemeCriteriaLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_DiscountSchemeCriteriaLines_Products");
            });

            modelBuilder.Entity<DiscountSchemeCustomer>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.DiscountSchemeCustomers)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DiscountSchemeCustomers_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.DiscountSchemeCustomers)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_DiscountSchemeCustomers_Customers");

                entity.HasOne(d => d.DiscountScheme)
                    .WithMany(p => p.DiscountSchemeCustomers)
                    .HasForeignKey(d => d.DiscountSchemeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DiscountSchemeCustomers_DiscountSchemes");
            });

            modelBuilder.Entity<DiscountSchemeLine>(entity =>
            {
                entity.HasOne(d => d.DiscountScheme)
                    .WithMany(p => p.DiscountSchemeLines)
                    .HasForeignKey(d => d.DiscountSchemeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DiscountSchemeLines_DiscountSchemes");

                entity.HasOne(d => d.ProductHierarchyId2Navigation)
                    .WithMany(p => p.DiscountSchemeLines)
                    .HasForeignKey(d => d.ProductHierarchyId2)
                    .HasConstraintName("FK_DiscountSchemeLines_ProductHierarchy2");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.DiscountSchemeLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_DiscountSchemeLines_Products");
            });

            modelBuilder.Entity<Division>(entity =>
            {
                entity.Property(e => e.DivisionCode).IsUnicode(false);

                entity.Property(e => e.Email).IsUnicode(false);

                entity.Property(e => e.Telephone).IsUnicode(false);

                entity.Property(e => e.Website).IsUnicode(false);

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.Divisions)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_Divisions_Currencies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.Divisions)
                    .HasForeignKey(d => d.DepartmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Divisions_Departments");
            });

            modelBuilder.Entity<DocCodeConfig>(entity =>
            {
                entity.Property(e => e.CodePrefix).IsUnicode(false);

                entity.Property(e => e.CodePrefixSeperator).IsUnicode(false);

                entity.Property(e => e.CodeSufix).IsUnicode(false);

                entity.Property(e => e.CodeSufixSeperator).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.DocCodeConfigs)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DocCodeConfig_Companies");
            });

            modelBuilder.Entity<DocTypeConfig>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.DocTypeConfigs)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_DocTypeConfig_Companies");
            });

            modelBuilder.Entity<EmailTemplateLine>(entity =>
            {
                entity.HasOne(d => d.EmailTemplate)
                    .WithMany(p => p.EmailTemplateLines)
                    .HasForeignKey(d => d.EmailTemplateId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_EmailTemplateLines_EmailTemplates");
            });

            modelBuilder.Entity<Employee>(entity =>
            {
                entity.Property(e => e.Email).IsUnicode(false);

                entity.Property(e => e.EmployeeCode).IsUnicode(false);

                entity.Property(e => e.Epfnumber).IsUnicode(false);

                entity.Property(e => e.Nicnumber).IsUnicode(false);

                entity.Property(e => e.PassportNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Employees)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Employees_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.Employees)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_Employees_Departments");

                entity.HasOne(d => d.Designation)
                    .WithMany(p => p.Employees)
                    .HasForeignKey(d => d.DesignationId)
                    .HasConstraintName("FK_Employees_Designations");
            });

            modelBuilder.Entity<EmployeeAccount>(entity =>
            {
                entity.HasKey(e => new { e.EmployeeId, e.AccountId });

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.EmployeeAccounts)
                    .HasForeignKey(d => d.AccountId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerAccounts_Accounts");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.EmployeeAccounts)
                    .HasForeignKey(d => d.EmployeeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerEmployees_Employees");
            });

            modelBuilder.Entity<EmployeeContact>(entity =>
            {
                entity.HasKey(e => new { e.EmployeeId, e.ContactId });

                entity.HasOne(d => d.Contact)
                    .WithMany(p => p.EmployeeContacts)
                    .HasForeignKey(d => d.ContactId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerContacts_Contacts");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.EmployeeContacts)
                    .HasForeignKey(d => d.EmployeeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_CustomerContacts_Customers");
            });

            modelBuilder.Entity<EmployeeIncrement>(entity =>
            {
                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.EmployeeIncrements)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_EmployeeIncrements_Employees");
            });

            modelBuilder.Entity<EmployeeLeaf>(entity =>
            {
                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.EmployeeLeaves)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_EmployeeLeaves_Employees");
            });

            modelBuilder.Entity<ExchangeOrder>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber, e.VersionNo })
                    .HasName("IX_ExchangeOrders_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.PortOfShipment).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ExchangeOrders)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ExchangeOrders_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.ExchangeOrders)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_ExchangeOrders_Departments");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.ExchangeOrders)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_ExchangeOrders_GoodsReceiveNotes");

                entity.HasOne(d => d.ShipmentPackingMethod)
                    .WithMany(p => p.ExchangeOrders)
                    .HasForeignKey(d => d.ShipmentPackingMethodId)
                    .HasConstraintName("FK_ExchangeOrders_SupportData");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.ExchangeOrders)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_ExchangeOrders_Suppliers");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.ExchangeOrders)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_ExchangeOrders_TxnWorkFlows");
            });

            modelBuilder.Entity<ExchangeOrderLine>(entity =>
            {
                entity.Property(e => e.WarrantyPeriod).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.ExchangeOrderLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_ExchangeOrderLines_UnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.ExchangeOrderLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_ExchangeOrderLines_UnitOfMeasures1");

                entity.HasOne(d => d.ExchangeOrder)
                    .WithMany(p => p.ExchangeOrderLines)
                    .HasForeignKey(d => d.ExchangeOrderId)
                    .HasConstraintName("FK_ExchangeOrderLines_ExchangeOrders");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ExchangeOrderLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ExchangeOrderLines_Products");

                entity.HasOne(d => d.Reason)
                    .WithMany(p => p.ExchangeOrderLines)
                    .HasForeignKey(d => d.ReasonId)
                    .HasConstraintName("FK_ExchangeOrderLines_SupportData");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.ExchangeOrderLines)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_ExchangeOrderLines_Warehouses");
            });

            modelBuilder.Entity<FinancialStatement>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.FinancialStatements)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FinancialStatements_Companies");
            });

            modelBuilder.Entity<FinancialStatementLine>(entity =>
            {
                entity.HasOne(d => d.FinancialStatement)
                    .WithMany(p => p.FinancialStatementLines)
                    .HasForeignKey(d => d.FinancialStatementId)
                    .HasConstraintName("FK_FinancialStatementLines_FinancialStatements");

                entity.HasOne(d => d.Parent)
                    .WithMany(p => p.InverseParent)
                    .HasForeignKey(d => d.ParentId)
                    .HasConstraintName("FK_FinancialStatementLines_FinancialStatementLines");
            });

            modelBuilder.Entity<FinancialStatementLineDetail>(entity =>
            {
                entity.HasOne(d => d.ChartOfAccount)
                    .WithMany(p => p.FinancialStatementLineDetails)
                    .HasForeignKey(d => d.ChartOfAccountId)
                    .HasConstraintName("FK_FinancialStatementLineDetails_ChartOfAccounts");

                entity.HasOne(d => d.FinancialStatementLine)
                    .WithMany(p => p.FinancialStatementLineDetails)
                    .HasForeignKey(d => d.FinancialStatementLineId)
                    .HasConstraintName("FK_FinancialStatementLineDetails_FinancialStatementLines");
            });

            modelBuilder.Entity<FixedAsset>(entity =>
            {
                entity.Property(e => e.FixedAssetCode).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.Property(e => e.TagNumber).IsUnicode(false);

                entity.HasOne(d => d.AssetGroup)
                    .WithMany(p => p.FixedAssetAssetGroups)
                    .HasForeignKey(d => d.AssetGroupId)
                    .HasConstraintName("FK_FixedAssets_AssetGroups");

                entity.HasOne(d => d.AssetLocation)
                    .WithMany(p => p.FixedAssetAssetLocations)
                    .HasForeignKey(d => d.AssetLocationId)
                    .HasConstraintName("FK_FixedAssets_AssetLocations");

                entity.HasOne(d => d.AssetType)
                    .WithMany(p => p.FixedAssetAssetTypes)
                    .HasForeignKey(d => d.AssetTypeId)
                    .HasConstraintName("FK_FixedAssets_AssetTypes");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.FixedAssets)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_FixedAssets_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.FixedAssets)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_FixedAssets_Employees");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.FixedAssets)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_FixedAssets_Machines");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.FixedAssets)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_FixedAssets_Users");
            });

            modelBuilder.Entity<FixedAssetMaintenance>(entity =>
            {
                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.FixedAssetMaintenances)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_FixedAssetMaintenance_Employees");

                entity.HasOne(d => d.ServiceInvoice)
                    .WithMany(p => p.FixedAssetMaintenances)
                    .HasForeignKey(d => d.ServiceInvoiceId)
                    .HasConstraintName("FK_FixedAssetMaintenance_ServiceInvoices");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.FixedAssetMaintenances)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_FixedAssetMaintenance_Suppliers");
            });

            modelBuilder.Entity<FixedAssetRegistry>(entity =>
            {
                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.FixedAsset)
                    .WithMany(p => p.FixedAssetRegistries)
                    .HasForeignKey(d => d.FixedAssetId)
                    .HasConstraintName("FK_FixedAssetRegistry_FixedAssets");
            });

            modelBuilder.Entity<FreeIssue>(entity =>
            {
                entity.Property(e => e.FreeIssueCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.FreeIssues)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FreeIssues_Companies");
            });

            modelBuilder.Entity<FreeIssueCriteriaLine>(entity =>
            {
                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.FreeIssueCriteriaLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_FreeIssueCriteriaLine_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.FreeIssueCriteriaLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_FreeIssueCriteriaLine_UnitOfMeasures");

                entity.HasOne(d => d.FreeIssue)
                    .WithMany(p => p.FreeIssueCriteriaLines)
                    .HasForeignKey(d => d.FreeIssueId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FreeIssueCriteriaLine_FreeIssues");

                entity.HasOne(d => d.ProductBrand)
                    .WithMany(p => p.FreeIssueCriteriaLines)
                    .HasForeignKey(d => d.ProductBrandId)
                    .HasConstraintName("FK_FreeIssueCriteriaLine_SupportData_ProductBrandId");

                entity.HasOne(d => d.ProductHierarchyId2Navigation)
                    .WithMany(p => p.FreeIssueCriteriaLines)
                    .HasForeignKey(d => d.ProductHierarchyId2)
                    .HasConstraintName("FK_FreeIssueCriteriaLine_ProductHierarchy2");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.FreeIssueCriteriaLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_FreeIssueCriteriaLine_Products");
            });

            modelBuilder.Entity<FreeIssueCustomer>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.FreeIssueCustomers)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FreeIssueCustomers_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.FreeIssueCustomers)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_FreeIssueCustomers_Customers");

                entity.HasOne(d => d.FreeIssue)
                    .WithMany(p => p.FreeIssueCustomers)
                    .HasForeignKey(d => d.FreeIssueId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FreeIssueCustomers_FreeIssues");
            });

            modelBuilder.Entity<FreeIssueLine>(entity =>
            {
                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.FreeIssueLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_FreeIssueLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.FreeIssueLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_FreeIssueLines_UnitOfMeasures");

                entity.HasOne(d => d.FreeIssue)
                    .WithMany(p => p.FreeIssueLines)
                    .HasForeignKey(d => d.FreeIssueId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_FreeIssueLines_FreeIssues");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.FreeIssueLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_FreeIssueLines_Products");
            });

            modelBuilder.Entity<GeneralLedgerLine>(entity =>
            {
                entity.Property(e => e.LinkTxnDocNumber).IsUnicode(false);

                entity.Property(e => e.Notes).IsUnicode(false);

                entity.HasOne(d => d.ChartOfAccount)
                    .WithMany(p => p.GeneralLedgerLines)
                    .HasForeignKey(d => d.ChartOfAccountId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GeneralLedgerLines_ChartOfAccounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.GeneralLedgerLines)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_GeneralLedgerLines_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.GeneralLedgerLines)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_GeneralLedgerLines_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.GeneralLedgerLines)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_GeneralLedgerLines_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.GeneralLedgerLines)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_GeneralLedgerLines_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.GeneralLedgerLines)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_GeneralLedgerLines_Employees");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.GeneralLedgerLines)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_GeneralLedgerLines_Suppliers");
            });

            modelBuilder.Entity<GeneralLedgerSettlement>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.GeneralLedgerSettlements)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GeneralLedgerSettlements_Company");

                entity.HasOne(d => d.GeneralLedgerLine)
                    .WithMany(p => p.GeneralLedgerSettlements)
                    .HasForeignKey(d => d.GeneralLedgerLineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GeneralLedgerSettlements_GeneralLedgerLines");
            });

            modelBuilder.Entity<GeneralLedgerSettlementLine>(entity =>
            {
                entity.HasOne(d => d.GeneralLedgerLine)
                    .WithMany(p => p.GeneralLedgerSettlementLines)
                    .HasForeignKey(d => d.GeneralLedgerLineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GeneralLedgerSettlementLines_GeneralLedgerLines");

                entity.HasOne(d => d.GeneralLedgerSettlement)
                    .WithMany(p => p.GeneralLedgerSettlementLines)
                    .HasForeignKey(d => d.GeneralLedgerSettlementId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GeneralLedgerSettlementLines_GeneralLedgerSettlements");
            });

            modelBuilder.Entity<GoodsDispatchNote>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_GoodsDispatchNotes_Code")
                    .IsUnique();

                entity.Property(e => e.Aodnumber).IsUnicode(false);

                entity.Property(e => e.ContainerNo).IsUnicode(false);

                entity.Property(e => e.CustomerReference).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.DriverId).IsUnicode(false);

                entity.Property(e => e.DriverName).IsUnicode(false);

                entity.Property(e => e.GatePassNumber).IsUnicode(false);

                entity.Property(e => e.InvoiceNumber).IsUnicode(false);

                entity.Property(e => e.PortOfShipment).IsUnicode(false);

                entity.Property(e => e.Reason).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.SealNumber).IsUnicode(false);

                entity.Property(e => e.Sfamessage).IsUnicode(false);

                entity.Property(e => e.TermsAndConditions).IsUnicode(false);

                entity.Property(e => e.TimeOut).IsUnicode(false);

                entity.Property(e => e.VehicleNo).IsUnicode(false);

                entity.Property(e => e.VesselNo).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_GoodsDispatchNotes_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_GoodsDispatchNotes_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_GoodsDispatchNotes_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_GoodsDispatchNotes_Departments");

                entity.HasOne(d => d.LoadingPlan)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.LoadingPlanId)
                    .HasConstraintName("FK_GoodsDispatchNotes_LoadingPlans");

                entity.HasOne(d => d.LoanOrder)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.LoanOrderId)
                    .HasConstraintName("FK_GoodsDispatchNotes_LoanOrders");

                entity.HasOne(d => d.PaymentTerm)
                    .WithMany(p => p.GoodsDispatchNotePaymentTerms)
                    .HasForeignKey(d => d.PaymentTermId)
                    .HasConstraintName("FK_GoodsDispatchNotes_SupportData1");

                entity.HasOne(d => d.PurchaseReturn)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.PurchaseReturnId)
                    .HasConstraintName("FK_GoodsDispatchNotes_PurchaseReturns");

                entity.HasOne(d => d.SalesInvoice)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.SalesInvoiceId)
                    .HasConstraintName("FK_GoodsDispatchNotes_SalesInvoices");

                entity.HasOne(d => d.SalesOrder)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.SalesOrderId)
                    .HasConstraintName("FK_GoodsDispatchNotes_SalesOrders");

                entity.HasOne(d => d.ShipmentPackingMethod)
                    .WithMany(p => p.GoodsDispatchNoteShipmentPackingMethods)
                    .HasForeignKey(d => d.ShipmentPackingMethodId)
                    .HasConstraintName("FK_GoodsDispatchNotes_SupportData");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_GoodsDispatchNotes_Suppliers");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_GoodsDispatchNotes_TaxGroups");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.GoodsDispatchNotes)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_GoodsDispatchNotes_TxnWorkFlows");
            });

            modelBuilder.Entity<GoodsDispatchNoteLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.CustomerReferenceNo).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.GoodsDispatchNoteLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_GoodsDispatchNoteLines_UnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.GoodsDispatchNoteLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_GoodsDispatchNoteLines_UnitOfMeasures1");

                entity.HasOne(d => d.GoodsDispatchNote)
                    .WithMany(p => p.GoodsDispatchNoteLines)
                    .HasForeignKey(d => d.GoodsDispatchNoteId)
                    .HasConstraintName("FK_GoodsDispatchNoteLines_GoodsDispatchNotes");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.GoodsDispatchNoteLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_GoodsDispatchNoteLines_Products");

                entity.HasOne(d => d.SalesOrderLine)
                    .WithMany(p => p.GoodsDispatchNoteLines)
                    .HasForeignKey(d => d.SalesOrderLineId)
                    .HasConstraintName("FK_GoodsDispatchNoteLines_SalesOrderLines");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.GoodsDispatchNoteLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_GoodsDispatchNoteLines_TaxGroups");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.GoodsDispatchNoteLines)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_GoodsDispatchNoteLines_Warehouses");
            });

            modelBuilder.Entity<GoodsDispatchNoteLineDetail>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.GoodsDispatchNoteLine)
                    .WithMany(p => p.GoodsDispatchNoteLineDetails)
                    .HasForeignKey(d => d.GoodsDispatchNoteLineId)
                    .HasConstraintName("FK_GoodsDispatchNoteLineDetails_GoodsDispatchNoteLines");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.GoodsDispatchNoteLineDetails)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_GoodsDispatchNoteLineDetails_Warehouses");
            });

            modelBuilder.Entity<GoodsReceiveNote>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_GoodsReceiveNotes_Code")
                    .IsUnique();

                entity.Property(e => e.ContainerNo).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.InvoiceNumber).IsUnicode(false);

                entity.Property(e => e.PortOfShipment).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.SupplierReference).IsUnicode(false);

                entity.Property(e => e.TermsAndConditionsId).IsUnicode(false);

                entity.Property(e => e.VehicleNo).IsUnicode(false);

                entity.Property(e => e.VesselNo).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.GoodsReceiveNotes)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_GoodsReceiveNotes_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.GoodsReceiveNotes)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_GoodsReceiveNotes_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.GoodsReceiveNotes)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_GoodsReceiveNotes_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.GoodsReceiveNotes)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_GoodsReceiveNotes_Departments");

                entity.HasOne(d => d.PurchaseOrder)
                    .WithMany(p => p.GoodsReceiveNotes)
                    .HasForeignKey(d => d.PurchaseOrderId)
                    .HasConstraintName("FK_GoodsReceiveNotes_PurchaseOrders");

                entity.HasOne(d => d.SupplementaryManufacturer)
                    .WithMany(p => p.GoodsReceiveNotes)
                    .HasForeignKey(d => d.SupplementaryManufacturerId)
                    .HasConstraintName("FK_GoodsReceiveNotes_SupplementaryManufacturers");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.GoodsReceiveNotes)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_GoodsReceiveNotes_Suppliers");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.GoodsReceiveNotes)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_GoodsReceiveNotes_TxnWorkFlows");
            });

            modelBuilder.Entity<GoodsReceiveNoteLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.GoodsReceiveNoteLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_GoodsReceiveNoteLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.GoodsReceiveNoteLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_GoodsReceiveNoteLines_DocUnitOfMeasures");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.GoodsReceiveNoteLines)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_GoodsReceiveNoteLines_GoodsReceiveNotes");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.GoodsReceiveNoteLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_GoodsReceiveNoteLines_Products");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.GoodsReceiveNoteLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_GoodsReceiveNoteLines_TaxGroups");
            });

            modelBuilder.Entity<GoodsReceiveNoteLineDetail>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.GoodsReceiveNoteLine)
                    .WithMany(p => p.GoodsReceiveNoteLineDetails)
                    .HasForeignKey(d => d.GoodsReceiveNoteLineId)
                    .HasConstraintName("FK_GoodsReceiveNoteLineDetails_GoodsReceiveNoteLines");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.GoodsReceiveNoteLineDetails)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_GoodsReceiveNoteLineDetails_Warehouses");
            });

            modelBuilder.Entity<GroupRole>(entity =>
            {
                entity.HasKey(e => new { e.GroupId, e.RoleId });

                entity.HasOne(d => d.Group)
                    .WithMany(p => p.GroupRoles)
                    .HasForeignKey(d => d.GroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GroupRoles_Groups");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.GroupRoles)
                    .HasForeignKey(d => d.RoleId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_GroupRoles_Roles");
            });

            modelBuilder.Entity<Holding>(entity =>
            {
                entity.Property(e => e.Email).IsUnicode(false);

                entity.Property(e => e.Telephone).IsUnicode(false);

                entity.Property(e => e.Website).IsUnicode(false);
            });

            modelBuilder.Entity<HoreturnStockTransfer>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_HOReturnStockTransfers_Code")
                    .IsUnique();

                entity.Property(e => e.Aodnumber).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.Sfamessage).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.HoreturnStockTransfers)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_HOReturnStockTransfers_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.HoreturnStockTransfers)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_HOReturnStockTransfers_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.HoreturnStockTransfers)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_HOReturnStockTransfers_Departments");

                entity.HasOne(d => d.FromWarehouse)
                    .WithMany(p => p.HoreturnStockTransferFromWarehouses)
                    .HasForeignKey(d => d.FromWarehouseId)
                    .HasConstraintName("FK_HOReturnStockTransfers_Warehouses_From");

                entity.HasOne(d => d.ToWarehouse)
                    .WithMany(p => p.HoreturnStockTransferToWarehouses)
                    .HasForeignKey(d => d.ToWarehouseId)
                    .HasConstraintName("FK_HOReturnStockTransfers_Warehouses_To");
            });

            modelBuilder.Entity<HoreturnStockTransferDetail>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.HoreturnStockTransferLine)
                    .WithMany(p => p.HoreturnStockTransferDetails)
                    .HasForeignKey(d => d.HoreturnStockTransferLineId)
                    .HasConstraintName("FK_HOReturnStockTransferDetails_HOReturnStockTransferLines");
            });

            modelBuilder.Entity<HoreturnStockTransferIntegrationLine>(entity =>
            {
                entity.Property(e => e.ItemBatchCode).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.HoreturnStockTransferIntegrationLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_OReturnStockTransferIntegrationLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.HoreturnStockTransferIntegrationLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_OReturnStockTransferIntegrationLines_DocUnitOfMeasures");

                entity.HasOne(d => d.HoreturnStockTransfer)
                    .WithMany(p => p.HoreturnStockTransferIntegrationLines)
                    .HasForeignKey(d => d.HoreturnStockTransferId)
                    .HasConstraintName("FK_OReturnStockTransferIntegrationLines_HOReturnStockTransfers");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.HoreturnStockTransferIntegrationLines)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_OReturnStockTransferIntegrationLines_Products");
            });

            modelBuilder.Entity<HoreturnStockTransferLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.HoreturnStockTransferLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_HOReturnStockTransferLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.HoreturnStockTransferLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_HOReturnStockTransferLines_DocUnitOfMeasures");

                entity.HasOne(d => d.HoreturnStockTransfer)
                    .WithMany(p => p.HoreturnStockTransferLines)
                    .HasForeignKey(d => d.HoreturnStockTransferId)
                    .HasConstraintName("FK_HOReturnStockTransferLines_ConsignmentStockTransfers");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.HoreturnStockTransferLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_HOReturnStockTransferLines_Products");
            });

            modelBuilder.Entity<IAttendance>(entity =>
            {
                entity.HasNoKey();

                entity.Property(e => e.EmployeeCode).IsUnicode(false);

                entity.Property(e => e.Error).IsUnicode(false);
            });

            modelBuilder.Entity<IBom>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IChartOfAccount>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<ICustomer>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IDepartment>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IEmployee>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IFgBom>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IFgBoo>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IFgBooLineDetail>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IFgBooLineDetails2>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IMachine>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IMould>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IMouldProduct>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IPricelist>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IProduct>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IProductDie>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IQcMapping>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IRawMaterialPlanning>(entity =>
            {
                entity.HasNoKey();

                entity.Property(e => e.Error).IsUnicode(false);

                entity.Property(e => e.ProductCode).IsUnicode(false);

                entity.Property(e => e.ProductName).IsUnicode(false);
            });

            modelBuilder.Entity<IRolePermission>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<ISalesOrder>(entity =>
            {
                entity.HasNoKey();

                entity.Property(e => e.CustomerName).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.Error).IsUnicode(false);

                entity.Property(e => e.ProductCode).IsUnicode(false);

                entity.Property(e => e.ProductName).IsUnicode(false);

                entity.Property(e => e.RequestingUom).IsUnicode(false);
            });

            modelBuilder.Entity<ISfgBoo>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<ISupplier>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<ISupplierProduct>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<ISupplymentaryManufacture>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<ISupportData>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<ITyreSpecification>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IUser>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IUserDepartment>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IUserRole>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<IWarehouseProduct>(entity =>
            {
                entity.HasNoKey();

                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.Error).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.ProductCode).IsUnicode(false);

                entity.Property(e => e.ProductName).IsUnicode(false);

                entity.Property(e => e.Remarks).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.Property(e => e.WarehouseName).IsUnicode(false);
            });

            modelBuilder.Entity<IWorkStationCadre>(entity =>
            {
                entity.HasNoKey();
            });

            modelBuilder.Entity<InboundReceipt>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_InboundReceipts_Code")
                    .IsUnique();

                entity.Property(e => e.ChequeNumber).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.IsPostDatedCheque).HasDefaultValueSql("((0))");

                entity.Property(e => e.RefNumber).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.InboundReceipts)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_InboundReceipts_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.InboundReceipts)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_InboundReceipts_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.InboundReceipts)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_InboundReceipts_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.InboundReceipts)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_InboundReceipts_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.InboundReceipts)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_InboundReceipts_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.InboundReceipts)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_InboundReceipts_Employees");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.InboundReceipts)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_InboundReceipts_Suppliers");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.InboundReceipts)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_InboundReceipts_TaxGroups");
            });

            modelBuilder.Entity<InboundReceiptCostAllocation>(entity =>
            {
                entity.HasOne(d => d.Department)
                    .WithMany(p => p.InboundReceiptCostAllocations)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_InboundReceiptCostAllocations_Departments");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.InboundReceiptCostAllocations)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_InboundReceiptCostAllocations_GoodsReceiveNotes");

                entity.HasOne(d => d.InboundReceiptLine)
                    .WithMany(p => p.InboundReceiptCostAllocations)
                    .HasForeignKey(d => d.InboundReceiptLineId)
                    .HasConstraintName("FK_InboundReceiptCostAllocations_InboundReceiptLines");
            });

            modelBuilder.Entity<InboundReceiptLine>(entity =>
            {
                entity.HasOne(d => d.GeneralLedgerLine)
                    .WithMany(p => p.InboundReceiptLines)
                    .HasForeignKey(d => d.GeneralLedgerLineId)
                    .HasConstraintName("FK_InboundReceiptLines_GeneralLedgerLines");

                entity.HasOne(d => d.InboundReceipt)
                    .WithMany(p => p.InboundReceiptLines)
                    .HasForeignKey(d => d.InboundReceiptId)
                    .HasConstraintName("FK_InboundReceiptLines_InboundReceipts");

                entity.HasOne(d => d.PaymentAdvice)
                    .WithMany(p => p.InboundReceiptLines)
                    .HasForeignKey(d => d.PaymentAdviceId)
                    .HasConstraintName("FK_InboundReceiptLines_PaymentAdvices");
            });

            modelBuilder.Entity<IntegrationStatu>(entity =>
            {
                entity.HasKey(e => e.IntegrationStatusId)
                    .HasName("PK__Integrat__304783FE4C80DB58");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.IntegrationStatus)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_IntegrationStatus_Companies");
            });

            modelBuilder.Entity<InternalDispatch>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_InternalDispatches_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.InternalDispatches)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_InternalDispatches_Companies");

                entity.HasOne(d => d.DeliveryWarehouse)
                    .WithMany(p => p.InternalDispatches)
                    .HasForeignKey(d => d.DeliveryWarehouseId)
                    .HasConstraintName("FK_InternalDispatches_Warehouses");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.InternalDispatches)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_InternalDispatches_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.InternalDispatches)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_InternalDispatches_Employees");

                entity.HasOne(d => d.MaterialRequisitionNote)
                    .WithMany(p => p.InternalDispatches)
                    .HasForeignKey(d => d.MaterialRequisitionNoteId)
                    .HasConstraintName("FK_InternalDispatches_MaterialRequisitionNotes");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.InternalDispatches)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_InternalDispatches_TxnWorkFlows");
            });

            modelBuilder.Entity<InternalDispatchLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.InternalDispatchLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_InternalDispatchLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DeliveryWarehouse)
                    .WithMany(p => p.InternalDispatchLineDeliveryWarehouses)
                    .HasForeignKey(d => d.DeliveryWarehouseId)
                    .HasConstraintName("FK_InternalDispatchLines_Warehouses");

                entity.HasOne(d => d.DestinationWarehouse)
                    .WithMany(p => p.InternalDispatchLineDestinationWarehouses)
                    .HasForeignKey(d => d.DestinationWarehouseId)
                    .HasConstraintName("FK_InternalDispatchLines_DstWarehouses");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.InternalDispatchLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_InternalDispatchLines_DocUnitOfMeasures");

                entity.HasOne(d => d.InternalDispatch)
                    .WithMany(p => p.InternalDispatchLines)
                    .HasForeignKey(d => d.InternalDispatchId)
                    .HasConstraintName("FK_InternalDispatchLines_InternalDispatches");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.InternalDispatchLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_InternalDispatchLines_Products");
            });

            modelBuilder.Entity<InternalDispatchLineDetail>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.InternalDispatchLine)
                    .WithMany(p => p.InternalDispatchLineDetails)
                    .HasForeignKey(d => d.InternalDispatchLineId)
                    .HasConstraintName("FK_InternalDispatchLineDetails_InternalDispatchLines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.InternalDispatchLineDetails)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_InternalDispatchLineDetails_Products");
            });

            modelBuilder.Entity<InternalOrder>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_InternalOrders_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.InternalOrders)
                    .HasForeignKey(d => d.DepartmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InternalOrders_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.InternalOrders)
                    .HasForeignKey(d => d.EmployeeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InternalOrders_Employees");
            });

            modelBuilder.Entity<InternalOrderLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.InternalOrderLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_InternalOrderLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.BillOfMaterial)
                    .WithMany(p => p.InternalOrderLines)
                    .HasForeignKey(d => d.BillOfMaterialId)
                    .HasConstraintName("FK_InternalOrderLines_BillOfMaterials");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.InternalOrderLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_InternalOrderLines_UnitOfMeasures");

                entity.HasOne(d => d.InternalOrder)
                    .WithMany(p => p.InternalOrderLines)
                    .HasForeignKey(d => d.InternalOrderId)
                    .HasConstraintName("FK_InternalOrderLines_InternalOrders");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.InternalOrderLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_InternalOrderLines_Products");

                entity.HasOne(d => d.ProductionOrderIdLinkedToReworkNavigation)
                    .WithMany(p => p.InternalOrderLines)
                    .HasForeignKey(d => d.ProductionOrderIdLinkedToRework)
                    .HasConstraintName("FK_InternalOrderLines_ProductionOrders");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.InternalOrderLines)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_InternalOrderLines_Warehouses");
            });

            modelBuilder.Entity<InternalReturn>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_InternalReturns_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.InternalReturns)
                    .HasForeignKey(d => d.DepartmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InternalReturns_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.InternalReturns)
                    .HasForeignKey(d => d.EmployeeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InternalReturns_Employees");
            });

            modelBuilder.Entity<InternalReturnLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.InternalReturnLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_InternalReturnLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.InternalReturnLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_InternalReturnLines_UnitOfMeasures");

                entity.HasOne(d => d.InternalReturn)
                    .WithMany(p => p.InternalReturnLines)
                    .HasForeignKey(d => d.InternalReturnId)
                    .HasConstraintName("FK_InternalReturnLines_InternalReturns");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.InternalReturnLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_InternalReturnLines_Products");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.InternalReturnLineWarehouses)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_InternalReturnLines_Warehouses");
            });

            modelBuilder.Entity<InternalServiceInvoice>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_InternalServiceInvoices_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.InternalServiceInvoices)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_InternalServiceInvoices_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.InternalServiceInvoices)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_InternalServiceInvoices_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.InternalServiceInvoices)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_InternalServiceInvoices_Employees");
            });

            modelBuilder.Entity<InternalServiceInvoiceLine>(entity =>
            {
                entity.HasOne(d => d.InternalServiceInvoice)
                    .WithMany(p => p.InternalServiceInvoiceLines)
                    .HasForeignKey(d => d.InternalServiceInvoiceId)
                    .HasConstraintName("FK_InternalServiceInvoiceLines_InternalServiceInvoices");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.InternalServiceInvoiceLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_InternalServiceInvoiceLines_Products");
            });

            modelBuilder.Entity<InternalServiceInvoiceLineDetail>(entity =>
            {
                entity.HasOne(d => d.InternalServiceInvoiceLine)
                    .WithMany(p => p.InternalServiceInvoiceLineDetails)
                    .HasForeignKey(d => d.InternalServiceInvoiceLineId)
                    .HasConstraintName("FK_InternalServiceInvoiceLineDetails_InternalServiceInvoiceLines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.InternalServiceInvoiceLineDetails)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_InternalServiceInvoiceLineDetails_Products");

                entity.HasOne(d => d.UnitOfMeasure)
                    .WithMany(p => p.InternalServiceInvoiceLineDetails)
                    .HasForeignKey(d => d.UnitOfMeasureId)
                    .HasConstraintName("FK_InternalServiceInvoiceLineDetails_UnitOfMeasures");
            });

            modelBuilder.Entity<LeaveApplicationForm>(entity =>
            {
                entity.HasOne(d => d.ApplyingEmployee)
                    .WithMany(p => p.LeaveApplicationFormApplyingEmployees)
                    .HasForeignKey(d => d.ApplyingEmployeeId)
                    .HasConstraintName("FK_LeaveApplicationForms_Employees");
            });

            modelBuilder.Entity<LeaveConfiguration>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.LeaveConfigurations)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_LeaveConfigurations_Companies");
            });

            modelBuilder.Entity<LoadingPlan>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber, e.VersionNo })
                    .HasName("IX_LoadingPlans_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.LoadingPlans)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_LoadingPlans_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.LoadingPlans)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_LoadingPlans_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.LoadingPlans)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_LoadingPlans_Customers");

                entity.HasOne(d => d.SalesInvoice)
                    .WithMany(p => p.LoadingPlans)
                    .HasForeignKey(d => d.SalesInvoiceId)
                    .HasConstraintName("FK_LoadingPlans_SalesInvoices");
            });

            modelBuilder.Entity<LoadingPlanLine>(entity =>
            {
                entity.Property(e => e.CustomerReferenceNo).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.LoadingPlan)
                    .WithMany(p => p.LoadingPlanLines)
                    .HasForeignKey(d => d.LoadingPlanId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_LoadingPlanLines_LoadingPlans");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.LoadingPlanLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_LoadingPlanLines_Products");

                entity.HasOne(d => d.SalesInvoiceLine)
                    .WithMany(p => p.LoadingPlanLines)
                    .HasForeignKey(d => d.SalesInvoiceLineId)
                    .HasConstraintName("FK_LoadingPlanLines_SalesInvoiceLines");
            });

            modelBuilder.Entity<LoadingPlanLineDetail>(entity =>
            {
                entity.Property(e => e.PalletCode).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.LoadingPlanLine)
                    .WithMany(p => p.LoadingPlanLineDetails)
                    .HasForeignKey(d => d.LoadingPlanLineId)
                    .HasConstraintName("FK_LoadingPlanLineDetails_LoadingPlanLines");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.LoadingPlanLineDetails)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_LoadingPlanLineDetails_Warehouses");
            });

            modelBuilder.Entity<LoanOrder>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_LoanOrders_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.LoanOrders)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_LoanOrders_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.LoanOrders)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_LoanOrders_Departments");

                entity.HasOne(d => d.SupplementaryManufacturer)
                    .WithMany(p => p.LoanOrders)
                    .HasForeignKey(d => d.SupplementaryManufacturerId)
                    .HasConstraintName("FK_LoanOrders_SupplementaryManufacturers");
            });

            modelBuilder.Entity<LoanOrderLine>(entity =>
            {
                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.LoanOrderLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_LoanOrderLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.LoanOrderLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_LoanOrderLines_DocUnitOfMeasures");

                entity.HasOne(d => d.LoanOrder)
                    .WithMany(p => p.LoanOrderLines)
                    .HasForeignKey(d => d.LoanOrderId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_LoanOrderLines_LoanOrders");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.LoanOrderLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_LoanOrderLines_Products");

                entity.HasOne(d => d.Reason)
                    .WithMany(p => p.LoanOrderLines)
                    .HasForeignKey(d => d.ReasonId)
                    .HasConstraintName("FK_LoanOrderLines_SupportData");
            });

            modelBuilder.Entity<Login>(entity =>
            {
                entity.Property(e => e.LoginId).ValueGeneratedNever();

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Logins)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Products_Logins");

                entity.HasOne(d => d.LoginNavigation)
                    .WithOne(p => p.Login)
                    .HasForeignKey<Login>(d => d.LoginId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Uers_Logins");
            });

            modelBuilder.Entity<Machine>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.MachineCode })
                    .HasName("IX_Machines_Code")
                    .IsUnique();

                entity.Property(e => e.MachineCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Machines)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Machines_Companies");

                entity.HasOne(d => d.PressLine)
                    .WithMany(p => p.Machines)
                    .HasForeignKey(d => d.PressLineId)
                    .HasConstraintName("FK_Machines_PressLines");
            });

            modelBuilder.Entity<MachineLine>(entity =>
            {
                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.MachineLines)
                    .HasForeignKey(d => d.MachineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MachineLines_Machines");
            });

            modelBuilder.Entity<MachineMould>(entity =>
            {
                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.MachineMoulds)
                    .HasForeignKey(d => d.MachineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MachineMoulds_Machines");

                entity.HasOne(d => d.Mould)
                    .WithMany(p => p.MachineMoulds)
                    .HasForeignKey(d => d.MouldId)
                    .HasConstraintName("FK_MachineMoulds_Moulds");
            });

            modelBuilder.Entity<MachineProductComponent>(entity =>
            {
                entity.HasOne(d => d.DefaultUnitOfMeasure)
                    .WithMany(p => p.MachineProductComponents)
                    .HasForeignKey(d => d.DefaultUnitOfMeasureId)
                    .HasConstraintName("FK_MachineProductComponents_UnitOfMeasures");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.MachineProductComponents)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_MachineProductComponents_Machines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.MachineProductComponents)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_MachineProductComponents_Products");
            });

            modelBuilder.Entity<MachineVisualPlanLine>(entity =>
            {
                entity.HasOne(d => d.BillOfOperation)
                    .WithMany(p => p.MachineVisualPlanLines)
                    .HasForeignKey(d => d.BillOfOperationId)
                    .HasConstraintName("FK_MachineVisualPlanLines_BillOfOperations");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.MachineVisualPlanLines)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_MachineVisualPlanLines_Companies");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.MachineVisualPlanLines)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_MachineVisualPlanLines_Machines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.MachineVisualPlanLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_MachineVisualPlanLines_Products");

                entity.HasOne(d => d.ProductionPlanLine)
                    .WithMany(p => p.MachineVisualPlanLines)
                    .HasForeignKey(d => d.ProductionPlanLineId)
                    .HasConstraintName("FK_MachineVisualPlanLines_ProductionPlanLines");
            });

            modelBuilder.Entity<ManualJournal>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_ManualJournals_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ManualJournals)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ManualJournals_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.ManualJournals)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_ManualJournals_Currencies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.ManualJournals)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_ManualJournals_Departments");
            });

            modelBuilder.Entity<ManualJournalCostAllocation>(entity =>
            {
                entity.HasOne(d => d.Department)
                    .WithMany(p => p.ManualJournalCostAllocations)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_ManualJournalCostAllocations_Departments");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.ManualJournalCostAllocations)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_ManualJournalCostAllocations_GoodsReceiveNotes");

                entity.HasOne(d => d.ManualJournalLine)
                    .WithMany(p => p.ManualJournalCostAllocations)
                    .HasForeignKey(d => d.ManualJournalLineId)
                    .HasConstraintName("FK_ManualJournalCostAllocations_ManualJournalLines");
            });

            modelBuilder.Entity<ManualJournalLine>(entity =>
            {
                entity.HasOne(d => d.ChartOfAccount)
                    .WithMany(p => p.ManualJournalLines)
                    .HasForeignKey(d => d.ChartOfAccountId)
                    .HasConstraintName("FK_ManualJournalLines_ChartOfAccounts");

                entity.HasOne(d => d.ManualJournal)
                    .WithMany(p => p.ManualJournalLines)
                    .HasForeignKey(d => d.ManualJournalId)
                    .HasConstraintName("FK_ManualJournalLines_ManualJournals");
            });

            modelBuilder.Entity<MaterialRequisitionNote>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_MaterialRequisitionNotes_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.MaterialRequisitionNotes)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_MaterialRequisitionNotes_Companies");

                entity.HasOne(d => d.DeliveryWarehouse)
                    .WithMany(p => p.MaterialRequisitionNotes)
                    .HasForeignKey(d => d.DeliveryWarehouseId)
                    .HasConstraintName("FK_MaterialRequisitionNotes_Warehouses");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.MaterialRequisitionNotes)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_MaterialRequisitionNotes_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.MaterialRequisitionNotes)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_MaterialRequisitionNotes_Employees");

                entity.HasOne(d => d.FixedAssetGroup)
                    .WithMany(p => p.MaterialRequisitionNoteFixedAssetGroups)
                    .HasForeignKey(d => d.FixedAssetGroupId)
                    .HasConstraintName("FK_MaterialRequisitionNotes_SupportData_AssetGroups");

                entity.HasOne(d => d.FixedAsset)
                    .WithMany(p => p.MaterialRequisitionNotes)
                    .HasForeignKey(d => d.FixedAssetId)
                    .HasConstraintName("FK_MaterialRequisitionNotes_FixedAssets");
            });

            modelBuilder.Entity<MaterialRequisitionNoteLine>(entity =>
            {
                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.MaterialRequisitionNoteLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_MaterialRequisitionNoteLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DeliveryWarehouse)
                    .WithMany(p => p.MaterialRequisitionNoteLines)
                    .HasForeignKey(d => d.DeliveryWarehouseId)
                    .HasConstraintName("FK_MaterialRequisitionNoteLines_Warehouses");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.MaterialRequisitionNoteLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_MaterialRequisitionNoteLines_DocUnitOfMeasures");

                entity.HasOne(d => d.MaterialRequisitionNote)
                    .WithMany(p => p.MaterialRequisitionNoteLines)
                    .HasForeignKey(d => d.MaterialRequisitionNoteId)
                    .HasConstraintName("FK_MaterialRequisitionNoteLines_MaterialRequisitionNotes");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.MaterialRequisitionNoteLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_MaterialRequisitionNoteLines_Products");
            });

            modelBuilder.Entity<MaterialRequisitionPlan>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_MaterialRequisitionPlans_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.MaterialRequisitionPlans)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_MaterialRequisitionPlans_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.MaterialRequisitionPlans)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_MaterialRequisitionPlans_Products");
            });

            modelBuilder.Entity<MaterialRequisitionPlanLine>(entity =>
            {
                entity.HasOne(d => d.MaterialRequisitionPlan)
                    .WithMany(p => p.MaterialRequisitionPlanLines)
                    .HasForeignKey(d => d.MaterialRequisitionPlanId)
                    .HasConstraintName("FK_MaterialRequisitionPlanLines_MaterialRequisitionPlans");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.MaterialRequisitionPlanLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_MaterialRequisitionPlanLines_Products");
            });

            modelBuilder.Entity<Module>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Modules)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Modules_Companies");
            });

            modelBuilder.Entity<MonthEndWarehouseProduct>(entity =>
            {
                entity.Property(e => e.AllocatedQuantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.OnHandQuantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.PalletCode).IsUnicode(false);

                entity.Property(e => e.PhysicalQuantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);
            });

            modelBuilder.Entity<Mould>(entity =>
            {
                entity.Property(e => e.MouldCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Moulds)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Moulds_Companies");
            });

            modelBuilder.Entity<MouldCavity>(entity =>
            {
                entity.HasKey(e => e.CavityId)
                    .HasName("PK_ModuleCavities");

                entity.HasOne(d => d.Mould)
                    .WithMany(p => p.MouldCavities)
                    .HasForeignKey(d => d.MouldId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ModuleCavities_Moulds");
            });

            modelBuilder.Entity<MouldProduct>(entity =>
            {
                entity.HasKey(e => new { e.MouldId, e.ProductId });

                entity.HasOne(d => d.Mould)
                    .WithMany(p => p.MouldProducts)
                    .HasForeignKey(d => d.MouldId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MouldProducts_Moulds");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.MouldProducts)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_MouldProducts_Products");
            });

            modelBuilder.Entity<Notification>(entity =>
            {
                entity.Property(e => e.Description).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Notifications)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Notifications_Companies");
            });

            modelBuilder.Entity<OutboundPayment>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_OutboundPayments_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.IsPostDatedCheque).HasDefaultValueSql("((0))");

                entity.Property(e => e.RefNumber).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_OutboundPayments_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_OutboundPayments_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_OutboundPayments_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_OutboundPayments_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_OutboundPayments_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_OutboundPayments_Employees");

                entity.HasOne(d => d.PayBook)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.PayBookId)
                    .HasConstraintName("FK_OutboundPayments_PayBooks");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_OutboundPayments_Suppliers");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.OutboundPayments)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_OutboundPayments_TaxGroups");
            });

            modelBuilder.Entity<OutboundPaymentCostAllocation>(entity =>
            {
                entity.HasOne(d => d.Department)
                    .WithMany(p => p.OutboundPaymentCostAllocations)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_OutboundPaymentCostAllocations_Departments");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.OutboundPaymentCostAllocations)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_OutboundPaymentCostAllocations_GoodsReceiveNotes");

                entity.HasOne(d => d.OutboundPaymentLine)
                    .WithMany(p => p.OutboundPaymentCostAllocations)
                    .HasForeignKey(d => d.OutboundPaymentLineId)
                    .HasConstraintName("FK_OutboundPaymentCostAllocations_OutboundPaymentLines");
            });

            modelBuilder.Entity<OutboundPaymentLine>(entity =>
            {
                entity.HasOne(d => d.GeneralLedgerLine)
                    .WithMany(p => p.OutboundPaymentLines)
                    .HasForeignKey(d => d.GeneralLedgerLineId)
                    .HasConstraintName("FK_OutboundPaymentLines_GeneralLedgerLines");

                entity.HasOne(d => d.OutboundPayment)
                    .WithMany(p => p.OutboundPaymentLines)
                    .HasForeignKey(d => d.OutboundPaymentId)
                    .HasConstraintName("FK_OutboundPaymentLines_OutboundPayments");

                entity.HasOne(d => d.PaymentAdvice)
                    .WithMany(p => p.OutboundPaymentLines)
                    .HasForeignKey(d => d.PaymentAdviceId)
                    .HasConstraintName("FK_OutboundPaymentLines_PaymentAdvices");
            });

            modelBuilder.Entity<PackingList>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_PackingLists_Code")
                    .IsUnique();

                entity.Property(e => e.ContainerEngineNo).IsUnicode(false);

                entity.Property(e => e.ContainerNo).IsUnicode(false);

                entity.Property(e => e.DestinationPort).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.Measurement).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.Remark).IsUnicode(false);

                entity.Property(e => e.SealNo).IsUnicode(false);

                entity.Property(e => e.TrailerNo).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PackingLists)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PackingLists_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.PackingLists)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_PackingLists_Customers");

                entity.HasOne(d => d.GoodsDispatchNote)
                    .WithMany(p => p.PackingLists)
                    .HasForeignKey(d => d.GoodsDispatchNoteId)
                    .HasConstraintName("FK_PackingLists_GoodsDispatchNotes");

                entity.HasOne(d => d.SalesOrder)
                    .WithMany(p => p.PackingLists)
                    .HasForeignKey(d => d.SalesOrderId)
                    .HasConstraintName("FK_PackingLists_SalesOrders");
            });

            modelBuilder.Entity<PackingListLine>(entity =>
            {
                entity.Property(e => e.CustomerReferenceNo).IsUnicode(false);

                entity.Property(e => e.PalletNumbers).IsUnicode(false);

                entity.HasOne(d => d.PackingList)
                    .WithMany(p => p.PackingListLines)
                    .HasForeignKey(d => d.PackingListId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PackingListLines_PackingLists");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.PackingListLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_PackingListLines_Products");

                entity.HasOne(d => d.SalesOrderLine)
                    .WithMany(p => p.PackingListLines)
                    .HasForeignKey(d => d.SalesOrderLineId)
                    .HasConstraintName("FK_PackingListLines_SalesOrderLines");
            });

            modelBuilder.Entity<PalletBarcode>(entity =>
            {
                entity.HasIndex(e => new { e.Date, e.SequenceNumber })
                    .HasName("IX_PalletBarcodes_Column")
                    .IsUnique();

                entity.Property(e => e.Barcode).IsUnicode(false);
            });

            modelBuilder.Entity<PasswordPolicy>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PasswordPolicies)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_PasswordPolicies_Company");
            });

            modelBuilder.Entity<PayBook>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.PayBookCode })
                    .HasName("IX_PayBooks_Code")
                    .IsUnique();

                entity.Property(e => e.PayBookCode).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.PayBooks)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_PayBooks_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PayBooks)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_PayBooks_Companies");
            });

            modelBuilder.Entity<PaymentAdvice>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_PaymentAdvices_Code")
                    .IsUnique();

                entity.Property(e => e.AdviceNumber).IsUnicode(false);

                entity.Property(e => e.CustomerReference).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.InvoiceNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.SupplierReference).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_PaymentAdvices_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_PaymentAdvices_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_PaymentAdvices_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_PaymentAdvices_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_PaymentAdvices_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_PaymentAdvices_Employees");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_PaymentAdvices_GoodsReceiveNotes");

                entity.HasOne(d => d.PurchaseOrder)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.PurchaseOrderId)
                    .HasConstraintName("FK_PaymentAdvices_PurchaseOrders");

                entity.HasOne(d => d.SalesOrder)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.SalesOrderId)
                    .HasConstraintName("FK_PaymentAdvices_SalesOrders");

                entity.HasOne(d => d.ShipmentCost)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.ShipmentCostId)
                    .HasConstraintName("FK_PaymentAdvices_ShipmentCosts");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_PaymentAdvices_Suppliers");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.PaymentAdvices)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_PaymentAdvices_TaxGroups");
            });

            modelBuilder.Entity<PaymentAdviceCostAllocation>(entity =>
            {
                entity.HasOne(d => d.Department)
                    .WithMany(p => p.PaymentAdviceCostAllocations)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_PaymentAdviceCostAllocations_Departments");

                entity.HasOne(d => d.PaymentAdviceLine)
                    .WithMany(p => p.PaymentAdviceCostAllocations)
                    .HasForeignKey(d => d.PaymentAdviceLineId)
                    .HasConstraintName("FK_PaymentAdviceCostAllocations_PaymentAdviceLines");
            });

            modelBuilder.Entity<PaymentAdviceLine>(entity =>
            {
                entity.HasOne(d => d.ChartOfAccount)
                    .WithMany(p => p.PaymentAdviceLines)
                    .HasForeignKey(d => d.ChartOfAccountId)
                    .HasConstraintName("FK_PaymentAdviceLines_ChartOfAccounts");

                entity.HasOne(d => d.PaymentAdvice)
                    .WithMany(p => p.PaymentAdviceLines)
                    .HasForeignKey(d => d.PaymentAdviceId)
                    .HasConstraintName("FK_PaymentAdviceLines_PaymentAdvices");

                entity.HasOne(d => d.ShipmentCostLine)
                    .WithMany(p => p.PaymentAdviceLines)
                    .HasForeignKey(d => d.ShipmentCostLineId)
                    .HasConstraintName("FK_PaymentAdviceLines_ShipmentCostLines");
            });

            modelBuilder.Entity<Permission>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.ModuleId, e.ResourceId, e.RoleId })
                    .HasName("IX_Permissions_Unique");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Permissions)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Permissions_Companies");

                entity.HasOne(d => d.Group)
                    .WithMany(p => p.Permissions)
                    .HasForeignKey(d => d.GroupId)
                    .HasConstraintName("FK_Permissions_Groups");

                entity.HasOne(d => d.Module)
                    .WithMany(p => p.Permissions)
                    .HasForeignKey(d => d.ModuleId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Permissions_Modules");

                entity.HasOne(d => d.Resource)
                    .WithMany(p => p.Permissions)
                    .HasForeignKey(d => d.ResourceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Permissions_Resources");

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.Permissions)
                    .HasForeignKey(d => d.RoleId)
                    .HasConstraintName("FK_Permissions_Roles");
            });

            modelBuilder.Entity<PettyCash>(entity =>
            {
                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.PettyCashes)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_PettyCashes_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PettyCashes)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_PettyCashes_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.PettyCashes)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_PettyCashes_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.PettyCashes)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_PettyCashes_Employee");

                entity.HasOne(d => d.PettyCashIou)
                    .WithMany(p => p.InversePettyCashIou)
                    .HasForeignKey(d => d.PettyCashIouid)
                    .HasConstraintName("FK_PettyCashes_PettyCashes");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.PettyCashes)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_PettyCashes_Suppliers");
            });

            modelBuilder.Entity<PettyCashCostAllocation>(entity =>
            {
                entity.HasOne(d => d.Department)
                    .WithMany(p => p.PettyCashCostAllocations)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_PettyCashCostAllocations_Departments");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.PettyCashCostAllocations)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_PettyCashCostAllocations_GoodsReceiveNotes");

                entity.HasOne(d => d.PettyCashLine)
                    .WithMany(p => p.PettyCashCostAllocations)
                    .HasForeignKey(d => d.PettyCashLineId)
                    .HasConstraintName("FK_PettyCashCostAllocations_PettyCashLines");
            });

            modelBuilder.Entity<PettyCashLine>(entity =>
            {
                entity.HasOne(d => d.ChartOfAccount)
                    .WithMany(p => p.PettyCashLines)
                    .HasForeignKey(d => d.ChartOfAccountId)
                    .HasConstraintName("FK_PettyCashLines_ChartOfAccounts");

                entity.HasOne(d => d.PettyCash)
                    .WithMany(p => p.PettyCashLines)
                    .HasForeignKey(d => d.PettyCashId)
                    .HasConstraintName("FK_PettyCashLines_PettyCashes");
            });

            modelBuilder.Entity<PressLine>(entity =>
            {
                entity.Property(e => e.Description).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PressLines)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Customers_PressLines");
            });

            modelBuilder.Entity<PriceList>(entity =>
            {
                entity.Property(e => e.Sfamessage).IsUnicode(false);

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.PriceLists)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_PriceLists_Products");
            });

            modelBuilder.Entity<Product>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.Erpcode })
                    .HasName("IX_Products_ERPCode")
                    .IsUnique();

                entity.HasIndex(e => new { e.CompanyId, e.ProductCode })
                    .HasName("IX_Products_ProductCode")
                    .IsUnique();

                entity.Property(e => e.CostingMethodEnum).HasComment("FIFO, LIFO, Weighted Average");

                entity.Property(e => e.Hscode).IsUnicode(false);

                entity.Property(e => e.MovementMethodEnum).HasComment("FIFO, FEFO, LIFO, Serial");

                entity.Property(e => e.RegistrationName).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Products)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Products_Companies");

                entity.HasOne(d => d.DefaultUnitOfMeasure)
                    .WithMany(p => p.Products)
                    .HasForeignKey(d => d.DefaultUnitOfMeasureId)
                    .HasConstraintName("FK_Products_UnitOfMeasures");

                entity.HasOne(d => d.DefaultWarehouse)
                    .WithMany(p => p.Products)
                    .HasForeignKey(d => d.DefaultWarehouseId)
                    .HasConstraintName("FK_Products_Warehouse");

                entity.HasOne(d => d.ProductHierarchyId1Navigation)
                    .WithMany(p => p.ProductProductHierarchyId1Navigation)
                    .HasForeignKey(d => d.ProductHierarchyId1)
                    .HasConstraintName("FK_Products_ProductHierarchy1");

                entity.HasOne(d => d.ProductHierarchyId2Navigation)
                    .WithMany(p => p.ProductProductHierarchyId2Navigation)
                    .HasForeignKey(d => d.ProductHierarchyId2)
                    .HasConstraintName("FK_Products_ProductHierarchy2");

                entity.HasOne(d => d.ProductHierarchyId3Navigation)
                    .WithMany(p => p.ProductProductHierarchyId3Navigation)
                    .HasForeignKey(d => d.ProductHierarchyId3)
                    .HasConstraintName("FK_Products_ProductHierarchy3");

                entity.HasOne(d => d.ProductHierarchyId4Navigation)
                    .WithMany(p => p.ProductProductHierarchyId4Navigation)
                    .HasForeignKey(d => d.ProductHierarchyId4)
                    .HasConstraintName("FK_Products_ProductHierarchy4");

                entity.HasOne(d => d.ProductHierarchyId5Navigation)
                    .WithMany(p => p.ProductProductHierarchyId5Navigation)
                    .HasForeignKey(d => d.ProductHierarchyId5)
                    .HasConstraintName("FK_Products_ProductHierarchy5");

                entity.HasOne(d => d.ProductHierarchyId6Navigation)
                    .WithMany(p => p.ProductProductHierarchyId6Navigation)
                    .HasForeignKey(d => d.ProductHierarchyId6)
                    .HasConstraintName("FK_Products_ProductHierarchy6");
            });

            modelBuilder.Entity<ProductBrandDiscount>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ProductBrandDiscounts)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductBrandDiscounts_Companies");
            });

            modelBuilder.Entity<ProductDy>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ProductDies)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ProductDies_Companies");

                entity.HasOne(d => d.Die)
                    .WithMany(p => p.ProductDies)
                    .HasForeignKey(d => d.DieId)
                    .HasConstraintName("FK_ProductDies_SupportData");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductDies)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ProductDies_Products");
            });

            modelBuilder.Entity<ProductHierarchy>(entity =>
            {
                entity.Property(e => e.Code).IsUnicode(false);

                entity.Property(e => e.Description).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ProductHierarchies)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductHierachy_Companies");
            });

            modelBuilder.Entity<ProductLicenceRegistration>(entity =>
            {
                entity.Property(e => e.LicenseRegistrationStatus).IsUnicode(false);

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductLicenceRegistrations)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ProductLicenceRegistration_Products");
            });

            modelBuilder.Entity<ProductList>(entity =>
            {
                entity.HasIndex(e => e.Code)
                    .IsUnique();

                entity.HasIndex(e => e.Erpcode)
                    .IsUnique();

                entity.Property(e => e.Erpcode).IsUnicode(false);
            });

            modelBuilder.Entity<ProductPackingMethod>(entity =>
            {
                entity.HasIndex(e => new { e.ProductId, e.ShipmentPackingMethodId })
                    .HasName("IX_ProductPackingMethods_ProductId_PackingMethodId")
                    .IsUnique();

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductPackingMethods)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ProductPackingMethods_Products");

                entity.HasOne(d => d.ShipmentPackingMethod)
                    .WithMany(p => p.ProductPackingMethods)
                    .HasForeignKey(d => d.ShipmentPackingMethodId)
                    .HasConstraintName("FK_ProductPackingMethods_SupportData");
            });

            modelBuilder.Entity<ProductSale>(entity =>
            {
                entity.Property(e => e.Barcode).IsUnicode(false);

                entity.Property(e => e.PalletBarcode).IsUnicode(false);
            });

            modelBuilder.Entity<ProductStock>(entity =>
            {
                entity.HasIndex(e => new { e.ProductListId, e.Barcode })
                    .HasName("IX_ProductStock_Column")
                    .IsUnique();

                entity.Property(e => e.Barcode).IsUnicode(false);

                entity.Property(e => e.PalletBarcode).IsUnicode(false);
            });

            modelBuilder.Entity<ProductStockCount>(entity =>
            {
                entity.HasIndex(e => new { e.ProductListId, e.Barcode })
                    .HasName("IX_ProductStockCount_Column")
                    .IsUnique();

                entity.Property(e => e.Barcode).IsUnicode(false);

                entity.Property(e => e.CreationDate).HasDefaultValueSql("(getdate())");

                entity.Property(e => e.PalletBarcode).IsUnicode(false);
            });

            modelBuilder.Entity<ProductUnitOfMeasure>(entity =>
            {
                entity.HasKey(e => new { e.ProductId, e.UnitOfMeasureId });

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductUnitOfMeasures)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductUnitOfMeasures_Products");

                entity.HasOne(d => d.UnitOfMeasure)
                    .WithMany(p => p.ProductUnitOfMeasures)
                    .HasForeignKey(d => d.UnitOfMeasureId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductUnitOfMeasures_UnitOfMeasures");
            });

            modelBuilder.Entity<ProductionOrder>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_ProductionOrders_Code")
                    .IsUnique();

                entity.Property(e => e.Barcode).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.BillOfMaterial)
                    .WithMany(p => p.ProductionOrders)
                    .HasForeignKey(d => d.BillOfMaterialId)
                    .HasConstraintName("FK_ProductionOrders_BillOfMaterials");

                entity.HasOne(d => d.BillOfOperation)
                    .WithMany(p => p.ProductionOrders)
                    .HasForeignKey(d => d.BillOfOperationId)
                    .HasConstraintName("FK_ProductionOrders_BillOfOperations");

                entity.HasOne(d => d.CancelledActivity)
                    .WithMany(p => p.ProductionOrders)
                    .HasForeignKey(d => d.CancelledActivityId)
                    .HasConstraintName("FK_ProductionOrders_SupportData_ActivityId");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ProductionOrders)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ProductionOrders_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductionOrders)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ProductionOrders_Products");
            });

            modelBuilder.Entity<ProductionOrderLine>(entity =>
            {
                entity.HasOne(d => d.Cavity)
                    .WithMany(p => p.ProductionOrderLines)
                    .HasForeignKey(d => d.CavityId)
                    .HasConstraintName("FK_ProductionOrderLines_MouldCavities");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.ProductionOrderLines)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_ProductionOrderLines_Employees");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.ProductionOrderLines)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_ProductionOrderLines_Machines");

                entity.HasOne(d => d.ProductionOrder)
                    .WithMany(p => p.ProductionOrderLines)
                    .HasForeignKey(d => d.ProductionOrderId)
                    .HasConstraintName("FK_ProductionOrderLines_ProductionOrders");
            });

            modelBuilder.Entity<ProductionOrderLineDetail>(entity =>
            {
                entity.Property(e => e.Barcode).IsUnicode(false);

                entity.Property(e => e.UsedBatchNumber).IsUnicode(false);

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductionOrderLineDetails)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ProductionOrderLineDetails_Products");

                entity.HasOne(d => d.ProductionOrderLine)
                    .WithMany(p => p.ProductionOrderLineDetails)
                    .HasForeignKey(d => d.ProductionOrderLineId)
                    .HasConstraintName("FK_ProductionOrderLineDetails_ProductionOrderLines");
            });

            modelBuilder.Entity<ProductionPlan>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.MachineId, e.ProductId, e.DayLightNo, e.StartDateTime })
                    .HasName("IX_ProductionPlans_UniqueIX")
                    .IsUnique();

                entity.Property(e => e.ProductionPlanNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ProductionPlans)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ProductionPlans_Companies");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.ProductionPlans)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_ProductionPlans_Machines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductionPlans)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ProductionPlans_Products");
            });

            modelBuilder.Entity<ProductionPlanBarcode>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.Barcode })
                    .HasName("IX_ProductionPlanBarcodes_Barcode")
                    .IsUnique();

                entity.Property(e => e.Barcode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ProductionPlanBarcodes)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ProductionPlanBarcodes_Companies");
            });

            modelBuilder.Entity<ProductionPlanLine>(entity =>
            {
                entity.Property(e => e.Barcode).IsUnicode(false);

                entity.Property(e => e.DayLightNumbers).IsUnicode(false);

                entity.Property(e => e.ProductionPlanCode).IsUnicode(false);

                entity.HasOne(d => d.BillOfOperation)
                    .WithMany(p => p.ProductionPlanLines)
                    .HasForeignKey(d => d.BillOfOperationId)
                    .HasConstraintName("FK_ProductionPlanLines_BillOfOperations");

                entity.HasOne(d => d.Cavity)
                    .WithMany(p => p.ProductionPlanLines)
                    .HasForeignKey(d => d.CavityId)
                    .HasConstraintName("FK_ProductionPlanLines_MouldCavities");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ProductionPlanLines)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ProductionPlanLines_Companies");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.ProductionPlanLines)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_ProductionPlanLines_Machines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ProductionPlanLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ProductionPlanLines_Products");

                entity.HasOne(d => d.ProductionPlan)
                    .WithMany(p => p.ProductionPlanLines)
                    .HasForeignKey(d => d.ProductionPlanId)
                    .HasConstraintName("FK_ProductionPlanLines_ProductionPlans");
            });

            modelBuilder.Entity<ProductionWarehouseProduct>(entity =>
            {
                entity.Property(e => e.WarehouseProductId).ValueGeneratedNever();

                entity.Property(e => e.BatchNumber).IsUnicode(false);
            });

            modelBuilder.Entity<ProfileBuilding>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ProfileBuildings)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ProfileBuilding_Companies");

                entity.HasOne(d => d.Die)
                    .WithMany(p => p.ProfileBuildings)
                    .HasForeignKey(d => d.DieId)
                    .HasConstraintName("FK_ProfileBuilding_Dies");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.ProfileBuildings)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_ProfileBuilding_Machines");
            });

            modelBuilder.Entity<ProfileBuildingLine>(entity =>
            {
                entity.HasOne(d => d.ProductionOrderLine)
                    .WithMany(p => p.ProfileBuildingLines)
                    .HasForeignKey(d => d.ProductionOrderLineId)
                    .HasConstraintName("FK_ProfileBuildingLines_ProductionOrderLines");

                entity.HasOne(d => d.ProfileBuilding)
                    .WithMany(p => p.ProfileBuildingLines)
                    .HasForeignKey(d => d.ProfileBuildingId)
                    .HasConstraintName("FK_ProfileBuildingLines_ProfileBuilding");
            });

            modelBuilder.Entity<ProfileBuildingWarehouseProduct>(entity =>
            {
                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.ProfileBuildingWarehouseProducts)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_ProfileBuildingWarehouseProducts_Machines");
            });

            modelBuilder.Entity<Promotion>(entity =>
            {
                entity.Property(e => e.Code).IsUnicode(false);
            });

            modelBuilder.Entity<PromotionLine>(entity =>
            {
                entity.HasOne(d => d.DiscountScheme)
                    .WithMany(p => p.PromotionLines)
                    .HasForeignKey(d => d.DiscountSchemeId)
                    .HasConstraintName("FK_PromotionLines_DiscountSchemes");

                entity.HasOne(d => d.FreeIssue)
                    .WithMany(p => p.PromotionLines)
                    .HasForeignKey(d => d.FreeIssueId)
                    .HasConstraintName("FK_PromotionLines_FreeIssues");

                entity.HasOne(d => d.Promotion)
                    .WithMany(p => p.PromotionLines)
                    .HasForeignKey(d => d.PromotionId)
                    .HasConstraintName("FK_PromotionLines_Promotion");
            });

            modelBuilder.Entity<PurchaseOrder>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber, e.VersionNo })
                    .HasName("IX_PurchaseOrders_Code")
                    .IsUnique();

                entity.Property(e => e.Aodnumber).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.SupplierReference).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.PurchaseOrders)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_PurchaseOrders_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PurchaseOrders)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_PurchaseOrders_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.PurchaseOrders)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_PurchaseOrders_Currencies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.PurchaseOrders)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_PurchaseOrders_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.PurchaseOrders)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_PurchaseOrders_Employees");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.PurchaseOrders)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_PurchaseOrders_Suppliers");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.PurchaseOrders)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_PurchaseOrders_TxnWorkFlows");
            });

            modelBuilder.Entity<PurchaseOrderAdditionalCharge>(entity =>
            {
                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.PurchaseOrderAdditionalCharges)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_PurchaseOrderAdditionalCharges_Currencies");

                entity.HasOne(d => d.PurchaseOrder)
                    .WithMany(p => p.PurchaseOrderAdditionalCharges)
                    .HasForeignKey(d => d.PurchaseOrderId)
                    .HasConstraintName("FK_PurchaseOrderAdditionalCharges_PurchaseOrders");
            });

            modelBuilder.Entity<PurchaseOrderAdditionalChargeLine>(entity =>
            {
                entity.HasOne(d => d.ChargeGroup)
                    .WithMany(p => p.PurchaseOrderAdditionalChargeLines)
                    .HasForeignKey(d => d.ChargeGroupId)
                    .HasConstraintName("FK_PurchaseOrderAdditionalChargeLines_SupportData_ChargeGroup");

                entity.HasOne(d => d.PurchaseOrderAdditionalCharge)
                    .WithMany(p => p.PurchaseOrderAdditionalChargeLines)
                    .HasForeignKey(d => d.PurchaseOrderAdditionalChargeId)
                    .HasConstraintName("FK_PurchaseOrderAdditionalChargeLines_PurchaseOrderAdditionalCharges");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.PurchaseOrderAdditionalChargeLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_PurchaseOrderAdditionalChargeLines_TaxGroups");
            });

            modelBuilder.Entity<PurchaseOrderLine>(entity =>
            {
                entity.Property(e => e.WarrantyPeriod).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.PurchaseOrderLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_PurchaseOrderLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.PurchaseOrderLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_PurchaseOrderLines_UnitOfMeasures");

                entity.HasOne(d => d.FixedAsset)
                    .WithMany(p => p.PurchaseOrderLines)
                    .HasForeignKey(d => d.FixedAssetId)
                    .HasConstraintName("FK_PurchaseOrderLines_FixedAssets");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.PurchaseOrderLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_PurchaseOrderLines_Products");

                entity.HasOne(d => d.PurchaseOrder)
                    .WithMany(p => p.PurchaseOrderLines)
                    .HasForeignKey(d => d.PurchaseOrderId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PurchaseOrderLines_PurchaseOrders");

                entity.HasOne(d => d.PurchaseRequisitionNoteLine)
                    .WithMany(p => p.PurchaseOrderLines)
                    .HasForeignKey(d => d.PurchaseRequisitionNoteLineId)
                    .HasConstraintName("FK_PurchaseOrderLines_PurchaseRequisitionNoteLines");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.PurchaseOrderLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_PurchaseOrderLines_TaxGroups");
            });

            modelBuilder.Entity<PurchaseOrderLineDetail>(entity =>
            {
                entity.HasOne(d => d.FixedAsset)
                    .WithMany(p => p.PurchaseOrderLineDetails)
                    .HasForeignKey(d => d.FixedAssetId)
                    .HasConstraintName("FK_PurchaseOrderLineDetails_FixedAssets");

                entity.HasOne(d => d.PurchaseOrderLine)
                    .WithMany(p => p.PurchaseOrderLineDetails)
                    .HasForeignKey(d => d.PurchaseOrderLineId)
                    .HasConstraintName("FK_PurchaseOrderLineDetails_PurchaseOrderLines");
            });

            modelBuilder.Entity<PurchaseOrderSalesInvoice>(entity =>
            {
                entity.HasIndex(e => new { e.PurchaseOrderId, e.SalesInvoiceId })
                    .HasName("IX_PurchaseOrderSalesInvoices")
                    .IsUnique();

                entity.HasOne(d => d.PurchaseOrder)
                    .WithMany(p => p.PurchaseOrderSalesInvoices)
                    .HasForeignKey(d => d.PurchaseOrderId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PurchaseOrderSalesInvoices_PurchaseOrders");

                entity.HasOne(d => d.SalesInvoice)
                    .WithMany(p => p.PurchaseOrderSalesInvoices)
                    .HasForeignKey(d => d.SalesInvoiceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PurchaseOrderSalesInvoices_SalesInvoices");
            });

            modelBuilder.Entity<PurchaseRequisitionNote>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_PurchaseRequisitionNotes_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PurchaseRequisitionNotes)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_PurchaseRequisitionNotes_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.PurchaseRequisitionNotes)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_PurchaseRequisitionNotes_Departments");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.PurchaseRequisitionNotes)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_PurchaseRequisitionNotes_Employees");
            });

            modelBuilder.Entity<PurchaseRequisitionNoteLine>(entity =>
            {
                entity.Property(e => e.WarrantyPeriod).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.PurchaseRequisitionNoteLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_PurchaseRequisitionNoteProducts_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.PurchaseRequisitionNoteLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_PurchaseRequisitionNoteProducts_DocUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.PurchaseRequisitionNoteLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_PurchaseRequisitionNoteProducts_Products");

                entity.HasOne(d => d.PurchaseRequisitionNote)
                    .WithMany(p => p.PurchaseRequisitionNoteLines)
                    .HasForeignKey(d => d.PurchaseRequisitionNoteId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PurchaseRequisitionNoteProducts_PurchaseRequisitionNotes");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.PurchaseRequisitionNoteLines)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_PurchaseRequisitionNoteProducts_Warehouses");
            });

            modelBuilder.Entity<PurchaseReturn>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_PurchaseReturns_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.PurchaseReturns)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_PurchaseReturns_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.PurchaseReturns)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_PurchaseReturns_Currencies");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.PurchaseReturns)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_PurchaseReturns_GoodsReceiveNotes");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.PurchaseReturns)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_PurchaseReturns_Suppliers");
            });

            modelBuilder.Entity<PurchaseReturnLine>(entity =>
            {
                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.PurchaseReturnLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_PurchaseReturnLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.PurchaseReturnLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_PurchaseReturnLines_DocUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.PurchaseReturnLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_PurchaseReturnLines_Products");

                entity.HasOne(d => d.PurchaseReturn)
                    .WithMany(p => p.PurchaseReturnLines)
                    .HasForeignKey(d => d.PurchaseReturnId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_PurchaseReturnLines_PurchaseReturns");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.PurchaseReturnLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_PurchaseReturnLines_TaxGroups");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.PurchaseReturnLines)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_PurchaseReturnLines_Warehouses");
            });

            modelBuilder.Entity<Qcparameter>(entity =>
            {
                entity.Property(e => e.UnitOfMeasure).IsUnicode(false);
            });

            modelBuilder.Entity<QcparameterMapping>(entity =>
            {
                entity.HasKey(e => new { e.ParameterId, e.ProductId });

                entity.HasOne(d => d.Parameter)
                    .WithMany(p => p.QcparameterMappings)
                    .HasForeignKey(d => d.ParameterId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_QCParameterMappings_QCParameters");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.QcparameterMappings)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_QCParameterMappings_Products");
            });

            modelBuilder.Entity<Quotation>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber, e.VersionNo })
                    .HasName("IX_Quotations_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.StatusEnum).HasComment("Under Negotiation");

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.Quotations)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_Quotations_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Quotations)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Quotations_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.Quotations)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_Quotations_Currencies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.Quotations)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_Quotations_Departments");

                entity.HasOne(d => d.PurchaseRequisitionNote)
                    .WithMany(p => p.Quotations)
                    .HasForeignKey(d => d.PurchaseRequisitionNoteId)
                    .HasConstraintName("FK_Quotations_PurchaseRequisitionNotes");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.Quotations)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_Quotations_Suppliers");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.Quotations)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_Quotations_TxnWorkFlows");
            });

            modelBuilder.Entity<QuotationLine>(entity =>
            {
                entity.Property(e => e.WarrantyPeriod).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.QuotationLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_QuotationLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.QuotationLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_QuotationLines_DocUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.QuotationLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_QuotationLines_Products");

                entity.HasOne(d => d.PurchaseRequisitionNoteLine)
                    .WithMany(p => p.QuotationLines)
                    .HasForeignKey(d => d.PurchaseRequisitionNoteLineId)
                    .HasConstraintName("FK_QuotationLines_PurchaseRequisitionNoteLines");

                entity.HasOne(d => d.Quotation)
                    .WithMany(p => p.QuotationLines)
                    .HasForeignKey(d => d.QuotationId)
                    .HasConstraintName("FK_QuotationLines_Quotations");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.QuotationLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_QuotationLines_TaxGroups");
            });

            modelBuilder.Entity<RawMaterialPlanning>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.RawMaterialPlannings)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RawMaterialPlannings_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.RawMaterialPlannings)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_RawMaterialPlannings_Products");
            });

            modelBuilder.Entity<Resource>(entity =>
            {
                entity.HasOne(d => d.Module)
                    .WithMany(p => p.Resources)
                    .HasForeignKey(d => d.ModuleId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_Resources_Modules");
            });

            modelBuilder.Entity<Role>(entity =>
            {
                entity.Property(e => e.RoleName).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Roles)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Roles_Companies");
            });

            modelBuilder.Entity<SalesInvoice>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber, e.VersionNo })
                    .HasName("IX_SalesInvoices_Code")
                    .IsUnique();

                entity.Property(e => e.Aodnumber).IsUnicode(false);

                entity.Property(e => e.CustomerReferenceNo).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.PayingParty).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.SalesInvoices)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_SalesInvoices_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SalesInvoices)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesInvoices_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.SalesInvoices)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_SalesInvoices_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.SalesInvoices)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_SalesInvoices_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.SalesInvoices)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_SalesInvoices_Departments");

                entity.HasOne(d => d.DiscountScheme)
                    .WithMany(p => p.SalesInvoices)
                    .HasForeignKey(d => d.DiscountSchemeId)
                    .HasConstraintName("FK_SalesInvoices_Discounts");

                entity.HasOne(d => d.DispatchWarehouse)
                    .WithMany(p => p.SalesInvoices)
                    .HasForeignKey(d => d.DispatchWarehouseId)
                    .HasConstraintName("FK_SalesInvoices_Warehouses");

                entity.HasOne(d => d.RecommendedPerson)
                    .WithMany(p => p.SalesInvoiceRecommendedPersons)
                    .HasForeignKey(d => d.RecommendedPersonId)
                    .HasConstraintName("FK_SalesInvoices_Employees_RecommendedPerson");

                entity.HasOne(d => d.SalesOrder)
                    .WithMany(p => p.SalesInvoices)
                    .HasForeignKey(d => d.SalesOrderId)
                    .HasConstraintName("FK_SalesInvoices_SalesOrders");

                entity.HasOne(d => d.SalesRepEmployee)
                    .WithMany(p => p.SalesInvoiceSalesRepEmployees)
                    .HasForeignKey(d => d.SalesRepEmployeeId)
                    .HasConstraintName("FK_SalesInvoices_Employees");
            });

            modelBuilder.Entity<SalesInvoiceChargeGroup>(entity =>
            {
                entity.HasKey(e => new { e.SalesInvoiceId, e.ChargeGroupId });

                entity.HasOne(d => d.ChargeGroup)
                    .WithMany(p => p.SalesInvoiceChargeGroups)
                    .HasForeignKey(d => d.ChargeGroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesInvoiceChargeGroups_ChargeGroups");

                entity.HasOne(d => d.SalesInvoice)
                    .WithMany(p => p.SalesInvoiceChargeGroups)
                    .HasForeignKey(d => d.SalesInvoiceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesInvoiceChargeGroups_SalesInvoices");
            });

            modelBuilder.Entity<SalesInvoiceLine>(entity =>
            {
                entity.Property(e => e.CustomerReferenceNo).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.SalesInvoiceLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_SalesInvoiceLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DiscountScheme)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.DiscountSchemeId)
                    .HasConstraintName("FK_SalesInvoiceLines_Discounts");

                entity.HasOne(d => d.DispatchWarehouse)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.DispatchWarehouseId)
                    .HasConstraintName("FK_SalesInvoiceLines_Warehouses");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.SalesInvoiceLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_SalesInvoiceLines_UnitOfMeasures");

                entity.HasOne(d => d.FreeIssue)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.FreeIssueId)
                    .HasConstraintName("FK_SalesInvoiceLines_FreeIssues");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SalesInvoiceLines_Products");

                entity.HasOne(d => d.SalesInvoice)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.SalesInvoiceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesInvoiceLines_SalesInvoices");

                entity.HasOne(d => d.SalesOrderLine)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.SalesOrderLineId)
                    .HasConstraintName("FK_SalesInvoiceLines_SalesOrderLines");

                entity.HasOne(d => d.SalesServiceOrderJob)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.SalesServiceOrderJobId)
                    .HasConstraintName("FK_SalesInvoiceLines_SalesServiceOrderJob");

                entity.HasOne(d => d.SalesServiceOrderLine)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.SalesServiceOrderLineId)
                    .HasConstraintName("FK_SalesInvoiceLines_SalesServiceOrderLines");

                entity.HasOne(d => d.Therapist)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.TherapistId)
                    .HasConstraintName("FK_SalesInvoiceLines_Employees");

                entity.HasOne(d => d.TherapyRoom)
                    .WithMany(p => p.SalesInvoiceLines)
                    .HasForeignKey(d => d.TherapyRoomId)
                    .HasConstraintName("FK_SalesInvoiceLines_Divisions");
            });

            modelBuilder.Entity<SalesOrder>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_SalesOrders_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.PayingParty).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.SfaPoDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SalesOrders)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesOrders_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.SalesOrders)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_SalesOrders_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.SalesOrders)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_SalesOrders_Customers");

                entity.HasOne(d => d.DispatchWarehouse)
                    .WithMany(p => p.SalesOrders)
                    .HasForeignKey(d => d.DispatchWarehouseId)
                    .HasConstraintName("FK_SalesOrders_Warehouses");

                entity.HasOne(d => d.SalesRepEmployee)
                    .WithMany(p => p.SalesOrders)
                    .HasForeignKey(d => d.SalesRepEmployeeId)
                    .HasConstraintName("FK_SalesOrders_Employees");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.SalesOrders)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_SalesOrders_TxnWorkFlows");
            });

            modelBuilder.Entity<SalesOrderLine>(entity =>
            {
                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.SalesOrderLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_SalesOrderLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DispatchWarehouse)
                    .WithMany(p => p.SalesOrderLines)
                    .HasForeignKey(d => d.DispatchWarehouseId)
                    .HasConstraintName("FK_SalesOrderLines_Warehouses");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.SalesOrderLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_SalesOrderLines_UnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SalesOrderLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SalesOrderLines_Products");

                entity.HasOne(d => d.SalesOrder)
                    .WithMany(p => p.SalesOrderLines)
                    .HasForeignKey(d => d.SalesOrderId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesOrderLines_SalesOrders");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.SalesOrderLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_SalesOrderLines_TaxGroups");
            });

            modelBuilder.Entity<SalesReturn>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_SalesReturns_Code")
                    .IsUnique();

                entity.Property(e => e.CustomerReferenceNo).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.PayingParty).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Account)
                    .WithMany(p => p.SalesReturns)
                    .HasForeignKey(d => d.AccountId)
                    .HasConstraintName("FK_SalesReturns_Accounts");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SalesReturns)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesReturns_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.SalesReturns)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_SalesReturns_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.SalesReturns)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_SalesReturns_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.SalesReturns)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_SalesReturns_Departments");

                entity.HasOne(d => d.SalesInvoice)
                    .WithMany(p => p.SalesReturns)
                    .HasForeignKey(d => d.SalesInvoiceId)
                    .HasConstraintName("FK_SalesReturns_SalesInvoices");
            });

            modelBuilder.Entity<SalesReturnLine>(entity =>
            {
                entity.HasOne(d => d.DispatchWarehouse)
                    .WithMany(p => p.SalesReturnLines)
                    .HasForeignKey(d => d.DispatchWarehouseId)
                    .HasConstraintName("FK_SalesReturnLines_Warehouses");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.SalesReturnLines)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_SalesReturnLines_DocUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SalesReturnLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SalesReturnLines_Products");

                entity.HasOne(d => d.SalesReturn)
                    .WithMany(p => p.SalesReturnLines)
                    .HasForeignKey(d => d.SalesReturnId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesReturnLines_SalesReturns");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.SalesReturnLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_SalesReturnLines_TaxGroups");

                entity.HasOne(d => d.TyreSpecification)
                    .WithMany(p => p.SalesReturnLines)
                    .HasForeignKey(d => d.TyreSpecificationId)
                    .HasConstraintName("FK_SalesReturnLines_TyreSpecifications");
            });

            modelBuilder.Entity<SalesReturnLineDetail>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.SalesReturnLine)
                    .WithMany(p => p.SalesReturnLineDetails)
                    .HasForeignKey(d => d.SalesReturnLineId)
                    .HasConstraintName("FK_SalesReturnLineDetails_SalesReturnLines");
            });

            modelBuilder.Entity<SalesServiceOrder>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_SalesServiceOrders_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.SalesServiceOrders)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_SalesServiceOrders_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.SalesServiceOrders)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_SalesServiceOrders_Departments");
            });

            modelBuilder.Entity<SalesServiceOrderJob>(entity =>
            {
                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.SalesServiceOrderLine)
                    .WithMany(p => p.SalesServiceOrderJobs)
                    .HasForeignKey(d => d.SalesServiceOrderLineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesServiceOrderJobs_SalesServiceOrderLines");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.SalesServiceOrderJobs)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_SalesServiceOrderJobs_TxnWorkFlows");
            });

            modelBuilder.Entity<SalesServiceOrderJobLine>(entity =>
            {
                entity.HasOne(d => d.SalesServiceOrderJob)
                    .WithMany(p => p.SalesServiceOrderJobLines)
                    .HasForeignKey(d => d.SalesServiceOrderJobId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesServiceOrderJobLines_SalesServiceOrderJobs");
            });

            modelBuilder.Entity<SalesServiceOrderLine>(entity =>
            {
                entity.HasOne(d => d.BillOfMaterial)
                    .WithMany(p => p.SalesServiceOrderLines)
                    .HasForeignKey(d => d.BillOfMaterialId)
                    .HasConstraintName("FK_SalesServiceOrderLines_BillOfMaterials");

                entity.HasOne(d => d.Brand)
                    .WithMany(p => p.SalesServiceOrderLines)
                    .HasForeignKey(d => d.BrandId)
                    .HasConstraintName("FK_SalesServiceOrderLines_SupportData");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SalesServiceOrderLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SalesServiceOrderLines_Products");

                entity.HasOne(d => d.SalesServiceOrder)
                    .WithMany(p => p.SalesServiceOrderLines)
                    .HasForeignKey(d => d.SalesServiceOrderId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SalesServiceOrderLines_SalesOrders");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.SalesServiceOrderLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_SalesServiceOrderLines_TaxGroups");

                entity.HasOne(d => d.Therapist)
                    .WithMany(p => p.SalesServiceOrderLines)
                    .HasForeignKey(d => d.TherapistId)
                    .HasConstraintName("FK_SalesServiceOrderLines_Employees");

                entity.HasOne(d => d.TherapyRoom)
                    .WithMany(p => p.SalesServiceOrderLines)
                    .HasForeignKey(d => d.TherapyRoomId)
                    .HasConstraintName("FK_SalesServiceOrderLines_Divisions");
            });

            modelBuilder.Entity<SemiFinishedGoodsProductionPlan>(entity =>
            {
                entity.Property(e => e.SemiFinishedGoodsProductionPlanCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SemiFinishedGoodsProductionPlans)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_SemiFinishedGoodsProductionPlans_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SemiFinishedGoodsProductionPlans)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SemiFinishedGoodsProductionPlans_Products");

                entity.HasOne(d => d.ProductionOrderIdLinkedToReworkNavigation)
                    .WithMany(p => p.SemiFinishedGoodsProductionPlans)
                    .HasForeignKey(d => d.ProductionOrderIdLinkedToRework)
                    .HasConstraintName("FK_InternalOrderLines_ProductionOrders");

                entity.HasOne(d => d.SemiFinishedGoodsProductionPlanSummary)
                    .WithMany(p => p.SemiFinishedGoodsProductionPlans)
                    .HasForeignKey(d => d.SemiFinishedGoodsProductionPlanSummaryId)
                    .HasConstraintName("FK_SemiFinishedGoodsProductionPlans_SemiFinishedGoodsProductionPlanSummary");
            });

            modelBuilder.Entity<SemiFinishedGoodsProductionPlanSummary>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SemiFinishedGoodsProductionPlanSummaries)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_SemiFinishedGoodsProductionPlanSummary_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SemiFinishedGoodsProductionPlanSummaries)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SemiFinishedGoodsProductionPlanSummary_Products");
            });

            modelBuilder.Entity<SemiFinishedGoodsQcscanning>(entity =>
            {
                entity.Property(e => e.Barcode).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.ReferenceNumber).IsUnicode(false);

                entity.Property(e => e.ReworkAction).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SemiFinishedGoodsQcscannings)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_SemiFinishedGoodsQCScannings_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SemiFinishedGoodsQcscannings)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SemiFinishedGoodsQCScannings_Products");

                entity.HasOne(d => d.QcengineerUser)
                    .WithMany(p => p.SemiFinishedGoodsQcscannings)
                    .HasForeignKey(d => d.QcengineerUserId)
                    .HasConstraintName("FK_SemiFinishedGoodsQCScannings_Users");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.SemiFinishedGoodsQcscannings)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_SemiFinishedGoodsQCScannings_Warehouses");
            });

            modelBuilder.Entity<SemiFinishedGoodsQcscanningLine>(entity =>
            {
                entity.HasOne(d => d.Parameter)
                    .WithMany(p => p.SemiFinishedGoodsQcscanningLines)
                    .HasForeignKey(d => d.ParameterId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SemiFinishedGoodsQCScanningLines_QCParameters");

                entity.HasOne(d => d.SemiFinishedGoodsQcscanning)
                    .WithMany(p => p.SemiFinishedGoodsQcscanningLines)
                    .HasForeignKey(d => d.SemiFinishedGoodsQcscanningId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SemiFinishedGoodsQCScanningLines_SemiFinishedGoodsQCScannings");
            });

            modelBuilder.Entity<ServiceInquiry>(entity =>
            {
                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.OtherTreatment).IsUnicode(false);

                entity.Property(e => e.PastTreatment).IsUnicode(false);

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.ServiceInquiries)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_ServiceInquiry_Customer");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.ServiceInquiries)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_ServiceInquiry_Departments");

                entity.HasOne(d => d.SkinConditions)
                    .WithMany(p => p.ServiceInquirySkinConditions)
                    .HasForeignKey(d => d.SkinConditionsId)
                    .HasConstraintName("FK_ServiceInquiry_SupportData_SkinConditions");

                entity.HasOne(d => d.SkinType)
                    .WithMany(p => p.ServiceInquirySkinTypes)
                    .HasForeignKey(d => d.SkinTypeId)
                    .HasConstraintName("FK_ServiceInquiry_SupportData_SkinType");
            });

            modelBuilder.Entity<ServiceInquiryLine>(entity =>
            {
                entity.Property(e => e.FrequencyofUseProductId).IsUnicode(false);

                entity.Property(e => e.FrequencyofUseServiceId).IsUnicode(false);

                entity.Property(e => e.Remarks).IsUnicode(false);

                entity.HasOne(d => d.MethodOfUse)
                    .WithMany(p => p.ServiceInquiryLines)
                    .HasForeignKey(d => d.MethodOfUseId)
                    .HasConstraintName("FK_ServiceInquiryLines_SupportData_MethodOfUse");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ServiceInquiryLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ServiceInquiryLines_Products");

                entity.HasOne(d => d.ServiceInquiry)
                    .WithMany(p => p.ServiceInquiryLines)
                    .HasForeignKey(d => d.ServiceInquiryId)
                    .HasConstraintName("FK_ServiceInquiryLines_ServiceInquiries");

                entity.HasOne(d => d.Therapist)
                    .WithMany(p => p.ServiceInquiryLines)
                    .HasForeignKey(d => d.TherapistId)
                    .HasConstraintName("FK_ServiceInquiryLines_Therapist");
            });

            modelBuilder.Entity<ServiceInvoice>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_ServiceInvoices_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.SupplierInvoiceNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ServiceInvoices)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceInvoices_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.ServiceInvoices)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_ServiceInvoices_Currencies");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.ServiceInvoices)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_ServiceInvoices_Employees");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.ServiceInvoices)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_ServiceInvoices_GoodsReceiveNotes");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.ServiceInvoices)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_ServiceInvoices_Suppliers");
            });

            modelBuilder.Entity<ServiceInvoiceLine>(entity =>
            {
                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ServiceInvoiceLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ServiceInvoiceLines_Products");

                entity.HasOne(d => d.ServiceInvoice)
                    .WithMany(p => p.ServiceInvoiceLines)
                    .HasForeignKey(d => d.ServiceInvoiceId)
                    .HasConstraintName("FK_ServiceInvoiceLines_ServiceInvoices");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.ServiceInvoiceLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_ServiceInvoiceLines_TaxGroups");
            });

            modelBuilder.Entity<ServiceInvoiceMatarialRequestNote>(entity =>
            {
                entity.HasKey(e => new { e.InternalServiceInvoiceId, e.MaterialRequisitionNoteId })
                    .HasName("PK_ServiceInvoiceMatarialRequestNote");

                entity.HasOne(d => d.InternalServiceInvoice)
                    .WithMany(p => p.ServiceInvoiceMatarialRequestNotes)
                    .HasForeignKey(d => d.InternalServiceInvoiceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceInvoiceMatarialRequestNote_InternalServiceInvoices");

                entity.HasOne(d => d.MaterialRequisitionNote)
                    .WithMany(p => p.ServiceInvoiceMatarialRequestNotes)
                    .HasForeignKey(d => d.MaterialRequisitionNoteId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceInvoiceMatarialRequestNote_MaterialRequisitionNotes");
            });

            modelBuilder.Entity<ServiceInvoiceSalesInvoice>(entity =>
            {
                entity.HasIndex(e => new { e.ServiceInvoiceId, e.SalesInvoiceId })
                    .HasName("IX_ServiceInvoiceSalesInvoices")
                    .IsUnique();

                entity.HasOne(d => d.SalesInvoice)
                    .WithMany(p => p.ServiceInvoiceSalesInvoices)
                    .HasForeignKey(d => d.SalesInvoiceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceInvoiceSalesInvoices_SalesInvoices");

                entity.HasOne(d => d.ServiceInvoice)
                    .WithMany(p => p.ServiceInvoiceSalesInvoices)
                    .HasForeignKey(d => d.ServiceInvoiceId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_ServiceInvoiceSalesInvoices_ServiceInvoices");
            });

            modelBuilder.Entity<ShipmentCost>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_ShipmentCosts_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.ReferenceNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ShipmentCosts)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ShipmentCosts_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.ShipmentCosts)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_ShipmentCosts_Departments");

                entity.HasOne(d => d.ShipmentQualityControl)
                    .WithMany(p => p.ShipmentCosts)
                    .HasForeignKey(d => d.ShipmentQualityControlId)
                    .HasConstraintName("FK_ShipmentCosts_ShipmentQualityControls");
            });

            modelBuilder.Entity<ShipmentCostLine>(entity =>
            {
                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.ShipmentCostLines)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_ShipmentCostLines_Currencies");

                entity.HasOne(d => d.ShipmentCost)
                    .WithMany(p => p.ShipmentCostLines)
                    .HasForeignKey(d => d.ShipmentCostId)
                    .HasConstraintName("FK_ShipmentCostLines_ShipmentCosts");
            });

            modelBuilder.Entity<ShipmentQualityControl>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_ShipmentQualityControls_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ShipmentQualityControls)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ShipmentQualityControls_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.ShipmentQualityControls)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_ShipmentQualityControls_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.ShipmentQualityControls)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_ShipmentQualityControls_Departments");

                entity.HasOne(d => d.GoodsReceiveNote)
                    .WithMany(p => p.ShipmentQualityControls)
                    .HasForeignKey(d => d.GoodsReceiveNoteId)
                    .HasConstraintName("FK_ShipmentQualityControls_GoodsReceiveNotes");

                entity.HasOne(d => d.SupplementaryManufacturer)
                    .WithMany(p => p.ShipmentQualityControls)
                    .HasForeignKey(d => d.SupplementaryManufacturerId)
                    .HasConstraintName("FK_ShipmentQualityControls_SupplementaryManufacturers");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.ShipmentQualityControls)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_ShipmentQualityControls_Suppliers");
            });

            modelBuilder.Entity<ShipmentQualityControlLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.ShipmentQualityControlLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_ShipmentQualityControlLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.ShipmentQualityControlLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_ShipmentQualityControlLines_DocUnitOfMeasures");

                entity.HasOne(d => d.GoodsReceiveNoteLineDetail)
                    .WithMany(p => p.ShipmentQualityControlLines)
                    .HasForeignKey(d => d.GoodsReceiveNoteLineDetailId)
                    .HasConstraintName("FK_ShipmentQualityControlLines_GoodsReceiveNoteLineDetails");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ShipmentQualityControlLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ShipmentQualityControlLines_Products");

                entity.HasOne(d => d.ShipmentQualityControl)
                    .WithMany(p => p.ShipmentQualityControlLines)
                    .HasForeignKey(d => d.ShipmentQualityControlId)
                    .HasConstraintName("FK_ShipmentQualityControlLines_ShipmentQualityControls");
            });

            modelBuilder.Entity<ShipmentQualityControlReturn>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_ShipmentQualityControlReturns_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.Reference).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.ShipmentQualityControlReturns)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_ShipmentQualityControlReturns_Companies");

                entity.HasOne(d => d.Currency)
                    .WithMany(p => p.ShipmentQualityControlReturns)
                    .HasForeignKey(d => d.CurrencyId)
                    .HasConstraintName("FK_ShipmentQualityControlReturns_Currencies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.ShipmentQualityControlReturns)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_ShipmentQualityControlReturns_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.ShipmentQualityControlReturns)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_ShipmentQualityControlReturns_Departments");

                entity.HasOne(d => d.ShipmentQualityControl)
                    .WithMany(p => p.ShipmentQualityControlReturns)
                    .HasForeignKey(d => d.ShipmentQualityControlId)
                    .HasConstraintName("FK_ShipmentQualityControlReturns_ShipmentQualityControls");

                entity.HasOne(d => d.SupplementaryManufacturer)
                    .WithMany(p => p.ShipmentQualityControlReturns)
                    .HasForeignKey(d => d.SupplementaryManufacturerId)
                    .HasConstraintName("FK_ShipmentQualityControlReturns_SupplementaryManufacturers");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.ShipmentQualityControlReturns)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_ShipmentQualityControlReturns_Suppliers");
            });

            modelBuilder.Entity<ShipmentQualityControlReturnLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.ShipmentQualityControlReturnLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_ShipmentQualityControlReturnLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.ShipmentQualityControlReturnLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_ShipmentQualityControlReturnLines_DocUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.ShipmentQualityControlReturnLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_ShipmentQualityControlReturnLines_Products");

                entity.HasOne(d => d.ShipmentQualityControlReturn)
                    .WithMany(p => p.ShipmentQualityControlReturnLines)
                    .HasForeignKey(d => d.ShipmentQualityControlReturnId)
                    .HasConstraintName("FK_ShipmentQualityControlReturnLines_ShipmentQualityControls");

                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.ShipmentQualityControlReturnLines)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_ShipmentQualityControlReturnLines_TaxGroups");
            });

            modelBuilder.Entity<StockAdjustment>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_StockAdjustments_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StockAdjustments)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_StockAdjustments_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.StockAdjustments)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_StockAdjustments_Departments");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.StockAdjustments)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_StockAdjustments_TxnWorkFlows");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.StockAdjustments)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_StockAdjustments_Warehouses");
            });

            modelBuilder.Entity<StockAdjustmentLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.StockAdjustmentLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_StockAdjustmentLines_Products");

                entity.HasOne(d => d.StockAdjustment)
                    .WithMany(p => p.StockAdjustmentLines)
                    .HasForeignKey(d => d.StockAdjustmentId)
                    .HasConstraintName("FK_StockAdjustmentLines_StockAdjustments");

                entity.HasOne(d => d.UnitOfMeasure)
                    .WithMany(p => p.StockAdjustmentLines)
                    .HasForeignKey(d => d.UnitOfMeasureId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockAdjustmentLines_UnitOfMeasures");
            });

            modelBuilder.Entity<StockAllocation>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StockAllocations)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_StockAllocations_Companies");

                entity.HasOne(d => d.WarehouseProduct)
                    .WithMany(p => p.StockAllocations)
                    .HasForeignKey(d => d.WarehouseProductId)
                    .HasConstraintName("FK_StockAllocations_WarehouseProducts");
            });

            modelBuilder.Entity<StockCountSheet>(entity =>
            {
                entity.Property(e => e.RefDocCode).IsUnicode(false);

                entity.Property(e => e.StockCountSheetCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StockCountSheets)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockCountSheets_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.StockCountSheets)
                    .HasForeignKey(d => d.CustomerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockCountSheets_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.StockCountSheets)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_StockCountSheets_Departments");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.StockCountSheets)
                    .HasForeignKey(d => d.WarehouseId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockCountSheets_Warehouse");
            });

            modelBuilder.Entity<StockCountSheetLine>(entity =>
            {
                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.StockCountSheetLines)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_StockCountSheetLines_UnitOfMeasures");

                entity.HasOne(d => d.StockCountSheet)
                    .WithMany(p => p.StockCountSheetLines)
                    .HasForeignKey(d => d.StockCountSheetId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_StockCountSheetLines_StockCountSheet");
            });

            modelBuilder.Entity<StockTake>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_StockTakes_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StockTakes)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_StockTakes_Companies");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.StockTakes)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .HasConstraintName("FK_StockTakes_TxnWorkFlows");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.StockTakes)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_StockTakes_Warehouses");
            });

            modelBuilder.Entity<StockTakeLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.Remarks).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.StockTakeLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_StockTakeLines_Products");

                entity.HasOne(d => d.StockTake)
                    .WithMany(p => p.StockTakeLines)
                    .HasForeignKey(d => d.StockTakeId)
                    .HasConstraintName("FK_StockTakeLines_StockTakes");
            });

            modelBuilder.Entity<StockTransfer>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_StockTransfers_Code")
                    .IsUnique();

                entity.Property(e => e.Aodnumber).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StockTransfers)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_StockTransfers_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.StockTransfers)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_StockTransfers_Departments");

                entity.HasOne(d => d.FromWarehouse)
                    .WithMany(p => p.StockTransferFromWarehouses)
                    .HasForeignKey(d => d.FromWarehouseId)
                    .HasConstraintName("FK_StockTransfers_Warehouses_From");

                entity.HasOne(d => d.ToWarehouse)
                    .WithMany(p => p.StockTransferToWarehouses)
                    .HasForeignKey(d => d.ToWarehouseId)
                    .HasConstraintName("FK_StockTransfers_Warehouses_To");
            });

            modelBuilder.Entity<StockTransferDetail>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.StockTransferLine)
                    .WithMany(p => p.StockTransferDetails)
                    .HasForeignKey(d => d.StockTransferLineId)
                    .HasConstraintName("FK_StockTransferDetails_StockTransferLines");
            });

            modelBuilder.Entity<StockTransferLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.StockTransferLines)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_StockTransferLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.StockTransferLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_StockTransferLines_Products");

                entity.HasOne(d => d.StockTransfer)
                    .WithMany(p => p.StockTransferLines)
                    .HasForeignKey(d => d.StockTransferId)
                    .HasConstraintName("FK_StockTransferLines_StockTransfers");
            });

            modelBuilder.Entity<StockTransferReceipt>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_StockTransferReceipts_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.StockTransferReceipts)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_StockTransferReceipts_Companies");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.StockTransferReceipts)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_StockTransferReceipts_Departments");

                entity.HasOne(d => d.FromWarehouse)
                    .WithMany(p => p.StockTransferReceiptFromWarehouses)
                    .HasForeignKey(d => d.FromWarehouseId)
                    .HasConstraintName("FK_StockTransferReceipts_Warehouses_From");

                entity.HasOne(d => d.StockTransfer)
                    .WithMany(p => p.StockTransferReceipts)
                    .HasForeignKey(d => d.StockTransferId)
                    .HasConstraintName("FK_StockTransferReceipts_StockTransfers");

                entity.HasOne(d => d.ToWarehouse)
                    .WithMany(p => p.StockTransferReceiptToWarehouses)
                    .HasForeignKey(d => d.ToWarehouseId)
                    .HasConstraintName("FK_StockTransferReceipts_Warehouses_To");
            });

            modelBuilder.Entity<StockTransferReceiptLine>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.StockTransferReceiptLines)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_StockTransferReceiptLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.StockTransferReceiptLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_StockTransferReceiptLines_Products");

                entity.HasOne(d => d.StockTransferReceipt)
                    .WithMany(p => p.StockTransferReceiptLines)
                    .HasForeignKey(d => d.StockTransferReceiptId)
                    .HasConstraintName("FK_StockTransferReceiptLines_StockTransferReceipts");
            });

            modelBuilder.Entity<SubContractOrder>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.DocNumber })
                    .HasName("IX_SubContractOrders_Code")
                    .IsUnique();

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.HasOne(d => d.BillOfMaterial)
                    .WithMany(p => p.SubContractOrders)
                    .HasForeignKey(d => d.BillOfMaterialId)
                    .HasConstraintName("FK_SubContractOrderes_BillOfMaterials");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SubContractOrders)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_SubContractOrders_Companies");

                entity.HasOne(d => d.Customer)
                    .WithMany(p => p.SubContractOrders)
                    .HasForeignKey(d => d.CustomerId)
                    .HasConstraintName("FK_SubContractOrders_Customers");

                entity.HasOne(d => d.Department)
                    .WithMany(p => p.SubContractOrders)
                    .HasForeignKey(d => d.DepartmentId)
                    .HasConstraintName("FK_SubContractOrderes_Departments");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.SubContractOrders)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_SubContractOrders_UnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SubContractOrders)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SubContractOrders_Products");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.SubContractOrders)
                    .HasForeignKey(d => d.SupplierId)
                    .HasConstraintName("FK_SubContractOrders_Suppliers");
            });

            modelBuilder.Entity<SubContractOrderLine>(entity =>
            {
                entity.HasOne(d => d.BaseUnitOfMeasure)
                    .WithMany(p => p.SubContractOrderLineBaseUnitOfMeasures)
                    .HasForeignKey(d => d.BaseUnitOfMeasureId)
                    .HasConstraintName("FK_SubContractOrderLines_BaseUnitOfMeasures");

                entity.HasOne(d => d.BillOfMaterial)
                    .WithMany(p => p.SubContractOrderLines)
                    .HasForeignKey(d => d.BillOfMaterialId)
                    .HasConstraintName("FK_SubContractOrderLines_BillOfMaterials");

                entity.HasOne(d => d.DocUnitOfMeasure)
                    .WithMany(p => p.SubContractOrderLineDocUnitOfMeasures)
                    .HasForeignKey(d => d.DocUnitOfMeasureId)
                    .HasConstraintName("FK_SubContractOrderLines_UnitOfMeasures");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SubContractOrderLines)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_SubContractOrderLines_Products");

                entity.HasOne(d => d.SalesOrderLine)
                    .WithMany(p => p.SubContractOrderLines)
                    .HasForeignKey(d => d.SalesOrderLineId)
                    .HasConstraintName("FK_SubContractOrderLines_SalesOrderLines");

                entity.HasOne(d => d.SubContractOrder)
                    .WithMany(p => p.SubContractOrderLines)
                    .HasForeignKey(d => d.SubContractOrderId)
                    .HasConstraintName("FK_SubContractOrderLines_SubContractOrders");
            });

            modelBuilder.Entity<SubGroup>(entity =>
            {
                entity.Property(e => e.Email).IsUnicode(false);

                entity.Property(e => e.SubGroupCode).IsUnicode(false);

                entity.Property(e => e.TaxRegistrationNo).IsUnicode(false);

                entity.Property(e => e.Telephone).IsUnicode(false);

                entity.Property(e => e.Website).IsUnicode(false);

                entity.HasOne(d => d.Holding)
                    .WithMany(p => p.SubGroups)
                    .HasForeignKey(d => d.HoldingId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SubGroups_Holdings");
            });

            modelBuilder.Entity<SupplementaryManufacturer>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.SupplementaryManufacturerCode })
                    .HasName("IX_SupplementaryManufacturers_Code")
                    .IsUnique();

                entity.Property(e => e.SupplementaryManufacturerCode).IsUnicode(false);

                entity.Property(e => e.TaxRegistrationNo).IsUnicode(false);

                entity.HasOne(d => d.Category)
                    .WithMany(p => p.SupplementaryManufacturers)
                    .HasForeignKey(d => d.CategoryId)
                    .HasConstraintName("FK_SupplementaryManufacturers_Category");

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SupplementaryManufacturers)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Products_SupplementaryManufacturers");
            });

            modelBuilder.Entity<SupplementaryManufacturerContact>(entity =>
            {
                entity.HasKey(e => new { e.SupplementaryManufacturerId, e.ContactId });

                entity.HasOne(d => d.Contact)
                    .WithMany(p => p.SupplementaryManufacturerContacts)
                    .HasForeignKey(d => d.ContactId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupplementaryManufacturerContacts_Contacts");

                entity.HasOne(d => d.SupplementaryManufacturer)
                    .WithMany(p => p.SupplementaryManufacturerContacts)
                    .HasForeignKey(d => d.SupplementaryManufacturerId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupplementaryManufacturerContacts_SupplementaryManufacturers");
            });

            modelBuilder.Entity<Supplier>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.SupplierCode })
                    .HasName("IX_Suppliers_Code")
                    .IsUnique();

                entity.Property(e => e.SupplierCode).IsUnicode(false);

                entity.Property(e => e.TaxRegistrationNo).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Suppliers)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Suppliers_Companies");
            });

            modelBuilder.Entity<SupplierContact>(entity =>
            {
                entity.HasKey(e => new { e.SupplierId, e.ContactId });

                entity.HasOne(d => d.Contact)
                    .WithMany(p => p.SupplierContacts)
                    .HasForeignKey(d => d.ContactId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupplierContacts_Contacts");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.SupplierContacts)
                    .HasForeignKey(d => d.SupplierId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupplierContacts_Suppliers");
            });

            modelBuilder.Entity<SupplierProduct>(entity =>
            {
                entity.HasKey(e => new { e.SupplierId, e.ProductId });

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.SupplierProducts)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupplierProducts_Products");

                entity.HasOne(d => d.Supplier)
                    .WithMany(p => p.SupplierProducts)
                    .HasForeignKey(d => d.SupplierId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupplierProducts_Suppliers");
            });

            modelBuilder.Entity<SupportData>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.SupportDatas)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_SupportData_Companies");
            });

            modelBuilder.Entity<TaxGroup>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.TaxGroupCode })
                    .HasName("IX_TaxGroups_Code")
                    .IsUnique();

                entity.Property(e => e.TaxGroupCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.TaxGroups)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_TaxGroups_Companies");

                entity.HasOne(d => d.TaxType)
                    .WithMany(p => p.TaxGroups)
                    .HasForeignKey(d => d.TaxTypeId)
                    .HasConstraintName("FK_TaxGroups_TaxTypes");
            });

            modelBuilder.Entity<TaxGroupTaxType>(entity =>
            {
                entity.HasOne(d => d.TaxGroup)
                    .WithMany(p => p.TaxGroupTaxTypes)
                    .HasForeignKey(d => d.TaxGroupId)
                    .HasConstraintName("FK_TaxGroupTaxTypes_TaxGroups");

                entity.HasOne(d => d.TaxType)
                    .WithMany(p => p.TaxGroupTaxTypes)
                    .HasForeignKey(d => d.TaxTypeId)
                    .HasConstraintName("FK_TaxGroupTaxTypes_TaxTypes");
            });

            modelBuilder.Entity<TaxType>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.TaxTypeCode })
                    .HasName("IX_TaxTypes_Code")
                    .IsUnique();

                entity.Property(e => e.TaxTypeCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.TaxTypes)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_TaxTypes_Companies");
            });

            modelBuilder.Entity<Token>(entity =>
            {
                entity.Property(e => e.TokenId).HasDefaultValueSql("(newid())");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.Tokens)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_Tokens_Users");
            });

            modelBuilder.Entity<TouchScreenHistory>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.PalletCode).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.TouchScreenHistories)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_TouchScreenHistory_Companies");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.TouchScreenHistories)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_TouchScreenHistory_Machines");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.TouchScreenHistories)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_TouchScreenHistory_Products");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.TouchScreenHistories)
                    .HasForeignKey(d => d.WarehouseId)
                    .HasConstraintName("FK_TouchScreenHistory_Warehouses");
            });

            modelBuilder.Entity<TransactionDocument>(entity =>
            {
                entity.Property(e => e.Remarks).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.TransactionDocuments)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_TransactionDocuments_Companies");
            });

            modelBuilder.Entity<TxnLineHistory>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.Quantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.TxnLineHistories)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_TxnLineHistory_Companies");

                entity.HasOne(d => d.DstWarehouse)
                    .WithMany(p => p.TxnLineHistoryDstWarehouses)
                    .HasForeignKey(d => d.DstWarehouseId)
                    .HasConstraintName("FK_TxnLineHistory_DstWarehouse");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.TxnLineHistories)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_TxnLineHistory_Products");

                entity.HasOne(d => d.SrcWarehouse)
                    .WithMany(p => p.TxnLineHistorySrcWarehouses)
                    .HasForeignKey(d => d.SrcWarehouseId)
                    .HasConstraintName("FK_TxnLineHistory_SrcWarehouse");

                entity.HasOne(d => d.UnitOfMeasure)
                    .WithMany(p => p.TxnLineHistories)
                    .HasForeignKey(d => d.UnitOfMeasureId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_TxnLineHistory_UnitOfMeasures");
            });

            modelBuilder.Entity<TxnWorkFlow>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.TxnWorkFlows)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_TxnWorkFlows_Companies");

                entity.HasOne(d => d.WorkFlow)
                    .WithMany(p => p.TxnWorkFlows)
                    .HasForeignKey(d => d.WorkFlowId)
                    .HasConstraintName("FK_TxnWorkFlows_WorkFlows");
            });

            modelBuilder.Entity<TxnWorkFlowLine>(entity =>
            {
                entity.HasOne(d => d.Designation)
                    .WithMany(p => p.TxnWorkFlowLines)
                    .HasForeignKey(d => d.DesignationId)
                    .HasConstraintName("FK_TxnWorkFlowLines_Designations");

                entity.HasOne(d => d.TxnWorkFlow)
                    .WithMany(p => p.TxnWorkFlowLines)
                    .HasForeignKey(d => d.TxnWorkFlowId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_TxnWorkFlowLines_TxnWorkFlows");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.TxnWorkFlowLines)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_TxnWorkFlowLines_Users");

                entity.HasOne(d => d.WorkFlowLine)
                    .WithMany(p => p.TxnWorkFlowLines)
                    .HasForeignKey(d => d.WorkFlowLineId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_TxnWorkFlowLines_WorkFlowLines");
            });

            modelBuilder.Entity<TyreSpecification>(entity =>
            {
                entity.Property(e => e.TyreSpecificationCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.TyreSpecifications)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_TyreSpecifications_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.TyreSpecifications)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_TyreSpecifications_Products");
            });

            modelBuilder.Entity<UnitOfMeasure>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.UnitOfMeasures)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_UnitOfMeasures_Companies");
            });

            modelBuilder.Entity<User>(entity =>
            {
                entity.Property(e => e.UserCode).IsUnicode(false);

                entity.HasOne(d => d.Title)
                    .WithMany(p => p.Users)
                    .HasForeignKey(d => d.TitleId)
                    .HasConstraintName("FK_Users_SupportData");
            });

            modelBuilder.Entity<UserCompany>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.UserCompanies)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserCompanies_Companies");

                entity.HasOne(d => d.Designation)
                    .WithMany(p => p.UserCompanies)
                    .HasForeignKey(d => d.DesignationId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserCompanies_Designations");

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.UserCompanies)
                    .HasForeignKey(d => d.EmployeeId)
                    .HasConstraintName("FK_UserCompanies_Employees");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserCompanies)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserCompanies_Users");
            });

            modelBuilder.Entity<UserDepartment>(entity =>
            {
                entity.HasOne(d => d.Department)
                    .WithMany(p => p.UserDepartments)
                    .HasForeignKey(d => d.DepartmentId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserDepartments_Departments");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserDepartments)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserDepartments_Users");
            });

            modelBuilder.Entity<UserDivision>(entity =>
            {
                entity.HasOne(d => d.Division)
                    .WithMany(p => p.UserDivisions)
                    .HasForeignKey(d => d.DivisionId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserDivisions_Divisions");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserDivisions)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserDivisions_Users");
            });

            modelBuilder.Entity<UserGroup>(entity =>
            {
                entity.HasKey(e => new { e.UserId, e.GroupId });

                entity.HasOne(d => d.Group)
                    .WithMany(p => p.UserGroups)
                    .HasForeignKey(d => d.GroupId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserGroups_Groups");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserGroups)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserGroups_Users");
            });

            modelBuilder.Entity<UserRole>(entity =>
            {
                entity.HasKey(e => new { e.RoleId, e.UserId });

                entity.HasOne(d => d.Role)
                    .WithMany(p => p.UserRoles)
                    .HasForeignKey(d => d.RoleId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserRoles_Roles");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserRoles)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserRoles_Users");
            });

            modelBuilder.Entity<UserSupervisor>(entity =>
            {
                entity.HasOne(d => d.Company)
                    .WithMany(p => p.UserSupervisors)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserSupervisors_Companies");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.UserSupervisors)
                    .HasForeignKey(d => d.UserId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_UserSupervisors_Users");
            });

            modelBuilder.Entity<Warehous>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.WarehouseCode })
                    .HasName("IX_Warehouses_Code")
                    .IsUnique();

                entity.Property(e => e.IsAutoGenerateLotNumbers).HasComment("Based on this at GRN point lots will be allocated automatically");

                entity.Property(e => e.ShippingAddress).IsUnicode(false);

                entity.Property(e => e.TypeEnum).HasComment("Physical Store – Actual Warehouse, Virtual Store, WIP Store, Quarantine Store");

                entity.Property(e => e.WarehouseCode).IsUnicode(false);

                entity.Property(e => e.WarehouseName).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.Warehous)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_Warehouses_Companies");
            });

            modelBuilder.Entity<WarehousePallet>(entity =>
            {
                entity.HasIndex(e => new { e.CreationDate, e.SequenceNumber, e.CompanyId })
                    .HasName("IX_WarehousePallets_Company_Date_SequenceNumber")
                    .IsUnique();

                entity.Property(e => e.GeneratedLocation).IsUnicode(false);

                entity.Property(e => e.WarehousePalletCode).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.WarehousePallets)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_WarehousePallets_Companies");
            });

            modelBuilder.Entity<WarehouseProduct>(entity =>
            {
                entity.HasIndex(e => new { e.CompanyId, e.SerialNumber })
                    .HasName("IX_WarehouseProducts_Company_SerialNumber")
                    .IsUnique()
                    .HasFilter("([SerialNumber] IS NOT NULL AND [SerialNumber]<>'')");

                entity.HasIndex(e => new { e.WarehouseId, e.ProductId, e.BatchNumber, e.SerialNumber, e.UnitCost, e.RefDocNumber, e.ProductExpiryDate, e.WarrantyDate })
                    .HasName("IX_WarehouseProducts")
                    .IsUnique();

                entity.Property(e => e.AllocatedQuantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.OnHandQuantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.PalletCode).IsUnicode(false);

                entity.Property(e => e.PhysicalQuantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.RefDocNumber).IsUnicode(false);

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.WarehouseProducts)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_WarehouseProducts_Companies");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.WarehouseProducts)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WarehouseProducts_Products");

                entity.HasOne(d => d.UnitOfMeasure)
                    .WithMany(p => p.WarehouseProducts)
                    .HasForeignKey(d => d.UnitOfMeasureId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WarehouseProducts_UnitOfMeasures");

                entity.HasOne(d => d.Warehouse)
                    .WithMany(p => p.WarehouseProducts)
                    .HasForeignKey(d => d.WarehouseId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WarehouseProducts_Warehouses");
            });

            modelBuilder.Entity<WarehouseStockIssue>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.Quantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.WarehouseStockIssues)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_WarehouseStockIssues_Companies");

                entity.HasOne(d => d.DstWarehouse)
                    .WithMany(p => p.WarehouseStockIssueDstWarehouses)
                    .HasForeignKey(d => d.DstWarehouseId)
                    .HasConstraintName("FK_WarehouseStockIssues_DstWarehouse");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.WarehouseStockIssues)
                    .HasForeignKey(d => d.ProductId)
                    .HasConstraintName("FK_WarehouseStockIssues_Products");

                entity.HasOne(d => d.SrcWarehouse)
                    .WithMany(p => p.WarehouseStockIssueSrcWarehouses)
                    .HasForeignKey(d => d.SrcWarehouseId)
                    .HasConstraintName("FK_WarehouseStockIssues_SrcWarehouse");

                entity.HasOne(d => d.UnitOfMeasure)
                    .WithMany(p => p.WarehouseStockIssues)
                    .HasForeignKey(d => d.UnitOfMeasureId)
                    .HasConstraintName("FK_WarehouseStockIssues_UnitOfMeasures");
            });

            modelBuilder.Entity<WarehouseStockQualityControl>(entity =>
            {
                entity.Property(e => e.BatchNumber).IsUnicode(false);

                entity.Property(e => e.DocNumber).IsUnicode(false);

                entity.Property(e => e.LotNumber).IsUnicode(false);

                entity.Property(e => e.Quantity).HasDefaultValueSql("((0))");

                entity.Property(e => e.SerialNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.WarehouseStockQualityControls)
                    .HasForeignKey(d => d.CompanyId)
                    .HasConstraintName("FK_WarehouseStockQualityControls_Companies");

                entity.HasOne(d => d.DstWarehouse)
                    .WithMany(p => p.WarehouseStockQualityControlDstWarehouses)
                    .HasForeignKey(d => d.DstWarehouseId)
                    .HasConstraintName("FK_WarehouseStockQualityControls_DstWarehouse");

                entity.HasOne(d => d.Product)
                    .WithMany(p => p.WarehouseStockQualityControls)
                    .HasForeignKey(d => d.ProductId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WarehouseStockQualityControls_Products");

                entity.HasOne(d => d.SrcWarehouse)
                    .WithMany(p => p.WarehouseStockQualityControlSrcWarehouses)
                    .HasForeignKey(d => d.SrcWarehouseId)
                    .HasConstraintName("FK_WarehouseStockQualityControls_SrcWarehouse");

                entity.HasOne(d => d.UnitOfMeasure)
                    .WithMany(p => p.WarehouseStockQualityControls)
                    .HasForeignKey(d => d.UnitOfMeasureId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WarehouseStockQualityControls_UnitOfMeasures");
            });

            modelBuilder.Entity<WorkFlow>(entity =>
            {
                entity.Property(e => e.WorkFlowNumber).IsUnicode(false);

                entity.HasOne(d => d.Company)
                    .WithMany(p => p.WorkFlows)
                    .HasForeignKey(d => d.CompanyId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WorkFlows_Companies");

                entity.HasOne(d => d.Designation)
                    .WithMany(p => p.WorkFlows)
                    .HasForeignKey(d => d.DesignationId)
                    .HasConstraintName("FK_WorkFlows_Designations");
            });

            modelBuilder.Entity<WorkFlowLine>(entity =>
            {
                entity.HasOne(d => d.Designation)
                    .WithMany(p => p.WorkFlowLines)
                    .HasForeignKey(d => d.DesignationId)
                    .HasConstraintName("FK_WorkFlowLines_Designations");

                entity.HasOne(d => d.User)
                    .WithMany(p => p.WorkFlowLines)
                    .HasForeignKey(d => d.UserId)
                    .HasConstraintName("FK_WorkFlowLines_Users");

                entity.HasOne(d => d.WorkFlow)
                    .WithMany(p => p.WorkFlowLines)
                    .HasForeignKey(d => d.WorkFlowId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WorkFlowLines_WorkFlows");
            });

            modelBuilder.Entity<WorkstationCadre>(entity =>
            {
                entity.Property(e => e.WorkStationCadreCode).IsUnicode(false);

                entity.HasOne(d => d.Employee)
                    .WithMany(p => p.WorkstationCadres)
                    .HasForeignKey(d => d.EmployeeId)
                    .OnDelete(DeleteBehavior.ClientSetNull)
                    .HasConstraintName("FK_WorkstationCadres_Employees");

                entity.HasOne(d => d.Machine)
                    .WithMany(p => p.WorkstationCadres)
                    .HasForeignKey(d => d.MachineId)
                    .HasConstraintName("FK_WorkstationCadres_Machines");

                entity.HasOne(d => d.PressLine)
                    .WithMany(p => p.WorkstationCadres)
                    .HasForeignKey(d => d.PressLineId)
                    .HasConstraintName("FK_WorkstationCadres_PressLines");
            });

            OnModelCreatingPartial(modelBuilder);
        }

        partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
    }
}
