﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InboundReceiptLines", Schema = "fin")]
    public partial class InboundReceiptLine
    {
        public InboundReceiptLine()
        {
            InboundReceiptCostAllocations = new HashSet<InboundReceiptCostAllocation>();
        }

        [Key]
        public int InboundReceiptLineId { get; set; }
        public int? InboundReceiptId { get; set; }
        public int? PaymentAdviceId { get; set; }
        public int? GeneralLedgerLineId { get; set; }
        [Column(TypeName = "money")]
        public decimal? BalanceAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? AmountInBaseCurrency { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocumentAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? SettlementValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? SettledAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? Discount { get; set; }
        [Column(TypeName = "money")]
        public decimal? CrossPaidAmount { get; set; }
        [Column(TypeName = "date")]
        public DateTime? DueDate { get; set; }
        public bool? IsOldestOutstanding { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(GeneralLedgerLineId))]
        [InverseProperty("InboundReceiptLines")]
        public virtual GeneralLedgerLine GeneralLedgerLine { get; set; }
        [ForeignKey(nameof(InboundReceiptId))]
        [InverseProperty("InboundReceiptLines")]
        public virtual InboundReceipt InboundReceipt { get; set; }
        [ForeignKey(nameof(PaymentAdviceId))]
        [InverseProperty("InboundReceiptLines")]
        public virtual PaymentAdvice PaymentAdvice { get; set; }
        [InverseProperty(nameof(InboundReceiptCostAllocation.InboundReceiptLine))]
        public virtual ICollection<InboundReceiptCostAllocation> InboundReceiptCostAllocations { get; set; }
    }
}
