﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductUOMs", Schema = "inv")]
    public partial class ProductUom
    {
        [Key]
        public int ProductId { get; set; }
        [Key]
        public int UnitOfMeasureId { get; set; }
        public int? Conversion { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
    }
}
