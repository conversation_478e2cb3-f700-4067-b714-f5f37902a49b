﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]  
    public class MachineProductComponentController : ControllerBase
    {
        private readonly IMachineProductComponentService _machineProductComponentService;  

        public MachineProductComponentController(IMachineProductComponentService machineProductComponentService)  
        {
            _machineProductComponentService = machineProductComponentService;
        }

        [HttpGet]
        [Route("{machineId}")]
        public async Task<IActionResult> Get(int machineId)
        {
            return Ok(await _machineProductComponentService.GetAsync(machineId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("GetWithStock/machine/{machineId}")]
        public async Task<IActionResult> GetWithStock(int machineId)
        {
            return Ok(await _machineProductComponentService.GetWithStockAsync(machineId));
        }
    }
}