﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class MaterialRequisitionPlansController : ControllerBase
    {
        private readonly IMaterialRequisitionPlanService _materialRequisitionPlanService;

        public MaterialRequisitionPlansController(IMaterialRequisitionPlanService materialRequisitionPlanService)
        {
            _materialRequisitionPlanService = materialRequisitionPlanService;
        }

        [HttpGet]
        [Route("startDate/{startDate}/endDate/{endDate}/company/{companyId}")]
        public async Task<IActionResult> Get(DateTime startDate, DateTime endDate, int companyId)
        {
            return Ok(await _materialRequisitionPlanService.GetAllAsync(startDate, endDate, companyId));
        }
    }
}