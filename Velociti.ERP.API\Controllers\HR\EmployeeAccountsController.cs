﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Services.HR;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class EmployeeAccountsController : ControllerBase      
    {
        private readonly IEmployeeAccountService _employeeAccountService;    

        public EmployeeAccountsController(IEmployeeAccountService employeeAccountService)       
        {
            _employeeAccountService = employeeAccountService;
        }

        [HttpGet]
        [Route("{employeeId}")]
        public async Task<IActionResult> Get(int employeeId)  
        {
            return Ok(await _employeeAccountService.GetByEmployeeIdAsync(employeeId));    
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]EmployeeAccount employeeAccount)    
        {
            if (employeeAccount == null)
                return BadRequest();

            await _employeeAccountService.SaveAsync(employeeAccount);

            return Ok();
        }

        [HttpDelete]
        [Route("{employeeId}/Account/{accountId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int employeeId, int accountId, int userId)
        {
            await _employeeAccountService.ToggleActivationAsync(employeeId, accountId, userId);

            return Ok();
        }
    }
}