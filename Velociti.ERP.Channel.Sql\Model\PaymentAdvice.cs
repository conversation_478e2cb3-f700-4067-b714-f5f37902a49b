﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PaymentAdvices", Schema = "fin")]
    public partial class PaymentAdvice
    {
        public PaymentAdvice()
        {
            InboundReceiptLines = new HashSet<InboundReceiptLine>();
            OutboundPaymentLines = new HashSet<OutboundPaymentLine>();
            PaymentAdviceLines = new HashSet<PaymentAdviceLine>();
        }

        [Key]
        public int PaymentAdviceId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [StringLength(50)]
        public string AdviceNumber { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? SubTypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? SupplierId { get; set; }
        [StringLength(50)]
        public string SupplierReference { get; set; }
        [StringLength(50)]
        public string CustomerReference { get; set; }
        public int? CustomerId { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        public int? EmployeeId { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [StringLength(50)]
        public string InvoiceNumber { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        public int? PurchaseOrderId { get; set; }
        public int? SalesOrderId { get; set; }
        public int? ShipmentCostId { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        public int? TaxGroupId { get; set; }
        public int? AccountId { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }
        [Column(TypeName = "money")]
        public decimal? BalanceAmount { get; set; }
        public int? DepartmentId { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
        public bool? IsAdvanceRefund { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("PaymentAdvices")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PaymentAdvices")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("PaymentAdvices")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("PaymentAdvices")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("PaymentAdvices")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("PaymentAdvices")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("PaymentAdvices")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
        [ForeignKey(nameof(PurchaseOrderId))]
        [InverseProperty("PaymentAdvices")]
        public virtual PurchaseOrder PurchaseOrder { get; set; }
        [ForeignKey(nameof(SalesOrderId))]
        [InverseProperty("PaymentAdvices")]
        public virtual SalesOrder SalesOrder { get; set; }
        [ForeignKey(nameof(ShipmentCostId))]
        [InverseProperty("PaymentAdvices")]
        public virtual ShipmentCost ShipmentCost { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("PaymentAdvices")]
        public virtual Supplier Supplier { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("PaymentAdvices")]
        public virtual TaxGroup TaxGroup { get; set; }
        [InverseProperty(nameof(InboundReceiptLine.PaymentAdvice))]
        public virtual ICollection<InboundReceiptLine> InboundReceiptLines { get; set; }
        [InverseProperty(nameof(OutboundPaymentLine.PaymentAdvice))]
        public virtual ICollection<OutboundPaymentLine> OutboundPaymentLines { get; set; }
        [InverseProperty(nameof(PaymentAdviceLine.PaymentAdvice))]
        public virtual ICollection<PaymentAdviceLine> PaymentAdviceLines { get; set; }
    }
}
