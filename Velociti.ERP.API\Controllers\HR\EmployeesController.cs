﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.HR;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.HR;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class EmployeesController : ControllerBase
    {
        private readonly IEmployeeService _employeeService;

        public EmployeesController(IEmployeeService employeeService)
        {
            _employeeService = employeeService;
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _employeeService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/Designation/{designationEnum}/company/{companyId}")]
        public async Task<IActionResult> GetShortListByDesignation(int companyId, Domain.Entities.Administration.Designation.Type designationEnum)
        {
            var list = await _employeeService.GetShortListByDesignationAsync(designationEnum, companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("AllEmployees/Department/{departmentId}/company/{companyId}")]
        public async Task<IActionResult> GetAllEmployeesByDepartment(int departmentId, int companyId)
        {
            var list = await _employeeService.GetAllEmployeesByDepartment(departmentId, companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("AllEmployees/Designation/{designationEnum}/company/{companyId}")]
        public async Task<IActionResult> GetAllEmployeesByDesignation(int companyId, Domain.Entities.Administration.Designation.Type designationEnum)
        {
            var list = await _employeeService.GetAllEmployeesByDesignation(designationEnum, companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("User/{userId}/Designation/{designationId}")]
        public async Task<IActionResult> GetForUserAssign(int userId, int designationId)
        {
            var list = await _employeeService.GetForUserAssignAsync(userId, designationId);

            return Ok(list);
        }

        [HttpGet]
        [Route("{employeeId}/departments/{companyId}")]
        public async Task<IActionResult> GetAssignedDepartmentsShortList(int companyId, int employeeId)
        {
            var list = await _employeeService.GetAssignedDepartmentShortListAsync(companyId, employeeId);

            return Ok(list);
        }

        [HttpGet]
        [Route("{employeeId}/defaultdepartment/{companyId}")]
        public async Task<IActionResult> GetDefaultDepartment(int companyId, int employeeId)
        {
            var record = await _employeeService.GetDefaultDepartmentAsync(companyId, employeeId);

            return Ok(record);
        }

        [HttpGet]
        [Route("company/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<Employee>>>> Get(int companyId)
        {
            return Ok(await _employeeService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("{employeeId}")]
        public async Task<ActionResult<Response<Employee>>> FindById(int employeeId)
        {
            return Ok(await _employeeService.FindByIdAsync(employeeId));
        }

        [HttpGet]
        [Route("GetDefaultEmployee/{companyId}")]
        public async Task<ActionResult<Response<Employee>>> GetDefaultEmployee(int companyId)
        {
            return Ok(await _employeeService.GetDefaultEmployeeAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Employee employee)
        {
            if (employee == null)
                return BadRequest();

            await _employeeService.SaveAsync(employee);

            return Ok();
        }

        [HttpDelete]
        [Route("{employeeId}/User/{userId}/Company/{companyId}")]
        public async Task<IActionResult> ToggleActivation(int employeeId, int userId, int companyId)
        {
            await _employeeService.ToggleActivationAsync(employeeId, userId, companyId);

            return Ok();
        }
    }
}