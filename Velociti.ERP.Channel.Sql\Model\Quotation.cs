﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Quotations", Schema = "prc")]
    public partial class Quotation
    {
        public Quotation()
        {
            QuotationLines = new HashSet<QuotationLine>();
        }

        [Key]
        public int QuotationId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        public int? PurchaseRequisitionNoteId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? VersionNo { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        public int? SupplierId { get; set; }
        [StringLength(255)]
        public string SupplierAddress { get; set; }
        public int? PaymentTermId { get; set; }
        [StringLength(50)]
        public string PortOfShipment { get; set; }
        public int? ShippingTermId { get; set; }
        public int? AccountId { get; set; }
        public int? ParentId { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? LineDiscTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DocDiscValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValueTotal { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? FinalValue { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? TxnWorkFlowId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("Quotations")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Quotations")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("Quotations")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("Quotations")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(PurchaseRequisitionNoteId))]
        [InverseProperty("Quotations")]
        public virtual PurchaseRequisitionNote PurchaseRequisitionNote { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("Quotations")]
        public virtual Supplier Supplier { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("Quotations")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [InverseProperty(nameof(QuotationLine.Quotation))]
        public virtual ICollection<QuotationLine> QuotationLines { get; set; }
    }
}
