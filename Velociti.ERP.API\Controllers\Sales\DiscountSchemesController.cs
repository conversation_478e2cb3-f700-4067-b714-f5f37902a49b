﻿using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class DiscountSchemesController : ControllerBase
    {
        private readonly IDiscountSchemesService _discountSchemesService;

        public DiscountSchemesController(IDiscountSchemesService discountSchemesService)
        {
            _discountSchemesService = discountSchemesService;
        }

        [HttpGet]
        [Route("Single/{discountSchemeId}")]
        public async Task<IActionResult> FindById(int discountSchemeId)
        {
            return Ok(await _discountSchemesService.FindByIdAsync(discountSchemeId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _discountSchemesService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("AllActive")]
        public async Task<IActionResult> GetAllActive(int companyId, int userId)
        {
            return Ok(await _discountSchemesService.GetAllActiveAsync(companyId, userId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]DiscountScheme discountScheme)
        {
            switch (discountScheme.Action)
            {
                case "save": await _discountSchemesService.SaveAsync(discountScheme); break;
            }

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]DiscountScheme discountScheme)
        {
            switch (discountScheme.Action)
            {
                case "submit": await _discountSchemesService.SubmitAsync(discountScheme.DiscountSchemeId, discountScheme.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}