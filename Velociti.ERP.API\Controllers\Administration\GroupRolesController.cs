﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class GroupRolesController : ControllerBase
    {
        private readonly IGroupRoleService _groupRoleService;

        public GroupRolesController(IGroupRoleService groupRoleService)
        {
            _groupRoleService = groupRoleService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            return Ok(await _groupRoleService.GetAllAsync());
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]List<GroupRole> groupRoles)
        {
            if (groupRoles == null)
                return BadRequest();

            await _groupRoleService.SaveAsync(groupRoles);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Delete([FromBody]GroupRole groupRole)
        {
            await _groupRoleService.DeleteAsync(groupRole);

            return Ok();
        }
    }
}