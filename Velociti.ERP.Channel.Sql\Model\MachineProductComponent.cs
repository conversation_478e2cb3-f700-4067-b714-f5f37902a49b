﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MachineProductComponents", Schema = "man")]
    public partial class MachineProductComponent
    {
        [Key]
        public int MachineProductComponentId { get; set; }
        public int? ProductId { get; set; }
        public int? DefaultUnitOfMeasureId { get; set; }
        public int? MachineId { get; set; }
        public int? ComponentId { get; set; }
        public int? Quantity { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DefaultUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.MachineProductComponents))]
        public virtual UnitOfMeasure DefaultUnitOfMeasure { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("MachineProductComponents")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("MachineProductComponents")]
        public virtual Product Product { get; set; }
    }
}
