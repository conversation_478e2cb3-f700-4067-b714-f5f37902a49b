﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Services.HR;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class EmployeeIncrementsController : ControllerBase        
    {
        private readonly IEmployeeIncrementService _employeeIncrementService;    

        public EmployeeIncrementsController(IEmployeeIncrementService employeeIncrementService)       
        {
            _employeeIncrementService = employeeIncrementService;
        }

        [HttpGet]
        [Route("{employeeId}")]
        public async Task<IActionResult> Get(int employeeId)  
        {
            return Ok(await _employeeIncrementService.GetByEmployeeIdAsync(employeeId));    
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]EmployeeIncrement employeeIncrement)    
        {
            if (employeeIncrement == null)
                return BadRequest();

            await _employeeIncrementService.SaveAsync(employeeIncrement);

            return Ok();
        }

        [HttpDelete]
        [Route("{employeeId}/Increment/{employeeIncrementId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int employeeId, int employeeIncrementId, int userId)
        {
            await _employeeIncrementService.ToggleActivationAsync(employeeId, employeeIncrementId, userId);

            return Ok();
        }
    }
}