﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ScrapPenalty", Schema = "hr")]
    public partial class ScrapPenalty
    {
        [Key]
        public int ScrapPenaltyId { get; set; }
        public int? LowerLimit { get; set; }
        public int? UpperLimit { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? DeductionRate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
    }
}
