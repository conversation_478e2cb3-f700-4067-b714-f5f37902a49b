﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class DesignationsController : ControllerBase
    {
        private readonly IDesignationService _designationService;

        public DesignationsController(IDesignationService designationService)
        {
            _designationService = designationService;
        }

        [HttpGet]
        [Route("Hierarchy/{companyId}/")]
        public async Task<ActionResult<Response<IEnumerable<HierarchyTemplate>>>> GetHierarchy(int companyId)
        {
            return Ok(await _designationService.GetHierarchyAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetShortList(int companyId)
        {
            var list = await _designationService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Designation designation)
        {
            if (designation == null)
                return BadRequest();

            await _designationService.SaveAsync(designation);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivation(int id)
        {
            await _designationService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}