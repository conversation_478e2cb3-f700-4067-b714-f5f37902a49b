﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("CustomerPayments", Schema = "sales")]
    public partial class CustomerPayment
    {
        public CustomerPayment()
        {
            CustomerPaymentLines = new HashSet<CustomerPaymentLine>();
            CustomerPaymentVouchers = new HashSet<CustomerPaymentVoucher>();
        }

        [Key]
        public int CustomerPaymentId { get; set; }
        public int CompanyId { get; set; }
        public int? CustomerId { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        [StringLength(500)]
        public string BillingAddress { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? ExchangeRate { get; set; }
        public int? CashAccountId { get; set; }
        public int? BankAccountId { get; set; }
        public int? TaxGroupId { get; set; }
        public int? DepartmentId { get; set; }
        [Column(TypeName = "money")]
        public decimal? CashAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? ChequeAmount { get; set; }
        [StringLength(30)]
        public string ChequeNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ChequeDate { get; set; }
        [StringLength(400)]
        public string ChequeDescription { get; set; }
        [Column(TypeName = "money")]
        public decimal? CreditCardAmount { get; set; }
        [StringLength(30)]
        public string CreditCardNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreditCardExpiryDate { get; set; }
        public byte? CreditCardTypeEnum { get; set; }
        [Column(TypeName = "money")]
        public decimal? VoucherAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? TotalAmount { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
        public int? PrintCount { get; set; }

        [ForeignKey(nameof(BankAccountId))]
        [InverseProperty(nameof(Account.CustomerPaymentBankAccounts))]
        public virtual Account BankAccount { get; set; }
        [ForeignKey(nameof(CashAccountId))]
        [InverseProperty(nameof(Account.CustomerPaymentCashAccounts))]
        public virtual Account CashAccount { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("CustomerPayments")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("CustomerPayments")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("CustomerPayments")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("CustomerPayments")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("CustomerPayments")]
        public virtual TaxGroup TaxGroup { get; set; }
        [InverseProperty(nameof(CustomerPaymentLine.CustomerPayment))]
        public virtual ICollection<CustomerPaymentLine> CustomerPaymentLines { get; set; }
        [InverseProperty(nameof(CustomerPaymentVoucher.CustomerPayment))]
        public virtual ICollection<CustomerPaymentVoucher> CustomerPaymentVouchers { get; set; }
    }
}
