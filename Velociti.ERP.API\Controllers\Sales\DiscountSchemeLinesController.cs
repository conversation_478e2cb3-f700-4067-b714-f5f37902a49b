﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class DiscountSchemeLinesController : ControllerBase
    {
        private readonly IDiscountSchemeLinesService _discountSchemeLinesService;

        public DiscountSchemeLinesController(IDiscountSchemeLinesService discountSchemeLinesService)
        {
            _discountSchemeLinesService = discountSchemeLinesService;
        }

        [HttpGet]
        [Route("{discountSchemeId}")]
        public async Task<IActionResult> Get(int discountSchemeId)
        {
            return Ok(await _discountSchemeLinesService.GetAsync(discountSchemeId));
        }
    }
}