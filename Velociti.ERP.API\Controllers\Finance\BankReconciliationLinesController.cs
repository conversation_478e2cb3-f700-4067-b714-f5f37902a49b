﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class BankReconciliationLinesController : ControllerBase
    {
        private readonly IBankReconciliationLineService _bankReconciliationLineService;
        
        public BankReconciliationLinesController(IBankReconciliationLineService bankReconciliationLineService)
        {
            _bankReconciliationLineService = bankReconciliationLineService;
        }

        [HttpGet]
        [Route("{bankReconciliationId}")]
        public async Task<IActionResult> GetByHeaderId(int bankReconciliationId)
        {
            return Ok(await _bankReconciliationLineService.GetByHeaderIdAsync(bankReconciliationId));
        }
    }
}