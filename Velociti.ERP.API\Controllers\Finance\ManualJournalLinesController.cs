﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class ManualJournalLinesController : ControllerBase
    {
        private readonly IManualJournalLineService _manualJournalLineService;

        public ManualJournalLinesController(IManualJournalLineService manualJournalLineService)
        {
            _manualJournalLineService = manualJournalLineService;
        }

        [HttpGet]
        [Route("{manualJournalId}")]
        public async Task<IActionResult> GetByHeader(int manualJournalId)
        {
            return Ok(await _manualJournalLineService.GetByHeaderAsync(manualJournalId));
        }
    }
}