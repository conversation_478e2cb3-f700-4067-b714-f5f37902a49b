﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ConsignmentPurchaseOrderLines", Schema = "inv")]
    public partial class ConsignmentPurchaseOrderLine
    {
        [Key]
        public int ConsignmentPurchaseOrderLineId { get; set; }
        public int? ConsignmentStockTransferId { get; set; }
        public int ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int UnitQty { get; set; }
        [Column(TypeName = "money")]
        public decimal UnitPrice { get; set; }
        [Column(TypeName = "money")]
        public decimal GrossValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ConsignmentPurchaseOrderLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ConsignmentStockTransferId))]
        [InverseProperty("ConsignmentPurchaseOrderLines")]
        public virtual ConsignmentStockTransfer ConsignmentStockTransfer { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ConsignmentPurchaseOrderLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ConsignmentPurchaseOrderLines")]
        public virtual Product Product { get; set; }
    }
}
