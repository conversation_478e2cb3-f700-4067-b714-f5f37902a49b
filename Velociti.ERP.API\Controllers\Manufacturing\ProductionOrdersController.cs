﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [ApiController]
    public class ProductionOrdersController : ControllerBase
    {
        private readonly IProductionOrderService _productionOrderService;

        public ProductionOrdersController(IProductionOrderService productionOrderService)
        {
            _productionOrderService = productionOrderService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(byte docTypeEnum, int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _productionOrderService.GetAllAsync(docTypeEnum, startDate, endDate, companyId));
        }

        [HttpGet]
        [Route("GetForSemiFinishedGoods")]
        public async Task<IActionResult> GetForSemiFinishedGoods(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _productionOrderService.GetForSemiFinishedGoodsAsync(startDate, endDate, companyId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("BarcodePrint/documentType/{documentType}/company/{companyId}")]
        public async Task<IActionResult> GetForBarcodePrint(byte documentType, int companyId)
        {
            return Ok(await _productionOrderService.GetForBarcodePrintAsync((Domain.Entities.Administration.Module.DocumentType)documentType, companyId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("GetSemiFinishedGoodsForBarcodePrint/company/{companyId}")]
        public async Task<IActionResult> GetSemiFinishedGoodsForBarcodePrint(int companyId)
        {
            return Ok(await _productionOrderService.GetSemiFinishedGoodsForBarcodePrintAsync(companyId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("OtherMachineTouchScreenI/machine/{machineId}/documentType/{documentTypeEnum}")]
        public async Task<IActionResult> GetForOtherMachineTouchScreenIFunction(int machineId, byte documentTypeEnum)
        {
            return Ok(await _productionOrderService.GetForOtherMachineTouchScreenIFunctionAsync((Domain.Entities.Administration.Module.DocumentType)documentTypeEnum, machineId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("OtherMachineTouchScreenII/machine/{machineId}")]
        public async Task<IActionResult> GetForOtherMachineTouchScreenIIFunction(int machineId)
        {
            return Ok(await _productionOrderService.GetForOtherMachineTouchScreenIIFunctionAsync(machineId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]ProductionOrder productionOrder)
        {
            try
            {
                await _productionOrderService.SaveSemiFinishedGoodsOrderAsync(productionOrder);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("UpdateFromTC2")]
        public async Task<IActionResult> UpdateFromTC2([FromBody]ProductionOrderLine productionOrderLine)
        {
            try
            {
                switch (productionOrderLine.Action)
                {
                    case "scrap": await _productionOrderService.ScrapProductionOrderAsync(productionOrderLine); break;
                }

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("UpdateFromTC")]
        public async Task<IActionResult> UpdateFromTC([FromBody]ProductionOrder productionOrder)
        {
            try
            {
                switch(productionOrder.Action)
                {
                    case "confirm": await _productionOrderService.ConfirmAsync(productionOrder);break;
                    case "print barcode": await _productionOrderService.UpdateBarcodePrintDateAsync(productionOrder.ProductionOrderId); break;
                }

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("Dispose")]
        public async Task<IActionResult> Dispose([FromBody]ViewForm viewForm)
        {
            try
            {
                await _productionOrderService.DisposeFinishedGoodsAsync(viewForm.IntegerList);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpPut]
        [AllowAnonymous]
        [Route("Scrap")]
        public async Task<IActionResult> Scrap([FromBody]ViewForm viewForm)
        {
            try
            {
                await _productionOrderService.ScrapFinishedGoodsAsync(viewForm.IntegerList);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("SaveProductionStockLine/warehouse/{warehouseId}/barcode/{barcode}")]
        public async Task<IActionResult> SaveProductionStockLine(int warehouseId, string barcode)
        {
            try
            {
                await _productionOrderService.SaveProductionStockLineAsync(warehouseId, barcode);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("DeleteProductionStockLine/warehouseProduct/{warehouseProductId}")]
        public async Task<IActionResult> DeleteProductionStockLine(int warehouseProductId)
        {
            try
            {
                await _productionOrderService.DeleteProductionStockLineAsync(warehouseProductId);

                var jsonObject = new
                {
                    status = "success",
                    statusCode = 200
                };

                return Ok(jsonObject);
            }
            catch (Exception ex)
            {
                var jsonObject = new
                {
                    status = ex.Message,
                    statusCode = 500
                };

                return Ok(jsonObject);
            }
        }

        [HttpDelete]
        [Route("{id}/User/{userId}")]
        public async Task<IActionResult> Delete(int id, int userId)
        {
            await _productionOrderService.DeleteAsync(id, userId);

            return Ok();
        }
    }
}