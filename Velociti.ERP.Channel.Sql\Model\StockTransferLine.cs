﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockTransferLines", Schema = "inv")]
    public partial class StockTransferLine
    {
        public StockTransferLine()
        {
            StockTransferDetails = new HashSet<StockTransferDetail>();
        }

        [Key]
        public int StockTransferLineId { get; set; }
        public int? StockTransferId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int? Quantity { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.StockTransferLines))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("StockTransferLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(StockTransferId))]
        [InverseProperty("StockTransferLines")]
        public virtual StockTransfer StockTransfer { get; set; }
        [InverseProperty(nameof(StockTransferDetail.StockTransferLine))]
        public virtual ICollection<StockTransferDetail> StockTransferDetails { get; set; }
    }
}
