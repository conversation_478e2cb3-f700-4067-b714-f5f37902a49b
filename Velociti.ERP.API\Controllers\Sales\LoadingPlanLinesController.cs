﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class LoadingPlanLinesController : ControllerBase
    {
        private readonly ILoadingPlanLineService _loadingPlanLineService;

        public LoadingPlanLinesController(ILoadingPlanLineService loadingPlanLineService)
        {
            _loadingPlanLineService = loadingPlanLineService;
        }

        [HttpGet]
        [Route("{loadingPlanId}")]
        public async Task<IActionResult> Get(int loadingPlanId)
        {
            return Ok(await _loadingPlanLineService.GetAsync(loadingPlanId));
        }

        [HttpGet]
        [Route("All/{loadingPlanId}")]
        public async Task<IActionResult> GetAll(int loadingPlanId)  
        {
            return Ok(await _loadingPlanLineService.GetAllAsync(loadingPlanId));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]LoadingPlanLine loadingPlanLine)
        {
            return Ok(await _loadingPlanLineService.UpdateAsync(loadingPlanLine));
        }
    }
}