﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    public partial class Barcode
    {
        [Key]
        public int Id { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Date { get; set; }
        public int? SequenceNumber { get; set; }
        [Column("Barcode")]
        [StringLength(50)]
        public string Barcode1 { get; set; }
        public bool? IsPrinted { get; set; }
        public bool? IsSecondGrade { get; set; }
        public int? TempId { get; set; }
    }
}
