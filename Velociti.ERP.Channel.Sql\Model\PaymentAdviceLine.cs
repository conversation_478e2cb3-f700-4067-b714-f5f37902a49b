﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PaymentAdviceLines", Schema = "fin")]
    public partial class PaymentAdviceLine
    {
        public PaymentAdviceLine()
        {
            PaymentAdviceCostAllocations = new HashSet<PaymentAdviceCostAllocation>();
        }

        [Key]
        public int PaymentAdviceLineId { get; set; }
        public int? PaymentAdviceId { get; set; }
        public int? ChartOfAccountId { get; set; }
        public int? ShipmentCostLineId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        public bool? IsCr { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChartOfAccountId))]
        [InverseProperty("PaymentAdviceLines")]
        public virtual ChartOfAccount ChartOfAccount { get; set; }
        [ForeignKey(nameof(PaymentAdviceId))]
        [InverseProperty("PaymentAdviceLines")]
        public virtual PaymentAdvice PaymentAdvice { get; set; }
        [ForeignKey(nameof(ShipmentCostLineId))]
        [InverseProperty("PaymentAdviceLines")]
        public virtual ShipmentCostLine ShipmentCostLine { get; set; }
        [InverseProperty(nameof(PaymentAdviceCostAllocation.PaymentAdviceLine))]
        public virtual ICollection<PaymentAdviceCostAllocation> PaymentAdviceCostAllocations { get; set; }
    }
}
