﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MaterialRequisitionPlanLines", Schema = "man")]
    public partial class MaterialRequisitionPlanLine
    {
        [Key]
        public int MaterialRequisitionPlanLineId { get; set; }
        public int? MaterialRequisitionPlanId { get; set; }
        public int? RefDocLineId { get; set; }
        public int? ProductId { get; set; }
        public int? RequiredQuantity { get; set; }
        public byte? RefDocTypeEnum { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(MaterialRequisitionPlanId))]
        [InverseProperty("MaterialRequisitionPlanLines")]
        public virtual MaterialRequisitionPlan MaterialRequisitionPlan { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("MaterialRequisitionPlanLines")]
        public virtual Product Product { get; set; }
    }
}
