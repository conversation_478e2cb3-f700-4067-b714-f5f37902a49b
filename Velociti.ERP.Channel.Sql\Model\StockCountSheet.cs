﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("StockCountSheets", Schema = "inv")]
    public partial class StockCountSheet
    {
        public StockCountSheet()
        {
            StockCountSheetLines = new HashSet<StockCountSheetLine>();
        }

        [Key]
        public int StockCountSheetId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string StockCountSheetCode { get; set; }
        public short? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime DocDate { get; set; }
        [StringLength(50)]
        public string RefDocCode { get; set; }
        public int CustomerId { get; set; }
        public int? PriceListId { get; set; }
        public int WarehouseId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? TotalAmount { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? TotalDiscountAmount { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? TotalNetAmount { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? CreditCardPayment { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DueAmount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreatedDateTime { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? DepartmentId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("StockCountSheets")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("StockCountSheets")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("StockCountSheets")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(PriceListId))]
        [InverseProperty(nameof(SupportData.StockCountSheets))]
        public virtual SupportData PriceList { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.StockCountSheets))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(StockCountSheetLine.StockCountSheet))]
        public virtual ICollection<StockCountSheetLine> StockCountSheetLines { get; set; }
    }
}
