﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class MachineVisualPlanLinesController : ControllerBase
    {
        private readonly IMachineVisualPlanLineService _productionPlanLineService;

        public MachineVisualPlanLinesController(IMachineVisualPlanLineService productionPlanLineService)
        {
            _productionPlanLineService = productionPlanLineService;
        }

        [HttpGet]
        public async Task<IActionResult> Get(string action, DateTime startDate, DateTime endDate, int companyId)
        {
            return action switch
            {
                "Active" => Ok(await _productionPlanLineService.GetActiveAllAsync(startDate, endDate, companyId)),
                "Headers" => Ok(await _productionPlanLineService.GetHeadersAsync(companyId, startDate, endDate)),

                _ => BadRequest(),
            };
        }

        [HttpGet]
        [Route("OrderLine/{salesOrderLineId}")]
        public async Task<IActionResult> GetByOrderLine(int salesOrderLineId)
        {
            return Ok(await _productionPlanLineService.GetByOrderLineIdAsync(salesOrderLineId));
        }

        [HttpPost]
        public async Task<IActionResult> Save(MachineVisualPlanLine productionPlanLine)
        {
            //this had to be done cause the time returned from scheduler was GMT time
            productionPlanLine.StartDate = DateTime.Parse(productionPlanLine.StartDateString);

            await _productionPlanLineService.SaveAsync(productionPlanLine);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update(MachineVisualPlanLine productionPlanLine)
        {
            productionPlanLine.StartDate = DateTime.Parse(productionPlanLine.StartDateString);

            switch (productionPlanLine.Action)
            {
                case "Process Machine Availability": await _productionPlanLineService.ProcessMachineAvailabilityAsync(productionPlanLine.StartDate.Value, productionPlanLine.ModifiedUserId.Value); break;
                case "Change To Seconday Machine": await _productionPlanLineService.ChangeToSecondaryMachineAsync(productionPlanLine); break;
            }

            return Ok();
        }
    }
}