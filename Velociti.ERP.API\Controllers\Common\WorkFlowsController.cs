﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Common;

namespace Velociti.ERP.API.Controllers.Common
{
    [Route("api/Common/[controller]")]
    [Authorize]
    [ApiController]
    public class WorkFlowsController : ControllerBase
    {
        private readonly IWorkFlowService _workFlowService;

        public WorkFlowsController(IWorkFlowService workFlowService)
        {
            _workFlowService = workFlowService;
        }

        [HttpGet]
        [Route("Single/{workFlowId}")]
        public async Task<IActionResult> GetById(int workFlowId)
        {
            var result = await _workFlowService.FindByIdAsync(workFlowId);

            return Ok(result);
        }

        [HttpGet]
        [Route("{companyId}/user/{userId}")]
        public async Task<IActionResult> GetAll(int companyId, int userId)
        {
            var result = await _workFlowService.GetAllAsync(companyId, userId);

            return Ok(result);
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]WorkFlow workFlow)
        {
            if (workFlow == null)
                return BadRequest();

            await _workFlowService.SaveAsync(workFlow);

            return Ok();
        }

        [HttpDelete]
        [Route("{workFlowId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int workFlowId, int userId)
        {
            await _workFlowService.ToggleActivationAsync(workFlowId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]WorkFlow workFlow)
        {
            switch (workFlow.Action)
            {
                case "submit": await _workFlowService.SubmitAsync(workFlow.WorkFlowId, workFlow.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}