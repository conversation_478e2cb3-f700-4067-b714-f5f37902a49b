﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Authorize]
    [Route("api/Finance/[controller]")]
    [ApiController]
    public class OutboundPaymentCostAllocationsController : ControllerBase
    {
        private readonly IOutboundPaymentCostAllocationService _outboundPaymentCostAllocationService;

        public OutboundPaymentCostAllocationsController(IOutboundPaymentCostAllocationService outboundPaymentCostAllocationService)
        {
            _outboundPaymentCostAllocationService = outboundPaymentCostAllocationService;
        }

        [HttpGet]
        [Route("{outboundPaymentLineId}")]
        public async Task<IActionResult> GetByOutboundPaymentLineIdAsync(int outboundPaymentLineId)
        {
            return Ok(await _outboundPaymentCostAllocationService.GetByOutboundPaymentLineIdAsync(outboundPaymentLineId));
        }
    }
}