﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using Microsoft.EntityFrameworkCore;
using System.Linq;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class PermissionRepository : IPermissionRepository
    {
        private readonly MarangoniERPContext _context;

        public PermissionRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<IEnumerable<Domain.Entities.Administration.Permission>> GetAllAsync(int companyId)
        {
            var result = from p in _context.Permissions
                         join r in _context.Roles on p.RoleId equals r.RoleId
                         join m in _context.Modules on p.ModuleId equals m.ModuleId
                         join re in _context.Resources on p.ResourceId equals re.ResourceId
                         where p.CompanyId == companyId
                         select new Domain.Entities.Administration.Permission
                         {
                             PermissionId = p.PermissionId,
                             CompanyId = p.CompanyId,
                             ModuleId = p.ModuleId,
                             ModuleName = m.ModuleName,
                             ResourceId = p.ResourceId,
                             ResourceName = re.ResourceName,
                             RoleId = p.RoleId,
                             RoleName = r.RoleName,
                             CrudPermission = p.CrudPermission,
                             Insert = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Create & p.CrudPermission) > 0 ? true : false,
                             Update = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Update & p.CrudPermission) > 0 ? true : false,
                             Delete = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Delete & p.CrudPermission) > 0 ? true : false,
                             Retrieve = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Retrieve & p.CrudPermission) > 0 ? true : false,
                             Approve = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Approve & p.CrudPermission) > 0 ? true : false
                         };

            return await result.ToListAsync();

        }

        public async Task<IEnumerable<Domain.Entities.Administration.Permission>> GetAllAsync(int companyId, int roleId)
        {
            var result = from p in _context.Permissions
                         join r in _context.Roles on p.RoleId equals r.RoleId
                         join m in _context.Modules on p.ModuleId equals m.ModuleId
                         join re in _context.Resources on p.ResourceId equals re.ResourceId
                         where p.CompanyId == companyId
                            && p.RoleId == roleId
                         select new Domain.Entities.Administration.Permission
                         {
                             PermissionId = p.PermissionId,
                             CompanyId = p.CompanyId,
                             ModuleId = p.ModuleId,
                             ModuleName = m.ModuleName,
                             ResourceId = p.ResourceId,
                             ResourceName = re.ResourceName,
                             RoleId = p.RoleId,
                             RoleName = r.RoleName,
                             CrudPermission = p.CrudPermission,
                             Insert = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Create & p.CrudPermission) > 0 ? true : false,
                             Update = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Update & p.CrudPermission) > 0 ? true : false,
                             Delete = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Delete & p.CrudPermission) > 0 ? true : false,
                             Retrieve = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Retrieve & p.CrudPermission) > 0 ? true : false,
                             Approve = ((int)Domain.Entities.Administration.Permission.PermissionEnum.Approve & p.CrudPermission) > 0 ? true : false
                         };

            return await result.ToListAsync();

        }

        public async Task<IEnumerable<Domain.Entities.Administration.Permission>> GetPermissionsByUserAsync(int userId)
        {
            var result = from ur in _context.UserRoles
                         join r in _context.Roles on ur.RoleId equals r.RoleId
                         join p in _context.Permissions on r.RoleId equals p.RoleId
                         join m in _context.Modules on p.ModuleId equals m.ModuleId
                         join re in _context.Resources on p.ResourceId equals re.ResourceId
                         where ur.UserId == userId
                         select new Domain.Entities.Administration.Permission
                         {
                             PermissionId = p.PermissionId,
                             ModuleId = p.ModuleId,
                             ModuleName = m.ModuleName,
                             ResourceId = p.ResourceId,
                             ResourceName = re.ResourceName,
                             RoleId = p.RoleId,
                             RoleName = r.RoleName,
                             PermissionName = p.PermissionName,
                             CrudPermission = p.CrudPermission
                         };

            return await result.ToListAsync();
        }

        public async Task SaveAsync(List<Permission> permissions)
        {
            foreach(var permission in permissions)
            {
                var dbPermission = await _context.Permissions.Where(p => p.CompanyId == permission.CompanyId
                                && p.ModuleId == permission.ModuleId && p.ResourceId == permission.ResourceId && p.RoleId == permission.RoleId).SingleOrDefaultAsync();

                if (dbPermission != null)
                {
                    if (permission.CrudPermission == 0)
                        _context.Permissions.Remove(dbPermission);
                    else
                        dbPermission.CrudPermission = permission.CrudPermission;
                }
                else if(permission.CrudPermission > 0)
                {
                    _context.Permissions.Add(permission);
                }
            }
            
            await _context.SaveChangesAsync();
        }

        public async Task DeleteAsync(int id)
        {
            var record = await _context.Permissions.FirstAsync(c => c.PermissionId == id);

            _context.Permissions.Remove(record);
            await _context.SaveChangesAsync();
        }
    }
}
