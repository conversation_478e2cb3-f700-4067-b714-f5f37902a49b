﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Machines")]
    public partial class IMachine
    {
        [StringLength(255)]
        public string ResourceCode { get; set; }
        [StringLength(255)]
        public string ResourceName { get; set; }
        [StringLength(255)]
        public string Category { get; set; }
        [StringLength(255)]
        public string Type { get; set; }
        [StringLength(255)]
        public string NoOfDaylights { get; set; }
        public double? HourlyMachineRunningStandardRate { get; set; }
        public double? StandardHourlyLabourRate { get; set; }
        [StringLength(255)]
        public string AvgDurationPerUnit { get; set; }
        [StringLength(255)]
        public string AvgHandlingTimePerUnit { get; set; }
        [StringLength(255)]
        public string PressLine { get; set; }
        public double? MaxNoOfworkers { get; set; }
        [StringLength(255)]
        public string BarcodePrinting { get; set; }
        [StringLength(255)]
        public string Profile { get; set; }
    }
}
