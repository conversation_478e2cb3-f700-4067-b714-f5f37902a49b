﻿<?xml version="1.0" encoding="utf-8" ?>
<nlog xmlns="http://www.nlog-project.org/schemas/NLog.xsd"
			xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
			autoReload="true"
			throwConfigExceptions="true"
			internalLogLevel="info"
			internalLogFile="c:\temp\internal-nlog.txt">

	<extensions>
		<add assembly="NLog.Web.AspNetCore"/>
	</extensions>

	<targets>
		<target name="file" xsi:type="File"
						layout="${longdate} ${logger} ${message}${exception:format=ToString}" 
						fileName="c:\logs\nlog-all-${shortdate}.log" 
						keepFileOpen="true"
						maxArchiveFiles="10"
						archiveAboveSize="10240"
						encoding="utf-8" />
						
		<!--<target name="database" xsi:type="Database"
						dbProvider="sqlserver"
						dbHost="${configsetting:name=NlogConnection.DbHost}"
						dbDatabase="${configsetting:name=NlogConnection.Database}"
						dbUserName="${configsetting:name=NlogConnection.User}"
						dbPassword="${configsetting:name=NlogConnection.Password}">
			<commandText>
				insert into cmn.Log (
				Application, Logged, Level, Message,
				Logger, CallSite, StackTrace, InnerException
				) values (
				@Application, @Logged, @Level, @Message,
				@Logger, @Callsite, @stackTrace, @innerException
				);
			</commandText>
			<parameter name="@application" layout="AspNetCoreNlog" />
			<parameter name="@logged" layout="${date}" />
			<parameter name="@level" layout="${level}" />
			<parameter name="@message" layout="${message}" />
			<parameter name="@logger" layout="${logger}" />
			<parameter name="@callSite" layout="${callsite:filename=true}" />
			<parameter name="@stackTrace" layout="${exception:format=stackTrace}" />
			<parameter name="@innerException" layout="${exception:method:maxInnerExceptionLevel=5:innerFormat=shortType,message,method}" />
		</target>-->
	</targets>

	<rules>
		<logger name="*" minlevel="Error" writeTo="file" />
		<!--<logger name="*" minlevel="Error" writeTo="database" />-->
	</rules>
</nlog>