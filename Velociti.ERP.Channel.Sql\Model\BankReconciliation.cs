﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("BankReconciliations", Schema = "fin")]
    public partial class BankReconciliation
    {
        public BankReconciliation()
        {
            BankReconciliationLines = new HashSet<BankReconciliationLine>();
        }

        [Key]
        public int BankReconciliationId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime DocDate { get; set; }
        public int? CompanyId { get; set; }
        public byte? StatusEnum { get; set; }
        public int? AccountId { get; set; }
        [Column(TypeName = "money")]
        public decimal StatementBalance { get; set; }
        [Column(TypeName = "money")]
        public decimal LastStatementBalance { get; set; }
        [Column(TypeName = "money")]
        public decimal? ReconciledAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnreconciledAmount { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("BankReconciliations")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("BankReconciliations")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(BankReconciliationLine.BankReconciliation))]
        public virtual ICollection<BankReconciliationLine> BankReconciliationLines { get; set; }
    }
}
