﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("WarehouseStockQualityControls", Schema = "inv")]
    public partial class WarehouseStockQualityControl
    {
        [Key]
        public int WarehouseStockQualityControlId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string DocNumber { get; set; }
        public int? SrcWarehouseId { get; set; }
        public int? DstWarehouseId { get; set; }
        public int ProductId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int UnitOfMeasureId { get; set; }
        public long? Quantity { get; set; }
        public byte? Grade { get; set; }
        [Column("QCParameters")]
        [StringLength(255)]
        public string Qcparameters { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("WarehouseStockQualityControls")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DstWarehouseId))]
        [InverseProperty(nameof(Warehous.WarehouseStockQualityControlDstWarehouses))]
        public virtual Warehous DstWarehouse { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("WarehouseStockQualityControls")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SrcWarehouseId))]
        [InverseProperty(nameof(Warehous.WarehouseStockQualityControlSrcWarehouses))]
        public virtual Warehous SrcWarehouse { get; set; }
        [ForeignKey(nameof(UnitOfMeasureId))]
        [InverseProperty("WarehouseStockQualityControls")]
        public virtual UnitOfMeasure UnitOfMeasure { get; set; }
    }
}
