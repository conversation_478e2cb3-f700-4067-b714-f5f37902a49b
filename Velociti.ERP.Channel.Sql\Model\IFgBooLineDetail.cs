﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_FG_BOO_LineDetails")]
    public partial class IFgBooLineDetail
    {
        [StringLength(255)]
        public string Code { get; set; }
        [StringLength(255)]
        public string Item { get; set; }
        [Column("Bead Rings 4\"")]
        [StringLength(255)]
        public string BeadRings4 { get; set; }
        [StringLength(255)]
        public string Machine { get; set; }
        [Column("Bead Rings 6\"")]
        [StringLength(255)]
        public string BeadRings6 { get; set; }
        [StringLength(255)]
        public string Machine1 { get; set; }
        [Column("Bead Rings 8\"")]
        [StringLength(255)]
        public string BeadRings8 { get; set; }
        [StringLength(255)]
        public string Machine2 { get; set; }
        [Column("Bead rings 9''")]
        [StringLength(255)]
        public string BeadRings9 { get; set; }
        [StringLength(255)]
        public string Machine3 { get; set; }
        [Column("Bead Rings 10\"")]
        [StringLength(255)]
        public string BeadRings10 { get; set; }
        [StringLength(255)]
        public string Machine4 { get; set; }
        [Column("Bead Rings 12\"")]
        [StringLength(255)]
        public string BeadRings12 { get; set; }
        [StringLength(255)]
        public string Machine5 { get; set; }
        [Column("Bead Rings 13\"")]
        [StringLength(255)]
        public string BeadRings13 { get; set; }
        [StringLength(255)]
        public string Machine6 { get; set; }
        [Column("Bead Rings 15\"")]
        [StringLength(255)]
        public string BeadRings15 { get; set; }
        [StringLength(255)]
        public string Machine7 { get; set; }
        [Column("Bead Rings 20\"")]
        [StringLength(255)]
        public string BeadRings20 { get; set; }
        [StringLength(255)]
        public string Machine8 { get; set; }
        [Column("Bead Rings 24\"")]
        [StringLength(255)]
        public string BeadRings24 { get; set; }
        [StringLength(255)]
        public string Machine9 { get; set; }
        [Column("TMT - Final")]
        [StringLength(255)]
        public string TmtFinal { get; set; }
        [StringLength(255)]
        public string Machine10 { get; set; }
        [Column("Friction Cord-10mm")]
        [StringLength(255)]
        public string FrictionCord10mm { get; set; }
        [StringLength(255)]
        public string Machine11 { get; set; }
        [Column("TMT - Final1")]
        [StringLength(255)]
        public string TmtFinal1 { get; set; }
        [StringLength(255)]
        public string Machine12 { get; set; }
        [Column("TEV - Final")]
        [StringLength(255)]
        public string TevFinal { get; set; }
        [StringLength(255)]
        public string Machine13 { get; set; }
        [Column("GPT-3 - Final")]
        [StringLength(255)]
        public string Gpt3Final { get; set; }
        [StringLength(255)]
        public string Machine14 { get; set; }
        [Column("GPT 1 - Final")]
        [StringLength(255)]
        public string Gpt1Final { get; set; }
        [StringLength(255)]
        public string Machine15 { get; set; }
        [Column("GPT 2 - Final")]
        [StringLength(255)]
        public string Gpt2Final { get; set; }
        [StringLength(255)]
        public string Machine16 { get; set; }
        [Column("GPS - Final")]
        [StringLength(255)]
        public string GpsFinal { get; set; }
        [StringLength(255)]
        public string Machine17 { get; set; }
        [Column("MBT - Final")]
        [StringLength(255)]
        public string MbtFinal { get; set; }
        [StringLength(255)]
        public string Machine18 { get; set; }
        [Column("BEV - Final")]
        [StringLength(255)]
        public string BevFinal { get; set; }
        [StringLength(255)]
        public string Machine19 { get; set; }
        [Column("BAP - Final")]
        [StringLength(255)]
        public string BapFinal { get; set; }
        [StringLength(255)]
        public string Machine20 { get; set; }
        [Column("BFP - Final")]
        [StringLength(255)]
        public string BfpFinal { get; set; }
        [StringLength(255)]
        public string Machine21 { get; set; }
        [Column("ECO NM Gray - Final")]
        [StringLength(255)]
        public string EcoNmGrayFinal { get; set; }
        [StringLength(255)]
        public string Machine22 { get; set; }
        [Column("ECO - DSI - Final")]
        [StringLength(255)]
        public string EcoDsiFinal { get; set; }
        [StringLength(255)]
        public string Machine23 { get; set; }
        [Column("ECO - TWW - Final")]
        [StringLength(255)]
        public string EcoTwwFinal { get; set; }
        [StringLength(255)]
        public string Machine24 { get; set; }
        [Column("Steel Band 90x120")]
        [StringLength(255)]
        public string SteelBand90x120 { get; set; }
        [Column("Steel Band 90x150")]
        [StringLength(255)]
        public string SteelBand90x150 { get; set; }
        [Column("Steel Band 80 X170-8mm")]
        [StringLength(255)]
        public string SteelBand80X1708mm { get; set; }
        [Column("Steel Band 100x203#2")]
        [StringLength(255)]
        public string SteelBand100x2032 { get; set; }
        [Column("Steel Band 127x203#2")]
        [StringLength(255)]
        public string SteelBand127x2032 { get; set; }
        [Column("Steel Band 114x203#2")]
        [StringLength(255)]
        public string SteelBand114x2032 { get; set; }
        [Column("Steel Band 152*285#8")]
        [StringLength(255)]
        public string SteelBand1522858 { get; set; }
        [Column("Steel Band 127x254")]
        [StringLength(255)]
        public string SteelBand127x254 { get; set; }
        [Column("Steel Band 152x254")]
        [StringLength(255)]
        public string SteelBand152x254 { get; set; }
        [Column("Steel Band 152x266#7")]
        [StringLength(255)]
        public string SteelBand152x2667 { get; set; }
        [Column("Steel Band 127x285#8")]
        [StringLength(255)]
        public string SteelBand127x2858 { get; set; }
        [Column("Steel Band 152*285#81")]
        [StringLength(255)]
        public string SteelBand15228581 { get; set; }
        [Column("Steel Band 178x285#8")]
        [StringLength(255)]
        public string SteelBand178x2858 { get; set; }
        [Column("Steel Band 152x308")]
        [StringLength(255)]
        public string SteelBand152x308 { get; set; }
        [Column("Steel Band 130 X356")]
        [StringLength(255)]
        public string SteelBand130X356 { get; set; }
        [Column("Steel Band 127X308")]
        [StringLength(255)]
        public string SteelBand127x308 { get; set; }
        [Column("Steel Band 152x3081")]
        [StringLength(255)]
        public string SteelBand152x3081 { get; set; }
        [Column("Steel Band 178x308")]
        [StringLength(255)]
        public string SteelBand178x308 { get; set; }
        [Column("Steel Band 152x3082")]
        [StringLength(255)]
        public string SteelBand152x3082 { get; set; }
        [Column("Steel Band 203x308")]
        [StringLength(255)]
        public string SteelBand203x308 { get; set; }
        [Column("Steel Band 229X308")]
        [StringLength(255)]
        public string SteelBand229x308 { get; set; }
        [Column("Steel Band 100*455")]
        [StringLength(255)]
        public string SteelBand100455 { get; set; }
        [Column("Steel Band 178*381")]
        [StringLength(255)]
        public string SteelBand178381 { get; set; }
        [Column("Steel Band 203*381")]
        [StringLength(255)]
        public string SteelBand203381 { get; set; }
        [Column("Steel Band 229*381")]
        [StringLength(255)]
        public string SteelBand229381 { get; set; }
        [Column("Steel Band 203*406#4")]
        [StringLength(255)]
        public string SteelBand2034064 { get; set; }
        [Column("Steel Band 203*406#41")]
        [StringLength(255)]
        public string SteelBand20340641 { get; set; }
        [Column("Steel Band 203*406#42")]
        [StringLength(255)]
        public string SteelBand20340642 { get; set; }
        [Column("Steel Band 229*406#4")]
        [StringLength(255)]
        public string SteelBand2294064 { get; set; }
        [Column("Steel Band 229*406#41")]
        [StringLength(255)]
        public string SteelBand22940641 { get; set; }
        [Column("Steel Band 305*406#4")]
        [StringLength(255)]
        public string SteelBand3054064 { get; set; }
        [Column("Steel Band 305*406#41")]
        [StringLength(255)]
        public string SteelBand30540641 { get; set; }
        [Column("Steel Band 305*406#42")]
        [StringLength(255)]
        public string SteelBand30540642 { get; set; }
        [Column("Steel Band 305*406#43")]
        [StringLength(255)]
        public string SteelBand30540643 { get; set; }
        [Column("Steel Band 254*508")]
        [StringLength(255)]
        public string SteelBand254508 { get; set; }
        [Column("Steel Band 254*5081")]
        [StringLength(255)]
        public string SteelBand2545081 { get; set; }
        [Column("Steel Band 254*559")]
        [StringLength(255)]
        public string SteelBand254559 { get; set; }
        [Column("Steel Band 254*5591")]
        [StringLength(255)]
        public string SteelBand2545591 { get; set; }
        [Column("Steel Band 305*559")]
        [StringLength(255)]
        public string SteelBand305559 { get; set; }
        [Column("Steel Band 305*5591")]
        [StringLength(255)]
        public string SteelBand3055591 { get; set; }
        [Column("Steel Band 305*5592")]
        [StringLength(255)]
        public string SteelBand3055592 { get; set; }
        [Column("Steel Band 360X559")]
        [StringLength(255)]
        public string SteelBand360x559 { get; set; }
        [Column("Steel Band 406X559")]
        [StringLength(255)]
        public string SteelBand406x559 { get; set; }
        [Column("Steel Band 406X5591")]
        [StringLength(255)]
        public string SteelBand406x5591 { get; set; }
        [Column("Steel Band 200 X555")]
        [StringLength(255)]
        public string SteelBand200X555 { get; set; }
        [Column("Steel Band 200 X5551")]
        [StringLength(255)]
        public string SteelBand200X5551 { get; set; }
        [Column("Steel Band 127*165#1")]
        [StringLength(255)]
        public string SteelBand1271651 { get; set; }
        [Column("Steel Band 114x203#21")]
        [StringLength(255)]
        public string SteelBand114x20321 { get; set; }
        [Column("Steel Band 127x2541")]
        [StringLength(255)]
        public string SteelBand127x2541 { get; set; }
        [Column("Steel Band 127x285#81")]
        [StringLength(255)]
        public string SteelBand127x28581 { get; set; }
        [Column("Steel Band 152*285#82")]
        [StringLength(255)]
        public string SteelBand15228582 { get; set; }
        [Column("Steel Band 127*266#7")]
        [StringLength(255)]
        public string SteelBand1272667 { get; set; }
        [Column("Steel Band 127*266#71")]
        [StringLength(255)]
        public string SteelBand12726671 { get; set; }
        [Column("Steel Band 152x266#71")]
        [StringLength(255)]
        public string SteelBand152x26671 { get; set; }
        [Column("Steel Band 152x266#72")]
        [StringLength(255)]
        public string SteelBand152x26672 { get; set; }
        [Column("Steel Band 152*285#83")]
        [StringLength(255)]
        public string SteelBand15228583 { get; set; }
        [Column("Steel Band 152x3083")]
        [StringLength(255)]
        public string SteelBand152x3083 { get; set; }
        [Column("Steel Band 152x3084")]
        [StringLength(255)]
        public string SteelBand152x3084 { get; set; }
        [Column("Steel Band 203x3081")]
        [StringLength(255)]
        public string SteelBand203x3081 { get; set; }
        [Column("Steel Band 178*3811")]
        [StringLength(255)]
        public string SteelBand1783811 { get; set; }
        [Column("Steel Band 178*3812")]
        [StringLength(255)]
        public string SteelBand1783812 { get; set; }
        [Column("Steel Band 178*3813")]
        [StringLength(255)]
        public string SteelBand1783813 { get; set; }
        [Column("Steel Band 203*3811")]
        [StringLength(255)]
        public string SteelBand2033811 { get; set; }
        [Column("Steel Band 203*3812")]
        [StringLength(255)]
        public string SteelBand2033812 { get; set; }
        [Column("Steel Band 229*406#42")]
        [StringLength(255)]
        public string SteelBand22940642 { get; set; }
        [Column("Steel Band 305*406#44")]
        [StringLength(255)]
        public string SteelBand30540644 { get; set; }
        [Column("Steel Band 127x285#82")]
        [StringLength(255)]
        public string SteelBand127x28582 { get; set; }
        [Column("Steel Band 127x285#83")]
        [StringLength(255)]
        public string SteelBand127x28583 { get; set; }
        [Column("Steel Band 178x3081")]
        [StringLength(255)]
        public string SteelBand178x3081 { get; set; }
        [Column("Steel Band 229*406#43")]
        [StringLength(255)]
        public string SteelBand22940643 { get; set; }
        [Column("Steel Band 127x285#84")]
        [StringLength(255)]
        public string SteelBand127x28584 { get; set; }
    }
}
