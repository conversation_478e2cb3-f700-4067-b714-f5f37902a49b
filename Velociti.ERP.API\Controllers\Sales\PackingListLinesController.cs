﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class PackingListLinesController : ControllerBase
    {
        private readonly IPackingListLineService _packingListLineService;

        public PackingListLinesController(IPackingListLineService packingListLineService)
        {
            _packingListLineService = packingListLineService;
        }

        [HttpGet]
        [Route("{packingListId}")]
        public async Task<IActionResult> Get(int packingListId)
        {
            return Ok(await _packingListLineService.GetAsync(packingListId));
        }
    }
}