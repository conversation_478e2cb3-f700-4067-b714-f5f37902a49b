﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FreeIssueCustomers", Schema = "sales")]
    public partial class FreeIssueCustomer
    {
        [Key]
        public int FreeIssueCustomerId { get; set; }
        public int CompanyId { get; set; }
        public int FreeIssueId { get; set; }
        public int? CustomerId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("FreeIssueCustomers")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("FreeIssueCustomers")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(FreeIssueId))]
        [InverseProperty("FreeIssueCustomers")]
        public virtual FreeIssue FreeIssue { get; set; }
    }
}
