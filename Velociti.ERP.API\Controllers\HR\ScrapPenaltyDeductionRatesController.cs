﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.HR;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Entities.Sales;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class ScrapPenaltyDeductionRatesController : ControllerBase
    {
        private readonly IScrapPenaltyDeductionRatesService _ScrapPenaltyDeductionRatesService;

        public ScrapPenaltyDeductionRatesController(IScrapPenaltyDeductionRatesService ScrapPenaltyDeductionRatesService)
        {
            _ScrapPenaltyDeductionRatesService = ScrapPenaltyDeductionRatesService;
        }

        [HttpGet]
        [Route("Single/{ScrapPenaltyDeductionRatesId}")]
        public async Task<IActionResult> FindById(int ScrapPenaltyDeductionRatesId)
        {
            return Ok(await _ScrapPenaltyDeductionRatesService.FindByIdAsync(ScrapPenaltyDeductionRatesId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            return Ok(await _ScrapPenaltyDeductionRatesService.GetAllAsync());
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]ScrapPenaltyDeductionRate ScrapPenaltyDeductionRates)
        {
            switch (ScrapPenaltyDeductionRates.Action)
            {
                case "save": await _ScrapPenaltyDeductionRatesService.SaveAsync(ScrapPenaltyDeductionRates); break;
                case "submit": await _ScrapPenaltyDeductionRatesService.UpdateAsync(ScrapPenaltyDeductionRates); break;
            }

            return Ok();
        }
    }
}