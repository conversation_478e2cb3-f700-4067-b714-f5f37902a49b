﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class PayBooksController : ControllerBase
    {
        private readonly IPayBookService _payBookService;

        public PayBooksController(IPayBookService payBookService)
        {
            _payBookService = payBookService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _payBookService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            return Ok(await _payBookService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("Single/{payBookId}")]
        public async Task<IActionResult> GetById(int payBookId)
        {
            return Ok(await _payBookService.GetByIdAsync(payBookId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]PayBook payBook)
        {
            await _payBookService.SaveAsync(payBook);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Toggle([FromBody]PayBook payBook)
        {
            await _payBookService.ToggleActivationAsync(payBook);

            return Ok();
        }
    }
}