﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductList")]
    public partial class ProductList
    {
        [Key]
        public int Id { get; set; }
        [StringLength(50)]
        public string Code { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        [Column("ERPCode")]
        [StringLength(50)]
        public string Erpcode { get; set; }
        public int? Qty { get; set; }
        public int? TempId { get; set; }
    }
}
