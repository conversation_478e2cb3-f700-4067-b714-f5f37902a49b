﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Companies", Schema = "adm")]
    public partial class Company
    {
        public Company()
        {
            AccountMappings = new HashSet<AccountMapping>();
            Accounts = new HashSet<Account>();
            AgingBuckets = new HashSet<AgingBucket>();
            AppSettings = new HashSet<AppSetting>();
            AttendanceAllowances = new HashSet<AttendanceAllowance>();
            Attendances = new HashSet<Attendance>();
            AuditLogs = new HashSet<AuditLog>();
            BankAdjustments = new HashSet<BankAdjustment>();
            BankReconciliations = new HashSet<BankReconciliation>();
            BillOfMaterials = new HashSet<BillOfMaterial>();
            BillOfOperations = new HashSet<BillOfOperation>();
            BundleProducts = new HashSet<BundleProduct>();
            ChargeInformations = new HashSet<ChargeInformation>();
            ChartOfAccounts = new HashSet<ChartOfAccount>();
            Coamappings = new HashSet<Coamapping>();
            ConsignmentStockTransfers = new HashSet<ConsignmentStockTransfer>();
            Contacts = new HashSet<Contact>();
            CustomerPayments = new HashSet<CustomerPayment>();
            Customers = new HashSet<Customer>();
            Departments = new HashSet<Department>();
            Designations = new HashSet<Designation>();
            DiscountSchemeCustomers = new HashSet<DiscountSchemeCustomer>();
            DiscountSchemes = new HashSet<DiscountScheme>();
            DocCodeConfigs = new HashSet<DocCodeConfig>();
            DocTypeConfigs = new HashSet<DocTypeConfig>();
            Employees = new HashSet<Employee>();
            ExchangeOrders = new HashSet<ExchangeOrder>();
            FinancialStatements = new HashSet<FinancialStatement>();
            FreeIssueCustomers = new HashSet<FreeIssueCustomer>();
            FreeIssues = new HashSet<FreeIssue>();
            GeneralLedgerLines = new HashSet<GeneralLedgerLine>();
            GeneralLedgerSettlements = new HashSet<GeneralLedgerSettlement>();
            GoodsDispatchNotes = new HashSet<GoodsDispatchNote>();
            GoodsReceiveNotes = new HashSet<GoodsReceiveNote>();
            HoreturnStockTransfers = new HashSet<HoreturnStockTransfer>();
            InboundReceipts = new HashSet<InboundReceipt>();
            IntegrationStatus = new HashSet<IntegrationStatu>();
            InternalDispatches = new HashSet<InternalDispatch>();
            InternalServiceInvoices = new HashSet<InternalServiceInvoice>();
            LeaveConfigurations = new HashSet<LeaveConfiguration>();
            LoadingPlans = new HashSet<LoadingPlan>();
            LoanOrders = new HashSet<LoanOrder>();
            Logins = new HashSet<Login>();
            MachineVisualPlanLines = new HashSet<MachineVisualPlanLine>();
            Machines = new HashSet<Machine>();
            ManualJournals = new HashSet<ManualJournal>();
            MaterialRequisitionNotes = new HashSet<MaterialRequisitionNote>();
            MaterialRequisitionPlans = new HashSet<MaterialRequisitionPlan>();
            Modules = new HashSet<Module>();
            Moulds = new HashSet<Mould>();
            Notifications = new HashSet<Notification>();
            OutboundPayments = new HashSet<OutboundPayment>();
            PackingLists = new HashSet<PackingList>();
            PasswordPolicies = new HashSet<PasswordPolicy>();
            PayBooks = new HashSet<PayBook>();
            PaymentAdvices = new HashSet<PaymentAdvice>();
            Permissions = new HashSet<Permission>();
            PettyCashes = new HashSet<PettyCash>();
            PressLines = new HashSet<PressLine>();
            ProductBrandDiscounts = new HashSet<ProductBrandDiscount>();
            ProductDies = new HashSet<ProductDy>();
            ProductHierarchies = new HashSet<ProductHierarchy>();
            ProductionOrders = new HashSet<ProductionOrder>();
            ProductionPlanBarcodes = new HashSet<ProductionPlanBarcode>();
            ProductionPlanLines = new HashSet<ProductionPlanLine>();
            ProductionPlans = new HashSet<ProductionPlan>();
            Products = new HashSet<Product>();
            ProfileBuildings = new HashSet<ProfileBuilding>();
            PurchaseOrders = new HashSet<PurchaseOrder>();
            PurchaseRequisitionNotes = new HashSet<PurchaseRequisitionNote>();
            PurchaseReturns = new HashSet<PurchaseReturn>();
            Quotations = new HashSet<Quotation>();
            RawMaterialPlannings = new HashSet<RawMaterialPlanning>();
            Roles = new HashSet<Role>();
            SalesInvoices = new HashSet<SalesInvoice>();
            SalesOrders = new HashSet<SalesOrder>();
            SalesReturns = new HashSet<SalesReturn>();
            SemiFinishedGoodsProductionPlanSummaries = new HashSet<SemiFinishedGoodsProductionPlanSummary>();
            SemiFinishedGoodsProductionPlans = new HashSet<SemiFinishedGoodsProductionPlan>();
            SemiFinishedGoodsQcscannings = new HashSet<SemiFinishedGoodsQcscanning>();
            ServiceInvoices = new HashSet<ServiceInvoice>();
            ShipmentCosts = new HashSet<ShipmentCost>();
            ShipmentQualityControlReturns = new HashSet<ShipmentQualityControlReturn>();
            ShipmentQualityControls = new HashSet<ShipmentQualityControl>();
            StockAdjustments = new HashSet<StockAdjustment>();
            StockAllocations = new HashSet<StockAllocation>();
            StockCountSheets = new HashSet<StockCountSheet>();
            StockTakes = new HashSet<StockTake>();
            StockTransferReceipts = new HashSet<StockTransferReceipt>();
            StockTransfers = new HashSet<StockTransfer>();
            SubContractOrders = new HashSet<SubContractOrder>();
            SupplementaryManufacturers = new HashSet<SupplementaryManufacturer>();
            Suppliers = new HashSet<Supplier>();
            SupportDatas = new HashSet<SupportData>();
            TaxGroups = new HashSet<TaxGroup>();
            TaxTypes = new HashSet<TaxType>();
            TouchScreenHistories = new HashSet<TouchScreenHistory>();
            TransactionDocuments = new HashSet<TransactionDocument>();
            TxnLineHistories = new HashSet<TxnLineHistory>();
            TxnWorkFlows = new HashSet<TxnWorkFlow>();
            TyreSpecifications = new HashSet<TyreSpecification>();
            UnitOfMeasures = new HashSet<UnitOfMeasure>();
            UserCompanies = new HashSet<UserCompany>();
            UserSupervisors = new HashSet<UserSupervisor>();
            Warehous = new HashSet<Warehous>();
            WarehousePallets = new HashSet<WarehousePallet>();
            WarehouseProducts = new HashSet<WarehouseProduct>();
            WarehouseStockIssues = new HashSet<WarehouseStockIssue>();
            WarehouseStockQualityControls = new HashSet<WarehouseStockQualityControl>();
            WorkFlows = new HashSet<WorkFlow>();
        }

        [Key]
        public int CompanyId { get; set; }
        public int SubGroupId { get; set; }
        [Required]
        [StringLength(50)]
        public string CompanyCode { get; set; }
        [Required]
        [StringLength(255)]
        public string CompanyName { get; set; }
        [StringLength(255)]
        public string StreetAddress { get; set; }
        [StringLength(255)]
        public string ShippingAddress { get; set; }
        [StringLength(255)]
        public string BillingAddress { get; set; }
        public byte[] CompanyLogo { get; set; }
        [StringLength(50)]
        public string TaxRegistrationNo { get; set; }
        [StringLength(20)]
        public string Telephone { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
        [StringLength(255)]
        public string Website { get; set; }
        public int? CurrencyId { get; set; }
        public int? DateFormatId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? FiscalYear { get; set; }
        [Column(TypeName = "date")]
        public DateTime? FinancialPeriod { get; set; }
        [Column("IsPDChequeBlocked")]
        public bool? IsPdchequeBlocked { get; set; }
        [StringLength(500)]
        public string FilePath1 { get; set; }
        [StringLength(500)]
        public string FilePath2 { get; set; }
        [StringLength(500)]
        public string FilePath3 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }
        [StringLength(500)]
        public string ReportHeader1 { get; set; }
        [StringLength(500)]
        public string ReportHeader2 { get; set; }
        [StringLength(500)]
        public string ReportHeader3 { get; set; }
        [StringLength(500)]
        public string ReportHeader4 { get; set; }

        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("Companies")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(SubGroupId))]
        [InverseProperty("Companies")]
        public virtual SubGroup SubGroup { get; set; }
        [InverseProperty(nameof(AccountMapping.Company))]
        public virtual ICollection<AccountMapping> AccountMappings { get; set; }
        [InverseProperty(nameof(Account.Company))]
        public virtual ICollection<Account> Accounts { get; set; }
        [InverseProperty(nameof(AgingBucket.Company))]
        public virtual ICollection<AgingBucket> AgingBuckets { get; set; }
        [InverseProperty(nameof(AppSetting.Company))]
        public virtual ICollection<AppSetting> AppSettings { get; set; }
        [InverseProperty(nameof(AttendanceAllowance.Company))]
        public virtual ICollection<AttendanceAllowance> AttendanceAllowances { get; set; }
        [InverseProperty(nameof(Attendance.Company))]
        public virtual ICollection<Attendance> Attendances { get; set; }
        [InverseProperty(nameof(AuditLog.Company))]
        public virtual ICollection<AuditLog> AuditLogs { get; set; }
        [InverseProperty(nameof(BankAdjustment.Company))]
        public virtual ICollection<BankAdjustment> BankAdjustments { get; set; }
        [InverseProperty(nameof(BankReconciliation.Company))]
        public virtual ICollection<BankReconciliation> BankReconciliations { get; set; }
        [InverseProperty(nameof(BillOfMaterial.Company))]
        public virtual ICollection<BillOfMaterial> BillOfMaterials { get; set; }
        [InverseProperty(nameof(BillOfOperation.Company))]
        public virtual ICollection<BillOfOperation> BillOfOperations { get; set; }
        [InverseProperty(nameof(BundleProduct.Company))]
        public virtual ICollection<BundleProduct> BundleProducts { get; set; }
        [InverseProperty(nameof(ChargeInformation.Company))]
        public virtual ICollection<ChargeInformation> ChargeInformations { get; set; }
        [InverseProperty(nameof(ChartOfAccount.Company))]
        public virtual ICollection<ChartOfAccount> ChartOfAccounts { get; set; }
        [InverseProperty(nameof(Coamapping.Company))]
        public virtual ICollection<Coamapping> Coamappings { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransfer.Company))]
        public virtual ICollection<ConsignmentStockTransfer> ConsignmentStockTransfers { get; set; }
        [InverseProperty(nameof(Contact.Company))]
        public virtual ICollection<Contact> Contacts { get; set; }
        [InverseProperty(nameof(CustomerPayment.Company))]
        public virtual ICollection<CustomerPayment> CustomerPayments { get; set; }
        [InverseProperty(nameof(Customer.Company))]
        public virtual ICollection<Customer> Customers { get; set; }
        [InverseProperty(nameof(Department.Company))]
        public virtual ICollection<Department> Departments { get; set; }
        [InverseProperty(nameof(Designation.Company))]
        public virtual ICollection<Designation> Designations { get; set; }
        [InverseProperty(nameof(DiscountSchemeCustomer.Company))]
        public virtual ICollection<DiscountSchemeCustomer> DiscountSchemeCustomers { get; set; }
        [InverseProperty(nameof(DiscountScheme.Company))]
        public virtual ICollection<DiscountScheme> DiscountSchemes { get; set; }
        [InverseProperty(nameof(DocCodeConfig.Company))]
        public virtual ICollection<DocCodeConfig> DocCodeConfigs { get; set; }
        [InverseProperty(nameof(DocTypeConfig.Company))]
        public virtual ICollection<DocTypeConfig> DocTypeConfigs { get; set; }
        [InverseProperty(nameof(Employee.Company))]
        public virtual ICollection<Employee> Employees { get; set; }
        [InverseProperty(nameof(ExchangeOrder.Company))]
        public virtual ICollection<ExchangeOrder> ExchangeOrders { get; set; }
        [InverseProperty(nameof(FinancialStatement.Company))]
        public virtual ICollection<FinancialStatement> FinancialStatements { get; set; }
        [InverseProperty(nameof(FreeIssueCustomer.Company))]
        public virtual ICollection<FreeIssueCustomer> FreeIssueCustomers { get; set; }
        [InverseProperty(nameof(FreeIssue.Company))]
        public virtual ICollection<FreeIssue> FreeIssues { get; set; }
        [InverseProperty(nameof(GeneralLedgerLine.Company))]
        public virtual ICollection<GeneralLedgerLine> GeneralLedgerLines { get; set; }
        [InverseProperty(nameof(GeneralLedgerSettlement.Company))]
        public virtual ICollection<GeneralLedgerSettlement> GeneralLedgerSettlements { get; set; }
        [InverseProperty(nameof(GoodsDispatchNote.Company))]
        public virtual ICollection<GoodsDispatchNote> GoodsDispatchNotes { get; set; }
        [InverseProperty(nameof(GoodsReceiveNote.Company))]
        public virtual ICollection<GoodsReceiveNote> GoodsReceiveNotes { get; set; }
        [InverseProperty(nameof(HoreturnStockTransfer.Company))]
        public virtual ICollection<HoreturnStockTransfer> HoreturnStockTransfers { get; set; }
        [InverseProperty(nameof(InboundReceipt.Company))]
        public virtual ICollection<InboundReceipt> InboundReceipts { get; set; }
        [InverseProperty(nameof(IntegrationStatu.Company))]
        public virtual ICollection<IntegrationStatu> IntegrationStatus { get; set; }
        [InverseProperty(nameof(InternalDispatch.Company))]
        public virtual ICollection<InternalDispatch> InternalDispatches { get; set; }
        [InverseProperty(nameof(InternalServiceInvoice.Company))]
        public virtual ICollection<InternalServiceInvoice> InternalServiceInvoices { get; set; }
        [InverseProperty(nameof(LeaveConfiguration.Company))]
        public virtual ICollection<LeaveConfiguration> LeaveConfigurations { get; set; }
        [InverseProperty(nameof(LoadingPlan.Company))]
        public virtual ICollection<LoadingPlan> LoadingPlans { get; set; }
        [InverseProperty(nameof(LoanOrder.Company))]
        public virtual ICollection<LoanOrder> LoanOrders { get; set; }
        [InverseProperty(nameof(Login.Company))]
        public virtual ICollection<Login> Logins { get; set; }
        [InverseProperty(nameof(MachineVisualPlanLine.Company))]
        public virtual ICollection<MachineVisualPlanLine> MachineVisualPlanLines { get; set; }
        [InverseProperty(nameof(Machine.Company))]
        public virtual ICollection<Machine> Machines { get; set; }
        [InverseProperty(nameof(ManualJournal.Company))]
        public virtual ICollection<ManualJournal> ManualJournals { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNote.Company))]
        public virtual ICollection<MaterialRequisitionNote> MaterialRequisitionNotes { get; set; }
        [InverseProperty(nameof(MaterialRequisitionPlan.Company))]
        public virtual ICollection<MaterialRequisitionPlan> MaterialRequisitionPlans { get; set; }
        [InverseProperty(nameof(Module.Company))]
        public virtual ICollection<Module> Modules { get; set; }
        [InverseProperty(nameof(Mould.Company))]
        public virtual ICollection<Mould> Moulds { get; set; }
        [InverseProperty(nameof(Notification.Company))]
        public virtual ICollection<Notification> Notifications { get; set; }
        [InverseProperty(nameof(OutboundPayment.Company))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
        [InverseProperty(nameof(PackingList.Company))]
        public virtual ICollection<PackingList> PackingLists { get; set; }
        [InverseProperty(nameof(PasswordPolicy.Company))]
        public virtual ICollection<PasswordPolicy> PasswordPolicies { get; set; }
        [InverseProperty(nameof(PayBook.Company))]
        public virtual ICollection<PayBook> PayBooks { get; set; }
        [InverseProperty(nameof(PaymentAdvice.Company))]
        public virtual ICollection<PaymentAdvice> PaymentAdvices { get; set; }
        [InverseProperty(nameof(Permission.Company))]
        public virtual ICollection<Permission> Permissions { get; set; }
        [InverseProperty(nameof(PettyCash.Company))]
        public virtual ICollection<PettyCash> PettyCashes { get; set; }
        [InverseProperty(nameof(PressLine.Company))]
        public virtual ICollection<PressLine> PressLines { get; set; }
        [InverseProperty(nameof(ProductBrandDiscount.Company))]
        public virtual ICollection<ProductBrandDiscount> ProductBrandDiscounts { get; set; }
        [InverseProperty(nameof(ProductDy.Company))]
        public virtual ICollection<ProductDy> ProductDies { get; set; }
        [InverseProperty(nameof(ProductHierarchy.Company))]
        public virtual ICollection<ProductHierarchy> ProductHierarchies { get; set; }
        [InverseProperty(nameof(ProductionOrder.Company))]
        public virtual ICollection<ProductionOrder> ProductionOrders { get; set; }
        [InverseProperty(nameof(ProductionPlanBarcode.Company))]
        public virtual ICollection<ProductionPlanBarcode> ProductionPlanBarcodes { get; set; }
        [InverseProperty(nameof(ProductionPlanLine.Company))]
        public virtual ICollection<ProductionPlanLine> ProductionPlanLines { get; set; }
        [InverseProperty(nameof(ProductionPlan.Company))]
        public virtual ICollection<ProductionPlan> ProductionPlans { get; set; }
        [InverseProperty(nameof(Product.Company))]
        public virtual ICollection<Product> Products { get; set; }
        [InverseProperty(nameof(ProfileBuilding.Company))]
        public virtual ICollection<ProfileBuilding> ProfileBuildings { get; set; }
        [InverseProperty(nameof(PurchaseOrder.Company))]
        public virtual ICollection<PurchaseOrder> PurchaseOrders { get; set; }
        [InverseProperty(nameof(PurchaseRequisitionNote.Company))]
        public virtual ICollection<PurchaseRequisitionNote> PurchaseRequisitionNotes { get; set; }
        [InverseProperty(nameof(PurchaseReturn.Company))]
        public virtual ICollection<PurchaseReturn> PurchaseReturns { get; set; }
        [InverseProperty(nameof(Quotation.Company))]
        public virtual ICollection<Quotation> Quotations { get; set; }
        [InverseProperty(nameof(RawMaterialPlanning.Company))]
        public virtual ICollection<RawMaterialPlanning> RawMaterialPlannings { get; set; }
        [InverseProperty(nameof(Role.Company))]
        public virtual ICollection<Role> Roles { get; set; }
        [InverseProperty(nameof(SalesInvoice.Company))]
        public virtual ICollection<SalesInvoice> SalesInvoices { get; set; }
        [InverseProperty(nameof(SalesOrder.Company))]
        public virtual ICollection<SalesOrder> SalesOrders { get; set; }
        [InverseProperty(nameof(SalesReturn.Company))]
        public virtual ICollection<SalesReturn> SalesReturns { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsProductionPlanSummary.Company))]
        public virtual ICollection<SemiFinishedGoodsProductionPlanSummary> SemiFinishedGoodsProductionPlanSummaries { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsProductionPlan.Company))]
        public virtual ICollection<SemiFinishedGoodsProductionPlan> SemiFinishedGoodsProductionPlans { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsQcscanning.Company))]
        public virtual ICollection<SemiFinishedGoodsQcscanning> SemiFinishedGoodsQcscannings { get; set; }
        [InverseProperty(nameof(ServiceInvoice.Company))]
        public virtual ICollection<ServiceInvoice> ServiceInvoices { get; set; }
        [InverseProperty(nameof(ShipmentCost.Company))]
        public virtual ICollection<ShipmentCost> ShipmentCosts { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturn.Company))]
        public virtual ICollection<ShipmentQualityControlReturn> ShipmentQualityControlReturns { get; set; }
        [InverseProperty(nameof(ShipmentQualityControl.Company))]
        public virtual ICollection<ShipmentQualityControl> ShipmentQualityControls { get; set; }
        [InverseProperty(nameof(StockAdjustment.Company))]
        public virtual ICollection<StockAdjustment> StockAdjustments { get; set; }
        [InverseProperty(nameof(StockAllocation.Company))]
        public virtual ICollection<StockAllocation> StockAllocations { get; set; }
        [InverseProperty(nameof(StockCountSheet.Company))]
        public virtual ICollection<StockCountSheet> StockCountSheets { get; set; }
        [InverseProperty(nameof(StockTake.Company))]
        public virtual ICollection<StockTake> StockTakes { get; set; }
        [InverseProperty(nameof(StockTransferReceipt.Company))]
        public virtual ICollection<StockTransferReceipt> StockTransferReceipts { get; set; }
        [InverseProperty(nameof(StockTransfer.Company))]
        public virtual ICollection<StockTransfer> StockTransfers { get; set; }
        [InverseProperty(nameof(SubContractOrder.Company))]
        public virtual ICollection<SubContractOrder> SubContractOrders { get; set; }
        [InverseProperty(nameof(SupplementaryManufacturer.Company))]
        public virtual ICollection<SupplementaryManufacturer> SupplementaryManufacturers { get; set; }
        [InverseProperty(nameof(Supplier.Company))]
        public virtual ICollection<Supplier> Suppliers { get; set; }
        [InverseProperty(nameof(SupportData.Company))]
        public virtual ICollection<SupportData> SupportDatas { get; set; }
        [InverseProperty(nameof(TaxGroup.Company))]
        public virtual ICollection<TaxGroup> TaxGroups { get; set; }
        [InverseProperty(nameof(TaxType.Company))]
        public virtual ICollection<TaxType> TaxTypes { get; set; }
        [InverseProperty(nameof(TouchScreenHistory.Company))]
        public virtual ICollection<TouchScreenHistory> TouchScreenHistories { get; set; }
        [InverseProperty(nameof(TransactionDocument.Company))]
        public virtual ICollection<TransactionDocument> TransactionDocuments { get; set; }
        [InverseProperty(nameof(TxnLineHistory.Company))]
        public virtual ICollection<TxnLineHistory> TxnLineHistories { get; set; }
        [InverseProperty(nameof(TxnWorkFlow.Company))]
        public virtual ICollection<TxnWorkFlow> TxnWorkFlows { get; set; }
        [InverseProperty(nameof(TyreSpecification.Company))]
        public virtual ICollection<TyreSpecification> TyreSpecifications { get; set; }
        [InverseProperty(nameof(UnitOfMeasure.Company))]
        public virtual ICollection<UnitOfMeasure> UnitOfMeasures { get; set; }
        [InverseProperty(nameof(UserCompany.Company))]
        public virtual ICollection<UserCompany> UserCompanies { get; set; }
        [InverseProperty(nameof(UserSupervisor.Company))]
        public virtual ICollection<UserSupervisor> UserSupervisors { get; set; }
        [InverseProperty("Company")]
        public virtual ICollection<Warehous> Warehous { get; set; }
        [InverseProperty(nameof(WarehousePallet.Company))]
        public virtual ICollection<WarehousePallet> WarehousePallets { get; set; }
        [InverseProperty(nameof(WarehouseProduct.Company))]
        public virtual ICollection<WarehouseProduct> WarehouseProducts { get; set; }
        [InverseProperty(nameof(WarehouseStockIssue.Company))]
        public virtual ICollection<WarehouseStockIssue> WarehouseStockIssues { get; set; }
        [InverseProperty(nameof(WarehouseStockQualityControl.Company))]
        public virtual ICollection<WarehouseStockQualityControl> WarehouseStockQualityControls { get; set; }
        [InverseProperty(nameof(WorkFlow.Company))]
        public virtual ICollection<WorkFlow> WorkFlows { get; set; }
    }
}
