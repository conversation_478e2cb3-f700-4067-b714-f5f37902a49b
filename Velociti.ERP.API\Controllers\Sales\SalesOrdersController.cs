﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Administration;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;
using static Velociti.ERP.Domain.Entities.Administration.Module;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesOrdersController : ControllerBase
    {
        private readonly IAppSettingsService _appSettingsService;
        private readonly ISalesOrderService _salesOrderService;
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;

        public SalesOrdersController(ISalesOrderService salesOrderService, IGoodsDispatchNoteService goodsDispatchNoteService, IAppSettingsService appSettingsService)
        {
            _salesOrderService = salesOrderService;
            _goodsDispatchNoteService = goodsDispatchNoteService;
            _appSettingsService = appSettingsService;
        }

        [HttpGet]
        [Route("Single/{salesOrderId}")]
        public async Task<IActionResult> FindById(int salesOrderId)
        {
            return Ok(await _salesOrderService.FindByIdAsync(salesOrderId));
        }

        [HttpGet]
        [Route("ShortList/Customer/{customerId}")]
        public async Task<IActionResult> GetShortListByCustomerAsync(int customerId)  
        {
            return Ok(await _salesOrderService.GetShortListByCustomerAsync(customerId));
        }
        [HttpGet]
        [Route("ShortList/Company/{companyId}/User/{loggedInEmployeeId}")]
        public async Task<IActionResult> GetAll(int companyId, int userId)
        {
            return Ok(await _salesOrderService.GetShortListAsync(companyId, userId, 1));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate, byte orderTypeEnum)
        {
            var appSettings = await _appSettingsService.GetAllAsync(companyId);
            if (appSettings.EffectiveDate.HasValue && appSettings.EffectiveDate < DateTime.Now)
                return BadRequest();

            return Ok(await _salesOrderService.GetAllAsync(companyId, userId, startDate, endDate, orderTypeEnum));
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _salesOrderService.GetForApprovalAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        [Route("Employee/{loggedInEmployeeId}")]
        public async Task<IActionResult> Post(int loggedInEmployeeId, [FromBody]SalesOrder salesOrder)
        {
            switch (salesOrder.Action)
            {
                case "save": await _salesOrderService.SaveAsync(loggedInEmployeeId, salesOrder); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{salesOrderId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int salesOrderId, int userId)
        {
            await _salesOrderService.CancelAsync(salesOrderId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]SalesOrder salesOrder)
        {
            switch (salesOrder.Action)
            {
                case "submit": await _salesOrderService.SubmitAsync(salesOrder.SalesOrderId, salesOrder.ModifiedUserId.Value); break;
                case "convert to sales order": return Ok(await _salesOrderService.ConvertToSalesOrderAsync(salesOrder.SalesOrderId, salesOrder.ModifiedUserId.Value)); //asles inquiry convert
                case "convertToGDN": return Ok(await _goodsDispatchNoteService.ConvertAsync(salesOrder.SalesOrderId, DocumentType.SalesOrder, salesOrder.ModifiedUserId));
                case "convert to sales invoice": return Ok(await _salesOrderService.ConvertToSalesInvoiceAsync(salesOrder.SalesOrderId, salesOrder.ModifiedUserId.Value));
                case "reverse": await _salesOrderService.ReverseAsync(salesOrder.SalesOrderId, salesOrder.ModifiedUserId); break;
                case "update status": await _salesOrderService.UpdateStatusAsync(salesOrder); break;
                case "send for approval": await _salesOrderService.SendForApprovalAsync(salesOrder); break;
                case "update work flow": await _salesOrderService.UpdateWorkFlowStatusAsync(salesOrder); break;
            }

            return Ok();
        }

        [HttpGet]
        [Route("UploadedLines")]
        public async Task<IActionResult> GetUploadedLines(int userId)
        {
            return Ok(await _salesOrderService.GetAllUploadedLinesAsync(userId));
        }

        [HttpPost]
        [Route("Upload")]
        public IActionResult UploadSave([FromBody]List<ISalesOrder> salesOrdersList)
        {
            if (salesOrdersList == null)
                return BadRequest();

            _salesOrderService.UploadSaveAsync(salesOrdersList);

            return Ok();
        }

        [HttpPost]
        public async Task<IActionResult> Save(int companyId, int modifiedUserId)
        {
            await _salesOrderService.SaveAllAsync(companyId, modifiedUserId);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> DeleteUploadedLines(int modifiedUserId)
        {
            await _salesOrderService.DeleteUploadedLinesAsync(modifiedUserId);

            return Ok();
        }
    }
}