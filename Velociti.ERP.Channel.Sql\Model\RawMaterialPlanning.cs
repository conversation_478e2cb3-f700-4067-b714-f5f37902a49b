﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("RawMaterialPlannings", Schema = "inv")]
    public partial class RawMaterialPlanning
    {
        [Key]
        public int RawMaterialPlanningId { get; set; }
        public int CompanyId { get; set; }
        public int ProductId { get; set; }
        public bool? IsMonth { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date { get; set; }
        [Column(TypeName = "money")]
        public decimal? Consumption { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("RawMaterialPlannings")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("RawMaterialPlannings")]
        public virtual Product Product { get; set; }
    }
}
