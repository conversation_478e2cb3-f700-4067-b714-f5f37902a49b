﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("WorkFlows", Schema = "cmn")]
    public partial class WorkFlow
    {
        public WorkFlow()
        {
            TxnWorkFlows = new HashSet<TxnWorkFlow>();
            WorkFlowLines = new HashSet<WorkFlowLine>();
        }

        [Key]
        public int WorkFlowId { get; set; }
        public int CompanyId { get; set; }
        [StringLength(50)]
        public string WorkFlowNumber { get; set; }
        public byte? DocTypeEnum { get; set; }
        public byte? DocStatusEnum { get; set; }
        public int? DesignationId { get; set; }
        public byte? StatusEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("WorkFlows")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DesignationId))]
        [InverseProperty("WorkFlows")]
        public virtual Designation Designation { get; set; }
        [InverseProperty(nameof(TxnWorkFlow.WorkFlow))]
        public virtual ICollection<TxnWorkFlow> TxnWorkFlows { get; set; }
        [InverseProperty(nameof(WorkFlowLine.WorkFlow))]
        public virtual ICollection<WorkFlowLine> WorkFlowLines { get; set; }
    }
}
