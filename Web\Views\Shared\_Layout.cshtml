@using Web.Controllers.Foundation;
@using Web.Models;

<!DOCTYPE html>
<html>
<!-- begin::Head -->
<head>
    <base href="../../">
    <meta charset="utf-8" />
    <meta name="description" content="@ViewBag.Title">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, shrink-to-fit=no">
    <title>@ViewBag.Title - Navition Retail</title>

    <!--begin::Fonts -->
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700|Roboto:300,400,500,600,700">
    <!--end::Fonts -->
    <!--begin::Page Vendors Styles(used by this page) -->
    <!--end::Page Vendors Styles -->
    <!--begin::Global Theme Styles(used by all pages) -->
    <!--begin:: Vendor Plugins -->
    <link href="~/assets/plugins/general/perfect-scrollbar/css/perfect-scrollbar.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/tether/dist/css/tether.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/bootstrap-datepicker/dist/css/bootstrap-datepicker3.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/bootstrap-datetime-picker/css/bootstrap-datetimepicker.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/bootstrap-timepicker/css/bootstrap-timepicker.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/bootstrap-select/dist/css/bootstrap-select.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/bootstrap-switch/dist/css/bootstrap3/bootstrap-switch.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/select2/dist/css/select2.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/ion-rangeslider/css/ion.rangeSlider.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/nouislider/distribute/nouislider.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/owl.carousel/dist/assets/owl.carousel.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/owl.carousel/dist/assets/owl.theme.default.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/dropzone/dist/dropzone.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/quill/dist/quill.snow.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/yaireo/tagify/dist/tagify.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/summernote/dist/summernote.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/bootstrap-markdown/css/bootstrap-markdown.min.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/animate.css/animate.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/toastr/build/toastr.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/dual-listbox/dist/dual-listbox.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/morris.js/morris.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/sweetalert2/dist/sweetalert2.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/socicon/css/socicon.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/plugins/line-awesome/css/line-awesome.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/plugins/flaticon/flaticon.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/plugins/flaticon2/flaticon.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/general/fortawesome/fontawesome-free/css/all.min.css" rel="stylesheet" type="text/css" />
    <!--end:: Vendor Plugins -->

    <link href="~/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />

    <!--begin:: Vendor Plugins for custom pages -->
    <link href="~/assets/plugins/custom/plugins/jquery-ui/jquery-ui.min.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/custom/fullcalendar/core/main.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/custom/fullcalendar/daygrid/main.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/custom/fullcalendar/list/main.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/custom/fullcalendar/timegrid/main.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/custom/jstree/dist/themes/default/style.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/custom/jqvmap/dist/jqvmap.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/plugins/custom/uppy/dist/uppy.min.css" rel="stylesheet" type="text/css" />
    <!--end:: Vendor Plugins for custom pages -->
    <!--end::Global Theme Styles -->
    <!--begin::Layout Skins(used by all pages) -->
    <link href="~/assets/css/skins/header/base/light.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/css/skins/header/menu/light.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/css/skins/brand/dark.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/css/skins/aside/dark.css" rel="stylesheet" type="text/css" />
    <!--end::Layout Skins -->
    <link rel="shortcut icon" href="~/assets/media/logos/favicon.ico" />

    <!-- Global site tag (gtag.js) - Google Analytics -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-R7MYNPH4CK"></script>
    <script>
        window.dataLayer = window.dataLayer || [];
        function gtag() { dataLayer.push(arguments); }
        gtag('js', new Date());

        gtag('config', 'G-R7MYNPH4CK');
    </script>

    <script src="https://cdnjs.cloudflare.com/ajax/libs/babel-polyfill/7.4.0/polyfill.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/exceljs/3.3.1/exceljs.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/1.3.8/FileSaver.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.1.5/jszip.min.js"></script>

    @Html.DevExpress().GetStyleSheets(
        new StyleSheet { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new StyleSheet { ExtensionSuite = ExtensionSuite.Editors },
        new StyleSheet { ExtensionSuite = ExtensionSuite.Report },
        new StyleSheet { ExtensionSuite = ExtensionSuite.TreeList }
    )
    @Html.DevExpress().GetScripts(
        new Script { ExtensionSuite = ExtensionSuite.NavigationAndLayout },
        new Script { ExtensionSuite = ExtensionSuite.Editors },
        new Script { ExtensionSuite = ExtensionSuite.Report },
        new Script { ExtensionSuite = ExtensionSuite.TreeList }
    )

    <link rel="stylesheet" type="text/css" href="https://cdn3.devexpress.com/jslib/19.2.3/css/dx.common.css" />
    <link rel="stylesheet" type="text/css" href="https://cdn3.devexpress.com/jslib/19.2.3/css/dx.light.compact.css" />
    <link href="~/assets/css/theme_change.css" rel="stylesheet" type="text/css" />
    <link href="~/assets/css/site.css" rel="stylesheet" />
    <style>
        @@media (max-width: 576px) {
            .responsive-logo {
                max-height: 30px !important;
            }
        }

        .kt-aside__brand-logo {
            display: flex !important;
            align-items: center !important;
            justify-content: center !important; /* centers horizontally */
            padding: 0.5rem !important;
            max-width: 100% !important;
        }

        .responsive-logo {
            max-height: 37.3px !important; /* limits logo height */
            width: auto !important; /* keep aspect ratio */
            height: auto !important;
            max-width: 100% !important; /* prevents overflow */
        }
    </style>
</head>
<!-- end::Head -->
<!-- begin::Body -->
<body class="kt-quick-panel--right kt-demo-panel--right kt-offcanvas-panel--right kt-header--fixed kt-header-mobile--fixed kt-subheader--enabled kt-subheader--fixed kt-subheader--solid kt-aside--enabled kt-aside--fixed kt-page--loading">
    <!-- begin:: Page -->
    <!-- begin:: Header Mobile -->
    <div id="kt_header_mobile" class="kt-header-mobile  kt-header-mobile--fixed ">
        <div class="kt-header-mobile__logo">
            <a href="index&demo=demo1.html">
                <img alt="Logo" src="~/assets/media/logos/logo.png" class="responsive-logo" />
            </a>
        </div>
        <div class="kt-header-mobile__toolbar">
            <button class="kt-header-mobile__toggler kt-header-mobile__toggler--left" id="kt_aside_mobile_toggler"><span></span></button>
            <button class="kt-header-mobile__toggler" id="kt_header_mobile_toggler"><span></span></button>
            <button class="kt-header-mobile__topbar-toggler" id="kt_header_mobile_topbar_toggler"><i class="flaticon-more"></i></button>
        </div>
    </div>
    <!-- end:: Header Mobile -->
    <div class="kt-grid kt-grid--hor kt-grid--root">
        <div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--ver kt-page">
            <!-- begin:: Aside -->
            <!-- Uncomment this to display the close button of the panel
            <button class="kt-aside-close " id="kt_aside_close_btn"><i class="la la-close"></i></button>
            -->
            <div class="kt-aside  kt-aside--fixed  kt-grid__item kt-grid kt-grid--desktop kt-grid--hor-desktop" id="kt_aside">
                <!-- begin:: Aside -->
                <div class="kt-aside__brand kt-grid__item " id="kt_aside_brand">
                    <div class="kt-aside__brand-logo">
                        <a href="@Url.Action("Index", "Home")">
                            <img alt="Logo" src="~/assets/media/logos/logo.png" class="responsive-logo" />
                        </a>
                    </div>
                    <div class="kt-aside__brand-tools">
                        <button class="kt-aside__brand-aside-toggler" id="kt_aside_toggler">
                            <span>
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24" />
                                        <path d="M5.29288961,6.70710318 C4.90236532,6.31657888 4.90236532,5.68341391 5.29288961,5.29288961 C5.68341391,4.90236532 6.31657888,4.90236532 6.70710318,5.29288961 L12.7071032,11.2928896 C13.0856821,11.6714686 13.0989277,12.281055 12.7371505,12.675721 L7.23715054,18.675721 C6.86395813,19.08284 6.23139076,19.1103429 5.82427177,18.7371505 C5.41715278,18.3639581 5.38964985,17.7313908 5.76284226,17.3242718 L10.6158586,12.0300721 L5.29288961,6.70710318 Z" fill="#000000" fill-rule="nonzero" transform="translate(8.999997, 11.999999) scale(-1, 1) translate(-8.999997, -11.999999) " />
                                        <path d="M10.7071009,15.7071068 C10.3165766,16.0976311 9.68341162,16.0976311 9.29288733,15.7071068 C8.90236304,15.3165825 8.90236304,14.6834175 9.29288733,14.2928932 L15.2928873,8.29289322 C15.6714663,7.91431428 16.2810527,7.90106866 16.6757187,8.26284586 L22.6757187,13.7628459 C23.0828377,14.1360383 23.1103407,14.7686056 22.7371482,15.1757246 C22.3639558,15.5828436 21.7313885,15.6103465 21.3242695,15.2371541 L16.0300699,10.3841378 L10.7071009,15.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" transform="translate(15.999997, 11.999999) scale(-1, 1) rotate(-270.000000) translate(-15.999997, -11.999999) " />
                                    </g>
                                </svg>
                            </span>
                            <span>
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <polygon points="0 0 24 0 24 24 0 24" />
                                        <path d="M12.2928955,6.70710318 C11.9023712,6.31657888 11.9023712,5.68341391 12.2928955,5.29288961 C12.6834198,4.90236532 13.3165848,4.90236532 13.7071091,5.29288961 L19.7071091,11.2928896 C20.085688,11.6714686 20.0989336,12.281055 19.7371564,12.675721 L14.2371564,18.675721 C13.863964,19.08284 13.2313966,19.1103429 12.8242777,18.7371505 C12.4171587,18.3639581 12.3896557,17.7313908 12.7628481,17.3242718 L17.6158645,12.0300721 L12.2928955,6.70710318 Z" fill="#000000" fill-rule="nonzero" />
                                        <path d="M3.70710678,15.7071068 C3.31658249,16.0976311 2.68341751,16.0976311 2.29289322,15.7071068 C1.90236893,15.3165825 1.90236893,14.6834175 2.29289322,14.2928932 L8.29289322,8.29289322 C8.67147216,7.91431428 9.28105859,7.90106866 9.67572463,8.26284586 L15.6757246,13.7628459 C16.0828436,14.1360383 16.1103465,14.7686056 15.7371541,15.1757246 C15.3639617,15.5828436 14.7313944,15.6103465 14.3242754,15.2371541 L9.03007575,10.3841378 L3.70710678,15.7071068 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" transform="translate(9.000003, 11.999999) rotate(-270.000000) translate(-9.000003, -11.999999) " />
                                    </g>
                                </svg>
                            </span>
                        </button>

                        <!--
                        <button class="kt-aside__brand-aside-toggler kt-aside__brand-aside-toggler--left" id="kt_aside_toggler"><span></span></button>
                        -->
                    </div>
                </div>
                <!-- end:: Aside -->
                <!-- begin:: Aside Menu -->
                <div class="kt-aside-menu-wrapper kt-grid__item kt-grid__item--fluid" id="kt_aside_menu_wrapper">
                    <div id="kt_aside_menu" class="kt-aside-menu " data-ktmenu-vertical="1" data-ktmenu-scroll="1" data-ktmenu-dropdown-timeout="500">
                        <ul class="kt-menu__nav ">
                            @*<li class="kt-menu__item " aria-haspopup="true"><a href="index&demo=demo1.html" class="kt-menu__link "><i class="kt-menu__link-icon flaticon-home"></i><span class="kt-menu__link-text">Dashboard</span></a></li>*@
                            @{
                                if (BaseController.IsModuleAssigned("Administration Module", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__section ">
                                        <h4 class="kt-menu__section-text">Administrator</h4>
                                        <i class="kt-menu__section-icon flaticon-more-v2"></i>
                                    </li>
                                    if (BaseController.IsResourceAssigned("Users", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-users"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Users</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Users", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "Users")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Users", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("All", "Users")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">All</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsResourceAssigned("Roles", ViewBag.DesignationTypeEnum)
                                                    || BaseController.IsResourceAssigned("User - Role Mapping", ViewBag.DesignationTypeEnum)
                                                    || BaseController.IsResourceAssigned("Permissions", ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-lock"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Authorization</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                            </a>
                                                            <div class="kt-menu__submenu ">
                                                                <span class="kt-menu__arrow"></span>
                                                                <ul class="kt-menu__subnav">
                                                                    @if (BaseController.IsResourceAssigned("Roles", ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        if (BaseController.IsPermissionAssigned("Roles", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                        {
                                                                            <li class="kt-menu__item " aria-haspopup="true">
                                                                                <a href='@Url.Action("Roles", "Authorization")' class="kt-menu__link ">
                                                                                    <span class="kt-menu__link-icon">
                                                                                        <i class="fa fa-angle-right"></i>
                                                                                    </span>
                                                                                    <span class="kt-menu__link-text">Roles</span>
                                                                                </a>
                                                                            </li>
                                                                        }
                                                                    }
                                                                    @if (BaseController.IsResourceAssigned("User - Role Mapping", ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        if (BaseController.IsPermissionAssigned("User - Role Mapping", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                        {
                                                                            <li class="kt-menu__item " aria-haspopup="true">
                                                                                <a href='@Url.Action("UserRoleMapping", "Authorization")' class="kt-menu__link ">
                                                                                    <span class="kt-menu__link-icon">
                                                                                        <i class="fa fa-angle-right"></i>
                                                                                    </span>
                                                                                    <span class="kt-menu__link-text">User - Role Mapping</span>
                                                                                </a>
                                                                            </li>
                                                                        }
                                                                    }
                                                                    @if (BaseController.IsResourceAssigned("Permissions", ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        if (BaseController.IsPermissionAssigned("Permissions", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                        {
                                                                            <li class="kt-menu__item " aria-haspopup="true">
                                                                                <a href='@Url.Action("Permissions", "Authorization")' class="kt-menu__link ">
                                                                                    <span class="kt-menu__link-icon">
                                                                                        <i class="fa fa-angle-right"></i>
                                                                                    </span>
                                                                                    <span class="kt-menu__link-text">Permissions</span>
                                                                                </a>
                                                                            </li>
                                                                        }
                                                                    }


                                                                </ul>
                                                            </div>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    @*if (BaseController.IsResourceAssigned("Users", ViewBag.DesignationTypeEnum))
                {
                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                            <span class="kt-menu__link-icon">
                                <i class="fa fa-users"></i>
                            </span>
                            <span class="kt-menu__link-text">Designation Hierarchy</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                        </a>
                        <div class="kt-menu__submenu ">
                            <span class="kt-menu__arrow"></span>
                            <ul class="kt-menu__subnav">
                                @if (BaseController.IsPermissionAssigned("Users", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Index", "Users")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">View Hierarchy</span>
                                        </a>
                                    </li>
                                }



                            </ul>
                        </div>
                    </li>
                }*@
                                }

                                if (BaseController.IsModuleAssigned("Master Module", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__section ">
                                        <h4 class="kt-menu__section-text">Masters</h4>
                                        <i class="kt-menu__section-icon flaticon-more-v2"></i>
                                    </li>

                                    if (BaseController.IsResourceAssigned("Products", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <rect x="0" y="0" width="24" height="24"></rect>
                                                            <rect fill="#000000" x="4" y="4" width="7" height="7" rx="1.5"></rect>
                                                            <path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" fill="#000000" opacity="0.3"></path>
                                                        </g>
                                                    </svg>
                                                </span><span class="kt-menu__link-text">Items</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsResourceAssigned("Products", ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Items</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                            </a>
                                                            <div class="kt-menu__submenu ">
                                                                <span class="kt-menu__arrow"></span>
                                                                <ul class="kt-menu__subnav">
                                                                    @if (BaseController.IsPermissionAssigned("Products", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                                            <a href='@Url.Action("New", "Products")' class="kt-menu__link ">
                                                                                <span class="kt-menu__link-icon">
                                                                                    <i class="fa fa-angle-double-right"></i>
                                                                                </span>
                                                                                <span class="kt-menu__link-text">New</span>
                                                                            </a>
                                                                        </li>
                                                                    }
                                                                    @if (BaseController.IsPermissionAssigned("Products", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                                            <a href='@Url.Action("Index", "Products")' class="kt-menu__link ">
                                                                                <span class="kt-menu__link-icon">
                                                                                    <i class="fa fa-angle-double-right"></i>
                                                                                </span>
                                                                                <span class="kt-menu__link-text">View</span>
                                                                            </a>
                                                                        </li>
                                                                    }
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }






                                    if (BaseController.IsResourceAssigned("Product Hierarchy", ViewBag.DesignationTypeEnum))
                                    {
                                        if (BaseController.IsPermissionAssigned("Product Hierarchy", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                        {
                                            <li class="kt-menu__item " aria-haspopup="true">
                                                <a href='@Url.Action("Index", "ProductHierarchy")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-sitemap"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">Category</span>
                                                </a>
                                            </li>
                                        }
                                    }


                                }

                                @*if (BaseController.IsResourceAssigned("PriceLists", ViewBag.DesignationTypeEnum))
            {
                if (BaseController.IsPermissionAssigned("PriceLists", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                {
                    <li class="kt-menu__item " aria-haspopup="true">
                        <a href='@Url.Action("Index", "PriceLists")' class="kt-menu__link ">
                            <span class="kt-menu__link-icon">
                                <i class="fa fa-sitemap"></i>
                            </span>
                            <span class="kt-menu__link-text">Price List</span>
                        </a>
                    </li>
                }
            }*@




                                if (BaseController.IsModuleAssigned("Procurement Module", ViewBag.DesignationTypeEnum))
                                {

                                    if (BaseController.IsResourceAssigned("Suppliers", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Supplementary Manufacturers", ViewBag.DesignationTypeEnum))


                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <rect x="0" y="0" width="24" height="24"></rect>
                                                            <rect fill="#000000" x="4" y="4" width="7" height="7" rx="1.5"></rect>
                                                            <path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" fill="#000000" opacity="0.3"></path>
                                                        </g>
                                                    </svg>
                                                </span><span class="kt-menu__link-text">Suppliers</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @{
                                                        if (BaseController.IsResourceAssigned("Suppliers", ViewBag.DesignationTypeEnum))
                                                        {
                                                            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">Suppliers</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                                </a>
                                                                <div class="kt-menu__submenu ">
                                                                    <span class="kt-menu__arrow"></span>
                                                                    <ul class="kt-menu__subnav">
                                                                        @{
                                                                            if (BaseController.IsPermissionAssigned("Suppliers", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("New", "Suppliers")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-double-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">New</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                            if (BaseController.IsPermissionAssigned("Suppliers", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("Index", "Suppliers")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-double-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">View</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                        }
                                                                    </ul>
                                                                </div>
                                                            </li>
                                                        }

                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }


                                }

                                @*if (BaseController.IsResourceAssigned("MenuHeads", ViewBag.DesignationTypeEnum))
            {
            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                    <span class="kt-menu__link-icon">
                        <i class="fa fa-retweet"></i>
                    </span>
                    <span class="kt-menu__link-text">Menu Heads</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                </a>
                <div class="kt-menu__submenu ">
                    <span class="kt-menu__arrow"></span>
                    <ul class="kt-menu__subnav">

                        @if (BaseController.IsPermissionAssigned("MenuHeads", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("New", "MenuHeads")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">New</span>
                                </a>
                            </li>
                        }
                        @if (BaseController.IsPermissionAssigned("MenuHeads", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                        {

                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("Index", "MenuHeads")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">View</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </li>
            }*@

                                if (BaseController.IsResourceAssigned("MRNS", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-retweet"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Menu Heads</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                        </a>
                                        <div class="kt-menu__submenu ">
                                            <span class="kt-menu__arrow"></span>
                                            <ul class="kt-menu__subnav">

                                                @if (BaseController.IsPermissionAssigned("MRNS", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("New", "MRNS")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">New</span>
                                                        </a>
                                                    </li>
                                                }
                                                @if (BaseController.IsPermissionAssigned("MenuHeads", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {

                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("Index", "MenuHeads")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">View</span>
                                                        </a>
                                                    </li>
                                                }
                                            </ul>
                                        </div>
                                    </li>
                                }

                                @* --- Customers ----------------------------------------------------- *@

                                if (BaseController.IsResourceAssigned("Customers", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-users"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Customers</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                        </a>
                                        <div class="kt-menu__submenu ">
                                            <span class="kt-menu__arrow"></span>
                                            <ul class="kt-menu__subnav">

                                                @if (BaseController.IsPermissionAssigned("Customers", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("New", "Customers")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">New</span>
                                                        </a>
                                                    </li>
                                                }
                                                @if (BaseController.IsPermissionAssigned("Customers", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {

                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("Index", "Customers")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">View</span>
                                                        </a>
                                                    </li>
                                                }
                                            </ul>
                                        </div>
                                    </li>
                                }

                                @* --- Bank Card Promotion ----------------------------------------------------- *@

                                if (BaseController.IsResourceAssigned("Bank Promotion", ViewBag.DesignationTypeEnum))
                                {
                                    if (BaseController.IsPermissionAssigned("Bank Promotion", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item " aria-haspopup="true">
                                            <a href='@Url.Action("Index", "BankPromotion")' class="kt-menu__link ">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-piggy-bank"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Promotions</span>
                                            </a>
                                        </li>
                                    }
                                }

                                if (BaseController.IsResourceAssigned("BillOfMaterials", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-retweet"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Recipes</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                        </a>
                                        <div class="kt-menu__submenu ">
                                            <span class="kt-menu__arrow"></span>
                                            <ul class="kt-menu__subnav">

                                                @if (BaseController.IsPermissionAssigned("BillOfMaterials", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("New", "BillOfMaterials")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">New</span>
                                                        </a>
                                                    </li>
                                                }
                                                @if (BaseController.IsPermissionAssigned("BillOfMaterials", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {

                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("Index", "BillOfMaterials")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">View</span>
                                                        </a>
                                                    </li>
                                                }
                                            </ul>
                                        </div>
                                    </li>
                                }


                                if (BaseController.IsResourceAssigned("Month End", ViewBag.DesignationTypeEnum))
                                {
                                    if (BaseController.IsPermissionAssigned("Month End", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item " aria-haspopup="true">
                                            <a href='@Url.Action("New", "MonthEnd")' class="kt-menu__link ">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-piggy-bank"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Month End</span>
                                            </a>
                                        </li>
                                    }
                                }

                                if (BaseController.IsModuleAssigned("Transaction Module", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__section ">
                                        <h4 class="kt-menu__section-text">Transaction</h4>
                                        <i class="kt-menu__section-icon flaticon-more-v2"></i>
                                    </li>

                                    if (BaseController.IsResourceAssigned("Purchase Orders", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-atom"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Purchase order</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Purchase Orders", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "PurchaseOrders")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Purchase Orders", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "PurchaseOrders")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @*@if (BaseController.IsPermissionAssigned("Purchase Orders", Permission.PermissionEnum.Approve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Approval", "PurchaseOrders")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Approval</span>
                                        </a>
                                    </li>
                                }*@
                                                </ul>
                                            </div>
                                        </li>
                                    }

                                    if (BaseController.IsResourceAssigned("Goods Received Notes", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-truck-moving"></i>
                                                </span>
                                                <span class="kt-menu__link-text"> Good Receive Note (GRNs)</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Goods Received Notes", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "GRNS")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @*@if (BaseController.IsPermissionAssigned("Goods Received Notes", Permission.PermissionEnum.Approve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Approval", "GRNS")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Approval</span>
                                        </a>
                                    </li>
                                }*@
                                                </ul>
                                            </div>
                                        </li>
                                    }

                                    if (BaseController.IsResourceAssigned("Good Issue Note", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-book-open"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Good Issue Note</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Good Issue Note", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "GoodIssueNote")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Good Issue Note", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "GoodIssueNote")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }

                                                </ul>
                                            </div>
                                        </li>
                                    }


                                    if (BaseController.IsResourceAssigned("Purchase Returns", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-retweet"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Supplier Return Note (SRN)</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">

                                                    @if (BaseController.IsPermissionAssigned("Purchase Returns", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "PurchaseReturns")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Purchase Returns", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {

                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "PurchaseReturns")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }

                                    if (BaseController.IsResourceAssigned("Stock Adjustments", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-blender"></i>
                                                </span>
                                                <span class="kt-menu__link-text">
                                                    Stock Adjustment (SAN)
                                                </span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Stock Adjustments", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "StockAdjustments")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-double-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Stock Adjustments", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "StockAdjustments")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-double-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @*@if (BaseController.IsPermissionAssigned("Stock Adjustments", Permission.PermissionEnum.Approve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Approval", "StockAdjustments")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Approval</span>
                                        </a>
                                    </li>
                                }*@
                                                </ul>
                                            </div>
                                        </li>
                                    }


                                    if (BaseController.IsResourceAssigned("InternalOrders", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-retweet"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Wastage</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">

                                                    @if (BaseController.IsPermissionAssigned("InternalOrders", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "InternalOrders")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("InternalOrders", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {

                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "InternalOrders")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }


                                    if (BaseController.IsResourceAssigned("InternalOrders", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-cogs"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Production Note</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">

                                                    @if (BaseController.IsPermissionAssigned("InternalOrders", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "ProductionNotes")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("InternalOrders", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {

                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "ProductionNotes")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }

                                    @*if (BaseController.IsResourceAssigned("BillOfOperations", ViewBag.DesignationTypeEnum))
                {
                <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                    <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                        <span class="kt-menu__link-icon">
                            <i class="fa fa-retweet"></i>
                        </span>
                        <span class="kt-menu__link-text">Bill Of Operations</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                    </a>
                    <div class="kt-menu__submenu ">
                        <span class="kt-menu__arrow"></span>
                        <ul class="kt-menu__subnav">

                            @if (BaseController.IsPermissionAssigned("BillOfOperations", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                            {
                                <li class="kt-menu__item " aria-haspopup="true">
                                    <a href='@Url.Action("New", "BillOfOperations")' class="kt-menu__link ">
                                        <span class="kt-menu__link-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                        <span class="kt-menu__link-text">New</span>
                                    </a>
                                </li>
                            }
                            @if (BaseController.IsPermissionAssigned("BillOfOperations", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                            {

                                <li class="kt-menu__item " aria-haspopup="true">
                                    <a href='@Url.Action("Index", "BillOfOperations")' class="kt-menu__link ">
                                        <span class="kt-menu__link-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                        <span class="kt-menu__link-text">View</span>
                                    </a>
                                </li>
                            }
                        </ul>
                    </div>
                </li>
                }*@


                                    @*if (BaseController.IsResourceAssigned("InternalOrders", ViewBag.DesignationTypeEnum))
                {
                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                            <span class="kt-menu__link-icon">
                                <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                                    <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                        <rect x="0" y="0" width="24" height="24"></rect>
                                        <rect fill="#000000" x="4" y="4" width="7" height="7" rx="1.5"></rect>
                                        <path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" fill="#000000" opacity="0.3"></path>
                                    </g>
                                </svg>
                            </span><span class="kt-menu__link-text">Wastage</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                        </a>
                        <div class="kt-menu__submenu ">
                            <span class="kt-menu__arrow"></span>
                            <ul class="kt-menu__subnav">
                                @if (BaseController.IsResourceAssigned("InternalOrders", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Items</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                        </a>
                                        <div class="kt-menu__submenu ">
                                            <span class="kt-menu__arrow"></span>
                                            <ul class="kt-menu__subnav">
                                                @if (BaseController.IsPermissionAssigned("Internal Orders", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("New", "InternalOrders")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-double-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">New</span>
                                                        </a>
                                                    </li>
                                                }
                                                @if (BaseController.IsPermissionAssigned("InternalOrders", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("Index", "InternalOrders")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-double-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">View</span>
                                                        </a>
                                                    </li>
                                                }
                                            </ul>
                                        </div>
                                    </li>
                                }
                            </ul>
                        </div>
                    </li>
                }*@



                                    @*if (BaseController.IsResourceAssigned("Stock Transfers", ViewBag.DesignationTypeEnum))
                {
                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                            <span class="kt-menu__link-icon">
                                <i class="fa fa-box"></i>
                            </span><span class="kt-menu__link-text">Stock Transfer</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                        </a>
                        <div class="kt-menu__submenu ">
                            <span class="kt-menu__arrow"></span>
                            <ul class="kt-menu__subnav">
                                @if (BaseController.IsPermissionAssigned("Stock Transfers", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("New", "StockTransfer")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">New</span>
                                        </a>
                                    </li>
                                }
                                @if (BaseController.IsPermissionAssigned("Stock Transfers", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Index", "StockTransfer")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">View</span>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </div>
                    </li>
                }*@

                                }

                                if (BaseController.IsModuleAssigned("Transaction Module", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__section ">
                                        <h4 class="kt-menu__section-text">Reports</h4>
                                        <i class="kt-menu__section-icon flaticon-more-v2"></i>
                                    </li>

                                    if (BaseController.IsResourceAssigned("Stock Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("GRN Summary Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("GRN Product Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Shipment QC Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Shipment Costing Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("MRN Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("GRN Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Movement Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Valuation Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Reconciliation Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Raw Material Planning Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Movement Summary Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Summary Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Transfer Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("PO Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("PO Charge Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("PO Summary Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Movement Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Valuation Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Reconciliation Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Movement Summary Report", ViewBag.DesignationTypeEnum
                                    || BaseController.IsResourceAssigned("Stock Summary Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Compound Stock Reconciliation Detail Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Stock Reconciliation Summary Report", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Supplier Return Note Report", ViewBag.DesignationTypeEnum)))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover" hidden>
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-atlas"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Reports</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">

                                                    @*@if (BaseController.IsPermissionAssigned("GRN Summary Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        @{
                                            if (System.Configuration.ConfigurationManager.AppSettings["Client"] == Enums.CLIENT_SOTHYS)
                                            {
                                                <a href='@Url.Action("GRNSummaryDetailReport", "Reports")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">GRN Summary Report</span>
                                                </a>
                                            }
                                            else
                                            {
                                                <a href='@Url.Action("GRNSummaryReport", "Reports")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">GRN Summary Report</span>
                                                </a>
                                            }
                                        }
                                    </li>
                                }*@
                                                    @*@if (BaseController.IsPermissionAssigned("GRN Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        @{
                                            if (System.Configuration.ConfigurationManager.AppSettings["Client"] == Enums.CLIENT_SOTHYS)
                                            {
                                                <a href='@Url.Action("GoodsReceiveNoteDetail_Sothys", "Reports")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">GRN Detail Report</span>
                                                </a>
                                            }
                                            else
                                            {
                                                <a href='@Url.Action("GRNDetailReport", "Reports")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">GRN Detail Report</span>
                                                </a>
                                            }
                                        }
                                    </li>
                                }*@
                                                    @*@if (BaseController.IsPermissionAssigned("GRN Product Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Viewer", "ReportDesigner")?id=13' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">GRN Product Detail Report</span>
                                        </a>
                                    </li>
                                }*@

                                                    @if (BaseController.IsPermissionAssigned("GRN Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">

                                                            <a href='@Url.Action("GRNListingReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">GRN Listing Report</span>
                                                            </a>
                                                        </li>
                                                    }


                                                    @if (BaseController.IsPermissionAssigned("Issue Note Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">

                                                            <a href='@Url.Action("IssueNoteReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Issue Note Report</span>
                                                            </a>
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("Invoice Wise Credit Details", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("InvoiceWiseCreditDetails", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Invoice Wise Credit Details</span>
                                                            </a>
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("Location Wise Sales Summery", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("LocationWiseSalesSummary", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Location Wise Sales Summery</span>
                                                            </a>
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("Menu Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("MenuDetailReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Menu Detail Report</span>
                                                            </a>
                                                        </li>
                                                    }



                                                    @if (BaseController.IsPermissionAssigned("Menu Wise Sale", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("MenuWiseSale", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Menu Wise Sale</span>
                                                            </a>
                                                        </li>
                                                    }



                                                    @if (BaseController.IsPermissionAssigned("PO Listing Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("POListingReport", "Reports")?id=8' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">PO Listing Report</span>
                                                            </a>
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("PO Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("PurchaseOrder_Sothys", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">PO Detail Report</span>

                                                            </a>
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("Receipe Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("ReceipeDetailReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Receipe Detail Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @*@if (BaseController.IsPermissionAssigned("PO Charge Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Viewer", "ReportDesigner")?id=8' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">PO Charge Detail Report</span>
                                        </a>
                                    </li>
                                }*@
                                                    @*@if (BaseController.IsPermissionAssigned("PO Summary Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        @{
                                            if (System.Configuration.ConfigurationManager.AppSettings["Client"] == Enums.CLIENT_SOTHYS)
                                            {
                                                <a href='@Url.Action("PurchaseOrderSummary_Sothys", "Reports")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">PO Summary Report</span>

                                                </a>

                                            }
                                            else
                                            {
                                                <a href='@Url.Action("Viewer", "ReportDesigner")?id=14' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">PO Summary Report</span>
                                                </a>

                                            }
                                        }
                                    </li>

                                }*@

                                                    @if (BaseController.IsPermissionAssigned("Stock Adjustment Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("StockAdjustment", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Stock Adjustment Report</span>

                                                            </a>
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("Stock Balance Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("StockBalanceReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Stock Balance Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @*@if (BaseController.IsPermissionAssigned("Stock Movement Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        @{
                                            if (System.Configuration.ConfigurationManager.AppSettings["Client"] == Enums.CLIENT_SOTHYS)
                                            {
                                                <a href='@Url.Action("StockMovementReport", "Reports")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">Stock Movement Report</span>

                                                </a>

                                            }
                                            else
                                            {
                                                <a href='@Url.Action("StockMovement", "Reports")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">Stock Movement Report</span>
                                                </a>

                                            }
                                        }
                                    </li>
                                }*@
                                                    <!--@if (BaseController.IsPermissionAssigned("Stock Valuation Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                            {
                                <li class="kt-menu__item " aria-haspopup="true">
                                    <a href='@Url.Action("StockValuation", "Reports")' class="kt-menu__link ">
                                        <span class="kt-menu__link-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                        <span class="kt-menu__link-text">Stock Valuation Report</span>
                                    </a>
                                </li>
                            }-->
                                                    @if (BaseController.IsPermissionAssigned("Stock Valuation Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("StockValuationReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Stock Valuation Report</span>
                                                            </a>
                                                        </li>
                                                    }


                                                    @if (BaseController.IsPermissionAssigned("Stock Variance Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("StockVariance", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Stock Variance Report</span>
                                                            </a>
                                                        </li>
                                                    }

                                                    @*@if (BaseController.IsPermissionAssigned("Stock Reconciliation Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("StockReconciliationDetail", "Reports")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Stock Reconciliation Detail Report</span>
                                        </a>
                                    </li>
                                }*@
                                                    @*@if (BaseController.IsPermissionAssigned("Stock Movement Summary Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("StockMovementSummary", "Reports")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Stock Movement Summary Report</span>
                                        </a>
                                    </li>
                                }*@
                                                    @*@if (BaseController.IsPermissionAssigned("Stock Summary Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("StockSummary", "Reports")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Stock Summary Report</span>
                                        </a>
                                    </li>
                                }*@
                                                    @*@if (BaseController.IsPermissionAssigned("Compound Stock Reconciliation Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))


                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("CompoundStockReconciliationDetails", "Reports")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Compound Stock Reconciliation Detail Report</span>
                                        </a>
                                    </li>
                                }*@
                                                    @*@if (BaseController.IsPermissionAssigned("Stock Reconciliation Summary Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                       {
                                           <li class="kt-menu__item " aria-haspopup="true">
                                               <a href='@Url.Action("StockReconciliationSummary", "Reports")' class="kt-menu__link ">
                                                   <span class="kt-menu__link-icon">
                                                       <i class="fa fa-angle-right"></i>
                                                   </span>
                                                   <span class="kt-menu__link-text">Stock Reconciliation Summary Report</span>
                                               </a>
                                           </li> @if (BaseController.IsPermissionAssigned("Supplier Return Note Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("SupplierReturnNote", "Reports")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Supplier Return Note Report</span>
                                        </a>
                                    </li>
                                }
                                       }
                                                    *@
                                                    @if (BaseController.IsPermissionAssigned("SRN Listing Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("SRNListingReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Supplier Return Note Listing Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Wastage Listing Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("WastageListing", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Wastage Listing Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Invoice Wise Sales Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("InvoiceWiseSalesSummary", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Invoice Wise Sales Summary</span>
                                                            </a>
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("Invoice Wise Discount Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("InvoiceWiseDiscountSummary", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Invoice Wise Discount Summary</span>
                                                            </a>
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("Invoice Wise Credit Card Details", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("InvoiceWiseCreditCardDetails", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Invoice Wise Credit Card Details</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @*@if (BaseController.IsPermissionAssigned("Counter Wise Sale Report - Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("CounterWiseSaleSummary", "Reports")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Counter Wise Sale Report - Summary</span>
                                        </a>
                                    </li>
                                }*@


                                                </ul>
                                            </div>
                                        </li>
                                    }




                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <rect x="0" y="0" width="24" height="24"></rect>
                                                            <rect fill="#000000" x="4" y="4" width="7" height="7" rx="1.5"></rect>
                                                            <path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" fill="#000000" opacity="0.3"></path>
                                                        </g>
                                                    </svg>
                                                </span><span class="kt-menu__link-text">Reports</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @{
                                                        if (BaseController.IsResourceAssigned("Reports", ViewBag.DesignationTypeEnum))
                                                        {
                                                            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">Transaction Listing Reports</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                                </a>
                                                                <div class="kt-menu__submenu ">
                                                                    <span class="kt-menu__arrow"></span>
                                                                    <ul class="kt-menu__subnav">
                                                                        @{
                                                                            if (BaseController.IsPermissionAssigned("GRN Listing Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">

                                                                                    <a href='@Url.Action("GRNListingReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">GRN Listing Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                            if (BaseController.IsPermissionAssigned("PO Listing Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("POListingReport", "Reports")?id=8' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">PO Listing Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                            if (BaseController.IsPermissionAssigned("SRN Listing Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("SRNListingReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Supplier Return Note Listing Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                            if (BaseController.IsPermissionAssigned("Wastage Listing Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("WastageListing", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Wastage Listing Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                        }
                                                                    </ul>
                                                                </div>
                                                            </li>

                                                            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">Stock Reports </span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                                </a>
                                                                <div class="kt-menu__submenu ">
                                                                    <span class="kt-menu__arrow"></span>
                                                                    <ul class="kt-menu__subnav">
                                                                        @{
                                                                            if (BaseController.IsPermissionAssigned("Stock Adjustment Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("StockAdjustment", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Stock Adjustment Report</span>

                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Stock Balance Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("StockBalanceReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Stock Balance Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Stock Valuation Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("StockValuationReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Stock Valuation Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }


                                                                            if (BaseController.IsPermissionAssigned("Stock Variance Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("StockVariance", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Stock Variance Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Stock Card Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("StockCardReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Stock Card Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                        }
                                                                    </ul>
                                                                </div>
                                                            </li>

                                                            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">Sales Reports</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                                </a>
                                                                <div class="kt-menu__submenu ">
                                                                    <span class="kt-menu__arrow"></span>
                                                                    <ul class="kt-menu__subnav">
                                                                        @{


                                                                            @*if (BaseController.IsPermissionAssigned("Counter Wise Sale Report - Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                        {
                                                            <li class="kt-menu__item " aria-haspopup="true">

                                                                <a href='@Url.Action("CounterWiseSalesSummaryReport", "Reports")' class="kt-menu__link ">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">Counter Wise Sale Report - Summary</span>
                                                                </a>
                                                            </li>
                                                        }*@


                                                                            @*if (BaseController.IsPermissionAssigned("Issue Note Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                        {
                                                            <li class="kt-menu__item " aria-haspopup="true">

                                                                <a href='@Url.Action("IssueNoteReport", "Reports")' class="kt-menu__link ">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">Issue Note Report</span>
                                                                </a>
                                                            </li>
                                                        }*@

                                                                            if (BaseController.IsPermissionAssigned("Bill Cancellation Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("BillCancellationDetailReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Bill Cancellation Detail Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }
                                                                            if (BaseController.IsPermissionAssigned("Bill Cancellation Summery Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("BillCancellationSummeryReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Bill Cancellation Summery Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }



                                                                            if (BaseController.IsPermissionAssigned("Counter Wise sale Report - Details", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("CounterWiseSaleReportDetails", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Counter Wise sale Report - Details</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Counter Wise Sale Report - Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("CounterWiseSaleSummary", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Counter Wise Sale Report - Summary</span>
                                                                                    </a>
                                                                                </li>
                                                                            }


                                                                            if (BaseController.IsPermissionAssigned("Credit Card Report - Details", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("CreditCardReportDetails", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Credit Card Report - Details</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            @*if (BaseController.IsPermissionAssigned("Delivery Report - Detail", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                        {
                                                            <li class="kt-menu__item " aria-haspopup="true">
                                                                <a href='@Url.Action("DeliveryReportDetail", "Reports")' class="kt-menu__link ">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">Delivery Report - Detail</span>
                                                                </a>
                                                            </li>
                                                        }*@

                                                                            if (BaseController.IsPermissionAssigned("Delivery Report - Details", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("DeliveryReportDetails", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Delivery Report - Details</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Discount Details Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("DiscountDetailsReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Discount Details Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Invoice Wise Credit Card Details", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("InvoiceWiseCreditCardDetails", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Invoice Wise Credit Card Details</span>
                                                                                    </a>
                                                                                </li>
                                                                            }



                                                                            if (BaseController.IsPermissionAssigned("Invoice Wise Credit Details", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("InvoiceWiseCreditDetails", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Invoice Wise Credit Details</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Invoice Wise Discount Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("InvoiceWiseDiscountSummary", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Invoice Wise Discount Summary</span>
                                                                                    </a>
                                                                                </li>
                                                                            }


                                                                            if (BaseController.IsPermissionAssigned("Invoice Wise sale Report - Details", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("InvoiceWiseSaleReportDetails", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Invoice Wise sale Report - Details</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Invoice Wise Sales Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("InvoiceWiseSalesSummary", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Invoice Wise Sales - Summary</span>
                                                                                    </a>
                                                                                </li>
                                                                            }






                                                                            if (BaseController.IsPermissionAssigned("Location Wise Sales Summery", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("LocationWiseSalesSummary", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Location Wise Sales - Summary</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            if (BaseController.IsPermissionAssigned("Menu Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("MenuDetailReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Menu Detail Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }



                                                                            if (BaseController.IsPermissionAssigned("Menu Wise Sale", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("MenuWiseSale", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Menu Wise Sale Report - Detail</span>
                                                                                    </a>
                                                                                </li>
                                                                            }


                                                                            if (BaseController.IsPermissionAssigned("Menu Wise Sale Report - Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("MenuWiseSalesSummaryReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Menu Wise Sale Report - Summary</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            @*if (BaseController.IsPermissionAssigned("PO Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                        {
                                                            <li class="kt-menu__item " aria-haspopup="true">
                                                                <a href='@Url.Action("PurchaseOrder_Sothys", "Reports")' class="kt-menu__link ">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">PO Detail Report</span>

                                                                </a>
                                                            </li>
                                                        }*@

                                                                            if (BaseController.IsPermissionAssigned("Receipe Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                            {
                                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                                    <a href='@Url.Action("ReceipeDetailReport", "Reports")' class="kt-menu__link ">
                                                                                        <span class="kt-menu__link-icon">
                                                                                            <i class="fa fa-angle-right"></i>
                                                                                        </span>
                                                                                        <span class="kt-menu__link-text">Receipe Detail Report</span>
                                                                                    </a>
                                                                                </li>
                                                                            }

                                                                            @*if (BaseController.IsPermissionAssigned("Daily Sales Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                        {
                                                            <li class="kt-menu__item " aria-haspopup="true">
                                                                <a href='@Url.Action("DailySalesSummaryReport", "Reports")' class="kt-menu__link ">
                                                                    <span class="kt-menu__link-icon">
                                                                        <i class="fa fa-angle-right"></i>
                                                                    </span>
                                                                    <span class="kt-menu__link-text">Daily Sales Summary Report</span>
                                                                </a>
                                                            </li>
                                                        }*@






                                                                        }
                                                                    </ul>
                                                                </div>
                                                            </li>

                                                        }

                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }


                                }


                                if (BaseController.IsResourceAssigned("GRN Listing Report", ViewBag.DesignationTypeEnum)
                                || BaseController.IsResourceAssigned("Counter Wise Sale Report - Summary", ViewBag.DesignationTypeEnum)
                            )

                                {
                                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-atlas"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Centrally Reports</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                        </a>
                                        <div class="kt-menu__submenu ">
                                            <span class="kt-menu__arrow"></span>
                                            <ul class="kt-menu__subnav">


                                                @if (BaseController.IsPermissionAssigned("GRN Listing Report - Centrally", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">

                                                        <a href='@Url.Action("GRNListingCentrallyReport", "Reports")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">GRN Listing Report (Centrally)</span>
                                                        </a>
                                                    </li>
                                                }

                                                @if (BaseController.IsPermissionAssigned("Counter Wise Sale Report - Summary", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("CounterWiseSaleSummaryCentrally", "Reports")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-angle-right"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">Counter Wise Sale Report - Summary (Centrally)</span>
                                                        </a>
                                                    </li>
                                                }

                                            </ul>
                                        </div>
                                    </li>
                                }



                                if (BaseController.IsModuleAssigned("Finance Module", ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__section ">
                                        <h4 class="kt-menu__section-text">Finance Module</h4>
                                        <i class="kt-menu__section-icon flaticon-more-v2"></i>
                                    </li>
                                    if (BaseController.IsResourceAssigned("Chart Of Accounts", ViewBag.DesignationTypeEnum))
                                    {
                                        if (BaseController.IsPermissionAssigned("Chart Of Accounts", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                        {
                                            <li class="kt-menu__item " aria-haspopup="true">
                                                <a href='@Url.Action("Index", "ChartOfAccounts")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-chart-pie"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">Chart Of Account</span>
                                                </a>
                                            </li>
                                        }
                                    }
                                    if (BaseController.IsResourceAssigned("General Ledger Lines", ViewBag.DesignationTypeEnum))
                                    {
                                        if (BaseController.IsPermissionAssigned("General Ledger Lines", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                        {
                                            <li class="kt-menu__item " aria-haspopup="true">
                                                <a href='@Url.Action("Index", "GeneralLedgerLines")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-file-invoice-dollar"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">General Ledger Lines</span>
                                                </a>
                                            </li>
                                        }
                                    }
                                    if (BaseController.IsResourceAssigned("Default Account Mapping", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-university"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Default Account Mapping</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Default Account Mapping", Permission.PermissionEnum.Update, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Update", "AccountMappings")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Update</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Default Account Mapping", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "AccountMappings")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    if (BaseController.IsResourceAssigned("Pay Books", ViewBag.DesignationTypeEnum))
                                    {
                                        if (BaseController.IsPermissionAssigned("Pay Books", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                        {
                                            <li class="kt-menu__item " aria-haspopup="true">
                                                <a href='@Url.Action("Index", "PayBooks")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-book-open"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">Pay Book</span>
                                                </a>
                                            </li>
                                        }
                                    }
                                    if (BaseController.IsResourceAssigned("Bank Adjustments", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-university"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Bank Adjustment</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Bank Adjustments", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "BankAdjustments")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Bank Adjustments", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "BankAdjustments")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    if (BaseController.IsResourceAssigned("Bank Reconciliations", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-university"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Bank Reconciliations</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Bank Reconciliations", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "BankReconciliations")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Bank Reconciliations", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "BankReconciliations")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    if (BaseController.IsResourceAssigned("Manual Journals", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fas fa-book"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Journal Voucher</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Manual Journals", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "ManualJournals")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Manual Journals", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "ManualJournals")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    if (BaseController.IsResourceAssigned("Payment Advices", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-blender"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Payable Invoices</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Payment Advices", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "PaymentAdvices")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-double-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Payment Advices", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "PaymentAdvices")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-double-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    @*if (BaseController.IsResourceAssigned("Receipt Advices", ViewBag.DesignationTypeEnum))
                {
                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                            <span class="kt-menu__link-icon">
                                <i class="fa fa-blender"></i>
                            </span>
                            <span class="kt-menu__link-text">Receipt Advices</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                        </a>
                        <div class="kt-menu__submenu ">
                            <span class="kt-menu__arrow"></span>
                            <ul class="kt-menu__subnav">
                                @if (BaseController.IsPermissionAssigned("Receipt Advices", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("New", "ReceiptAdvices")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-double-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">New</span>
                                        </a>
                                    </li>
                                }
                                @if (BaseController.IsPermissionAssigned("Receipt Advices", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Index", "ReceiptAdvices")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-double-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">View</span>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </div>
                    </li>
                }*@
                                    if (BaseController.IsResourceAssigned("Outbound Payments", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fas fa-money-bill"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Payment Vouchers</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Outbound Payments", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "OutboundPayments")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Outbound Payments", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "OutboundPayments")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    @*if (BaseController.IsResourceAssigned("Inbound Receipts", ViewBag.DesignationTypeEnum))
                {
                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                            <span class="kt-menu__link-icon">
                                <i class="fas fa-receipt"></i>
                            </span>
                            <span class="kt-menu__link-text">Inbound Receipts</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                        </a>
                        <div class="kt-menu__submenu ">
                            <span class="kt-menu__arrow"></span>
                            <ul class="kt-menu__subnav">
                                @if (BaseController.IsPermissionAssigned("Inbound Receipts", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("New", "InboundReceipts")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">New</span>
                                        </a>
                                    </li>
                                }
                                @if (BaseController.IsPermissionAssigned("Inbound Receipts", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Index", "InboundReceipts")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">View</span>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </div>
                    </li>
                }*@
                                    if (BaseController.IsResourceAssigned("Financial Statements", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Financial Statements - Reports", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-blender"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Financial Statements</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Financial Statements", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "FinancialStatements")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-double-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Financial Statements", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "FinancialStatements")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-double-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Financial Statements - Reports", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Report", "FinancialStatements")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-double-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Reports</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    if (BaseController.IsResourceAssigned("Petty Cash", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fas fa-wallet"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Petty Cash</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Petty Cash", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("New", "PettyCashOperations")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">New</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Petty Cash", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Index", "PettyCashOperations")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">View</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    if (BaseController.IsResourceAssigned("Trial Balance Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Outbound Payment Detail Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Inbound Receipt Detail Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Payment Advice Detail Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Receipt Advice Detail Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("GL Detail Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("GL Detail Report Generic", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Supplier Aging Detail Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Supplier Aging Summary Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Customer Ledger Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Supplier Ledger Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Customer Aging Detail Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Customer Aging Summary Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Bank Register Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Vat Report", ViewBag.DesignationTypeEnum)
                                        || BaseController.IsResourceAssigned("Bank Reconciliation Detail Report", ViewBag.DesignationTypeEnum))

                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-atlas"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Reports</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsPermissionAssigned("Outbound Payment Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("OutboundPaymentDetailReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Payment Voucher Detail Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    <!--  if (BaseController.IsPermissionAssigned("Inbound Receipt Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                            {
                                <li class="kt-menu__item " aria-haspopup="true">
                                    <a href='@Url.Action("InboundReceiptDetailReport", "Reports")' class="kt-menu__link ">
                                        <span class="kt-menu__link-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                        <span class="kt-menu__link-text">Inbound Receipt Detail Report</span>
                                    </a>
                                </li>
                            }-->
                                                    @if (BaseController.IsPermissionAssigned("Payment Advice Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("PaymentAdviceDetailReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Payable Invoice Detail Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Receipt Advice Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("ReceiptAdviceDetailReport", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Receipt Advice Detail Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    <!--if (BaseController.IsPermissionAssigned("Trial Balance Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                            {
                                <li class="kt-menu__item " aria-haspopup="true">


                                    {
                                        if (System.Configuration.ConfigurationManager.AppSettings["Client"] == Enums.CLIENT_SOTHYS)
                                        {
                                            <a href='@Url.Action("TrialBalance", "Reports")' class="kt-menu__link ">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-angle-right"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Trial Balance Report</span>
                                            </a>
                                        }
                                        else
                                        {
                                            <a href='@Url.Action("Viewer", "ReportDesigner")?id=23' class="kt-menu__link ">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-angle-right"></i>
                                                </span>
                                                <span class="kt-menu__link-text">Trial Balance Report</span>
                                            </a>
                                        }
                                    }
                                </li>


                            }-->
                                                    @if (BaseController.IsPermissionAssigned("GL Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("GLDetails", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">GL Detail Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("GL Detail Report Generic", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            @{
                                                                if (System.Configuration.ConfigurationManager.AppSettings["Client"] == Enums.CLIENT_SOTHYS)
                                                                {
                                                                    <a href='@Url.Action("GLDetailReport_Sothys", "Reports")' class="kt-menu__link ">
                                                                        <span class="kt-menu__link-icon">
                                                                            <i class="fa fa-angle-right"></i>
                                                                        </span>
                                                                        <span class="kt-menu__link-text">GL Detail Report - Generic</span>

                                                                    </a>

                                                                }
                                                            }
                                                        </li>
                                                    }

                                                    @if (BaseController.IsPermissionAssigned("Supplier Aging Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("SupplierAgingDetails", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Supplier Aging Detail Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Supplier Aging Summary Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("SupplierAgingSummary", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Supplier Aging Summary Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    <!--if (BaseController.IsPermissionAssigned("Customer Aging Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                            {
                                <li class="kt-menu__item " aria-haspopup="true">
                                    <a href='@Url.Action("CustomerAgingDetails", "Reports")' class="kt-menu__link ">
                                        <span class="kt-menu__link-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                        <span class="kt-menu__link-text">Customer Aging Detail Report</span>
                                    </a>
                                </li>
                            }
                            if (BaseController.IsPermissionAssigned("Customer Aging Summary Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                            {
                                <li class="kt-menu__item " aria-haspopup="true">
                                    <a href='@Url.Action("CustomerAgingSummary", "Reports")' class="kt-menu__link ">
                                        <span class="kt-menu__link-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                        <span class="kt-menu__link-text">Customer Aging Summary Report</span>
                                    </a>
                                </li>
                            }
                            if (BaseController.IsPermissionAssigned("Customer Ledger Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                            {
                                <li class="kt-menu__item " aria-haspopup="true">
                                    <a href='@Url.Action("CustomerLedger", "Reports")' class="kt-menu__link ">
                                        <span class="kt-menu__link-icon">
                                            <i class="fa fa-angle-right"></i>
                                        </span>
                                        <span class="kt-menu__link-text">Customer Ledger Report</span>
                                    </a>
                                </li>
                            }-->
                                                    @if (BaseController.IsPermissionAssigned("Supplier Ledger Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("SupplierLedger", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Supplier Ledger Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Bank Register Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("Viewer", "ReportDesigner")?id=72' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Bank Register Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Bank Reconciliation Summary Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("BankReconciliationSummary", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Bank Reconciliation Summary Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Vat Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("VatAndSVat", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Vat Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                    @if (BaseController.IsPermissionAssigned("Bank Reconciliation Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                            <a href='@Url.Action("BankReconciliationDetail", "Reports")' class="kt-menu__link ">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Bank Reconciliation Detail Report</span>
                                                            </a>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                    @*if (BaseController.IsResourceAssigned("GL Detail Report", ViewBag.DesignationTypeEnum))
                {
                    <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                        <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                            <span class="kt-menu__link-icon">
                                <i class="fa fa-atlas"></i>
                            </span>
                            <span class="kt-menu__link-text">Data Dumps</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                        </a>
                        <div class="kt-menu__submenu ">
                            <span class="kt-menu__arrow"></span>
                            <ul class="kt-menu__subnav">
                                @if (BaseController.IsPermissionAssigned("GL Detail Report", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("GLDetails", "DataDumps")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">GL Details</span>
                                        </a>
                                    </li>
                                }
                            </ul>
                        </div>
                    </li>
                }*@
                                    if (BaseController.IsResourceAssigned("Tax Types", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Tax Groups", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Charge Groups", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Charge Information", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Currency Exchange Rates", ViewBag.DesignationTypeEnum)
                                    || BaseController.IsResourceAssigned("Cash & Bank Accounts", ViewBag.DesignationTypeEnum))
                                    {
                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                <span class="kt-menu__link-icon">
                                                    <i class="fa fa-cogs"></i>
                                                </span><span class="kt-menu__link-text">Settings</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                            </a>
                                            <div class="kt-menu__submenu ">
                                                <span class="kt-menu__arrow"></span>
                                                <ul class="kt-menu__subnav">
                                                    @if (BaseController.IsResourceAssigned("Tax Types", ViewBag.DesignationTypeEnum)
                                                        || BaseController.IsResourceAssigned("Tax Groups", ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-angle-right"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Tax Configuration</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                            </a>
                                                            <div class="kt-menu__submenu ">
                                                                <span class="kt-menu__arrow"></span>
                                                                <ul class="kt-menu__subnav">
                                                                    @if (BaseController.IsResourceAssigned("Tax Types", ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        if (BaseController.IsPermissionAssigned("Tax Types", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                        {
                                                                            <li class="kt-menu__item " aria-haspopup="true">
                                                                                <a href='@Url.Action("Index", "TaxTypes")' class="kt-menu__link ">
                                                                                    <span class="kt-menu__link-icon">
                                                                                        <i class="fa fa-angle-double-right"></i>
                                                                                    </span>
                                                                                    <span class="kt-menu__link-text">Tax Types</span>
                                                                                </a>
                                                                            </li>
                                                                        }
                                                                    }
                                                                    @if (BaseController.IsResourceAssigned("Tax Groups", ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        if (BaseController.IsPermissionAssigned("Tax Groups", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                        {
                                                                            <li class="kt-menu__item " aria-haspopup="true">
                                                                                <a href='@Url.Action("Index", "TaxGroups")' class="kt-menu__link ">
                                                                                    <span class="kt-menu__link-icon">
                                                                                        <i class="fa fa-angle-double-right"></i>
                                                                                    </span>
                                                                                    <span class="kt-menu__link-text">Tax Groups</span>
                                                                                </a>
                                                                            </li>
                                                                        }
                                                                    }
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    }
                                                    <!--if (BaseController.IsResourceAssigned("Charge Groups", ViewBag.DesignationTypeEnum))
                            {
                                if (BaseController.IsPermissionAssigned("Charge Groups", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Index", "SupportData")?type=34' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Charge Groups</span>
                                        </a>
                                    </li>
                                }
                            }
                            if (BaseController.IsResourceAssigned("Charge Information", ViewBag.DesignationTypeEnum))
                            {
                                if (BaseController.IsPermissionAssigned("Charge Information", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Index", "ChargeInformations")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Charge Informations</span>
                                        </a>
                                    </li>
                                }
                            }
                            if (BaseController.IsResourceAssigned("Currency Exchange Rates", ViewBag.DesignationTypeEnum))
                            {
                                if (BaseController.IsPermissionAssigned("Currency Exchange Rates", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                {
                                    <li class="kt-menu__item " aria-haspopup="true">
                                        <a href='@Url.Action("Index", "CurrencyExchangeRates")' class="kt-menu__link ">
                                            <span class="kt-menu__link-icon">
                                                <i class="fa fa-angle-right"></i>
                                            </span>
                                            <span class="kt-menu__link-text">Currency Exchange Rates</span>
                                        </a>
                                    </li>
                                }
                            }-->
                                                    @if (BaseController.IsResourceAssigned("Cash & Bank Accounts", ViewBag.DesignationTypeEnum))
                                                    {
                                                        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                                <span class="kt-menu__link-icon">
                                                                    <i class="fa fa-blender"></i>
                                                                </span>
                                                                <span class="kt-menu__link-text">Cash & Bank Accounts</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                            </a>
                                                            <div class="kt-menu__submenu ">
                                                                <span class="kt-menu__arrow"></span>
                                                                <ul class="kt-menu__subnav">
                                                                    @if (BaseController.IsPermissionAssigned("Cash & Bank Accounts", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                                            <a href='@Url.Action("New", "Accounts")' class="kt-menu__link ">
                                                                                <span class="kt-menu__link-icon">
                                                                                    <i class="fa fa-angle-double-right"></i>
                                                                                </span>
                                                                                <span class="kt-menu__link-text">New</span>
                                                                            </a>
                                                                        </li>
                                                                    }
                                                                    @if (BaseController.IsPermissionAssigned("Cash & Bank Accounts", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                    {
                                                                        <li class="kt-menu__item " aria-haspopup="true">
                                                                            <a href='@Url.Action("Index", "Accounts")' class="kt-menu__link ">
                                                                                <span class="kt-menu__link-icon">
                                                                                    <i class="fa fa-angle-double-right"></i>
                                                                                </span>
                                                                                <span class="kt-menu__link-text">View</span>
                                                                            </a>
                                                                        </li>
                                                                    }
                                                                </ul>
                                                            </div>
                                                        </li>
                                                    }
                                                </ul>
                                            </div>
                                        </li>
                                    }
                                }
                            }



                            <!--if (BaseController.IsModuleAssigned("Inventory Module", ViewBag.DesignationTypeEnum))
    {
        <li class="kt-menu__section ">
            <h4 class="kt-menu__section-text">Inventory Modules</h4>
            <i class="kt-menu__section-icon flaticon-more-v2"></i>
        </li>
        if (BaseController.IsResourceAssigned("Goods Received Notes", ViewBag.DesignationTypeEnum))
        {
            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                    <span class="kt-menu__link-icon">
                        <i class="fa fa-truck-moving"></i>
                    </span>
                    <span class="kt-menu__link-text">GRNs</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                </a>
                <div class="kt-menu__submenu ">
                    <span class="kt-menu__arrow"></span>
                    <ul class="kt-menu__subnav">
                        @if (BaseController.IsPermissionAssigned("Goods Received Notes", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("Index", "GRNS")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">View</span>
                                </a>
                            </li>
                        }
                        @if (BaseController.IsPermissionAssigned("Goods Received Notes", Permission.PermissionEnum.Approve, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("Approval", "GRNS")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">Approval</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </li>
        }
        if (BaseController.IsResourceAssigned("Consignment Stock Transfer", ViewBag.DesignationTypeEnum))
        {
            if (BaseController.IsPermissionAssigned("Consignment Stock Transfer", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
            {
                <li class="kt-menu__item " aria-haspopup="true">
                    <a href='@Url.Action("Index", "ConsignmentStockTransfer")' class="kt-menu__link ">
                        <span class="kt-menu__link-icon">
                            <i class="fa fa-archive"></i>
                        </span>
                        <span class="kt-menu__link-text">Consignment Stock Transfer</span>
                    </a>
                </li>
            }
        }
        if (BaseController.IsResourceAssigned("HOReturn Stock Transfer", ViewBag.DesignationTypeEnum))
        {
            if (BaseController.IsPermissionAssigned("HOReturn Stock Transfer", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
            {
                <li class="kt-menu__item " aria-haspopup="true">
                    <a href='@Url.Action("Index", "HOReturnStockTransfer")' class="kt-menu__link ">
                        <span class="kt-menu__link-icon">
                            <i class="fa fa-archive"></i>
                        </span>
                        <span class="kt-menu__link-text">HOReturn Stock Transfer</span>
                    </a>
                </li>
            }
        }
        if (BaseController.IsResourceAssigned("Stock Takes", ViewBag.DesignationTypeEnum))
        {
            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                    <span class="kt-menu__link-icon">
                        <i class="fa fa-blender"></i>
                    </span>
                    <span class="kt-menu__link-text">Stock Takes</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                </a>
                <div class="kt-menu__submenu ">
                    <span class="kt-menu__arrow"></span>
                    <ul class="kt-menu__subnav">
                        @if (BaseController.IsPermissionAssigned("Stock Takes", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("Upload", "StockTakes")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-double-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">Upload</span>
                                </a>
                            </li>
                        }
                        @if (BaseController.IsPermissionAssigned("Stock Takes", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("Index", "StockTakes")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-double-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">View</span>
                                </a>
                            </li>
                        }
                        @if (BaseController.IsPermissionAssigned("Stock Takes", Permission.PermissionEnum.Approve, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("Approval", "StockTakes")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">Approval</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </li>
        }
        if (BaseController.IsResourceAssigned("Stock Transfers", ViewBag.DesignationTypeEnum))
        {
            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                    <span class="kt-menu__link-icon">
                        <i class="fa fa-box"></i>
                    </span><span class="kt-menu__link-text">Stock Transfer</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                </a>
                <div class="kt-menu__submenu ">
                    <span class="kt-menu__arrow"></span>
                    <ul class="kt-menu__subnav">
                        @if (BaseController.IsPermissionAssigned("Stock Transfers", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("New", "StockTransfer")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">New</span>
                                </a>
                            </li>
                        }
                        @if (BaseController.IsPermissionAssigned("Stock Transfers", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("Index", "StockTransfer")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">View</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </li>
        }
        if (BaseController.IsResourceAssigned("Stock Transfers FIFO", ViewBag.DesignationTypeEnum))
        {
            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                    <span class="kt-menu__link-icon">
                        <i class="fa fa-box"></i>
                    </span><span class="kt-menu__link-text">Stock Transfer FIFO</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                </a>
                <div class="kt-menu__submenu ">
                    <span class="kt-menu__arrow"></span>
                    <ul class="kt-menu__subnav">
                        @if (BaseController.IsPermissionAssigned("Stock Transfers FIFO", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("New", "StockTransferFIFO")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">New</span>
                                </a>
                            </li>
                        }
                        @if (BaseController.IsPermissionAssigned("Stock Transfers FIFO", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item " aria-haspopup="true">
                                <a href='@Url.Action("Index", "StockTransferFIFO")' class="kt-menu__link ">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">View</span>
                                </a>
                            </li>
                        }
                    </ul>
                </div>
            </li>
        }
        if (BaseController.IsResourceAssigned("Stock Transfer Receipts", ViewBag.DesignationTypeEnum))
        {
            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                    <span class="kt-menu__link-icon">
                        <i class="fa fa-blender"></i>
                    </span>
                    <span class="kt-menu__link-text">Stock Transfer Receipt</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                </a>
                <div class="kt-menu__submenu ">
                    <span class="kt-menu__arrow"></span>
                    <ul class="kt-menu__subnav">-->
                            @*<li class="kt-menu__item " aria-haspopup="true">
            <a href='@Url.Action("New", "StockTransferReceipts")' class="kt-menu__link ">
                <span class="kt-menu__link-icon">
                    <i class="fa fa-angle-double-right"></i>
                </span>
                <span class="kt-menu__link-text">New</span>
            </a>
        </li>*@
                            <!--@if (BaseController.IsPermissionAssigned("Stock Transfer Receipts", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                    {
                        <li class="kt-menu__item " aria-haspopup="true">
                            <a href='@Url.Action("Index", "StockTransferReceipts")' class="kt-menu__link ">
                                <span class="kt-menu__link-icon">
                                    <i class="fa fa-angle-double-right"></i>
                                </span>
                                <span class="kt-menu__link-text">View</span>
                            </a>
                        </li>
                    }
                </ul>
            </div>
        </li>
    }
    if (BaseController.IsResourceAssigned("Stock Adjustments", ViewBag.DesignationTypeEnum))
    {
        <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
            <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                <span class="kt-menu__link-icon">
                    <i class="fa fa-blender"></i>
                </span>
                <span class="kt-menu__link-text">Stock Adjustments</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
            </a>
            <div class="kt-menu__submenu ">
                <span class="kt-menu__arrow"></span>
                <ul class="kt-menu__subnav">
                    @if (BaseController.IsPermissionAssigned("Stock Adjustments", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                    {
                        <li class="kt-menu__item " aria-haspopup="true">
                            <a href='@Url.Action("New", "StockAdjustments")' class="kt-menu__link ">
                                <span class="kt-menu__link-icon">
                                    <i class="fa fa-angle-double-right"></i>
                                </span>
                                <span class="kt-menu__link-text">New</span>
                            </a>
                        </li>
                    }
                    @if (BaseController.IsPermissionAssigned("Stock Adjustments", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                    {
                        <li class="kt-menu__item " aria-haspopup="true">
                            <a href='@Url.Action("Index", "StockAdjustments")' class="kt-menu__link ">
                                <span class="kt-menu__link-icon">
                                    <i class="fa fa-angle-double-right"></i>
                                </span>
                                <span class="kt-menu__link-text">View</span>
                            </a>
                        </li>
                    }
                    @if (BaseController.IsPermissionAssigned("Stock Adjustments", Permission.PermissionEnum.Approve, ViewBag.DesignationTypeEnum))
                    {
                        <li class="kt-menu__item " aria-haspopup="true">
                            <a href='@Url.Action("Approval", "StockAdjustments")' class="kt-menu__link ">
                                <span class="kt-menu__link-icon">
                                    <i class="fa fa-angle-right"></i>
                                </span>
                                <span class="kt-menu__link-text">Approval</span>
                            </a>
                        </li>
                    }
                </ul>
            </div>
        </li>
    }
    if (BaseController.IsResourceAssigned("Stock Allocation Manager", ViewBag.DesignationTypeEnum))
    {
        if (BaseController.IsPermissionAssigned("Stock Allocation Manager", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
        {
            <li class="kt-menu__item " aria-haspopup="true">
                <a href='@Url.Action("Index", "StockAllocations")' class="kt-menu__link ">
                    <span class="kt-menu__link-icon">
                        <i class="fa fa-box-open"></i>
                    </span>
                    <span class="kt-menu__link-text">Stock Allocation Manager</span>
                </a>
            </li>
        }
    }-->
                            @*if (BaseController.IsResourceAssigned("Products", ViewBag.DesignationTypeEnum))
        {
            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                    <span class="kt-menu__link-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                <rect x="0" y="0" width="24" height="24"></rect>
                                <rect fill="#000000" x="4" y="4" width="7" height="7" rx="1.5"></rect>
                                <path d="M5.5,13 L9.5,13 C10.3284271,13 11,13.6715729 11,14.5 L11,18.5 C11,19.3284271 10.3284271,20 9.5,20 L5.5,20 C4.67157288,20 4,19.3284271 4,18.5 L4,14.5 C4,13.6715729 4.67157288,13 5.5,13 Z M14.5,4 L18.5,4 C19.3284271,4 20,4.67157288 20,5.5 L20,9.5 C20,10.3284271 19.3284271,11 18.5,11 L14.5,11 C13.6715729,11 13,10.3284271 13,9.5 L13,5.5 C13,4.67157288 13.6715729,4 14.5,4 Z M14.5,13 L18.5,13 C19.3284271,13 20,13.6715729 20,14.5 L20,18.5 C20,19.3284271 19.3284271,20 18.5,20 L14.5,20 C13.6715729,20 13,19.3284271 13,18.5 L13,14.5 C13,13.6715729 13.6715729,13 14.5,13 Z" fill="#000000" opacity="0.3"></path>
                            </g>
                        </svg>
                    </span><span class="kt-menu__link-text">Items</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                </a>
                <div class="kt-menu__submenu ">
                    <span class="kt-menu__arrow"></span>
                    <ul class="kt-menu__subnav">
                        @if (BaseController.IsResourceAssigned("Products", ViewBag.DesignationTypeEnum))
                        {
                            <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                    <span class="kt-menu__link-icon">
                                        <i class="fa fa-angle-right"></i>
                                    </span>
                                    <span class="kt-menu__link-text">Items</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                </a>
                                <div class="kt-menu__submenu ">
                                    <span class="kt-menu__arrow"></span>
                                    <ul class="kt-menu__subnav">
                                        @if (BaseController.IsPermissionAssigned("Products", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                        {
                                            <li class="kt-menu__item " aria-haspopup="true">
                                                <a href='@Url.Action("New", "Products")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-double-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">New</span>
                                                </a>
                                            </li>
                                        }
                                        @if (BaseController.IsPermissionAssigned("Products", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                        {
                                            <li class="kt-menu__item " aria-haspopup="true">
                                                <a href='@Url.Action("Index", "Products")' class="kt-menu__link ">
                                                    <span class="kt-menu__link-icon">
                                                        <i class="fa fa-angle-double-right"></i>
                                                    </span>
                                                    <span class="kt-menu__link-text">View</span>
                                                </a>
                                            </li>
                                        }
                                    </ul>
                                </div>
                            </li>
                        }
                    </ul>
                </div>
            </li>
        }*@

                            <!--}-->
                            @*if (BaseController.IsModuleAssigned("Sales and Marketing Module", ViewBag.DesignationTypeEnum))
                                        {
                                            <li class="kt-menu__section ">
                                                <h4 class="kt-menu__section-text">Sales Module</h4>
                                                <i class="kt-menu__section-icon flaticon-more-v2"></i>
                                            </li>
                                            if (BaseController.IsResourceAssigned("Customer Payments", ViewBag.DesignationTypeEnum))
                                            {
                                                <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                    <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                        <span class="kt-menu__link-icon">
                                                            <i class="fa fa-compact-disc"></i>
                                                        </span>
                                                        <span class="kt-menu__link-text">Customer Payments</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                    </a>
                                                    <div class="kt-menu__submenu ">
                                                        <span class="kt-menu__arrow"></span>
                                                        <ul class="kt-menu__subnav">
                                                            @if (BaseController.IsPermissionAssigned("Customer Payments", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                            {
                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                    <a href='@Url.Action("Index", "CustomerPayments")' class="kt-menu__link ">
                                                                        <span class="kt-menu__link-icon">
                                                                            <i class="fa fa-angle-right"></i>
                                                                        </span>
                                                                        <span class="kt-menu__link-text">View</span>
                                                                    </a>
                                                                </li>
                                                            }
                                                            @if (BaseController.IsPermissionAssigned("Customer Payments", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                            {
                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                    <a href='@Url.Action("New", "CustomerPayments")' class="kt-menu__link ">
                                                                        <span class="kt-menu__link-icon">
                                                                            <i class="fa fa-angle-right"></i>
                                                                        </span>
                                                                        <span class="kt-menu__link-text">New</span>
                                                                    </a>
                                                                </li>
                                                            }
                                                        </ul>
                                                    </div>
                                                </li>
                                            }
                                            if (BaseController.IsResourceAssigned("Service Invoices", ViewBag.DesignationTypeEnum))
                                            {
                                                <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                    <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                        <span class="kt-menu__link-icon">
                                                            <i class="fa fa-clipboard"></i>
                                                        </span>
                                                        <span class="kt-menu__link-text">Service Invoice (Sales)</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                    </a>
                                                    <div class="kt-menu__submenu ">
                                                        <span class="kt-menu__arrow"></span>
                                                        <ul class="kt-menu__subnav">
                                                            @if (BaseController.IsPermissionAssigned("Service Invoices", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                            {
                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                    <a href='@Url.Action("Index", "SalesServiceInvoices")' class="kt-menu__link ">
                                                                        <span class="kt-menu__link-icon">
                                                                            <i class="fa fa-angle-right"></i>
                                                                        </span>
                                                                        <span class="kt-menu__link-text">View</span>
                                                                    </a>
                                                                </li>
                                                            }
                                                            @if (BaseController.IsPermissionAssigned("Service Invoices", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                            {
                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                    <a href='@Url.Action("New", "SalesServiceInvoices")' class="kt-menu__link ">
                                                                        <span class="kt-menu__link-icon">
                                                                            <i class="fa fa-angle-right"></i>
                                                                        </span>
                                                                        <span class="kt-menu__link-text">New</span>
                                                                    </a>
                                                                </li>
                                                            }
                                                        </ul>
                                                    </div>
                                                </li>
                                            }
                                            if (BaseController.IsResourceAssigned("Performa Invoices", ViewBag.DesignationTypeEnum)
        || BaseController.IsResourceAssigned("Performa Invoice Desk", ViewBag.DesignationTypeEnum))
                                            {
                                                <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                    <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                        <span class="kt-menu__link-icon">
                                                            <i class="fa fa-table"></i>
                                                        </span>
                                                        <span class="kt-menu__link-text">Performa Invoices</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                    </a>
                                                    <div class="kt-menu__submenu ">
                                                        <span class="kt-menu__arrow"></span>
                                                        <ul class="kt-menu__subnav">
                                                            @if (BaseController.IsResourceAssigned("Performa Invoices", ViewBag.DesignationTypeEnum))
                                                            {
                                                                if (BaseController.IsPermissionAssigned("Performa Invoices", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                {
                                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                                        <a href='@Url.Action("Index", "PerformaInvoice")' class="kt-menu__link ">
                                                                            <span class="kt-menu__link-icon">
                                                                                <i class="fa fa-angle-right"></i>
                                                                            </span>
                                                                            <span class="kt-menu__link-text">View</span>
                                                                        </a>
                                                                    </li>
                                                                }
                                                            }
                                                            @if (BaseController.IsResourceAssigned("Performa Invoice Desk", ViewBag.DesignationTypeEnum))
                                                            {
                                                                if (BaseController.IsPermissionAssigned("Performa Invoice Desk", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                                {
                                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                                        <a href='@Url.Action("Desk", "PerformaInvoice")' class="kt-menu__link ">
                                                                            <span class="kt-menu__link-icon">
                                                                                <i class="fa fa-angle-right"></i>
                                                                            </span>
                                                                            <span class="kt-menu__link-text">Performa Invoice Desk</span>
                                                                        </a>
                                                                    </li>
                                                                }
                                                            }
                                                        </ul>
                                                    </div>
                                                </li>
                                            }
                                            if (BaseController.IsResourceAssigned("Commercial Invoices", ViewBag.DesignationTypeEnum))
                                            {
                                                if (BaseController.IsPermissionAssigned("Commercial Invoices", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("Index", "CommercialInvoices")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-copyright"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">Commercial Invoices</span>
                                                        </a>
                                                    </li>
                                                }
                                            }
                                            if (BaseController.IsResourceAssigned("Sales Invoices Walk In", ViewBag.DesignationTypeEnum))
                                            {
                                                <li class="kt-menu__item  kt-menu__item--submenu" aria-haspopup="true" data-ktmenu-submenu-toggle="hover">
                                                    <a href="javascript:;" class="kt-menu__link kt-menu__toggle">
                                                        <span class="kt-menu__link-icon">
                                                            <i class="fa fa-compact-disc"></i>
                                                        </span>
                                                        <span class="kt-menu__link-text">Sales Invoices Walk In</span><i class="kt-menu__ver-arrow la la-angle-right"></i>
                                                    </a>
                                                    <div class="kt-menu__submenu ">
                                                        <span class="kt-menu__arrow"></span>
                                                        <ul class="kt-menu__subnav">
                                                            @if (BaseController.IsPermissionAssigned("Sales Invoices Walk In", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                            {
                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                    <a href='@Url.Action("Index", "SalesInvoicesWalkIn")' class="kt-menu__link ">
                                                                        <span class="kt-menu__link-icon">
                                                                            <i class="fa fa-angle-right"></i>
                                                                        </span>
                                                                        <span class="kt-menu__link-text">View</span>
                                                                    </a>
                                                                </li>
                                                            }
                                                            @if (BaseController.IsPermissionAssigned("Sales Invoices Walk In", Permission.PermissionEnum.Create, ViewBag.DesignationTypeEnum))
                                                            {
                                                                <li class="kt-menu__item " aria-haspopup="true">
                                                                    <a href='@Url.Action("New", "SalesInvoicesWalkIn")' class="kt-menu__link ">
                                                                        <span class="kt-menu__link-icon">
                                                                            <i class="fa fa-angle-right"></i>
                                                                        </span>
                                                                        <span class="kt-menu__link-text">New</span>
                                                                    </a>
                                                                </li>
                                                            }
                                                        </ul>
                                                    </div>
                                                </li>
                                            }
                                            if (BaseController.IsResourceAssigned("Sales Invoices", ViewBag.DesignationTypeEnum))
                                            {
                                                if (BaseController.IsPermissionAssigned("Sales Invoices", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("Index", "SalesInvoices")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-inbox"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">Sales Invoices</span>
                                                        </a>
                                                    </li>
                                                }
                                            }
                                            if (BaseController.IsResourceAssigned("Consignment Sales Invoices", ViewBag.DesignationTypeEnum))
                                            {
                                                if (BaseController.IsPermissionAssigned("Consignment Sales Invoices", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("Index", "ConsignmentSalesInvoices")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-inbox"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">Consignment Sales Invoices</span>
                                                        </a>
                                                    </li>
                                                }
                                            }
                                            if (BaseController.IsResourceAssigned("Sales Cashier", ViewBag.DesignationTypeEnum))
                                            {
                                                if (BaseController.IsPermissionAssigned("Sales Cashier", Permission.PermissionEnum.Retrieve, ViewBag.DesignationTypeEnum))
                                                {
                                                    <li class="kt-menu__item " aria-haspopup="true">
                                                        <a href='@Url.Action("Index", "SalesCashier")' class="kt-menu__link ">
                                                            <span class="kt-menu__link-icon">
                                                                <i class="fa fa-table"></i>
                                                            </span>
                                                            <span class="kt-menu__link-text">Sales Cashier</span>
                                                        </a>
                                                    </li>
                                                }
                                            }
                                        }*@
                            }
                        </ul>
                    </div>
                </div>
                <!-- end:: Aside Menu -->
            </div>
            <!-- end:: Aside -->
            <div class="kt-grid__item kt-grid__item--fluid kt-grid kt-grid--hor kt-wrapper" id="kt_wrapper">
                <!-- begin:: Header -->
                <div id="kt_header" class="kt-header kt-grid__item  kt-header--fixed ">
                    <!-- begin:: Header Menu -->
                    <!-- Uncomment this to display the close button of the panel
                    <button class="kt-header-menu-wrapper-close" id="kt_header_menu_mobile_close_btn"><i class="la la-close"></i></button>
                    -->
                    <div class="kt-header-menu-wrapper" id="kt_header_menu_wrapper">
                        <div id="kt_header_menu" class="kt-header-menu kt-header-menu-mobile  kt-header-menu--layout-default ">
                            <ul class="kt-menu__nav ">
                                <li><h2 style="margin-top: 17px;">@ViewBag.Title</h2></li>
                            </ul>
                        </div>
                    </div>

                    <!-- end:: Header Menu -->
                    <!-- begin:: Header Topbar -->
                    <div class="kt-header__topbar">
                        <!--begin: Notifications -->
                        @*<div class="kt-header__topbar-item dropdown">
                                            <div class="kt-header__topbar-wrapper" data-toggle="dropdown" data-offset="30px,0px" aria-expanded="true">
                                                <span class="kt-header__topbar-icon kt-pulse kt-pulse--brand">
                                                    <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                                                        <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                            <rect x="0" y="0" width="24" height="24" />
                                                            <path d="M2.56066017,10.6819805 L4.68198052,8.56066017 C5.26776695,7.97487373 6.21751442,7.97487373 6.80330086,8.56066017 L8.9246212,10.6819805 C9.51040764,11.267767 9.51040764,12.2175144 8.9246212,12.8033009 L6.80330086,14.9246212 C6.21751442,15.5104076 5.26776695,15.5104076 4.68198052,14.9246212 L2.56066017,12.8033009 C1.97487373,12.2175144 1.97487373,11.267767 2.56066017,10.6819805 Z M14.5606602,10.6819805 L16.6819805,8.56066017 C17.267767,7.97487373 18.2175144,7.97487373 18.8033009,8.56066017 L20.9246212,10.6819805 C21.5104076,11.267767 21.5104076,12.2175144 20.9246212,12.8033009 L18.8033009,14.9246212 C18.2175144,15.5104076 17.267767,15.5104076 16.6819805,14.9246212 L14.5606602,12.8033009 C13.9748737,12.2175144 13.9748737,11.267767 14.5606602,10.6819805 Z" fill="#000000" opacity="0.3" />
                                                            <path d="M8.56066017,16.6819805 L10.6819805,14.5606602 C11.267767,13.9748737 12.2175144,13.9748737 12.8033009,14.5606602 L14.9246212,16.6819805 C15.5104076,17.267767 15.5104076,18.2175144 14.9246212,18.8033009 L12.8033009,20.9246212 C12.2175144,21.5104076 11.267767,21.5104076 10.6819805,20.9246212 L8.56066017,18.8033009 C7.97487373,18.2175144 7.97487373,17.267767 8.56066017,16.6819805 Z M8.56066017,4.68198052 L10.6819805,2.56066017 C11.267767,1.97487373 12.2175144,1.97487373 12.8033009,2.56066017 L14.9246212,4.68198052 C15.5104076,5.26776695 15.5104076,6.21751442 14.9246212,6.80330086 L12.8033009,8.9246212 C12.2175144,9.51040764 11.267767,9.51040764 10.6819805,8.9246212 L8.56066017,6.80330086 C7.97487373,6.21751442 7.97487373,5.26776695 8.56066017,4.68198052 Z" fill="#000000" />
                                                        </g>
                                                    </svg> <span class="kt-pulse__ring"></span>
                                                </span>

                                                <!--
                                Use dot badge instead of animated pulse effect:
                                <span class="kt-badge kt-badge--dot kt-badge--notify kt-badge--sm kt-badge--brand"></span>
                            -->
                                            </div>
                                            <div class="dropdown-menu dropdown-menu-fit dropdown-menu-right dropdown-menu-anim dropdown-menu-top-unround dropdown-menu-lg">
                                                <form>

                                                    <!--begin: Head -->
                                                    <div class="kt-head kt-head--skin-dark kt-head--fit-x kt-head--fit-b" style="background-image: url(marangonierp1/assets/media/misc/bg-1.jpg)">
                                                        <h3 class="kt-head__title">
                                                            User Notifications
                                                            &nbsp;
                                                            <span class="btn btn-success btn-sm btn-bold btn-font-md">23 new</span>
                                                        </h3>
                                                        <ul class="nav nav-tabs nav-tabs-line nav-tabs-bold nav-tabs-line-3x nav-tabs-line-success kt-notification-item-padding-x" role="tablist">
                                                            <li class="nav-item">
                                                                <a class="nav-link active show" data-toggle="tab" href="#topbar_notifications_notifications" role="tab" aria-selected="true">Alerts</a>
                                                            </li>
                                                            <li class="nav-item">
                                                                <a class="nav-link" data-toggle="tab" href="#topbar_notifications_events" role="tab" aria-selected="false">Events</a>
                                                            </li>
                                                            <li class="nav-item">
                                                                <a class="nav-link" data-toggle="tab" href="#topbar_notifications_logs" role="tab" aria-selected="false">Logs</a>
                                                            </li>
                                                        </ul>
                                                    </div>

                                                    <!--end: Head -->
                                                    <div class="tab-content">
                                                        <div class="tab-pane active show" id="topbar_notifications_notifications" role="tabpanel">
                                                            <div class="kt-notification kt-margin-t-10 kt-margin-b-10 kt-scroll" data-scroll="true" data-height="300" data-mobile-height="200">
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-line-chart kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New order has been received
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            2 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-box-1 kt-font-brand"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New customer is registered
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            3 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-chart2 kt-font-danger"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            Application has been approved
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            3 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-image-file kt-font-warning"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New file has been uploaded
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            5 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-drop kt-font-info"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New user feedback received
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            8 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-pie-chart-2 kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            System reboot has been successfully completed
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            12 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-favourite kt-font-danger"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New order has been placed
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            15 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item kt-notification__item--read">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-safe kt-font-primary"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            Company meeting canceled
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            19 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-psd kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New report has been received
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            23 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon-download-1 kt-font-danger"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            Finance report has been generated
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            25 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon-security kt-font-warning"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New customer comment recieved
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            2 days ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-pie-chart kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New customer is registered
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            3 days ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="tab-pane" id="topbar_notifications_events" role="tabpanel">
                                                            <div class="kt-notification kt-margin-t-10 kt-margin-b-10 kt-scroll" data-scroll="true" data-height="300" data-mobile-height="200">
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-psd kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New report has been received
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            23 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon-download-1 kt-font-danger"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            Finance report has been generated
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            25 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-line-chart kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New order has been received
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            2 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-box-1 kt-font-brand"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New customer is registered
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            3 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-chart2 kt-font-danger"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            Application has been approved
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            3 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-image-file kt-font-warning"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New file has been uploaded
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            5 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-drop kt-font-info"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New user feedback received
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            8 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-pie-chart-2 kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            System reboot has been successfully completed
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            12 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-favourite kt-font-brand"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New order has been placed
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            15 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item kt-notification__item--read">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-safe kt-font-primary"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            Company meeting canceled
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            19 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-psd kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New report has been received
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            23 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon-download-1 kt-font-danger"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            Finance report has been generated
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            25 hrs ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon-security kt-font-warning"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New customer comment recieved
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            2 days ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                                <a href="#" class="kt-notification__item">
                                                                    <div class="kt-notification__item-icon">
                                                                        <i class="flaticon2-pie-chart kt-font-success"></i>
                                                                    </div>
                                                                    <div class="kt-notification__item-details">
                                                                        <div class="kt-notification__item-title">
                                                                            New customer is registered
                                                                        </div>
                                                                        <div class="kt-notification__item-time">
                                                                            3 days ago
                                                                        </div>
                                                                    </div>
                                                                </a>
                                                            </div>
                                                        </div>
                                                        <div class="tab-pane" id="topbar_notifications_logs" role="tabpanel">
                                                            <div class="kt-grid kt-grid--ver" style="min-height: 200px;">
                                                                <div class="kt-grid kt-grid--hor kt-grid__item kt-grid__item--fluid kt-grid__item--middle">
                                                                    <div class="kt-grid__item kt-grid__item--middle kt-align-center">
                                                                        All caught up!
                                                                        <br>No new notifications.
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </form>
                                            </div>
                                        </div>*@

                        <!--end: Notifications -->
                        <!--begin: Quick Actions -->
                        @*<div class="kt-header__topbar-item dropdown">
                                <div class="kt-header__topbar-wrapper" data-toggle="dropdown" data-offset="30px,0px" aria-expanded="true">
                                    <span class="kt-header__topbar-icon">
                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon">
                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                <rect x="0" y="0" width="24" height="24" />
                                                <rect fill="#000000" opacity="0.3" x="13" y="4" width="3" height="16" rx="1.5" />
                                                <rect fill="#000000" x="8" y="9" width="3" height="11" rx="1.5" />
                                                <rect fill="#000000" x="18" y="11" width="3" height="9" rx="1.5" />
                                                <rect fill="#000000" x="3" y="13" width="3" height="7" rx="1.5" />
                                            </g>
                                        </svg>
                                    </span>
                                </div>
                                <div class="dropdown-menu dropdown-menu-fit dropdown-menu-right dropdown-menu-anim dropdown-menu-top-unround dropdown-menu-xl">
                                    <form>

                                        <!--begin: Head -->
                                        <div class="kt-head kt-head--skin-dark" style="background-image: url(MarangoniERP/assets/media/misc/bg-1.jpg)">
                                            <h3 class="kt-head__title">
                                                User Quick Actions
                                                <span class="kt-space-15"></span>
                                                <span class="btn btn-success btn-sm btn-bold btn-font-md">23 tasks pending</span>
                                            </h3>
                                        </div>

                                        <!--end: Head -->
                                        <!--begin: Grid Nav -->
                                        <div class="kt-grid-nav kt-grid-nav--skin-light">
                                            <div class="kt-grid-nav__row">
                                                <a href="#" class="kt-grid-nav__item">
                                                    <span class="kt-grid-nav__icon">
                                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon kt-svg-icon--success kt-svg-icon--lg">
                                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                                <rect x="0" y="0" width="24" height="24" />
                                                                <path d="M4.3618034,10.2763932 L4.8618034,9.2763932 C4.94649941,9.10700119 5.11963097,9 5.30901699,9 L15.190983,9 C15.4671254,9 15.690983,9.22385763 15.690983,9.5 C15.690983,9.57762255 15.6729105,9.65417908 15.6381966,9.7236068 L15.1381966,10.7236068 C15.0535006,10.8929988 14.880369,11 14.690983,11 L4.80901699,11 C4.53287462,11 4.30901699,10.7761424 4.30901699,10.5 C4.30901699,10.4223775 4.32708954,10.3458209 4.3618034,10.2763932 Z M14.6381966,13.7236068 L14.1381966,14.7236068 C14.0535006,14.8929988 13.880369,15 13.690983,15 L4.80901699,15 C4.53287462,15 4.30901699,14.7761424 4.30901699,14.5 C4.30901699,14.4223775 4.32708954,14.3458209 4.3618034,14.2763932 L4.8618034,13.2763932 C4.94649941,13.1070012 5.11963097,13 5.30901699,13 L14.190983,13 C14.4671254,13 14.690983,13.2238576 14.690983,13.5 C14.690983,13.5776225 14.6729105,13.6541791 14.6381966,13.7236068 Z" fill="#000000" opacity="0.3" />
                                                                <path d="M17.369,7.618 C16.976998,7.08599734 16.4660031,6.69750122 15.836,6.4525 C15.2059968,6.20749878 14.590003,6.085 13.988,6.085 C13.2179962,6.085 12.5180032,6.2249986 11.888,6.505 C11.2579969,6.7850014 10.7155023,7.16999755 10.2605,7.66 C9.80549773,8.15000245 9.45550123,8.72399671 9.2105,9.382 C8.96549878,10.0400033 8.843,10.7539961 8.843,11.524 C8.843,12.3360041 8.96199881,13.0779966 9.2,13.75 C9.43800119,14.4220034 9.7774978,14.9994976 10.2185,15.4825 C10.6595022,15.9655024 11.1879969,16.3399987 11.804,16.606 C12.4200031,16.8720013 13.1129962,17.005 13.883,17.005 C14.681004,17.005 15.3879969,16.8475016 16.004,16.5325 C16.6200031,16.2174984 17.1169981,15.8010026 17.495,15.283 L19.616,16.774 C18.9579967,17.6000041 18.1530048,18.2404977 17.201,18.6955 C16.2489952,19.1505023 15.1360064,19.378 13.862,19.378 C12.6999942,19.378 11.6325049,19.1855019 10.6595,18.8005 C9.68649514,18.4154981 8.8500035,17.8765035 8.15,17.1835 C7.4499965,16.4904965 6.90400196,15.6645048 6.512,14.7055 C6.11999804,13.7464952 5.924,12.6860058 5.924,11.524 C5.924,10.333994 6.13049794,9.25950479 6.5435,8.3005 C6.95650207,7.34149521 7.5234964,6.52600336 8.2445,5.854 C8.96550361,5.18199664 9.8159951,4.66400182 10.796,4.3 C11.7760049,3.93599818 12.8399943,3.754 13.988,3.754 C14.4640024,3.754 14.9609974,3.******** 15.479,3.8905 C15.9970026,3.******** 16.4939976,4.******** 16.97,4.3105 C17.4460024,4.******** 17.8939979,4.7339986 18.314,5.014 C18.7340021,5.2940014 19.0909985,5.******** 19.385,6.022 L17.369,7.618 Z" fill="#000000" />
                                                            </g>
                                                        </svg>
                                                    </span>
                                                    <span class="kt-grid-nav__title">Accounting</span>
                                                    <span class="kt-grid-nav__desc">eCommerce</span>
                                                </a>
                                                <a href="#" class="kt-grid-nav__item">
                                                    <span class="kt-grid-nav__icon">
                                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon kt-svg-icon--success kt-svg-icon--lg">
                                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                                <rect x="0" y="0" width="24" height="24" />
                                                                <path d="M14.8571499,13 C14.9499122,12.7223297 15,12.4263059 15,12.1190476 L15,6.******** C15,5.28984632 13.6568542,4 12,4 L11.7272727,4 C10.2210416,4 9,5.17258756 9,6.61904762 L10.0909091,6.61904762 C10.0909091,5.75117158 10.823534,5.04761905 11.7272727,5.04761905 L12,5.04761905 C13.0543618,5.04761905 13.9090909,5.86843034 13.9090909,6.******** L13.9090909,12.1190476 C13.9090909,12.4383379 13.8240964,12.7385644 13.6746497,13 L10.3253503,13 C10.1759036,12.7385644 10.0909091,12.4383379 10.0909091,12.1190476 L10.0909091,9.5 C10.0909091,9.06606198 10.4572216,8.71428571 10.9090909,8.71428571 C11.3609602,8.71428571 11.7272727,9.06606198 11.7272727,9.5 L11.7272727,11.3333333 L12.8181818,11.3333333 L12.8181818,9.5 C12.8181818,8.48747796 11.9634527,7.66666667 10.9090909,7.66666667 C9.85472911,7.66666667 9,8.48747796 9,9.5 L9,12.1190476 C9,12.4263059 9.0500878,12.7223297 9.14285008,13 L6,13 C5.44771525,13 5,12.5522847 5,12 L5,3 C5,2.44771525 5.44771525,2 6,2 L18,2 C18.5522847,2 19,2.44771525 19,3 L19,12 C19,12.5522847 18.5522847,13 18,13 L14.8571499,13 Z" fill="#000000" opacity="0.3" />
                                                                <path d="M9,10.3333333 L9,12.1190476 C9,13.7101537 10.3431458,15 12,15 C13.6568542,15 15,13.7101537 15,12.1190476 L15,10.3333333 L20.2072547,6.57253826 C20.4311176,6.4108595 20.7436609,6.46126971 20.9053396,6.68513259 C20.9668779,6.77033951 21,6.87277228 21,6.97787787 L21,17 C21,18.1045695 20.1045695,19 19,19 L5,19 C3.8954305,19 3,18.1045695 3,17 L3,6.97787787 C3,6.70173549 3.22385763,6.47787787 3.5,6.47787787 C3.60510559,6.47787787 3.70753836,6.51099993 3.79274528,6.57253826 L9,10.3333333 Z M10.0909091,11.1212121 L12,12.5 L13.9090909,11.1212121 L13.9090909,12.1190476 C13.9090909,13.1315697 13.0543618,13.952381 12,13.952381 C10.9456382,13.952381 10.0909091,13.1315697 10.0909091,12.1190476 L10.0909091,11.1212121 Z" fill="#000000" />
                                                            </g>
                                                        </svg>
                                                    </span>
                                                    <span class="kt-grid-nav__title">Administration</span>
                                                    <span class="kt-grid-nav__desc">Console</span>
                                                </a>
                                            </div>
                                            <div class="kt-grid-nav__row">
                                                <a href="#" class="kt-grid-nav__item">
                                                    <span class="kt-grid-nav__icon">
                                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon kt-svg-icon--success kt-svg-icon--lg">
                                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                                <rect x="0" y="0" width="24" height="24" />
                                                                <path d="M4,9.67471899 L10.880262,13.6470401 C10.9543486,13.689814 11.0320333,13.7207107 11.1111111,13.740321 L11.1111111,21.4444444 L4.49070127,17.526473 C4.18655139,17.3464765 4,17.0193034 4,16.6658832 L4,9.67471899 Z M20,9.56911707 L20,16.6658832 C20,17.0193034 19.8134486,17.3464765 19.5092987,17.526473 L12.8888889,21.4444444 L12.8888889,13.6728275 C12.9050191,13.6647696 12.9210067,13.6561758 12.9368301,13.6470401 L20,9.56911707 Z" fill="#000000" />
                                                                <path d="M4.21611835,7.74669402 C4.30015839,7.64056877 4.40623188,7.55087574 4.5299008,7.48500698 L11.5299008,3.75665466 C11.8237589,3.60013944 12.1762411,3.60013944 12.4700992,3.75665466 L19.4700992,7.48500698 C19.5654307,7.53578262 19.6503066,7.60071528 19.7226939,7.67641889 L12.0479413,12.1074394 C11.9974761,12.1365754 11.9509488,12.1699127 11.9085461,12.2067543 C11.8661433,12.1699127 11.819616,12.1365754 11.7691509,12.1074394 L4.21611835,7.74669402 Z" fill="#000000" opacity="0.3" />
                                                            </g>
                                                        </svg>
                                                    </span>
                                                    <span class="kt-grid-nav__title">Projects</span>
                                                    <span class="kt-grid-nav__desc">Pending Tasks</span>
                                                </a>
                                                <a href="#" class="kt-grid-nav__item">
                                                    <span class="kt-grid-nav__icon">
                                                        <svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" width="24px" height="24px" viewBox="0 0 24 24" version="1.1" class="kt-svg-icon kt-svg-icon--success kt-svg-icon--lg">
                                                            <g stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
                                                                <polygon points="0 0 24 0 24 24 0 24" />
                                                                <path d="M18,14 C16.3431458,14 15,12.6568542 15,11 C15,9.34314575 16.3431458,8 18,8 C19.6568542,8 21,9.34314575 21,11 C21,12.6568542 19.6568542,14 18,14 Z M9,11 C6.790861,11 5,9.209139 5,7 C5,4.790861 6.790861,3 9,3 C11.209139,3 13,4.790861 13,7 C13,9.209139 11.209139,11 9,11 Z" fill="#000000" fill-rule="nonzero" opacity="0.3" />
                                                                <path d="M17.6011961,15.0006174 C21.0077043,15.0378534 23.7891749,16.7601418 23.9984937,20.4 C24.0069246,20.5466056 23.9984937,21 23.4559499,21 L19.6,21 C19.6,18.7490654 18.8562935,16.6718327 17.6011961,15.0006174 Z M0.00065168429,20.1992055 C0.388258525,15.4265159 4.26191235,13 8.98334134,13 C13.7712164,13 17.7048837,15.2931929 17.9979143,20.2 C18.0095879,20.3954741 17.9979143,21 17.2466999,21 C13.541124,21 8.03472472,21 0.727502227,21 C0.476712155,21 -0.0204617505,20.45918 0.00065168429,20.1992055 Z" fill="#000000" fill-rule="nonzero" />
                                                            </g>
                                                        </svg>
                                                    </span>
                                                    <span class="kt-grid-nav__title">Customers</span>
                                                    <span class="kt-grid-nav__desc">Latest cases</span>
                                                </a>
                                            </div>
                                        </div>

                                        <!--end: Grid Nav -->
                                    </form>
                                </div>
                            </div>*@

                        <!--end: Quick Actions -->
                        <!--begin: Language bar -->
                        @*<div class="kt-header__topbar-item kt-header__topbar-item--langs">
                                <div class="kt-header__topbar-wrapper" data-toggle="dropdown" data-offset="10px,0px">
                                    <span class="kt-header__topbar-icon">
                                        <img class="" src="~/assets/media/flags/020-flag.svg" alt="" />
                                    </span>
                                </div>
                                <div class="dropdown-menu dropdown-menu-fit dropdown-menu-right dropdown-menu-anim dropdown-menu-top-unround">
                                    <ul class="kt-nav kt-margin-t-10 kt-margin-b-10">
                                        <li class="kt-nav__item kt-nav__item--active">
                                            <a href="#" class="kt-nav__link">
                                                <span class="kt-nav__link-icon"><img src="~/assets/media/flags/020-flag.svg" alt="" /></span>
                                                <span class="kt-nav__link-text">English</span>
                                            </a>
                                        </li>
                                        <li class="kt-nav__item">
                                            <a href="#" class="kt-nav__link">
                                                <span class="kt-nav__link-icon"><img src="~/assets/media/flags/016-spain.svg" alt="" /></span>
                                                <span class="kt-nav__link-text">Spanish</span>
                                            </a>
                                        </li>
                                        <li class="kt-nav__item">
                                            <a href="#" class="kt-nav__link">
                                                <span class="kt-nav__link-icon"><img src="~/assets/media/flags/017-germany.svg" alt="" /></span>
                                                <span class="kt-nav__link-text">German</span>
                                            </a>
                                        </li>
                                    </ul>
                                </div>
                            </div>*@

                        <!--end: Language bar -->
                        <!--begin: User Bar -->
                        <div class="kt-header__topbar-item kt-header__topbar-item--user">
                            <div class="kt-header__topbar-wrapper" data-toggle="dropdown" data-offset="0px,0px">
                                <div class="kt-header__topbar-user">
                                    <span class="kt-header__topbar-welcome kt-hidden-mobile">Hi,</span>
                                    <span class="kt-header__topbar-username kt-hidden-mobile">@ViewBag.UserName</span>
                                    <img class="kt-hidden" alt="Pic" src="~/assets/media/users/300_25.jpg" />

                                    <!--use below badge element instead the user avatar to display username's first letter(remove kt-hidden class to display it) -->
                                    <span class="kt-badge kt-badge--username kt-badge--unified-success kt-badge--lg kt-badge--rounded kt-badge--bold">S</span>
                                </div>
                            </div>
                            <div class="dropdown-menu dropdown-menu-fit dropdown-menu-right dropdown-menu-anim dropdown-menu-top-unround dropdown-menu-xl">

                                <!--begin: Head -->
                                <div class="kt-user-card kt-user-card--skin-dark kt-notification-item-padding-x" style="background-image: url(images/bg-1.jpg)">
                                    <div class="kt-user-card__avatar">
                                        <img class="kt-hidden" alt="Pic" src="~/assets/media/users/300_25.jpg" />

                                        <!--use below badge element instead the user avatar to display username's first letter(remove kt-hidden class to display it) -->
                                        <span class="kt-badge kt-badge--lg kt-badge--rounded kt-badge--bold kt-font-success">S</span>
                                    </div>
                                    <div class="kt-user-card__name">
                                        @ViewBag.UserFullName
                                    </div>
                                    @*<div class="kt-user-card__badge">
                                            <span class="btn btn-success btn-sm btn-bold btn-font-md">23 messages</span>
                                        </div>*@
                                </div>

                                <!--end: Head -->
                                <!--begin: Navigation -->
                                <div class="kt-notification">
                                    @*<a href="custom/apps/user/profile-1/personal-information&demo=demo1.html" class="kt-notification__item">
                                            <div class="kt-notification__item-icon">
                                                <i class="flaticon2-calendar-3 kt-font-success"></i>
                                            </div>
                                            <div class="kt-notification__item-details">
                                                <div class="kt-notification__item-title kt-font-bold">
                                                    My Profile
                                                </div>
                                                <div class="kt-notification__item-time">
                                                    Account settings and more
                                                </div>
                                            </div>
                                        </a>
                                        <a href="custom/apps/user/profile-3&demo=demo1.html" class="kt-notification__item">
                                            <div class="kt-notification__item-icon">
                                                <i class="flaticon2-mail kt-font-warning"></i>
                                            </div>
                                            <div class="kt-notification__item-details">
                                                <div class="kt-notification__item-title kt-font-bold">
                                                    My Messages
                                                </div>
                                                <div class="kt-notification__item-time">
                                                    Inbox and tasks
                                                </div>
                                            </div>
                                        </a>
                                        <a href="custom/apps/user/profile-2&demo=demo1.html" class="kt-notification__item">
                                            <div class="kt-notification__item-icon">
                                                <i class="flaticon2-rocket-1 kt-font-danger"></i>
                                            </div>
                                            <div class="kt-notification__item-details">
                                                <div class="kt-notification__item-title kt-font-bold">
                                                    My Activities
                                                </div>
                                                <div class="kt-notification__item-time">
                                                    Logs and notifications
                                                </div>
                                            </div>
                                        </a>
                                        <a href="custom/apps/user/profile-3&demo=demo1.html" class="kt-notification__item">
                                            <div class="kt-notification__item-icon">
                                                <i class="flaticon2-hourglass kt-font-brand"></i>
                                            </div>
                                            <div class="kt-notification__item-details">
                                                <div class="kt-notification__item-title kt-font-bold">
                                                    My Tasks
                                                </div>
                                                <div class="kt-notification__item-time">
                                                    latest tasks and projects
                                                </div>
                                            </div>
                                        </a>
                                        <a href="custom/apps/user/profile-1/overview&demo=demo1.html" class="kt-notification__item">
                                            <div class="kt-notification__item-icon">
                                                <i class="flaticon2-cardiogram kt-font-warning"></i>
                                            </div>
                                            <div class="kt-notification__item-details">
                                                <div class="kt-notification__item-title kt-font-bold">
                                                    Billing
                                                </div>
                                                <div class="kt-notification__item-time">
                                                    billing & statements <span class="kt-badge kt-badge--danger kt-badge--inline kt-badge--pill kt-badge--rounded">2 pending</span>
                                                </div>
                                            </div>
                                        </a>*@
                                    <div class="kt-notification__custom kt-space-between">
                                        <a href='@Url.Action("LogOut", "Account")' class="btn btn-label btn-label-brand btn-sm btn-bold">Sign Out</a>
                                        @*<a href="custom/user/login-v2&demo=demo1.html" target="_blank" class="btn btn-clean btn-sm btn-bold">Upgrade Plan</a>*@
                                    </div>
                                </div>

                                <!--end: Navigation -->
                            </div>
                        </div>
                        <div class="kt-header__topbar-item kt-header__topbar-item--user">
                            <div class="kt-header__topbar-wrapper" data-toggle="dropdown" data-offset="0px,0px">
                                <div class="kt-header__topbar-user">
                                    <!--use below badge element instead the user avatar to display username's first letter(remove kt-hidden class to display it) -->
                                    <div class="kt-badge kt-badge--username kt-badge--unified-success kt-badge--lg kt-badge--rounded kt-badge--bold" id="viewNotification">
                                        <div class="flaticon2-bell-4 text-success" style="margin-top: 24px; margin-left: 9px;">
                                            <div class="badge" id="notificationCount">
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="dropdown-menu dropdown-menu-fit dropdown-menu-right dropdown-menu-anim dropdown-menu-top-unround dropdown-menu-xl">
                                <!--begin: Navigation -->
                                <div class="row" id="clearAll">
                                </div>
                                <div class="kt-notification" id="notificationArea">
                                </div>

                                <!--end: Navigation -->
                            </div>
                        </div>
                        <!--<div class="kt-header__topbar-item kt-header__topbar-item--user">-->
                        @*<div class="kt-header__topbar-wrapper" data-toggle="dropdown" data-offset="0px,0px">
                                <div class="kt-header__topbar-user">
                                    <span class="kt-badge kt-badge--username kt-badge--unified-success kt-badge--lg kt-badge--rounded kt-badge--bold">
                                        <i class="flaticon2-bell-4 text-success"></i>
                                    </span>
                                </div>
                            </div>*@
                        <!--<div class="collapse navbar-collapse" id="navbarSupportedContent-5">

                                <ul class="navbar-nav ml-auto nav-flex-icons">
                                    <li class="nav-item avatar dropdown">
                                        <a class="nav-link dropdown-toggle waves-effect waves-light" id="navbarDropdownMenuLink-5" data-toggle="dropdown" aria-haspopup="true" aria-expanded="true">
                                            <span class="badge badge-danger ml-2">4</span>
                                            <i class="fas fa-bell"></i>
                                        </a>
                                        <div class="dropdown-menu dropdown-menu-lg-right dropdown-secondary" aria-labelledby="navbarDropdownMenuLink-5">
                                            <a class="dropdown-item waves-effect waves-light" href="#">Action <span class="badge badge-danger ml-2">4</span></a>
                                            <a class="dropdown-item waves-effect waves-light" href="#">Another action <span class="badge badge-danger ml-2">1</span></a>
                                            <a class="dropdown-item waves-effect waves-light" href="#">Something else here <span class="badge badge-danger ml-2">4</span></a>
                                        </div>
                                    </li>
                                </ul>
                            </div>
                        </div>-->
                        <!--end: User Bar -->
                    </div>
                    <!-- end:: Header Topbar -->
                </div>
                <!-- end:: Header -->

                @RenderBody()

                <input id="hiddenUserId" value="@ViewBag.UserId" type="hidden" />
                <input id="hiddenEmployeeId" value="@ViewBag.EmployeeId" type="hidden" />
                <input id="hiddenDefaultCompanyId" value="@ViewBag.DefaultCompanyId" type="hidden" />
                <input id="hiddenDefaultDepartmentId" value="@ViewBag.DefaultDepartmentId" type="hidden" />
                <input id="hiddenDesignationTypeEnum" value="@ViewBag.DesignationTypeEnum" type="hidden" />
                <input id="hiddenDesignationName" value="@ViewBag.DesignationName" type="hidden" />
                <!-- begin:: Footer -->
                <div class="kt-footer  kt-grid__item kt-grid kt-grid--desktop kt-grid--ver-desktop" id="kt_footer">
                    <div class="kt-container  kt-container--fluid ">
                        <div class="kt-footer__copyright">
                            Copyright © @DateTime.Now.Year &nbsp;&nbsp; <a href="https://navitsa.com/" target="_blank">Navitsa Technologies (Pvt) Ltd</a>. All rights reserved.
                        </div>
                        <div class="kt-footer__menu">
                            @*<a href="http://keenthemes.com/metronic" target="_blank" class="kt-footer__menu-link kt-link">About</a>
                                <a href="http://keenthemes.com/metronic" target="_blank" class="kt-footer__menu-link kt-link">Team</a>
                                <a href="http://keenthemes.com/metronic" target="_blank" class="kt-footer__menu-link kt-link">Contact</a>*@
                        </div>
                    </div>
                </div>

                <!-- end:: Footer -->
            </div>
        </div>
    </div>
    <!-- end:: Page -->
    <!-- begin::Scrolltop -->
    <div id="kt_scrolltop" class="kt-scrolltop">
        <i class="fa fa-arrow-up"></i>
    </div>

    <!-- end::Scrolltop -->
    <!-- begin::Global Config(global config for global JS sciprts) -->
    <script>
        var KTAppOptions = {
            "colors": {
                "state": {
                    "brand": "#5d78ff",
                    "dark": "#282a3c",
                    "light": "#ffffff",
                    "primary": "#F83F5F",
                    "success": "#34bfa3",
                    "info": "#36a3f7",
                    "warning": "#ffb822",
                    "danger": "#fd3995"
                },
                "base": {
                    "label": [
                        "#c5cbe3",
                        "#a1a8c3",
                        "#3d4465",
                        "#3e4466"
                    ],
                    "shape": [
                        "#f0f3ff",
                        "#d9dffa",
                        "#afb4d4",
                        "#646c9a"
                    ]
                }
            }
        };
        $("#viewNotification").on("click", function () {
            let loggedInUserId = parseInt($("#hiddenUserId").val());
            getNotificationByUserId(loggedInUserId)
        });

        function getNotificationByUserId(loggedInUserId) {
            $.ajax({
                url: apiUrl + "Administration/Notification/Notifications/" + loggedInUserId,
                method: "GET",
                success: function (response) {
                    var notification = response.data
                    var output = '';
                    var countoutput = '';
                    var clearAll = '';
                    var notificationCount = 0;
                    var area = document.getElementById("notificationArea");
                    var countarea = document.getElementById("notificationCount");
                    var clearAllArea = document.getElementById("clearAll");
                    $.each(notification, function (index, item) {
                        output += '<div class="kt-notification__item">';
                        output += '<div class="kt-notification__item-details">';
                        output += '<div class="kt-notification__item-time">' + item.description + '<span style="float: right;" onclick="clearNotification(' + item.notificationId + ')" class="flaticon2-rubbish-bin-delete-button"></span>';
                        output += '</div>';
                        output += '</div >';
                        output += '</div >';
                        notificationCount++
                    });
                    countoutput += '<div class="message-count">' + notificationCount + '</div>'
                    countarea.innerHTML = countoutput
                    area.innerHTML = output
                    if (notificationCount > 0) {
                        clearAll += '<div class="col-12" style="padding-top: 5px; padding-right:29.5px">';
                        clearAll += '<span style = "float: right; cursor:pointer" onclick = "clearAllNotification()"> Clear All</span >';
                        clearAll += '</div> ';
                    }
                    clearAllArea.innerHTML = clearAll
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    toastr.error(jqXHR.responseText);
                }
            });
        }
        function clearAllNotification() {
            let modifiedUserId = parseInt($("#hiddenUserId").val());
            let viewedDateTime = new Date();

            let vm = {
                modifiedUserId,
                viewedDateTime
            }

            $.ajax({
                url: apiUrl + "Administration/Notification/ClearAll",
                method: "PUT",
                data: JSON.stringify(vm),
                contentType: "application/json; charset=utf-8",
                success: function () {
                    getNotificationByUserId(modifiedUserId)
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    toastr.error(jqXHR.responseText);
                }
            });
        }
        function clearNotification(notificationId) {
            let modifiedUserId = parseInt($("#hiddenUserId").val());
            let viewedDateTime = new Date();

            let vm = {
                notificationId,
                modifiedUserId,
                viewedDateTime
            }

            $.ajax({
                url: apiUrl + "Administration/Notification",
                method: "PUT",
                data: JSON.stringify(vm),
                contentType: "application/json; charset=utf-8",
                success: function () {
                    getNotificationByUserId(modifiedUserId)
                },
                error: function (jqXHR, textStatus, errorThrown) {
                    toastr.error(jqXHR.responseText);
                }
            });
        }
    </script>
    <!--Drop zone lib scripts  -->
    <script src="~/scripts/dropzone/dropzone.min.js"></script>
    <link href="~/scripts/dropzone/dropzone.min.css" rel="stylesheet" />

    <!-- end::Global Config -->
    <!--begin::Global Theme Bundle(used by all pages) -->
    <!--begin:: Vendor Plugins -->
    @*To comment*@
    @*<script src="~/assets/plugins/general/jquery/dist/jquery.js" type="text/javascript"></script>*@
    <script src="~/assets/plugins/general/popper.js/dist/umd/popper.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap/dist/js/bootstrap.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js-cookie/src/js.cookie.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/moment/min/moment.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/tooltip.js/dist/umd/tooltip.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/perfect-scrollbar/dist/perfect-scrollbar.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/sticky-js/dist/sticky.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/wnumb/wNumb.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/jquery-form/dist/jquery.form.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/block-ui/jquery.blockUI.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-datepicker/dist/js/bootstrap-datepicker.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js/global/integration/plugins/bootstrap-datepicker.init.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-datetime-picker/js/bootstrap-datetimepicker.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-timepicker/js/bootstrap-timepicker.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js/global/integration/plugins/bootstrap-timepicker.init.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-daterangepicker/daterangepicker.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-touchspin/dist/jquery.bootstrap-touchspin.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-maxlength/src/bootstrap-maxlength.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/plugins/bootstrap-multiselectsplitter/bootstrap-multiselectsplitter.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-select/dist/js/bootstrap-select.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-switch/dist/js/bootstrap-switch.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js/global/integration/plugins/bootstrap-switch.init.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/select2/dist/js/select2.full.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/ion-rangeslider/js/ion.rangeSlider.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/typeahead.js/dist/typeahead.bundle.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/handlebars/dist/handlebars.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/inputmask/dist/jquery.inputmask.bundle.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/inputmask/dist/inputmask/inputmask.date.extensions.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/inputmask/dist/inputmask/inputmask.numeric.extensions.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/nouislider/distribute/nouislider.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/owl.carousel/dist/owl.carousel.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/autosize/dist/autosize.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/clipboard/dist/clipboard.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/dropzone/dist/dropzone.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js/global/integration/plugins/dropzone.init.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/quill/dist/quill.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/yaireo/tagify/dist/tagify.polyfills.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/yaireo/tagify/dist/tagify.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/summernote/dist/summernote.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/markdown/lib/markdown.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-markdown/js/bootstrap-markdown.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js/global/integration/plugins/bootstrap-markdown.init.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/bootstrap-notify/bootstrap-notify.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js/global/integration/plugins/bootstrap-notify.init.js" type="text/javascript"></script>
    @*To comment*@
    @*<script src="~/assets/plugins/general/jquery-validation/dist/jquery.validate.js" type="text/javascript"></script>*@
    <script src="~/assets/plugins/general/jquery-validation/dist/additional-methods.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js/global/integration/plugins/jquery-validation.init.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/toastr/build/toastr.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/dual-listbox/dist/dual-listbox.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/raphael/raphael.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/morris.js/morris.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/chart.js/dist/Chart.bundle.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/plugins/bootstrap-session-timeout/dist/bootstrap-session-timeout.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/plugins/jquery-idletimer/idle-timer.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/waypoints/lib/jquery.waypoints.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/counterup/jquery.counterup.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/es6-promise-polyfill/promise.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/sweetalert2/dist/sweetalert2.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/js/global/integration/plugins/sweetalert2.init.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/jquery.repeater/src/lib.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/jquery.repeater/src/jquery.input.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/jquery.repeater/src/repeater.js" type="text/javascript"></script>
    <script src="~/assets/plugins/general/dompurify/dist/purify.js" type="text/javascript"></script>

    <!--end:: Vendor Plugins -->
    <script src="~/assets/js/scripts.bundle.js" type="text/javascript"></script>

    <!--begin:: Vendor Plugins for custom pages -->
    <script src="~/assets/plugins/custom/plugins/jquery-ui/jquery-ui.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/fullcalendar/core/main.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/fullcalendar/daygrid/main.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/fullcalendar/google-calendar/main.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/fullcalendar/interaction/main.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/fullcalendar/list/main.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/fullcalendar/timegrid/main.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/gmaps/gmaps.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/flot/dist/es5/jquery.flot.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/flot/source/jquery.flot.resize.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/flot/source/jquery.flot.categories.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/flot/source/jquery.flot.pie.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/flot/source/jquery.flot.stack.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/flot/source/jquery.flot.crosshair.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/flot/source/jquery.flot.axislabels.js" type="text/javascript"></script>

    @*<script src="~/assets/plugins/custom/jszip/dist/jszip.min.js" type="text/javascript"></script>*@



    <script src="~/assets/plugins/custom/pdfmake/build/pdfmake.min.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/pdfmake/build/vfs_fonts.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/jstree/dist/jstree.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/jqvmap/dist/jquery.vmap.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/jqvmap/dist/maps/jquery.vmap.world.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/jqvmap/dist/maps/jquery.vmap.russia.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/jqvmap/dist/maps/jquery.vmap.usa.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/jqvmap/dist/maps/jquery.vmap.germany.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/jqvmap/dist/maps/jquery.vmap.europe.js" type="text/javascript"></script>
    <script src="~/assets/plugins/custom/uppy/dist/uppy.min.js" type="text/javascript"></script>



    <!--end:: Vendor Plugins for custom pages -->
    <!--end::Global Theme Bundle -->
    <!--begin::Page Vendors(used by this page) -->
    <script src="//maps.google.com/maps/api/js?key=AIzaSyBTGnKT7dt597vo9QgeQ7BFhvSRP4eiMSM" type="text/javascript"></script>

    <!--end::Page Vendors -->
    <!--begin::Page Scripts(used by this page) -->
    <script src="~/assets/js/pages/dashboard.js" type="text/javascript"></script>

    <!--end::Page Scripts -->
    @*<script src="https://cdn3.devexpress.com/jslib/19.2.3/js/dx.all.js"></script>*@
    <script src="~/scripts/common.js"></script>
    <script src="~/scripts/signature_pad.min.js"></script>
    <script src="~/scripts/dx.aspnet.data.js"></script>
    <script>
        const apiUrl = '@System.Configuration.ConfigurationManager.AppSettings["ServiceUrl"]';
        const designationTypeEnum = parseInt($("#hiddenDesignationTypeEnum").val());
        const designationName = $("#hiddenDesignationName").val();

        $.ajaxSetup({
            beforeSend: function (xhr) {
                xhr.setRequestHeader('Authorization', '@ViewBag.AccessToken');
            }
        });

        if ("@ViewBag.Notification")
            toastr.warning("@ViewBag.Notification");
        window.onload = function () {
            let loggedInUserId = parseInt($("#hiddenUserId").val());
            getNotificationByUserId(loggedInUserId)
        };
    </script>


    @RenderSection("scripts", required: false)
</body>
<!-- end::Body -->
</html>
