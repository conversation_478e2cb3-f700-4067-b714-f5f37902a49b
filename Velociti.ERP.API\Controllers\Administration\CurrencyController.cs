﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class CurrencyController : ControllerBase
    {
        private readonly ICurrencyService _currencyService;

        public CurrencyController(ICurrencyService currencyService)
        {
            _currencyService = currencyService;
        }

        [HttpGet]
        public async Task<ActionResult<Response<IEnumerable<Currency>>>> Get()
        {
            return Ok(await _currencyService.GetAllAsync());
        }


        [HttpGet]
        [Route("Single/{currencyId}")]
        public async Task<IActionResult> FindById(int currencyId)
        {
            return Ok(await _currencyService.FindByIdAsync(currencyId));
        }

        [HttpGet]
        [Route("{currencyCode}")]
        public async Task<IActionResult> FindByCode(string currencyCode)   
        {
            return Ok(await _currencyService.FindByCodeAsync(currencyCode));  
        }

        [HttpGet]
        [Route("ShortList")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetShortList()
        {
            var list = await _currencyService.GetShortListAsync();

            return Ok(list);
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Currency currency)
        {
            if (currency == null)
                return BadRequest();

            await _currencyService.SaveAsync(currency);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> ToggleActivation(int id)
        {
            await _currencyService.ToggleActivationAsync(id);

            return Ok();
        }
    }
}