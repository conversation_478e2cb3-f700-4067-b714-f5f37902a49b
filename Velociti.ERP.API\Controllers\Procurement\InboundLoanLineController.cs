﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class InboundLoanLineController : ControllerBase
    {
        private readonly IInboundLoanLineService inboundLoanLineService;
        public InboundLoanLineController(IInboundLoanLineService inboundLoanLineService)
        {
            this.inboundLoanLineService = inboundLoanLineService;
        }
        [HttpGet]
        [Route("{loanOrderId}/Company/{companyId}")]
        public async Task<IActionResult> GetAll(int loanOrderId, int companyId)
        {
            return Ok(await this.inboundLoanLineService.GetByHeaderIdAsync(companyId, loanOrderId));
        }
    }
}