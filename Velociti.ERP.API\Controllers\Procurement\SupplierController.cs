﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;
//using static Velociti.ERP.Domain.Entities..SupportData;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class SupplierController : ControllerBase
    {
        private readonly ISupplierService _supplierService;
        private readonly IWebHostEnvironment _hostingEnvironment;
        private readonly IConfiguration _config;

        public SupplierController(ISupplierService supplierService, IWebHostEnvironment webHostEnvironment, IConfiguration configuration)  
        {
            _supplierService = supplierService;
            _hostingEnvironment = webHostEnvironment;
            _config = configuration;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<Supplier>>>> Get(int companyId)
        {
            //string rootPath = _hostingEnvironment.ContentRootPath;
            //var folder = _config.GetValue<string>("AppSettings:TransImagesViewFolderPath");
            //var filepath = Path.Combine(rootPath, folder, "dsds");

            return Ok(await _supplierService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("Company/{companyId}/Products/{productIdList}")]
        public async Task<ActionResult<Response<IEnumerable<Supplier>>>> GetByProductIds(string productIdList, int companyId)
        {
            var productIds = productIdList.Split(',').Select(Int32.Parse).ToList();
            return Ok(await _supplierService.GeByProductsAsync(productIds, companyId));
        }

        [HttpGet]
        [Route("{supplierId}")]
        public async Task<ActionResult<Response<Supplier>>> FindById(int supplierId)
        {
            return Ok(await _supplierService.FindByIdAsync(supplierId));
        }

        [HttpGet]
        [Route("ShortList/Company/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            return Ok(await _supplierService.GetShortListAsync(companyId));
        }

        [HttpGet]
        [Route("QualifiedShortList/Company/{companyId}")]
        public async Task<IActionResult> GetQualifiedShortList(int companyId)
        {
            return Ok(await _supplierService.GetQualifiedShortListAsync(companyId));
        }

        [HttpGet]
        [Route("SubContractorShortList/Company/{companyId}")]
        public async Task<IActionResult> GetSubContractorShortList(int companyId)
        {
            return Ok(await _supplierService.GetSubContractorShortListAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Supplier supplier)
        {
            if (supplier == null)
                return BadRequest();

            await _supplierService.SaveAsync(supplier);

            return Ok();
        }

        [HttpDelete]
        [Route("{supplierId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int supplierId, int userId)  
        {
            await _supplierService.ToggleActivationAsync(supplierId, userId);

            return Ok();
        }
    }
}