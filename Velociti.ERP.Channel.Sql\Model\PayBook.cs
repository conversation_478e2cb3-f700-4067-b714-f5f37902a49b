﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("PayBooks", Schema = "fin")]
    public partial class PayBook
    {
        public PayBook()
        {
            ChequeBooks = new HashSet<ChequeBook>();
            OutboundPayments = new HashSet<OutboundPayment>();
        }

        [Key]
        public int PayBookId { get; set; }
        public int? CompanyId { get; set; }
        public byte? TypeEnum { get; set; }
        [StringLength(50)]
        public string PayBookCode { get; set; }
        [StringLength(255)]
        public string PayBookName { get; set; }
        public int? AccountId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(AccountId))]
        [InverseProperty("PayBooks")]
        public virtual Account Account { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("PayBooks")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(ChequeBook.PayBook))]
        public virtual ICollection<ChequeBook> ChequeBooks { get; set; }
        [InverseProperty(nameof(OutboundPayment.PayBook))]
        public virtual ICollection<OutboundPayment> OutboundPayments { get; set; }
    }
}
