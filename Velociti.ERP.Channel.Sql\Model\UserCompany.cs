﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("UserCompanies", Schema = "adm")]
    public partial class UserCompany
    {
        [Key]
        public int UserCompanyId { get; set; }
        public int UserId { get; set; }
        public int CompanyId { get; set; }
        public int DesignationId { get; set; }
        public int? EmployeeId { get; set; }
        public bool? IsDefault { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("UserCompanies")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DesignationId))]
        [InverseProperty("UserCompanies")]
        public virtual Designation Designation { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("UserCompanies")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(UserId))]
        [InverseProperty("UserCompanies")]
        public virtual User User { get; set; }
    }
}
