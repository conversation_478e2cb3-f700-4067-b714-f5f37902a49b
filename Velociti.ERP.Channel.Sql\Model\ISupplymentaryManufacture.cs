﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_SupplymentaryManufactures")]
    public partial class ISupplymentaryManufacture
    {
        [StringLength(255)]
        public string ManufacturerCode { get; set; }
        [StringLength(255)]
        public string Category { get; set; }
        [StringLength(255)]
        public string ManufacturerName { get; set; }
        [StringLength(255)]
        public string Address { get; set; }
        [StringLength(255)]
        public string Descriptions { get; set; }
        [StringLength(255)]
        public string TaxRegistrationNo { get; set; }
        [StringLength(255)]
        public string ContactPersonName { get; set; }
        [StringLength(255)]
        public string ContactNoPhone { get; set; }
        [StringLength(255)]
        public string ContactNoMobile { get; set; }
        [StringLength(255)]
        public string Email { get; set; }
    }
}
