﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class UserRolesController : ControllerBase
    {
        private readonly IUserRoleService _userRoleService;

        public UserRolesController(IUserRoleService userRoleService)
        {
            _userRoleService = userRoleService;
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> GetAll(int companyId)
        {
            return Ok(await _userRoleService.GetAllAsync(companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]List<UserRole> userRoles)
        {
            if (userRoles == null)
                return BadRequest();

            await _userRoleService.SaveAsync(userRoles);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> Delete([FromBody]UserRole userRole)
        {
            await _userRoleService.DeleteAsync(userRole);

            return Ok();
        }
    }
}