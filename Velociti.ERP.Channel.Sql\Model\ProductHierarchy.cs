﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ProductHierarchy", Schema = "inv")]
    public partial class ProductHierarchy
    {
        public ProductHierarchy()
        {
            BillOfMaterialLines = new HashSet<BillOfMaterialLine>();
            DiscountSchemeCriteriaLines = new HashSet<DiscountSchemeCriteriaLine>();
            DiscountSchemeLines = new HashSet<DiscountSchemeLine>();
            FreeIssueCriteriaLines = new HashSet<FreeIssueCriteriaLine>();
            ProductProductHierarchyId1Navigation = new HashSet<Product>();
            ProductProductHierarchyId2Navigation = new HashSet<Product>();
            ProductProductHierarchyId3Navigation = new HashSet<Product>();
            ProductProductHierarchyId4Navigation = new HashSet<Product>();
            ProductProductHierarchyId5Navigation = new HashSet<Product>();
            ProductProductHierarchyId6Navigation = new HashSet<Product>();
        }

        [Key]
        public int ProductHierarchyId { get; set; }
        public byte? LevelEnum { get; set; }
        [StringLength(50)]
        public string Code { get; set; }
        [StringLength(255)]
        public string Description { get; set; }
        public int? ParentProductHierarchyId { get; set; }
        public int CompanyId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ProductHierarchies")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(BillOfMaterialLine.ProductCategory))]
        public virtual ICollection<BillOfMaterialLine> BillOfMaterialLines { get; set; }
        [InverseProperty(nameof(DiscountSchemeCriteriaLine.ProductHierarchyId2Navigation))]
        public virtual ICollection<DiscountSchemeCriteriaLine> DiscountSchemeCriteriaLines { get; set; }
        [InverseProperty(nameof(DiscountSchemeLine.ProductHierarchyId2Navigation))]
        public virtual ICollection<DiscountSchemeLine> DiscountSchemeLines { get; set; }
        [InverseProperty(nameof(FreeIssueCriteriaLine.ProductHierarchyId2Navigation))]
        public virtual ICollection<FreeIssueCriteriaLine> FreeIssueCriteriaLines { get; set; }
        [InverseProperty(nameof(Product.ProductHierarchyId1Navigation))]
        public virtual ICollection<Product> ProductProductHierarchyId1Navigation { get; set; }
        [InverseProperty(nameof(Product.ProductHierarchyId2Navigation))]
        public virtual ICollection<Product> ProductProductHierarchyId2Navigation { get; set; }
        [InverseProperty(nameof(Product.ProductHierarchyId3Navigation))]
        public virtual ICollection<Product> ProductProductHierarchyId3Navigation { get; set; }
        [InverseProperty(nameof(Product.ProductHierarchyId4Navigation))]
        public virtual ICollection<Product> ProductProductHierarchyId4Navigation { get; set; }
        [InverseProperty(nameof(Product.ProductHierarchyId5Navigation))]
        public virtual ICollection<Product> ProductProductHierarchyId5Navigation { get; set; }
        [InverseProperty(nameof(Product.ProductHierarchyId6Navigation))]
        public virtual ICollection<Product> ProductProductHierarchyId6Navigation { get; set; }
    }
}
