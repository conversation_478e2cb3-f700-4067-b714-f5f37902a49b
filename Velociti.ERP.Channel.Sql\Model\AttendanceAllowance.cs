﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("AttendanceAllowance", Schema = "hr")]
    public partial class AttendanceAllowance
    {
        [Key]
        public int AttendanceAllowanceId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(100)]
        public string Description { get; set; }
        [Column(TypeName = "decimal(6, 2)")]
        public decimal? Days { get; set; }
        [Column(TypeName = "decimal(10, 2)")]
        public decimal? Allowance { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("AttendanceAllowances")]
        public virtual Company Company { get; set; }
    }
}
