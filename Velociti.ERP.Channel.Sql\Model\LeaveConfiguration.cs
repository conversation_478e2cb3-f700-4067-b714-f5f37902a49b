﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("LeaveConfigurations", Schema = "hr")]
    public partial class LeaveConfiguration
    {
        [Key]
        public int LeaveConfigurationId { get; set; }
        public int? CompanyId { get; set; }
        public int? EmployeeCategoryId { get; set; }
        public int? EmployeeGroupId { get; set; }
        public byte? LeaveTypeEnum { get; set; }
        public byte? Quarter1Days { get; set; }
        public byte? Quarter2Days { get; set; }
        public byte? Quarter3Days { get; set; }
        public byte? Quarter4Days { get; set; }
        public byte? NoOfDays { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("LeaveConfigurations")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(EmployeeCategoryId))]
        [InverseProperty(nameof(SupportData.LeaveConfigurationEmployeeCategories))]
        public virtual SupportData EmployeeCategory { get; set; }
        [ForeignKey(nameof(EmployeeGroupId))]
        [InverseProperty(nameof(SupportData.LeaveConfigurationEmployeeGroups))]
        public virtual SupportData EmployeeGroup { get; set; }
    }
}
