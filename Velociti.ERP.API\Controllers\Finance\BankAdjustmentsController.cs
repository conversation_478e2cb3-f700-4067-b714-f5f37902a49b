﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class BankAdjustmentsController : ControllerBase
    {
        private readonly IBankAdjustmentService _bankAdjustmentService;

        public BankAdjustmentsController(IBankAdjustmentService bankAdjustmentService)
        {
            _bankAdjustmentService = bankAdjustmentService;
        }

        [HttpGet]
        [Route("Single/{bankAdjustmentId}")]
        public async Task<IActionResult> GetById(int bankAdjustmentId)
        {
            return Ok(await _bankAdjustmentService.GetByIdAsync(bankAdjustmentId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _bankAdjustmentService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]BankAdjustment bankAdjustment)
        {
            await _bankAdjustmentService.SaveAsync(bankAdjustment);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]BankAdjustment bankAdjustment)
        {
            switch (bankAdjustment.Action)
            {
                case "submit":
                    await _bankAdjustmentService.SubmitAsync(bankAdjustment);
                    break;
                case "cancel":
                    await _bankAdjustmentService.CancelAsync(bankAdjustment);
                    break;
            }

            return Ok();
        }
    }
}