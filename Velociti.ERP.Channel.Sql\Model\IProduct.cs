﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Product")]
    public partial class IProduct
    {
        [StringLength(255)]
        public string ProductCode { get; set; }
        [StringLength(255)]
        public string ProductDescription { get; set; }
        [StringLength(255)]
        public string Category { get; set; }
        [StringLength(255)]
        public string DefaultUnitOfMeasure { get; set; }
        [StringLength(255)]
        public string SecondaryunitsofMeasure { get; set; }
        [Column("SecondaryUOMConversion")]
        [StringLength(50)]
        public string SecondaryUomconversion { get; set; }
        [StringLength(255)]
        public string CostingMethiod { get; set; }
        [StringLength(255)]
        public string ProductHierarchyLevel1 { get; set; }
        [StringLength(255)]
        public string ProductHierarchyLevel2 { get; set; }
        [StringLength(255)]
        public string ProductHierarchyLevel3 { get; set; }
        [StringLength(255)]
        public string ProductHierarchyLevel4 { get; set; }
        [StringLength(255)]
        public string ProductHierarchyLevel5 { get; set; }
        [StringLength(255)]
        public string ProductHierarchyLevel6 { get; set; }
        [StringLength(255)]
        public string ProductNature { get; set; }
        [StringLength(255)]
        public string DispatchOnly { get; set; }
        [StringLength(255)]
        public string ReceiptAndDispatch { get; set; }
        [StringLength(255)]
        public string StockManagementMethod { get; set; }
        [StringLength(255)]
        public string DefaultWarehouse { get; set; }
        [StringLength(255)]
        public string MaximumLevel { get; set; }
        [StringLength(255)]
        public string SafetyLevel { get; set; }
        [StringLength(255)]
        public string ReorderLevel { get; set; }
        [StringLength(255)]
        public string ReorderQuantity { get; set; }
        [StringLength(255)]
        public string BufferStock { get; set; }
        [StringLength(255)]
        public string LeadTime { get; set; }
        [Column("ToleranceForGRN")]
        [StringLength(255)]
        public string ToleranceForGrn { get; set; }
        [Column("HSCode")]
        [StringLength(255)]
        public string Hscode { get; set; }
        [StringLength(255)]
        public string MovementMethod { get; set; }
        [StringLength(255)]
        public string Brand { get; set; }
        [StringLength(255)]
        public string ExtendedDescription { get; set; }
        [StringLength(255)]
        public string ManufacturedProduct { get; set; }
        [Column("GRNQualityCheck")]
        [StringLength(255)]
        public string GrnqualityCheck { get; set; }
        [StringLength(255)]
        public string RegistrationName { get; set; }
        public double? AverageWeight { get; set; }
    }
}
