﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("UserSupervisors", Schema = "adm")]
    public partial class UserSupervisor
    {
        [Key]
        public int UserSupervisorId { get; set; }
        public int UserId { get; set; }
        public int CompanyId { get; set; }
        public int SupervisorId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("UserSupervisors")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(UserId))]
        [InverseProperty("UserSupervisors")]
        public virtual User User { get; set; }
    }
}
