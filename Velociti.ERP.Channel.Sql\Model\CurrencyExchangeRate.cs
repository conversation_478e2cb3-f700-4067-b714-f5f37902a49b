﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("CurrencyExchangeRates", Schema = "fin")]
    public partial class CurrencyExchangeRate
    {
        [Key]
        public int CurrencyExchangeRateId { get; set; }
        public int CurrencyId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? BuyingRate { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? SellingRate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? EffectiveDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("CurrencyExchangeRates")]
        public virtual Currency Currency { get; set; }
    }
}
