﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Moulds")]
    public partial class IMould
    {
        [Column("REALCODE")]
        [StringLength(255)]
        public string Realcode { get; set; }
        [StringLength(255)]
        public string CategoryName { get; set; }
        [Column("DESCRIPTION")]
        [StringLength(255)]
        public string Description { get; set; }
        [StringLength(255)]
        public string RimSize { get; set; }
        [StringLength(255)]
        public string Measurement { get; set; }
        [StringLength(255)]
        public string Brand { get; set; }
        [Column("FIX")]
        [StringLength(255)]
        public string Fix { get; set; }
        public byte? CavityCount { get; set; }
        [StringLength(255)]
        public string YearOfConstruction { get; set; }
        [StringLength(255)]
        public string CavityName1 { get; set; }
        [StringLength(255)]
        public string CavityName2 { get; set; }
        [StringLength(255)]
        public string CavityName3 { get; set; }
        [StringLength(255)]
        public string CavityName4 { get; set; }
        [StringLength(255)]
        public string CavityName5 { get; set; }
    }
}
