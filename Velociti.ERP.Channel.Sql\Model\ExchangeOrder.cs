﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ExchangeOrders", Schema = "prc")]
    public partial class ExchangeOrder
    {
        public ExchangeOrder()
        {
            ExchangeOrderLines = new HashSet<ExchangeOrderLine>();
        }

        [Key]
        public int ExchangeOrderId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        public int? SupplierId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? VersionNo { get; set; }
        public int? GoodsReceiveNoteId { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [StringLength(255)]
        public string DeliveryAddress { get; set; }
        [StringLength(255)]
        public string SupplierAddress { get; set; }
        public int? ShipmentPackingMethodId { get; set; }
        [StringLength(255)]
        public string PortOfShipment { get; set; }
        public byte? ShippingTermEnum { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        public int? TxnWorkFlowId { get; set; }
        public int? ParentId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("ExchangeOrders")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("ExchangeOrders")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(GoodsReceiveNoteId))]
        [InverseProperty("ExchangeOrders")]
        public virtual GoodsReceiveNote GoodsReceiveNote { get; set; }
        [ForeignKey(nameof(ShipmentPackingMethodId))]
        [InverseProperty(nameof(SupportData.ExchangeOrders))]
        public virtual SupportData ShipmentPackingMethod { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("ExchangeOrders")]
        public virtual Supplier Supplier { get; set; }
        [ForeignKey(nameof(TxnWorkFlowId))]
        [InverseProperty("ExchangeOrders")]
        public virtual TxnWorkFlow TxnWorkFlow { get; set; }
        [InverseProperty(nameof(ExchangeOrderLine.ExchangeOrder))]
        public virtual ICollection<ExchangeOrderLine> ExchangeOrderLines { get; set; }
    }
}
