﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Sql.Services.Manufacturing;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]
    public class BillOfMaterialsController : ControllerBase
    {
        private readonly IBillOfMaterialService _billOfMaterialService;

        public BillOfMaterialsController(IBillOfMaterialService billOfMaterialService)
        {
            _billOfMaterialService = billOfMaterialService;
        }

        [HttpGet]
        [Route("ShortList/{companyId}/Product/{productId}")]
        public async Task<IActionResult> GetShortList(int companyId, int productId)
        {
            var list = await _billOfMaterialService.GetShortListAsync(companyId, productId);

            return Ok(list);
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetShortList(int companyId)
        {
            var list = await _billOfMaterialService.GetShortListAsync(companyId);

            return Ok(list);
        }

        [HttpGet]
        [Route("GetAllActive/{companyId}/Product/{productId}")]
        public async Task<IActionResult> GetBOMAllActiveList(int companyId, int productId)
        {
            var list = await _billOfMaterialService.GetAllActiveListForProductAsync(companyId, productId);

            return Ok(list);
        }

        [HttpGet]
        [Route("Single/{billOfMaterialId}")]
        public async Task<IActionResult> FindById(int billOfMaterialId)
        {
            return Ok(await _billOfMaterialService.FindByIdAsync(billOfMaterialId));
        }

        [HttpGet]
        [Route("{companyId}")]
        public async Task<IActionResult> Get(string action, int companyId)
        {
            return action switch
            {
                "Active" => Ok(await _billOfMaterialService.GetActiveAllAsync(companyId)),

                _ => BadRequest(),
            };
        }

        [HttpGet]
        [Route("Company/{companyId}/User/{userId}")]
        public async Task<IActionResult> Get(int companyId, int userId)  
        {            
            return Ok(await _billOfMaterialService.GetAllAsync(companyId, userId));
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody] BillOfMaterial billOfMaterial)
        {
            await _billOfMaterialService.SaveAsync(billOfMaterial);

            return Ok();
        }

        [HttpPut("{id}")]
        public async Task<IActionResult> Put(int id, [FromBody] BillOfMaterial billOfMaterial)
        {
            if (id != billOfMaterial.BillOfMaterialId)
                return BadRequest("ID mismatch");

            await _billOfMaterialService.SaveAsync(billOfMaterial);
            return Ok();
        }


        [HttpDelete]
        [Route("{billOfMaterialId}/User/{userId}/Company/{companyId}")]
        public async Task<IActionResult> ToggleActivation(int billOfMaterialId, int userId, int companyId)
        {
            await _billOfMaterialService.ToggleActivationAsync(billOfMaterialId, userId, companyId);

            return Ok();
        }

        [HttpPatch]
        [Route("{billOfMaterialId}/User/{userId}/Approve/{isApproved}")]
        public async Task<IActionResult> ToggleApprove(int billOfMaterialId, int userId, bool isApproved)
        {
            await _billOfMaterialService.ToggleApproveAsync(billOfMaterialId, userId, isApproved);

            return Ok();
        }
        }
}