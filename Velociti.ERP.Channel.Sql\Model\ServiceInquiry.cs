﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ServiceInquiries", Schema = "sales")]
    public partial class ServiceInquiry
    {
        public ServiceInquiry()
        {
            ServiceInquiryLines = new HashSet<ServiceInquiryLine>();
        }

        [Key]
        public int ServiceInquiryId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? StatusEnum { get; set; }
        public int? CustomerId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        [StringLength(255)]
        public string CurrentHomeCare { get; set; }
        [StringLength(255)]
        public string SensitivityAllergies { get; set; }
        public int? SkinTypeId { get; set; }
        public int? SkinConditionsId { get; set; }
        [StringLength(255)]
        public string MainConcerns { get; set; }
        [StringLength(1000)]
        public string SpecialNotes { get; set; }
        public int? PriceListId { get; set; }
        public int? Age { get; set; }
        [StringLength(200)]
        public string PastTreatment { get; set; }
        [StringLength(500)]
        public string OtherTreatment { get; set; }
        [Column(TypeName = "date")]
        public DateTime? NextConsultation { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("ServiceInquiries")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("ServiceInquiries")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(SkinConditionsId))]
        [InverseProperty(nameof(SupportData.ServiceInquirySkinConditions))]
        public virtual SupportData SkinConditions { get; set; }
        [ForeignKey(nameof(SkinTypeId))]
        [InverseProperty(nameof(SupportData.ServiceInquirySkinTypes))]
        public virtual SupportData SkinType { get; set; }
        [InverseProperty(nameof(ServiceInquiryLine.ServiceInquiry))]
        public virtual ICollection<ServiceInquiryLine> ServiceInquiryLines { get; set; }
    }
}
