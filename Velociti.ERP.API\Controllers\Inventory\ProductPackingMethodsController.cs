﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductPackingMethodsController : ControllerBase
    {
        private readonly IProductPackingMethodService _productPackingMethodService;

        public ProductPackingMethodsController(IProductPackingMethodService productPackingMethodService)
        {
            _productPackingMethodService = productPackingMethodService;
        }

        [HttpGet]
        [Route("Product/{productId}")]
        public async Task<IActionResult> GetByProductId(int productId)
        {
            return Ok(await _productPackingMethodService.GetByProductIdAsync(productId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ProductPackingMethod productPackingMethod)
        {
            await _productPackingMethodService.SaveAsync(productPackingMethod);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> ToggleActivation([FromBody]ProductPackingMethod productPackingMethod)
        {
            await _productPackingMethodService.ToggleActivationAsync(productPackingMethod);

            return Ok();
        }
    }
}