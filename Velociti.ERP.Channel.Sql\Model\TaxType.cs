﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("TaxTypes", Schema = "fin")]
    public partial class TaxType
    {
        public TaxType()
        {
            TaxGroupTaxTypes = new HashSet<TaxGroupTaxType>();
            TaxGroups = new HashSet<TaxGroup>();
        }

        [Key]
        public int TaxTypeId { get; set; }
        public int? CompanyId { get; set; }
        [StringLength(50)]
        public string TaxTypeCode { get; set; }
        [StringLength(255)]
        public string TaxTypeName { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? CategoryEnum { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? TaxPct { get; set; }
        [Column("IsSVAT")]
        public bool? IsSvat { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("TaxTypes")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(TaxGroupTaxType.TaxType))]
        public virtual ICollection<TaxGroupTaxType> TaxGroupTaxTypes { get; set; }
        [InverseProperty(nameof(TaxGroup.TaxType))]
        public virtual ICollection<TaxGroup> TaxGroups { get; set; }
    }
}
