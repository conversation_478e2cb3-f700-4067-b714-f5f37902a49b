﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]  
    public class InternalReturnLinesController : ControllerBase
    {
        private readonly IInternalReturnLineService _internalReturnLineService;  

        public InternalReturnLinesController(IInternalReturnLineService internalReturnLineService)      
        {
            _internalReturnLineService = internalReturnLineService;
        }

        [HttpGet]
        [Route("{internalReturnId}/company/{companyId}")]
        public async Task<IActionResult> Get(int internalReturnId, int companyId)
        {
            return Ok(await _internalReturnLineService.GetAsync(internalReturnId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> GetInternalReturnLine([FromBody]InternalReturnLine internalReturnLine)
        {
            return Ok(await _internalReturnLineService.AddInternalReturnLineAsync(internalReturnLine));
        }
    }
}