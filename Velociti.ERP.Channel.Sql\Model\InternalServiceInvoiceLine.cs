﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("InternalServiceInvoiceLines", Schema = "prc")]
    public partial class InternalServiceInvoiceLine
    {
        public InternalServiceInvoiceLine()
        {
            InternalServiceInvoiceLineDetails = new HashSet<InternalServiceInvoiceLineDetail>();
        }

        [Key]
        public int InternalServiceInvoiceLineId { get; set; }
        public int? InternalServiceInvoiceId { get; set; }
        public int? ProductId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ServiceDate { get; set; }
        public int? Duration { get; set; }
        public byte? ComponentEnum { get; set; }
        [Column(TypeName = "money")]
        public decimal? Cost { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(InternalServiceInvoiceId))]
        [InverseProperty("InternalServiceInvoiceLines")]
        public virtual InternalServiceInvoice InternalServiceInvoice { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("InternalServiceInvoiceLines")]
        public virtual Product Product { get; set; }
        [InverseProperty(nameof(InternalServiceInvoiceLineDetail.InternalServiceInvoiceLine))]
        public virtual ICollection<InternalServiceInvoiceLineDetail> InternalServiceInvoiceLineDetails { get; set; }
    }
}
