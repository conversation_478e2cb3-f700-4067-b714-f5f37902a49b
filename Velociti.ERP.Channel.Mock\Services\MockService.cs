﻿using System;
using System.Collections.Generic;
using System.Text;
using Velociti.ERP.Channel.Sql.Model;

namespace Velociti.ERP.Channel.Mock.Services
{
    public class MockService
    {
        public Product GetProductByBarcode(string barCode)
        {
            var product = new Product
            {
                ProductId = 1,
                ProductCode = "P001",
                ProductDescription = "Product Description"
            };

            return product;
        }

        public IEnumerable<PressLine> GetPressLines()
        {
            var pressLines = new List<PressLine>();

            var line1 = new PressLine
            {
                Id = 1,
                Description = "1M11"
            };
            var line2 = new PressLine
            {
                Id = 2,
                Description = "1M22"
            };
            var line3 = new PressLine
            {
                Id = 3,
                Description = "1M33"
            };
            var line4 = new PressLine
            {
                Id = 4,
                Description = "1M44"
            };

            pressLines.Add(line1);
            pressLines.Add(line2);
            pressLines.Add(line3);
            pressLines.Add(line4);

            return pressLines;
        }

        public IEnumerable<PressMachine> GetPressMachines()
        {
            var pressMachines = new List<PressMachine>();

            for (int i = 1; i <= 8; i++)
            {
                var press = new PressMachine
                {
                    Id = i,
                    Description = "PM " + i,
                    PressLineId = 1

                };

                pressMachines.Add(press);
            }

            for (int i = 1; i <= 8; i++)
            {
                var press = new PressMachine
                {
                    Id = i,
                    Description = "PM " + i,
                    PressLineId = 2

                };

                pressMachines.Add(press);
            }

            for (int i = 1; i <= 8; i++)
            {
                var press = new PressMachine
                {
                    Id = i,
                    Description = "PM " + i,
                    PressLineId = 3

                };

                pressMachines.Add(press);
            }

            for (int i = 1; i <= 8; i++)
            {
                var press = new PressMachine
                {
                    Id = i,
                    Description = "PM " + i,
                    PressLineId = 4

                };

                pressMachines.Add(press);
            }

            return pressMachines;
        }

        //public IEnumerable<SupportData> GetQCReasons()
        //{
        //    var reasons = new List<SupportData>();

        //    for (int i = 1; i <= 10; i++)
        //    {
        //        var reason = new SupportData
        //        {
        //            SupportDataId = i,
        //            SupportDataDescription = "Reason " + i,

        //        };

        //        reasons.Add(reason);
        //    }

        //    return reasons;
        //}

        public IEnumerable<SupportData> GetQCReasons()
        {
            var reasons = new List<SupportData>();

            var reason = new SupportData
            {
                SupportDataId = 1,
                SupportDataDescription = "ත්‍රෙඩ් කැඩීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 2,
                SupportDataDescription = "ත්‍රෙඩ් එකෙහි බැක් රය්න්ඩින්"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 3,
                SupportDataDescription = "බේස් එකෙහි බැක් රය්න්ඩින්"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 4,
                SupportDataDescription = "ෆික්‍ස් එකෙහි බැක් රය්න්ඩින්"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 5,
                SupportDataDescription = "නිසිලෙස රෙපෙයාර් නොවීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 6,
                SupportDataDescription = "නය්ලෝන් කෙදි මතුවීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 7,
                SupportDataDescription = "බර මදිවීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 8,
                SupportDataDescription = "ත්‍රෙඩ් එකෙහි වායු සිරවීම "
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 9,
                SupportDataDescription = "කුශන් එකෙහි වායු සිරවීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 10,
                SupportDataDescription = "බේස් එකෙහි වායු සිරවීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 11,
                SupportDataDescription = "ෆික්‍ස් එකෙහි වායු සිරවීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 12,
                SupportDataDescription = "ෆ්ලෑශ් ඝනවීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 13,
                SupportDataDescription = "ඉවත් නොවූ ෆ්ලෑශ් තිබීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 14,
                SupportDataDescription = "නිසි ලෙස බම්පින් සිදුනොවීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 15,
                SupportDataDescription = "අන්ඩකියො"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 16,
                SupportDataDescription = "අච්චුවේ පළුදු"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 17,
                SupportDataDescription = "ත්‍රෙඩ් එක නිසිලෙස සැකසී නොතිබීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 18,
                SupportDataDescription = "මතුපිට බාහිර ද්‍රව්‍ය තිබීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 19,
                SupportDataDescription = "ප්‍රෙශර් බැසීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 20,
                SupportDataDescription = "අච්චුව කැරකීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 21,
                SupportDataDescription = "අච්චුව පැත්තටයාම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 22,
                SupportDataDescription = "නොන් මාර්කින් වල වර්ණ 2 ක් තිබීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 23,
                SupportDataDescription = "ත්‍රෙඩ් කට්ට රටා වල කුණු / කළුපාට තිබීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 24,
                SupportDataDescription = "ත්‍රෙඩ් එකෙහි කුශන් පැමිනීම (පීක්) "
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 25,
                SupportDataDescription = "ෆික්‍ස් එකට නොන් මාර්කින් පැමිණිම (BLACK T1)"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 26,
                SupportDataDescription = "ටයරයෙහි එක් පැත්තක කුණු / කළුපාට තිබීම"
            };
            reasons.Add(reason);

            reason = new SupportData
            {
                SupportDataId = 27,
                SupportDataDescription = "ෆික්‍ස් එකින් පිටතට කළු පාට පැමිණීම (BLACK T1)"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 28,
                SupportDataDescription = "කම්බි මතුවීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 29,
                SupportDataDescription = "ත්‍රෙඩ් එකෙහි ෆ්ලොමාක්"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 30,
                SupportDataDescription = "කුශන් එකෙහි ෆ්ලොමාක්"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 31,
                SupportDataDescription = "බේස් එකෙහි ෆ්ලොමාක්"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 32,
                SupportDataDescription = "ෆික්‍ස් එකෙහි ප්ලොමාක්"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 33,
                SupportDataDescription = "ෆික්‍ස් කැඩීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 34,
                SupportDataDescription = "අමුද්‍රව්‍ය සංක්‍රමණයවීම නිසා ඇතිවන පුල්ලි"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 35,
                SupportDataDescription = "ත්‍රෙඩ් එකෙහි වැඩිපුර ෆ්ලෑශ් තිබීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 36,
                SupportDataDescription = "කුශන් එකෙහි වැඩිපුර ෆ්ලෑශ් තිබීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 37,
                SupportDataDescription = "බේස් එකෙහි වැඩිපුර ෆ්ලෑශ් තිබීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 38,
                SupportDataDescription = "කැපීමේ පළුදු"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 39,
                SupportDataDescription = "මතුපිට පළුදු සහ කැපීම්"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 40,
                SupportDataDescription = "බේස් ඩැමේජ්"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 41,
                SupportDataDescription = "කම්බි සහ ෆික්‍ස් වලට හානි සිදුවීම සහ කැපීම්"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 42,
                SupportDataDescription = "අකුරු වලට හානි සිදුවීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 43,
                SupportDataDescription = "අකුරු මත කුණු / කළුපාට තිබීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 44,
                SupportDataDescription = "ස්ථිති විද්යුත් සන්නයන පරීක්ෂාව අසමත්"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 45,
                SupportDataDescription = "වර්ෂය දැක්වෙන ලෝගුව නිසිලෙස නොතිබීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 46,
                SupportDataDescription = "ටයරය කැපීම සදහා ගන්නා ලදි"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 47,
                SupportDataDescription = "වැරදි ටයරයක් දැමීම"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 48,
                SupportDataDescription = "ලිමිට් ස්විච් වල දෝෂ නිසා සිදුවන කාර්මික ආපදා"
            };
            reasons.Add(reason);
            reason = new SupportData
            {
                SupportDataId = 49,
                SupportDataDescription = "වැරදි කම්පවුන්ඩ් යොදා නිෂ්පාදනය"
            };
            reasons.Add(reason);

            return reasons;
        }

        public void SaveQCStatus(QCResponse qcResponse)
        {

        }

        public Product GetPalletByBarcode(string barCode)
        {
            var product = new Product
            {
                ProductId = 1,
                ProductCode = "PL01",
                ProductDescription = "Pallet Description"
            };

            return product;
        }

        public IEnumerable<PressMachine> GetCompoundMachines()
        {
            var pressMachines = new List<PressMachine>();

            var press = new PressMachine
            {
                Id = 1,
                Description = "Profile Extruder",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 2,
                Description = "Kneeder",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 3,
                Description = "TEV Base Builder",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 4,
                Description = "Non Marking Thread Builder I",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 5,
                Description = "Non Marking Thread Builder II",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 6,
                Description = "4''  Tyre builder",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 7,
                Description = "Warm Up Mill",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 8,
                Description = "Thread Building - Station I",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 9,
                Description = "Thread Building - Station II",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 10,
                Description = "Thread Building - Station III",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 11,
                Description = "Cushioning Machine",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 12,
                Description = "Base Building (Large Tyre) ",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 13,
                Description = "Thread Building (Large Tyre)",

            };

            pressMachines.Add(press);

            press = new PressMachine
            {
                Id = 14,
                Description = "Cushioning Machine (Large Tyre)",

            };

            pressMachines.Add(press);

            //for (int i = 1; i <= 16; i++)
            //{
            //    var press = new PressMachine
            //    {
            //        Id = i,
            //        Description = "M " + i,

            //    };

            //    pressMachines.Add(press);
            //}

            return pressMachines;
        }
    }

    public class PressLine
    {
        public int Id { get; set; }
        public string Description { get; set; }
    }

    public class PressMachine
    {
        public int Id { get; set; }
        public string Description { get; set; }
        public int PressLineId { get; set; }
    }

    public class QCResponse
    {
        public int PressLineId { get; set; }
        public int PressMachineId { get; set; }
        public int QCStatusEnum { get; set; }
        public int QCReasonId { get; set; }
    }
}
