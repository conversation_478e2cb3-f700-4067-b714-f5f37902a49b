﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class ProductHierarchyController : ControllerBase
    {
        private readonly IProductHierarchyService _productHierarchyService;

        public ProductHierarchyController(IProductHierarchyService productHierarchyService)
        {
            _productHierarchyService = productHierarchyService;
        }

        [HttpGet]
        [Route("Company/{companyId}")]
        public async Task<IActionResult> Get(int companyId)
        {
            return Ok(await _productHierarchyService.GetAllAsync(companyId));
        }

        [HttpGet]
        [Route("Parent/{parentId}/Company/{companyId}")]
        public async Task<IActionResult> GetByParent(int parentId, int companyId)
        {
            return Ok(await _productHierarchyService.FindByParentAsync(parentId, companyId));
        }

        [HttpGet]
        [Route("{productHierarchyId}")]
        public async Task<IActionResult> GetById(int productHierarchyId)
        {
            return Ok(await _productHierarchyService.FindByIdAsync(productHierarchyId));
        }

        [HttpGet]
        [Route("Level/{levelId}/company/{companyId}")]
        public async Task<IActionResult> GetByLevel(int levelId, int companyId)
        {
            return Ok(await _productHierarchyService.FindByLevelEnumAsync(levelId, companyId));
        }

        [HttpGet]
        [Route("ShortListByLevel/{levelId}/company/{companyId}")]
        public async Task<IActionResult> GetShorListByLevelEnum(int levelId, int companyId)
        {
            return Ok(await _productHierarchyService.GetShorListByLevelEnumAsync(levelId, companyId));
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]ProductHierarchy productHierarchy)
        {
            if (productHierarchy == null)
                return BadRequest();

            await _productHierarchyService.SaveAsync(productHierarchy);

            return Ok();
        }

        [HttpDelete]
        public async Task<IActionResult> ToggleActivation([FromBody]ProductHierarchy productHierarchy)
        {
            if (productHierarchy == null)
                return BadRequest();

            await _productHierarchyService.ToggleActivationAsync(productHierarchy.ProductHierarchyId, productHierarchy.ModifiedUserId);

            return Ok();
        }

    }
}