﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Sql.Services.Inventory;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class WarehouseProductsController : ControllerBase
    {
        private readonly IWarehouseProductService _warehouseProductsService;

        public WarehouseProductsController(IWarehouseProductService warehouseProductsService)
        {
            _warehouseProductsService = warehouseProductsService;
        }

        [HttpGet]
        [Route("{fromWarehouse}/company/{defaultCompanyId}")]
        public async Task<IActionResult> Get(int fromWarehouse, int defaultCompanyId)
        {
            return Ok(await _warehouseProductsService.GetShortListAsync(defaultCompanyId, fromWarehouse));
        }

        [HttpGet]
        [Route("UnitCostShortList/Warehouse/{warehouseId}/Product/{productId}")]
        public async Task<IActionResult> GetUnitCostShortList(int warehouseId, int productId)  
        {
            var list = await _warehouseProductsService.GetUnitCostShortListAsync(warehouseId, productId);

            return Ok(list);
        }

        [HttpGet]
        [Route("{productId}")]
        public async Task<IActionResult> GetUnitCostByProductIdShortList(int productId)
        {
            var list = await _warehouseProductsService.GetUnitCostByProductIdShortListAsync(productId);

            return Ok(list);
        }

        [HttpGet]
        [Route("UnitCostBySerialShortList/Warehouse/{warehouseId}/Product/{productId}/Serial/{serialNumber}")]
        public async Task<IActionResult> GetUnitCostBySerialShortList(int warehouseId, int productId, string serialNumber)
        {
            var list = await _warehouseProductsService.GetUnitCostBySerialShortListAsync(warehouseId, productId, serialNumber);

            return Ok(list);
        }

        [HttpGet]
        [Route("UnitCostByBatchShortList/Warehouse/{warehouseId}/Product/{productId}/Batch/{batchNumber}")]
        public async Task<IActionResult> GetUnitCostByBatchShortList(int warehouseId, int productId, string batchNumber)
        {
            var list = await _warehouseProductsService.GetUnitCostByBatchShortListAsync(warehouseId, productId, batchNumber);

            return Ok(list);
        }

        [HttpPost]
        [Route("UnitCostByBatch")]
        public async Task<IActionResult> GetUnitCostByBatchShortList([FromBody] WarehouseProduct warehouseProduct)
        {
            var list = await _warehouseProductsService.GetUnitCostByBatchShortListAsync(warehouseProduct.WarehouseId, warehouseProduct.ProductId, warehouseProduct.BatchNumber);

            return Ok(list);
        }

        [HttpGet]
        [Route("All/{warehouseId}/company/{companyId}")]
        public async Task<IActionResult> GetAllByWarehouse(int warehouseId, int companyId)
        {
            return Ok(await _warehouseProductsService.GetAllByWarehouse(companyId, warehouseId));
        }

        [HttpGet]
        [Route("WithOnHandQty/{warehouseId}/company/{companyId}")]
        public async Task<IActionResult> GetAllByWarehouseAndOnHandQty(int warehouseId, int companyId)
        {
            return Ok(await _warehouseProductsService.GetAllByWarehouseAndOnHandQtyAsync(companyId, warehouseId));
        }

        [HttpGet]
        [Route("StockDetails/{warehouseId}/company/{companyId}")]
        public async Task<IActionResult> GetStockDetails(int warehouseId, int companyId)
        {
            return Ok(await _warehouseProductsService.GetStockDetailsAsync(companyId, warehouseId));
        }

        [HttpGet]
        [Route("StockDetailsWithUOM/warehouse/{warehouseId}")]
        public async Task<IActionResult> StockDetailsWithUOM(int warehouseId)
        {
            return Ok(await _warehouseProductsService.GetAllWithUOMAsync(warehouseId));
        }

        [HttpGet]
        [Route("Company/{companyId}/Product/{productId}")]
        public async Task<IActionResult> GetProductQuantity(int companyId, int productId)
        {
            return Ok(await _warehouseProductsService.GetProductQuantity(companyId, productId));
        }

        [HttpGet]
        [Route("Warehouse/{warehouseId}/Product/{productId}")]
        public async Task<IActionResult> GetProductQuantityByWarehouseAndProduct(int warehouseId, int productId)
        {
            return Ok(await _warehouseProductsService.GetProductQuantityByWarehouseAndProduct(warehouseId, productId));
        }

        [HttpGet]
        [Route("GetWarehouseProductSerials/Warehouse/{warehouseId}/Product/{productId}/Company/{companyId}")]
        public async Task<IActionResult> GetWarehouseProductSerials(int warehouseId, int productId, int companyId)
        {
            return Ok(await _warehouseProductsService.GetWarehouseProductSerials(warehouseId, productId, companyId));
        }

        [HttpGet]
        [Route("SemiFinishedQC/{barcode}/Company/{companyId}")]
        public async Task<IActionResult> GetSemiFinishedQCProductQuantity(string barcode, int companyId)  
        {
            return Ok(await _warehouseProductsService.GetSemiFinishedQCProductQuantityAsync(barcode, companyId));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("{warehouseId}/Barcode/{barcode}")]
        public async Task<IActionResult> GetWarehouseProduct(int warehouseId, string barcode)  
        {
            return Ok(await _warehouseProductsService.GetAsync(warehouseId, barcode));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("GetByBatchOrSerialNumber/warehouse/{warehouseId}/Barcode/{barcode}")]
        public async Task<IActionResult> GetByBatchOrSerialNumber(int warehouseId, string barcode)
        {
            return Ok(await _warehouseProductsService.GetByBarcodeAsync(warehouseId, barcode));
        }

        [HttpGet]
        [Route("Warehouse/{warehouseId}/Product/{productId}/SerialNumber/{serialNumber}")]
        public async Task<IActionResult> GetProductQuantityByWarehouseAndProductSerialNumber(int warehouseId, int productId, string serialNumber)
        {
            return Ok(await _warehouseProductsService.GetProductQuantityByWarehouseAndProductSerialNumber(warehouseId, productId, serialNumber));
        }

        [HttpGet]
        [Route("Warehouse/{warehouseId}/Product/{productId}/BatchNumber/{batchNumber}")]
        public async Task<IActionResult> GetProductQuantityByWarehouseAndProductBatchNumber(int warehouseId, int productId, string batchNumber)
        {
            return Ok(await _warehouseProductsService.GetProductQuantityByWarehouseAndProductBatchNumber(warehouseId, productId, batchNumber));
        }

        [HttpGet]
        [Route("{warehouseProductId}/Warehouse/{warehouseId}/Product/{productId}")]
        public async Task<IActionResult> GetProductQuantityByWarehouseAndProduct(int warehouseProductId, int warehouseId, int productId)
        {
            return Ok(await _warehouseProductsService.GetProductQuantityByWarehouseAndProduct(warehouseProductId, warehouseId, productId));
        }

        [HttpGet]
        [Route("{warehouseProductId}/Warehouse/{warehouseId}/Product/{productId}/SerialNumber/{serialNumber}")]
        public async Task<IActionResult> GetProductQuantityByWarehouseAndProductSerialNumber(int warehouseProductId, int warehouseId, int productId, string serialNumber)
        {
            return Ok(await _warehouseProductsService.GetProductQuantityByWarehouseAndProductSerialNumber(warehouseProductId, warehouseId, productId, serialNumber));
        }

        [HttpGet]
        [Route("{warehouseProductId}/Warehouse/{warehouseId}/Product/{productId}/BatchNumber/{batchNumber}")]
        public async Task<IActionResult> GetProductQuantityByWarehouseAndProductBatchNumber(int warehouseProductId, int warehouseId, int productId, string batchNumber)
        {
            return Ok(await _warehouseProductsService.GetProductQuantityByWarehouseAndProductBatchNumber(warehouseProductId, warehouseId, productId, batchNumber));
        }

        [HttpPost]
        [Route("ProductQuantityByWarehouse")]
        public async Task<IActionResult> GetProductQuantityByWarehouseAndProductBatchNumber([FromBody] WarehouseProduct warehouseProduct)
        {
            return Ok(await _warehouseProductsService.GetProductQuantityByWarehouseAndProductBatchNumber(warehouseProduct.WarehouseProductId, warehouseProduct.WarehouseId, warehouseProduct.ProductId, warehouseProduct.BatchNumber));
        }

        [HttpGet]
        [Route("SerialProducts/Warehouse/{warehouseId}/Product/{productId}")]
        public async Task<IActionResult> GetSerialProducts(int warehouseId, int productId)
        {
            var warehouseProducts = await _warehouseProductsService.GetByWarehouseProducts(warehouseId, productId);

            var serialProducts = warehouseProducts.Where(wp => wp.OnHandQuantity > 0 && wp.NatureEnum == (byte)Domain.Entities.Inventory.Product.Nature.Serial);

            return Ok(serialProducts);
        }

        [HttpGet]
        [Route("BatchProducts/Warehouse/{warehouseId}/Product/{productId}")]
        public async Task<IActionResult> GetBatchProducts(int warehouseId, int productId)
        {
            var warehouseProducts = await _warehouseProductsService.GetByWarehouseProducts(warehouseId, productId);

            var batchProducts = warehouseProducts.Where(wp => wp.OnHandQuantity > 0 && wp.NatureEnum == (byte)Domain.Entities.Inventory.Product.Nature.Batch);

            return Ok(batchProducts);
        }

        [HttpGet]
        [Route("BatcheSerial/Warehouse/{warehouseId}/Product/{productId}/IsBatch/{isBatch}/Quantity/{qty}")]
        public async Task<IActionResult> GetBatchSerialInfo(int warehouseId, int productId, bool isBatch, int qty)
        {
            var warehouseProducts = await _warehouseProductsService.GetBatchSerialAsync(warehouseId, productId, qty, isBatch);

            return Ok(warehouseProducts);
        }

        [HttpGet]
        [Route("OnHandQty/Warehouse/{warehouseId}/Product/{productId}")]
        public async Task<IActionResult> GetOnHandQty(int warehouseId, int productId)
        {
            var warehouseProducts = await _warehouseProductsService.GetByWarehouseProducts(warehouseId, productId);

            var onHandQty = warehouseProducts.Where(wp => wp.OnHandQuantity > 0).Sum(p => p.OnHandQuantity);

            return Ok(onHandQty);
        }

        [HttpGet]
        [Route("WarehouseCode/{warehouseCode}/Company/{companyId}")]
        public async Task<IActionResult> GetByWarehouseCodeAsync(string warehouseCode, int companyId)
        {
            var warehouseProducts = await _warehouseProductsService.GetByWarehouseCodeAsync(warehouseCode, companyId);

            return Ok(warehouseProducts);
        }

        [HttpGet]
        [Route("{warehouseId}/Company/{companyId}/Pallet/{palletCode}")]
        public async Task<IActionResult> GetAllAsync(int warehouseId, int companyId, string palletCode)  
        {
            var warehouseProducts = await _warehouseProductsService.GetAllAsync(warehouseId, companyId, palletCode);

            return Ok(warehouseProducts);
        }

        [HttpGet]
        [Route("Company/{companyId}/Pallet/{palletCode}")]
        public async Task<IActionResult> GetStockInPallet(int companyId, string palletCode)
        {
            var warehouseProducts = await _warehouseProductsService.GetStockInPalletAsync(companyId, palletCode);

            return Ok(warehouseProducts);
        }

        [HttpGet]
        [Route("BatchNumbers/Product/{productId}/Warehouse/{warehouseId}")]
        public async Task<IActionResult> GetBatchNumbersAsync(int productId, int warehouseId)
        {
            return Ok(await _warehouseProductsService.GetBatchNumbersByProductAndWarehouseAsync(productId, warehouseId));
        }

        [HttpGet]
        public async Task<IActionResult> GetOnHandQtyByWarehouseProductAndBatchAsync(int warehouseId, int productId, string batchNumber)
        {
            var warehouseProducts = await _warehouseProductsService.GetByWarehouseProducts(warehouseId, productId);

            var onHandQty = warehouseProducts.Where(wp => wp.OnHandQuantity > 0 && wp.BatchNumber == batchNumber).Sum(p => p.OnHandQuantity ?? 0);

            return Ok(onHandQty);
        }

        [HttpGet]
        [Route("ByMachineIdAndBatchNumberAsync/machine/{machineId}/batchNumber/{batchNumber}")]
        public async Task<IActionResult> GetByMachineIdAndBatchNumberAsync(int machineId, string batchNumber)
        {
            var warehouseProduct = await _warehouseProductsService.GetByMachineIdAndBatchNumberAsync(machineId, batchNumber);

            return Ok(warehouseProduct);
        }

        [HttpGet]
        [Route("FinishedGoodsByMachine/machine/{machineId}")]
        public async Task<IActionResult> GetFinishedGoodsInMachineWarehouse(int machineId)
        {
            var warehouseProduct = await _warehouseProductsService.GetFinishedGoodsInMachineWarehouseAsync(machineId);

            return Ok(warehouseProduct);
        }

        [HttpPost]
        [Route("ByMachineAndProductCategories")]
        public async Task<IActionResult> GetWarehouseProductsByProductCategories([FromBody]ViewForm viewForm)
        {
            var warehouseProduct = await _warehouseProductsService.GetWarehouseProductsByProductCategoriesAsync(viewForm.ParameterOne, viewForm.IntegerList);

            return Ok(warehouseProduct);
        }

        [HttpPost]
        [Route("ProfileBuilding")]
        public async Task<IActionResult> GetWarehouseProductsForProfileBuilding([FromBody]ViewForm viewForm)
        {
            var warehouseProduct = await _warehouseProductsService.GetWarehouseProductsForProfileBuildingAsync(viewForm.ParameterOne, viewForm.IntegerList);

            return Ok(warehouseProduct);
        }

        [HttpPut]
        public async Task<IActionResult> Palatalizing([FromBody]Palatalizing palatalizing)    
        {
            switch (palatalizing.Action)
            {
                case "create":
                    {
                        IEnumerable<WarehouseProduct> warehouseProductList = palatalizing.WarehouseProducts;
                        await _warehouseProductsService.PalatalizingAsync(palatalizing.WarehouseId, palatalizing.PalletCode, warehouseProductList, palatalizing.ModifiedUserId.Value);
                    }
                    break;
                case "break":
                    await _warehouseProductsService.BreakPalletsAsync(palatalizing.WarehouseProductId, palatalizing.ModifiedUserId.Value);
                    break;
                case "breakAll":
                    await _warehouseProductsService.BreakAllPalletAsync(palatalizing.WarehouseId, palatalizing.PalletCode, palatalizing.ModifiedUserId.Value);
                    break;
            }
            return Ok();
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("GetProductionStockLines/warehouse/{warehouseId}")]
        public async Task<IActionResult> GetProductionStockLines(int warehouseId)
        {
            var warehouseProduct = await _warehouseProductsService.GetProductionStockLinesAsync(warehouseId);

            return Ok(warehouseProduct);
        }

        [HttpPost]
        [Route("save")]
        public async Task<IActionResult> Save([FromBody] WarehouseProduct warehouseProduct)
        {
            if (warehouseProduct == null)
                return BadRequest("Invalid product data");

            try
            {
                await _warehouseProductsService.SaveAsync(warehouseProduct);
                return Ok("Warehouse product saved successfully.");
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Exception: {ex.Message}\n{ex.StackTrace}");
            }

        }

    }
}