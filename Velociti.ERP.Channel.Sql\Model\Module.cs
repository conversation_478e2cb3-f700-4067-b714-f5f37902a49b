﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("Modules", Schema = "adm")]
    public partial class Module
    {
        public Module()
        {
            Permissions = new HashSet<Permission>();
            Resources = new HashSet<Resource>();
        }

        [Key]
        public int ModuleId { get; set; }
        public int CompanyId { get; set; }
        [Required]
        [StringLength(100)]
        public string ModuleName { get; set; }
        [Required]
        [StringLength(255)]
        public string Description { get; set; }
        public int PermissionValue { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("Modules")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(Permission.Module))]
        public virtual ICollection<Permission> Permissions { get; set; }
        [InverseProperty(nameof(Resource.Module))]
        public virtual ICollection<Resource> Resources { get; set; }
    }
}
