﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("UnitOfMeasures", Schema = "inv")]
    public partial class UnitOfMeasure
    {
        public UnitOfMeasure()
        {
            BillOfMaterialLines = new HashSet<BillOfMaterialLine>();
            BundleProductLines = new HashSet<BundleProductLine>();
            BundleProducts = new HashSet<BundleProduct>();
            ConsignmentPurchaseOrderLineBaseUnitOfMeasures = new HashSet<ConsignmentPurchaseOrderLine>();
            ConsignmentPurchaseOrderLineDocUnitOfMeasures = new HashSet<ConsignmentPurchaseOrderLine>();
            ConsignmentStockTransferLineBaseUnitOfMeasures = new HashSet<ConsignmentStockTransferLine>();
            ConsignmentStockTransferLineDocUnitOfMeasures = new HashSet<ConsignmentStockTransferLine>();
            ExchangeOrderLineBaseUnitOfMeasures = new HashSet<ExchangeOrderLine>();
            ExchangeOrderLineDocUnitOfMeasures = new HashSet<ExchangeOrderLine>();
            FreeIssueCriteriaLineBaseUnitOfMeasures = new HashSet<FreeIssueCriteriaLine>();
            FreeIssueCriteriaLineDocUnitOfMeasures = new HashSet<FreeIssueCriteriaLine>();
            FreeIssueLineBaseUnitOfMeasures = new HashSet<FreeIssueLine>();
            FreeIssueLineDocUnitOfMeasures = new HashSet<FreeIssueLine>();
            GoodsDispatchNoteLineBaseUnitOfMeasures = new HashSet<GoodsDispatchNoteLine>();
            GoodsDispatchNoteLineDocUnitOfMeasures = new HashSet<GoodsDispatchNoteLine>();
            GoodsReceiveNoteLineBaseUnitOfMeasures = new HashSet<GoodsReceiveNoteLine>();
            GoodsReceiveNoteLineDocUnitOfMeasures = new HashSet<GoodsReceiveNoteLine>();
            HoreturnStockTransferIntegrationLineBaseUnitOfMeasures = new HashSet<HoreturnStockTransferIntegrationLine>();
            HoreturnStockTransferIntegrationLineDocUnitOfMeasures = new HashSet<HoreturnStockTransferIntegrationLine>();
            HoreturnStockTransferLineBaseUnitOfMeasures = new HashSet<HoreturnStockTransferLine>();
            HoreturnStockTransferLineDocUnitOfMeasures = new HashSet<HoreturnStockTransferLine>();
            InternalDispatchLineBaseUnitOfMeasures = new HashSet<InternalDispatchLine>();
            InternalDispatchLineDocUnitOfMeasures = new HashSet<InternalDispatchLine>();
            InternalOrderLineBaseUnitOfMeasures = new HashSet<InternalOrderLine>();
            InternalOrderLineDocUnitOfMeasures = new HashSet<InternalOrderLine>();
            InternalReturnLineBaseUnitOfMeasures = new HashSet<InternalReturnLine>();
            InternalReturnLineDocUnitOfMeasures = new HashSet<InternalReturnLine>();
            InternalServiceInvoiceLineDetails = new HashSet<InternalServiceInvoiceLineDetail>();
            LoanOrderLineBaseUnitOfMeasures = new HashSet<LoanOrderLine>();
            LoanOrderLineDocUnitOfMeasures = new HashSet<LoanOrderLine>();
            MachineProductComponents = new HashSet<MachineProductComponent>();
            MaterialRequisitionNoteLineBaseUnitOfMeasures = new HashSet<MaterialRequisitionNoteLine>();
            MaterialRequisitionNoteLineDocUnitOfMeasures = new HashSet<MaterialRequisitionNoteLine>();
            ProductUnitOfMeasures = new HashSet<ProductUnitOfMeasure>();
            Products = new HashSet<Product>();
            PurchaseOrderLineBaseUnitOfMeasures = new HashSet<PurchaseOrderLine>();
            PurchaseOrderLineDocUnitOfMeasures = new HashSet<PurchaseOrderLine>();
            PurchaseRequisitionNoteLineBaseUnitOfMeasures = new HashSet<PurchaseRequisitionNoteLine>();
            PurchaseRequisitionNoteLineDocUnitOfMeasures = new HashSet<PurchaseRequisitionNoteLine>();
            PurchaseReturnLineBaseUnitOfMeasures = new HashSet<PurchaseReturnLine>();
            PurchaseReturnLineDocUnitOfMeasures = new HashSet<PurchaseReturnLine>();
            QuotationLineBaseUnitOfMeasures = new HashSet<QuotationLine>();
            QuotationLineDocUnitOfMeasures = new HashSet<QuotationLine>();
            SalesInvoiceLineBaseUnitOfMeasures = new HashSet<SalesInvoiceLine>();
            SalesInvoiceLineDocUnitOfMeasures = new HashSet<SalesInvoiceLine>();
            SalesOrderLineBaseUnitOfMeasures = new HashSet<SalesOrderLine>();
            SalesOrderLineDocUnitOfMeasures = new HashSet<SalesOrderLine>();
            SalesReturnLines = new HashSet<SalesReturnLine>();
            ShipmentQualityControlLineBaseUnitOfMeasures = new HashSet<ShipmentQualityControlLine>();
            ShipmentQualityControlLineDocUnitOfMeasures = new HashSet<ShipmentQualityControlLine>();
            ShipmentQualityControlReturnLineBaseUnitOfMeasures = new HashSet<ShipmentQualityControlReturnLine>();
            ShipmentQualityControlReturnLineDocUnitOfMeasures = new HashSet<ShipmentQualityControlReturnLine>();
            StockAdjustmentLines = new HashSet<StockAdjustmentLine>();
            StockCountSheetLines = new HashSet<StockCountSheetLine>();
            StockTransferLines = new HashSet<StockTransferLine>();
            StockTransferReceiptLines = new HashSet<StockTransferReceiptLine>();
            SubContractOrderLineBaseUnitOfMeasures = new HashSet<SubContractOrderLine>();
            SubContractOrderLineDocUnitOfMeasures = new HashSet<SubContractOrderLine>();
            SubContractOrders = new HashSet<SubContractOrder>();
            TxnLineHistories = new HashSet<TxnLineHistory>();
            WarehouseProducts = new HashSet<WarehouseProduct>();
            WarehouseStockIssues = new HashSet<WarehouseStockIssue>();
            WarehouseStockQualityControls = new HashSet<WarehouseStockQualityControl>();
        }

        [Key]
        public int UnitOfMeasureId { get; set; }
        public int? CompanyId { get; set; }
        [Column("UnitOfMeasure")]
        [StringLength(50)]
        public string UnitOfMeasure1 { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("UnitOfMeasures")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(BillOfMaterialLine.SecondaryUnitOfMeasure))]
        public virtual ICollection<BillOfMaterialLine> BillOfMaterialLines { get; set; }
        [InverseProperty(nameof(BundleProductLine.DocUnitOfMeasure))]
        public virtual ICollection<BundleProductLine> BundleProductLines { get; set; }
        [InverseProperty(nameof(BundleProduct.DocUnitOfMeasure))]
        public virtual ICollection<BundleProduct> BundleProducts { get; set; }
        [InverseProperty(nameof(ConsignmentPurchaseOrderLine.BaseUnitOfMeasure))]
        public virtual ICollection<ConsignmentPurchaseOrderLine> ConsignmentPurchaseOrderLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ConsignmentPurchaseOrderLine.DocUnitOfMeasure))]
        public virtual ICollection<ConsignmentPurchaseOrderLine> ConsignmentPurchaseOrderLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransferLine.BaseUnitOfMeasure))]
        public virtual ICollection<ConsignmentStockTransferLine> ConsignmentStockTransferLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ConsignmentStockTransferLine.DocUnitOfMeasure))]
        public virtual ICollection<ConsignmentStockTransferLine> ConsignmentStockTransferLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ExchangeOrderLine.BaseUnitOfMeasure))]
        public virtual ICollection<ExchangeOrderLine> ExchangeOrderLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ExchangeOrderLine.DocUnitOfMeasure))]
        public virtual ICollection<ExchangeOrderLine> ExchangeOrderLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(FreeIssueCriteriaLine.BaseUnitOfMeasure))]
        public virtual ICollection<FreeIssueCriteriaLine> FreeIssueCriteriaLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(FreeIssueCriteriaLine.DocUnitOfMeasure))]
        public virtual ICollection<FreeIssueCriteriaLine> FreeIssueCriteriaLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(FreeIssueLine.BaseUnitOfMeasure))]
        public virtual ICollection<FreeIssueLine> FreeIssueLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(FreeIssueLine.DocUnitOfMeasure))]
        public virtual ICollection<FreeIssueLine> FreeIssueLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLine.BaseUnitOfMeasure))]
        public virtual ICollection<GoodsDispatchNoteLine> GoodsDispatchNoteLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLine.DocUnitOfMeasure))]
        public virtual ICollection<GoodsDispatchNoteLine> GoodsDispatchNoteLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(GoodsReceiveNoteLine.BaseUnitOfMeasure))]
        public virtual ICollection<GoodsReceiveNoteLine> GoodsReceiveNoteLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(GoodsReceiveNoteLine.DocUnitOfMeasure))]
        public virtual ICollection<GoodsReceiveNoteLine> GoodsReceiveNoteLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferIntegrationLine.BaseUnitOfMeasure))]
        public virtual ICollection<HoreturnStockTransferIntegrationLine> HoreturnStockTransferIntegrationLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferIntegrationLine.DocUnitOfMeasure))]
        public virtual ICollection<HoreturnStockTransferIntegrationLine> HoreturnStockTransferIntegrationLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferLine.BaseUnitOfMeasure))]
        public virtual ICollection<HoreturnStockTransferLine> HoreturnStockTransferLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferLine.DocUnitOfMeasure))]
        public virtual ICollection<HoreturnStockTransferLine> HoreturnStockTransferLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(InternalDispatchLine.BaseUnitOfMeasure))]
        public virtual ICollection<InternalDispatchLine> InternalDispatchLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(InternalDispatchLine.DocUnitOfMeasure))]
        public virtual ICollection<InternalDispatchLine> InternalDispatchLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(InternalOrderLine.BaseUnitOfMeasure))]
        public virtual ICollection<InternalOrderLine> InternalOrderLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(InternalOrderLine.DocUnitOfMeasure))]
        public virtual ICollection<InternalOrderLine> InternalOrderLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(InternalReturnLine.BaseUnitOfMeasure))]
        public virtual ICollection<InternalReturnLine> InternalReturnLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(InternalReturnLine.DocUnitOfMeasure))]
        public virtual ICollection<InternalReturnLine> InternalReturnLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(InternalServiceInvoiceLineDetail.UnitOfMeasure))]
        public virtual ICollection<InternalServiceInvoiceLineDetail> InternalServiceInvoiceLineDetails { get; set; }
        [InverseProperty(nameof(LoanOrderLine.BaseUnitOfMeasure))]
        public virtual ICollection<LoanOrderLine> LoanOrderLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(LoanOrderLine.DocUnitOfMeasure))]
        public virtual ICollection<LoanOrderLine> LoanOrderLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(MachineProductComponent.DefaultUnitOfMeasure))]
        public virtual ICollection<MachineProductComponent> MachineProductComponents { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNoteLine.BaseUnitOfMeasure))]
        public virtual ICollection<MaterialRequisitionNoteLine> MaterialRequisitionNoteLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(MaterialRequisitionNoteLine.DocUnitOfMeasure))]
        public virtual ICollection<MaterialRequisitionNoteLine> MaterialRequisitionNoteLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ProductUnitOfMeasure.UnitOfMeasure))]
        public virtual ICollection<ProductUnitOfMeasure> ProductUnitOfMeasures { get; set; }
        [InverseProperty(nameof(Product.DefaultUnitOfMeasure))]
        public virtual ICollection<Product> Products { get; set; }
        [InverseProperty(nameof(PurchaseOrderLine.BaseUnitOfMeasure))]
        public virtual ICollection<PurchaseOrderLine> PurchaseOrderLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(PurchaseOrderLine.DocUnitOfMeasure))]
        public virtual ICollection<PurchaseOrderLine> PurchaseOrderLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(PurchaseRequisitionNoteLine.BaseUnitOfMeasure))]
        public virtual ICollection<PurchaseRequisitionNoteLine> PurchaseRequisitionNoteLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(PurchaseRequisitionNoteLine.DocUnitOfMeasure))]
        public virtual ICollection<PurchaseRequisitionNoteLine> PurchaseRequisitionNoteLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(PurchaseReturnLine.BaseUnitOfMeasure))]
        public virtual ICollection<PurchaseReturnLine> PurchaseReturnLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(PurchaseReturnLine.DocUnitOfMeasure))]
        public virtual ICollection<PurchaseReturnLine> PurchaseReturnLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(QuotationLine.BaseUnitOfMeasure))]
        public virtual ICollection<QuotationLine> QuotationLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(QuotationLine.DocUnitOfMeasure))]
        public virtual ICollection<QuotationLine> QuotationLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.BaseUnitOfMeasure))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.DocUnitOfMeasure))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(SalesOrderLine.BaseUnitOfMeasure))]
        public virtual ICollection<SalesOrderLine> SalesOrderLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(SalesOrderLine.DocUnitOfMeasure))]
        public virtual ICollection<SalesOrderLine> SalesOrderLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(SalesReturnLine.DocUnitOfMeasure))]
        public virtual ICollection<SalesReturnLine> SalesReturnLines { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlLine.BaseUnitOfMeasure))]
        public virtual ICollection<ShipmentQualityControlLine> ShipmentQualityControlLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlLine.DocUnitOfMeasure))]
        public virtual ICollection<ShipmentQualityControlLine> ShipmentQualityControlLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturnLine.BaseUnitOfMeasure))]
        public virtual ICollection<ShipmentQualityControlReturnLine> ShipmentQualityControlReturnLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlReturnLine.DocUnitOfMeasure))]
        public virtual ICollection<ShipmentQualityControlReturnLine> ShipmentQualityControlReturnLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(StockAdjustmentLine.UnitOfMeasure))]
        public virtual ICollection<StockAdjustmentLine> StockAdjustmentLines { get; set; }
        [InverseProperty(nameof(StockCountSheetLine.DocUnitOfMeasure))]
        public virtual ICollection<StockCountSheetLine> StockCountSheetLines { get; set; }
        [InverseProperty(nameof(StockTransferLine.BaseUnitOfMeasure))]
        public virtual ICollection<StockTransferLine> StockTransferLines { get; set; }
        [InverseProperty(nameof(StockTransferReceiptLine.BaseUnitOfMeasure))]
        public virtual ICollection<StockTransferReceiptLine> StockTransferReceiptLines { get; set; }
        [InverseProperty(nameof(SubContractOrderLine.BaseUnitOfMeasure))]
        public virtual ICollection<SubContractOrderLine> SubContractOrderLineBaseUnitOfMeasures { get; set; }
        [InverseProperty(nameof(SubContractOrderLine.DocUnitOfMeasure))]
        public virtual ICollection<SubContractOrderLine> SubContractOrderLineDocUnitOfMeasures { get; set; }
        [InverseProperty(nameof(SubContractOrder.DocUnitOfMeasure))]
        public virtual ICollection<SubContractOrder> SubContractOrders { get; set; }
        [InverseProperty(nameof(TxnLineHistory.UnitOfMeasure))]
        public virtual ICollection<TxnLineHistory> TxnLineHistories { get; set; }
        [InverseProperty(nameof(WarehouseProduct.UnitOfMeasure))]
        public virtual ICollection<WarehouseProduct> WarehouseProducts { get; set; }
        [InverseProperty(nameof(WarehouseStockIssue.UnitOfMeasure))]
        public virtual ICollection<WarehouseStockIssue> WarehouseStockIssues { get; set; }
        [InverseProperty(nameof(WarehouseStockQualityControl.UnitOfMeasure))]
        public virtual ICollection<WarehouseStockQualityControl> WarehouseStockQualityControls { get; set; }
    }
}
