﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class SalesReturnLineDetailsController : ControllerBase
    {
        private readonly ISalesReturnLineDetailService _salesReturnLineDetailService;

        public SalesReturnLineDetailsController(ISalesReturnLineDetailService salesReturnLineDetailService)
        {
            _salesReturnLineDetailService = salesReturnLineDetailService;
        }

        [HttpGet]
        [Route("{salesReturnLineId}")]
        public async Task<IActionResult> Get(int salesReturnLineId)
        {
            return Ok(await _salesReturnLineDetailService.GetAsync(salesReturnLineId));
        }
    }
}