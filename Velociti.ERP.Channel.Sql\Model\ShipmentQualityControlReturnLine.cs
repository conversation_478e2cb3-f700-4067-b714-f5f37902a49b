﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ShipmentQualityControlReturnLines", Schema = "inv")]
    public partial class ShipmentQualityControlReturnLine
    {
        [Key]
        public int ShipmentQualityControlReturnLineId { get; set; }
        public int? ShipmentQualityControlReturnId { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? RejectedQuantity { get; set; }
        public int? RejectReasonId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ProductExpiryDate { get; set; }
        [StringLength(32)]
        public string LotNumber { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ShipmentQualityControlReturnLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.ShipmentQualityControlReturnLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("ShipmentQualityControlReturnLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(ShipmentQualityControlReturnId))]
        [InverseProperty("ShipmentQualityControlReturnLines")]
        public virtual ShipmentQualityControlReturn ShipmentQualityControlReturn { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("ShipmentQualityControlReturnLines")]
        public virtual TaxGroup TaxGroup { get; set; }
    }
}
