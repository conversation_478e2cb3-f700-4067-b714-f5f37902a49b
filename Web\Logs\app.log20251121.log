2025-11-21 10:56:14,130 [32] INFO  Web.Controllers.AccountController - Login POST started.
2025-11-21 10:56:14,143 [32] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-11-21 10:56:14,337 [32] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"<PERSON><PERSON>@nemsu.lk","password":"12345"}
2025-11-21 10:56:14,340 [32] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: <EMAIL>
2025-11-21 10:56:20,405 [32] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-11-21 10:56:20,405 [32] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-0bee41d362b4e949904c31c42a79e4c6-3e631f0fc427734b-00"}
2025-11-21 10:56:20,405 [32] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-11-21 10:57:02,692 [42] INFO  Web.Controllers.AccountController - Login POST started.
2025-11-21 10:57:02,692 [42] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-11-21 10:57:02,692 [42] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"asanka@laugfs","password":"12345"}
2025-11-21 10:57:02,692 [42] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: asanka@laugfs
2025-11-21 10:57:02,821 [42] DEBUG Web.Controllers.AccountController - Response Status Code: Unauthorized
2025-11-21 10:57:02,821 [42] DEBUG Web.Controllers.AccountController - Response Content: {"type":"https://tools.ietf.org/html/rfc7235#section-3.1","title":"Unauthorized","status":401,"traceId":"00-4a1d8c2d17c5c0449f5e9d8216903cd9-5a9b8b10b7dfc44b-00"}
2025-11-21 10:57:02,821 [42] WARN  Web.Controllers.AccountController - Authentication failed: Invalid username or password.
2025-11-21 11:53:27,868 [68] INFO  Web.Controllers.AccountController - Login POST started.
2025-11-21 11:53:27,868 [68] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-11-21 11:53:27,868 [68] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"asanka@laugfs","password":"12345"}
2025-11-21 11:53:27,868 [68] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: asanka@laugfs
2025-11-21 11:53:29,160 [68] DEBUG Web.Controllers.AccountController - Response Status Code: OK
2025-11-21 11:53:29,160 [68] DEBUG Web.Controllers.AccountController - Response Content: {"id":3,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.evvULS_1BT5gdvbZOwuPCTsOIq-Lm4qtjUpjvOVq_GQ","refreshToken":"ebfd2b8f-4798-42d7-acba-2e7a869987c5","expiresIn":2073600,"error":null,"user":{"userId":3,"titleId":2,"title":null,"userCode":"U1","fullname":"Pasan Madhawa","displayName":"Navitsa System Admin","email":"<EMAIL>","signature":"","signatureFile":null,"isFilteredByDepartment":false,"employeeId":0,"defaultCompanyId":2,"defaultDepartmentId":0,"creationDate":"2020-08-14T12:00:51.62","expiryDate":null,"modifiedUserId":2,"loginId":0,"username":null,"passwordHash":null,"login":{"loginId":3,"username":"asanka@laugfs","passwordHash":"827ccb0eea8a706c4c34a16891f84e7b","modifiedUserId":2},"designationTypeEnum":254,"designationName":"Administrator","notificationMessage":null},"permissions":[]}
2025-11-21 11:53:29,267 [68] INFO  Web.Controllers.AccountController - Authentication succeeded for user: Navitsa System Admin
2025-11-21 11:53:29,270 [68] DEBUG Web.Controllers.AccountController - Claims created for user Navitsa System Admin
2025-11-21 11:53:29,273 [68] INFO  Web.Controllers.AccountController - User Navitsa System Admin signed in successfully.
2025-11-21 11:53:57,461 [46] INFO  Web.Controllers.AccountController - Login POST started.
2025-11-21 11:53:57,461 [46] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-11-21 11:53:57,461 [46] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"asanka@laugfs","password":"12345"}
2025-11-21 11:53:57,461 [46] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: asanka@laugfs
2025-11-21 11:53:57,578 [46] DEBUG Web.Controllers.AccountController - Response Status Code: OK
2025-11-21 11:53:57,578 [46] DEBUG Web.Controllers.AccountController - Response Content: {"id":3,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.sQg0GE_PpOzzRXYmpwpjuW0iWn8YYB3JJfP2CvF-WRo","refreshToken":"8a642353-507f-4592-8609-5c3691c9ec00","expiresIn":2073600,"error":null,"user":{"userId":3,"titleId":2,"title":null,"userCode":"U1","fullname":"Pasan Madhawa","displayName":"Navitsa System Admin","email":"<EMAIL>","signature":"","signatureFile":null,"isFilteredByDepartment":false,"employeeId":0,"defaultCompanyId":2,"defaultDepartmentId":0,"creationDate":"2020-08-14T12:00:51.62","expiryDate":null,"modifiedUserId":2,"loginId":0,"username":null,"passwordHash":null,"login":{"loginId":3,"username":"asanka@laugfs","passwordHash":"827ccb0eea8a706c4c34a16891f84e7b","modifiedUserId":2},"designationTypeEnum":254,"designationName":"Administrator","notificationMessage":null},"permissions":[]}
2025-11-21 11:53:57,578 [46] INFO  Web.Controllers.AccountController - Authentication succeeded for user: Navitsa System Admin
2025-11-21 11:53:57,578 [46] DEBUG Web.Controllers.AccountController - Claims created for user Navitsa System Admin
2025-11-21 11:53:57,578 [46] INFO  Web.Controllers.AccountController - User Navitsa System Admin signed in successfully.
2025-11-21 13:55:59,153 [9] INFO  Web.Controllers.AccountController - Login POST started.
2025-11-21 13:55:59,195 [9] DEBUG Web.Controllers.AccountController - Service URL: https://localhost:44382/api/administration/identity/authenticate
2025-11-21 13:55:59,716 [9] DEBUG Web.Controllers.AccountController - Request Payload: {"username":"asanka@laugfs","password":"12345"}
2025-11-21 13:55:59,725 [9] INFO  Web.Controllers.AccountController - Sending POST to auth service for user: asanka@laugfs
2025-11-21 13:56:17,247 [9] DEBUG Web.Controllers.AccountController - Response Status Code: OK
2025-11-21 13:56:17,247 [9] DEBUG Web.Controllers.AccountController - Response Content: {"id":3,"token":"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***************************************************************************************************************************************************************************.W4YjRyQSblM1AIAabBkf6j5fp9M_6wuLsHyLoLwLxp4","refreshToken":"********-918e-47c7-89a7-b20bd8c52406","expiresIn":2073600,"error":null,"user":{"userId":3,"titleId":2,"title":null,"userCode":"U1","fullname":"Pasan Madhawa","displayName":"Navitsa System Admin","email":"<EMAIL>","signature":"","signatureFile":null,"isFilteredByDepartment":false,"employeeId":0,"defaultCompanyId":2,"defaultDepartmentId":0,"creationDate":"2020-08-14T12:00:51.62","expiryDate":null,"modifiedUserId":2,"loginId":0,"username":null,"passwordHash":null,"login":{"loginId":3,"username":"asanka@laugfs","passwordHash":"827ccb0eea8a706c4c34a16891f84e7b","modifiedUserId":2},"designationTypeEnum":254,"designationName":"Administrator","notificationMessage":null},"permissions":[]}
2025-11-21 13:56:17,591 [9] INFO  Web.Controllers.AccountController - Authentication succeeded for user: Navitsa System Admin
2025-11-21 13:56:17,597 [9] DEBUG Web.Controllers.AccountController - Claims created for user Navitsa System Admin
2025-11-21 13:56:17,607 [9] INFO  Web.Controllers.AccountController - User Navitsa System Admin signed in successfully.
