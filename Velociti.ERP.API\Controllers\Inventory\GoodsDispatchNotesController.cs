﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Sales;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class GoodsDispatchNotesController : ControllerBase
    {
        private readonly IGoodsDispatchNoteService _goodsDispatchNoteService;
        private readonly ISalesInvoiceService salesInvoiceService;

        public GoodsDispatchNotesController(IGoodsDispatchNoteService goodsDispatchNoteService, ISalesInvoiceService salesInvoiceService )
        {
            _goodsDispatchNoteService = goodsDispatchNoteService;
            this.salesInvoiceService = salesInvoiceService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _goodsDispatchNoteService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("Submitted/{companyId}/startDate/{startDate}/endDate/{endDate}")]
        public async Task<IActionResult> GetSubmittedAll(int companyId, DateTime startDate, DateTime endDate)    
        {
            return Ok(await _goodsDispatchNoteService.GetSubmittedAllAsync(companyId, startDate, endDate));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("ForDownload/{companyId}/startDate/{startDate}/endDate/{endDate}")]
        public async Task<IActionResult> GetForDownload(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _goodsDispatchNoteService.GetForDownloadAllAsync(companyId, startDate, endDate));
        }

        [HttpGet]
        [Route("ForApproval")]
        public async Task<IActionResult> GetForApproval(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _goodsDispatchNoteService.GetForApprovalAsync(companyId, userId, startDate, endDate));
        }

        [HttpGet]
        [Route("ShortList/{companyId}")]
        public async Task<IActionResult> GetSummary(int companyId)
        {
            return Ok(await _goodsDispatchNoteService.GetSummaries(companyId));
        }

        [HttpGet]
        [Route("Customer/{customerId}")]
        public async Task<IActionResult> GetByCustomer(int customerId)
        {
            return Ok(await _goodsDispatchNoteService.GetByCustomer(customerId));
        }

        [HttpGet]
        [Route("{goodsDispatchNoteId}")]
        public async Task<IActionResult> GetById(int goodsDispatchNoteId)
        {
            var gdn = await _goodsDispatchNoteService.GetByIdAsync(goodsDispatchNoteId);

            return Ok(gdn);
        }

        [HttpGet]
        [Route("IsGeneratedFromExchangeOrder/{refDocNumber}")]
        public async Task<IActionResult> IsGeneratedFromExchangeOrder(string refDocNumber)
        {
            var gdn = await _goodsDispatchNoteService.IsGeneratedFromExchangeOrderAsync(refDocNumber);

            return Ok(gdn);
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]GoodsDispatchNote goodsDispatchNote)
        {
            switch (goodsDispatchNote.Action)
            {
                case "submit": await _goodsDispatchNoteService.SubmitAsync(goodsDispatchNote); break;
                case "cancel": await _goodsDispatchNoteService.CancelAsync(goodsDispatchNote); break;
                case "update": await _goodsDispatchNoteService.SaveAsync(goodsDispatchNote); break;
                case "convertToSalesInvoice": return Ok(await this.salesInvoiceService.ConvertSalesInvoiceAsync(goodsDispatchNote.GoodsDispatchNoteId,goodsDispatchNote.ModifiedUserId));
                case "convertToGRN": return Ok(await _goodsDispatchNoteService.ConvertToGRNAsync(goodsDispatchNote.GoodsDispatchNoteId, goodsDispatchNote.ModifiedUserId.Value));
                case "convertToPackingList": return Ok(await _goodsDispatchNoteService.ConvertToPackingListAsync(goodsDispatchNote.GoodsDispatchNoteId, goodsDispatchNote.ModifiedUserId.Value));
                case "reverse": await this._goodsDispatchNoteService.ReverseAsync(goodsDispatchNote); break;
                case "send for approval": await _goodsDispatchNoteService.SendForApprovalAsync(goodsDispatchNote.GoodsDispatchNoteId, goodsDispatchNote.CompanyId.Value, goodsDispatchNote.ModifiedUserId.Value); break;
                case "update work flow": await _goodsDispatchNoteService.UpdateWorkFlowStatusAsync(goodsDispatchNote); break;
                case "convert to sales return": return Ok(await _goodsDispatchNoteService.ConvertToSalesReturnAsync(goodsDispatchNote.GoodsDispatchNoteId, goodsDispatchNote.ModifiedUserId.Value));
            }

            return Ok();
        }
    }
}