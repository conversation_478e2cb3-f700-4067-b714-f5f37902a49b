﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Sales;
using Velociti.ERP.Domain.Services.Sales;

namespace Velociti.ERP.API.Controllers.Sales
{
    [Route("api/Sales/[controller]")]
    [Authorize]
    [ApiController]
    public class PackingListsController : ControllerBase
    {
        private readonly IPackingListService _packingListService;

        public PackingListsController(IPackingListService packingListService)
        {
            _packingListService = packingListService;
        }

        [HttpGet]
        [Route("Single/{packingListId}")]
        public async Task<IActionResult> FindById(int packingListId)
        {
            return Ok(await _packingListService.FindByIdAsync(packingListId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _packingListService.GetAllAsync(companyId, userId, startDate, endDate));
        }
        
        [HttpPost]
        public async Task<IActionResult> Post([FromBody]PackingList packingList)
        {
            switch (packingList.Action)
            {
                case "generate": return Ok(await _packingListService.GeneratePackingListAsync(packingList));
                case "save": await _packingListService.SaveAsync(packingList); break;
            }

            return Ok();
        }

        [HttpDelete]
        [Route("{packingListId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int packingListId, int userId)
        {
            await _packingListService.CancelAsync(packingListId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]PackingList packingList)
        {
            switch (packingList.Action)
            {
                case "submit": await _packingListService.SubmitAsync(packingList.PackingListId, packingList.ModifiedUserId.Value); break;
            }

            return Ok();
        }
    }
}