﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GoodsReceiveNoteLineDetails", Schema = "inv")]
    public partial class GoodsReceiveNoteLineDetail
    {
        public GoodsReceiveNoteLineDetail()
        {
            ShipmentQualityControlLines = new HashSet<ShipmentQualityControlLine>();
        }

        [Key]
        public int GoodsReceiveNoteLineDetailId { get; set; }
        public int? GoodsReceiveNoteLineId { get; set; }
        public int? WarehouseId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ProductExpiryDate { get; set; }
        [StringLength(32)]
        public string LotNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime? WarrantyDate { get; set; }
        [Column(TypeName = "decimal(18, 5)")]
        public decimal? Quantity { get; set; }
        [Column("QCPassedQuantity", TypeName = "decimal(18, 5)")]
        public decimal? QcpassedQuantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(GoodsReceiveNoteLineId))]
        [InverseProperty("GoodsReceiveNoteLineDetails")]
        public virtual GoodsReceiveNoteLine GoodsReceiveNoteLine { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.GoodsReceiveNoteLineDetails))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(ShipmentQualityControlLine.GoodsReceiveNoteLineDetail))]
        public virtual ICollection<ShipmentQualityControlLine> ShipmentQualityControlLines { get; set; }
    }
}
