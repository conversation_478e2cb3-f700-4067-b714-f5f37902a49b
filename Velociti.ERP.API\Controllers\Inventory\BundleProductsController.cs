﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class BundleProductsController : ControllerBase
    {
        private readonly IBundleProductService _bundleProductService;

        public BundleProductsController(IBundleProductService bundleProductService)
        {
            _bundleProductService = bundleProductService;
        }

        [HttpGet]
        [Route("Single/{bundleProductId}")]
        public async Task<IActionResult> GetByIdAsync(int bundleProductId)
        {
            return Ok(await _bundleProductService.GetByIdAsync(bundleProductId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _bundleProductService.GetAllAsync(companyId, startDate, endDate));
        }

        [HttpPost]
        public async Task<IActionResult> SaveAsync([FromBody]BundleProduct bundleProduct)
        {
            await _bundleProductService.SaveAsync(bundleProduct);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> UpdateAsync([FromBody]BundleProduct bundleProduct)
        {
            switch (bundleProduct.Action)
            {
                case "submit":
                    await _bundleProductService.SubmitAsync(bundleProduct);
                    break;
                case "cancel":
                    await _bundleProductService.CancelAsync(bundleProduct);
                    break;
                default:
                    throw new ArgumentException();
            }

            return Ok();
        }
    }
}