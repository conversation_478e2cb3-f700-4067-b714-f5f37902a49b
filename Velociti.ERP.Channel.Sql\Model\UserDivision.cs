﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("UserDivisions", Schema = "adm")]
    public partial class UserDivision
    {
        [Key]
        public int UserDivisionId { get; set; }
        public int UserId { get; set; }
        public int DivisionId { get; set; }
        public bool? IsDefault { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(DivisionId))]
        [InverseProperty("UserDivisions")]
        public virtual Division Division { get; set; }
        [ForeignKey(nameof(UserId))]
        [InverseProperty("UserDivisions")]
        public virtual User User { get; set; }
    }
}
