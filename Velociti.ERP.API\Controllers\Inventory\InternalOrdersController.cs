﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Channel.Sql.Services.Inventory;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;
using Microsoft.Extensions.Configuration;
using System.Data;
using System.Data.SqlClient;


namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class InternalOrdersController : ControllerBase
    {
        private readonly IInternalOrderService _internalOrderService;
        private readonly IInternalOrderLineService _internalOrderLineService;
        private readonly IConfiguration _configuration;

        public InternalOrdersController(IInternalOrderService internalOrderService, IInternalOrderLineService internalOrderLineService, IConfiguration configuration)
        {
            _internalOrderService = internalOrderService;
            _internalOrderLineService = internalOrderLineService;
            _configuration = configuration;
        }

        [HttpGet]
        [Route("Single/{internalOrderId}")]
        public async Task<IActionResult> FindById(int internalOrderId)
        {
            return Ok(await _internalOrderService.FindByIdAsync(internalOrderId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll(int companyId, int userId, DateTime startDate, DateTime endDate)
        {
            return Ok(await _internalOrderService.GetAllAsync(companyId, userId, startDate, endDate));
        }

        //[HttpPost]
        //public async Task<IActionResult> Post([FromBody]InternalOrder internalOrder)
        //{
        //    await _internalOrderService.SaveAsync(internalOrder);

        //    return Ok();
        //}

        [HttpPost("Save")]
        public async Task<IActionResult> Save([FromBody] InternalOrderSaveModel model)
        {
            if (model == null)
            {
                return BadRequest("Invalid request data");
            }

            if (model.IsHeader)
            {
                // Save header and lines
                if (model.HeaderData == null)
                {
                    return BadRequest("Header data is required");
                }

                await _internalOrderService.SaveAsync(model.HeaderData);
                return Ok(model.HeaderData);
            }
            else
            {
                // Save single line
                if (model.LineData == null)
                {
                    return BadRequest("Line data is required");
                }

                if (model.LineData.InternalOrderLineId > 0)
                {
                    // Update existing line
                    var updatedLine = await _internalOrderLineService.UpdateInternalOrderLineAsync(model.LineData);
                    return Ok(updatedLine);
                }
                else
                {
                    // Add new line
                    var newLine = await _internalOrderLineService.AddInternalOrderLineAsync(model.LineData);
                    return Ok(newLine);
                }
            }
        }

        [HttpDelete]
        [Route("{internalOrderId}/User/{userId}")]
        public async Task<IActionResult> Cancel(int internalOrderId, int userId)
        {
            await _internalOrderService.CancelAsync(internalOrderId, userId);

            return Ok();
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]InternalOrder internalOrder)
        {
            switch (internalOrder.Action)
            {
                case "submit": await _internalOrderService.SubmitAsync(internalOrder.InternalOrderId, internalOrder.ModifiedUserId.Value); break;
                case "convert": await _internalOrderService.ConvertAsync(internalOrder.InternalOrderId, internalOrder.ModifiedUserId.Value); break;
            }

            return Ok();
        }
        [HttpPost("UpdateStocks/{internalOrderId}")]
        public IActionResult UpdateStocks(int internalOrderId)
        {
            try
            {
                // Get connection string from appsettings.json
                string connectionString = _configuration.GetConnectionString("DefaultConnection");

                using (SqlConnection connection = new SqlConnection(connectionString))
                {
                    using (SqlCommand command = new SqlCommand("sp_Update_Stocks_WAT", connection))
                    {
                        command.CommandType = CommandType.StoredProcedure;
                        command.Parameters.AddWithValue("@WATID", internalOrderId);

                        connection.Open();
                        command.ExecuteNonQuery();
                    }
                }

                return Ok(new { success = true, message = "Stock updated successfully." });
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { success = false, message = "Error: " + ex.Message });
            }
        }

    }
}