﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("FinancialStatements", Schema = "fin")]
    public partial class FinancialStatement
    {
        public FinancialStatement()
        {
            FinancialStatementLines = new HashSet<FinancialStatementLine>();
        }

        [Key]
        public int FinancialStatementId { get; set; }
        public int CompanyId { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(500)]
        public string Description { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("FinancialStatements")]
        public virtual Company Company { get; set; }
        [InverseProperty(nameof(FinancialStatementLine.FinancialStatement))]
        public virtual ICollection<FinancialStatementLine> FinancialStatementLines { get; set; }
    }
}
