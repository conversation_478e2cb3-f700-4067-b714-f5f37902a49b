﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesInvoiceChargeGroups", Schema = "sales")]
    public partial class SalesInvoiceChargeGroup
    {
        [Key]
        public int SalesInvoiceId { get; set; }
        [Key]
        public int ChargeGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChargeGroupId))]
        [InverseProperty(nameof(SupportData.SalesInvoiceChargeGroups))]
        public virtual SupportData ChargeGroup { get; set; }
        [ForeignKey(nameof(SalesInvoiceId))]
        [InverseProperty("SalesInvoiceChargeGroups")]
        public virtual SalesInvoice SalesInvoice { get; set; }
    }
}
