﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesOrderLines", Schema = "sales")]
    public partial class SalesOrderLine
    {
        public SalesOrderLine()
        {
            GoodsDispatchNoteLines = new HashSet<GoodsDispatchNoteLine>();
            PackingListLines = new HashSet<PackingListLine>();
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SubContractOrderLines = new HashSet<SubContractOrderLine>();
        }

        [Key]
        public int SalesOrderLineId { get; set; }
        public int SalesOrderId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? DispatchWarehouseId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DeliveryDate { get; set; }
        public int? ProductId { get; set; }
        public int? BaseUnitOfMeasureId { get; set; }
        public int? DocUnitOfMeasureId { get; set; }
        public int? Quantity { get; set; }
        public long? QuantityInBase { get; set; }
        public int? BalanceQuantity { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [StringLength(50)]
        public string WarrantyApplicabilityPeriod { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BaseUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.SalesOrderLineBaseUnitOfMeasures))]
        public virtual UnitOfMeasure BaseUnitOfMeasure { get; set; }
        [ForeignKey(nameof(DispatchWarehouseId))]
        [InverseProperty(nameof(Warehous.SalesOrderLines))]
        public virtual Warehous DispatchWarehouse { get; set; }
        [ForeignKey(nameof(DocUnitOfMeasureId))]
        [InverseProperty(nameof(UnitOfMeasure.SalesOrderLineDocUnitOfMeasures))]
        public virtual UnitOfMeasure DocUnitOfMeasure { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SalesOrderLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SalesOrderId))]
        [InverseProperty("SalesOrderLines")]
        public virtual SalesOrder SalesOrder { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("SalesOrderLines")]
        public virtual TaxGroup TaxGroup { get; set; }
        [InverseProperty(nameof(GoodsDispatchNoteLine.SalesOrderLine))]
        public virtual ICollection<GoodsDispatchNoteLine> GoodsDispatchNoteLines { get; set; }
        [InverseProperty(nameof(PackingListLine.SalesOrderLine))]
        public virtual ICollection<PackingListLine> PackingListLines { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.SalesOrderLine))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SubContractOrderLine.SalesOrderLine))]
        public virtual ICollection<SubContractOrderLine> SubContractOrderLines { get; set; }
    }
}
