﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("TouchScreenHistory", Schema = "man")]
    public partial class TouchScreenHistory
    {
        [Key]
        public int TouchScreenHistoryId { get; set; }
        public int? CompanyId { get; set; }
        public byte? TypeEnum { get; set; }
        public int? WarehouseId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime Date { get; set; }
        public int? ProductId { get; set; }
        public int? MachineId { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string PalletCode { get; set; }
        public byte? Grade { get; set; }
        [Column("QCParameters")]
        [StringLength(255)]
        public string Qcparameters { get; set; }
        public long? Quantity { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("TouchScreenHistories")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(MachineId))]
        [InverseProperty("TouchScreenHistories")]
        public virtual Machine Machine { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("TouchScreenHistories")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.TouchScreenHistories))]
        public virtual Warehous Warehouse { get; set; }
    }
}
