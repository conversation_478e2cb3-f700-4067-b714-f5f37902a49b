﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MonthEndWarehouseProducts", Schema = "inv")]
    public partial class MonthEndWarehouseProduct
    {
        [Key]
        public long Id { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Month { get; set; }
        public int WarehouseId { get; set; }
        public int? CompanyId { get; set; }
        public int ProductId { get; set; }
        [StringLength(50)]
        public string LotNumber { get; set; }
        [StringLength(50)]
        public string BatchNumber { get; set; }
        [StringLength(50)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? UnitCost { get; set; }
        public int UnitOfMeasureId { get; set; }
        public long? AllocatedQuantity { get; set; }
        public long? OnHandQuantity { get; set; }
        public long? PhysicalQuantity { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ProductExpiryDate { get; set; }
        [Column(TypeName = "date")]
        public DateTime? WarrantyDate { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        public byte? Grade { get; set; }
        [StringLength(50)]
        public string PalletCode { get; set; }
        [Column("QCParameters")]
        [StringLength(255)]
        public string Qcparameters { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
    }
}
