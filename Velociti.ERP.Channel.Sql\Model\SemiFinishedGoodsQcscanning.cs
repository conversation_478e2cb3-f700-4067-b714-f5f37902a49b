﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SemiFinishedGoodsQCScannings", Schema = "inv")]
    public partial class SemiFinishedGoodsQcscanning
    {
        public SemiFinishedGoodsQcscanning()
        {
            SemiFinishedGoodsQcscanningLines = new HashSet<SemiFinishedGoodsQcscanningLine>();
        }

        [Key]
        [Column("SemiFinishedGoodsQCScanningId")]
        public int SemiFinishedGoodsQcscanningId { get; set; }
        public int? CompanyId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public int? ProductId { get; set; }
        [StringLength(50)]
        public string Barcode { get; set; }
        [Column("QCEngineerUserId")]
        public int? QcengineerUserId { get; set; }
        [Column(TypeName = "date")]
        public DateTime? ProducedDate { get; set; }
        [Column("QCScanningExpiryDate", TypeName = "date")]
        public DateTime? QcscanningExpiryDate { get; set; }
        [Column(TypeName = "money")]
        public decimal? Weight { get; set; }
        public int? WarehouseId { get; set; }
        public byte? StatusEnum { get; set; }
        [StringLength(50)]
        public string ReworkAction { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [StringLength(50)]
        public string ReferenceNumber { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("SemiFinishedGoodsQcscannings")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SemiFinishedGoodsQcscannings")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(QcengineerUserId))]
        [InverseProperty(nameof(User.SemiFinishedGoodsQcscannings))]
        public virtual User QcengineerUser { get; set; }
        [ForeignKey(nameof(WarehouseId))]
        [InverseProperty(nameof(Warehous.SemiFinishedGoodsQcscannings))]
        public virtual Warehous Warehouse { get; set; }
        [InverseProperty(nameof(SemiFinishedGoodsQcscanningLine.SemiFinishedGoodsQcscanning))]
        public virtual ICollection<SemiFinishedGoodsQcscanningLine> SemiFinishedGoodsQcscanningLines { get; set; }
    }
}
