﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("DiscountSchemeCustomers", Schema = "sales")]
    public partial class DiscountSchemeCustomer
    {
        [Key]
        public int DiscountSchemeCustomerId { get; set; }
        public int CompanyId { get; set; }
        public int DiscountSchemeId { get; set; }
        public int? CustomerId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("DiscountSchemeCustomers")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("DiscountSchemeCustomers")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DiscountSchemeId))]
        [InverseProperty("DiscountSchemeCustomers")]
        public virtual DiscountScheme DiscountScheme { get; set; }
    }
}
