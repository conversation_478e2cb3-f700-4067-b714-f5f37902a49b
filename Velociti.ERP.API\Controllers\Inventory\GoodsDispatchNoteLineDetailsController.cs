﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Inventory;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [ApiController]
    public class GoodsDispatchNoteLineDetailsController : ControllerBase
    {
        private readonly IGoodsDispatchNoteLineDetailService goodsDispatchNoteLineDetailService;

        public GoodsDispatchNoteLineDetailsController(IGoodsDispatchNoteLineDetailService goodsDispatchNoteLineDetailService)
        {
            this.goodsDispatchNoteLineDetailService = goodsDispatchNoteLineDetailService;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody]IEnumerable<GoodsDispatchNoteLineDetail> goodsDispatchNoteLineDetail)
        {
            await goodsDispatchNoteLineDetailService.SaveAsync(goodsDispatchNoteLineDetail);

            return Ok();
        }
        [HttpGet]
        [Route("{goodsDispatchNoteLineId}")]
        public async Task<IActionResult> Get(int goodsDispatchNoteLineId)
        {
            var result = await goodsDispatchNoteLineDetailService.GetByHeaderIdAsync(goodsDispatchNoteLineId);
            return Ok(result);
        }
        [HttpGet]
        [Route("Lines/{goodsDispatchNoteLineId}")]
        public async Task<IActionResult> GetDetails(int goodsDispatchNoteLineId)
        {
            var result = await goodsDispatchNoteLineDetailService.GetDetailsByHeaderId(goodsDispatchNoteLineId);
            return Ok(result);
        }
    }
}