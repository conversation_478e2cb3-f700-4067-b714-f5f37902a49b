﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("GeneralLedgerLines", Schema = "fin")]
    public partial class GeneralLedgerLine
    {
        public GeneralLedgerLine()
        {
            BankReconciliationLines = new HashSet<BankReconciliationLine>();
            CustomerPaymentLines = new HashSet<CustomerPaymentLine>();
            GeneralLedgerSettlementLines = new HashSet<GeneralLedgerSettlementLine>();
            GeneralLedgerSettlements = new HashSet<GeneralLedgerSettlement>();
            InboundReceiptLines = new HashSet<InboundReceiptLine>();
            OutboundPaymentLines = new HashSet<OutboundPaymentLine>();
        }

        [Key]
        public int GeneralLedgerLineId { get; set; }
        public int? DepartmentId { get; set; }
        public int ChartOfAccountId { get; set; }
        public byte? TypeEnum { get; set; }
        public int? LinkTxnId { get; set; }
        public byte? LinkTxnDocType { get; set; }
        [StringLength(32)]
        public string LinkTxnDocNumber { get; set; }
        public int? LinkSourceId { get; set; }
        public byte? LinkSourceDocType { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? Date { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? DueDate { get; set; }
        public int? SupplierId { get; set; }
        public int? CustomerId { get; set; }
        public int? EmployeeId { get; set; }
        public int? CompanyId { get; set; }
        public int? CurrencyId { get; set; }
        [Column(TypeName = "money")]
        public decimal? ExchangeRate { get; set; }
        [Column(TypeName = "money")]
        public decimal OriginalAmount { get; set; }
        [Column(TypeName = "money")]
        public decimal OriginalAmountInDocCurrency { get; set; }
        [Column(TypeName = "money")]
        public decimal BalanceAmount { get; set; }
        public bool IsCr { get; set; }
        public bool? IsReconciled { get; set; }
        [StringLength(1000)]
        public string Notes { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? LastModifiedDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChartOfAccountId))]
        [InverseProperty("GeneralLedgerLines")]
        public virtual ChartOfAccount ChartOfAccount { get; set; }
        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("GeneralLedgerLines")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CurrencyId))]
        [InverseProperty("GeneralLedgerLines")]
        public virtual Currency Currency { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("GeneralLedgerLines")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("GeneralLedgerLines")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(EmployeeId))]
        [InverseProperty("GeneralLedgerLines")]
        public virtual Employee Employee { get; set; }
        [ForeignKey(nameof(SupplierId))]
        [InverseProperty("GeneralLedgerLines")]
        public virtual Supplier Supplier { get; set; }
        [InverseProperty(nameof(BankReconciliationLine.GeneralLedgerLine))]
        public virtual ICollection<BankReconciliationLine> BankReconciliationLines { get; set; }
        [InverseProperty(nameof(CustomerPaymentLine.GeneralLedgerLine))]
        public virtual ICollection<CustomerPaymentLine> CustomerPaymentLines { get; set; }
        [InverseProperty(nameof(GeneralLedgerSettlementLine.GeneralLedgerLine))]
        public virtual ICollection<GeneralLedgerSettlementLine> GeneralLedgerSettlementLines { get; set; }
        [InverseProperty(nameof(GeneralLedgerSettlement.GeneralLedgerLine))]
        public virtual ICollection<GeneralLedgerSettlement> GeneralLedgerSettlements { get; set; }
        [InverseProperty(nameof(InboundReceiptLine.GeneralLedgerLine))]
        public virtual ICollection<InboundReceiptLine> InboundReceiptLines { get; set; }
        [InverseProperty(nameof(OutboundPaymentLine.GeneralLedgerLine))]
        public virtual ICollection<OutboundPaymentLine> OutboundPaymentLines { get; set; }
    }
}
