﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ServiceInvoiceMatarialRequestNotes", Schema = "prc")]
    public partial class ServiceInvoiceMatarialRequestNote
    {
        [Key]
        public int InternalServiceInvoiceId { get; set; }
        [Key]
        public int MaterialRequisitionNoteId { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(InternalServiceInvoiceId))]
        [InverseProperty("ServiceInvoiceMatarialRequestNotes")]
        public virtual InternalServiceInvoice InternalServiceInvoice { get; set; }
        [ForeignKey(nameof(MaterialRequisitionNoteId))]
        [InverseProperty("ServiceInvoiceMatarialRequestNotes")]
        public virtual MaterialRequisitionNote MaterialRequisitionNote { get; set; }
    }
}
