﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("I_Role_Permissions")]
    public partial class IRolePermission
    {
        [StringLength(255)]
        public string Role { get; set; }
        [StringLength(255)]
        public string C { get; set; }
        [StringLength(255)]
        public string D { get; set; }
        [StringLength(255)]
        public string E { get; set; }
        [StringLength(255)]
        public string F { get; set; }
        [StringLength(255)]
        public string G { get; set; }
        [StringLength(255)]
        public string H { get; set; }
        [StringLength(255)]
        public string I { get; set; }
        [StringLength(255)]
        public string J { get; set; }
        [StringLength(255)]
        public string K { get; set; }
        [StringLength(255)]
        public string L { get; set; }
        [StringLength(255)]
        public string M { get; set; }
        [StringLength(255)]
        public string N { get; set; }
        [StringLength(255)]
        public string O { get; set; }
        [StringLength(255)]
        public string P { get; set; }
        [StringLength(255)]
        public string Q { get; set; }
        [StringLength(255)]
        public string R { get; set; }
        [StringLength(255)]
        public string S { get; set; }
        [StringLength(255)]
        public string T { get; set; }
        [StringLength(255)]
        public string U { get; set; }
        [StringLength(255)]
        public string V { get; set; }
        [StringLength(255)]
        public string W { get; set; }
        [StringLength(255)]
        public string X { get; set; }
        [StringLength(255)]
        public string Y { get; set; }
        [StringLength(255)]
        public string Z { get; set; }
        [Column("AA")]
        [StringLength(255)]
        public string Aa { get; set; }
    }
}
