﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class PaymentAdviceCostAllocationsController : ControllerBase    
    {
        private readonly IPaymentAdviceCostAllocationService _paymentAdviceCostAllocationService;

        public PaymentAdviceCostAllocationsController(IPaymentAdviceCostAllocationService paymentAdviceCostAllocationService)
        {
            _paymentAdviceCostAllocationService = paymentAdviceCostAllocationService;
        }

        [HttpGet]
        [Route("{paymentAdviceLineId}")]
        public async Task<IActionResult> Get(int paymentAdviceLineId)  
        {
            return Ok(await _paymentAdviceCostAllocationService.GetAsync(paymentAdviceLineId));
        }

        [HttpPost]
        public async Task<IActionResult> GetPaymentAdviceCostAllocations([FromBody]PaymentAdviceCostAllocation paymentAdviceCostAllocation)
        {
            return Ok(await _paymentAdviceCostAllocationService.AddPaymentAdviceCostAllocationAsync(paymentAdviceCostAllocation));
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _paymentAdviceCostAllocationService.DeleteAsync(id);

            return Ok();
        }
    }
}