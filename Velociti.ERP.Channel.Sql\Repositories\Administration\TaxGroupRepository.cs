﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Velociti.ERP.Channel.Sql.Model;
using System.Linq;
using Microsoft.EntityFrameworkCore;
using Velociti.ERP.Channel.Sql.Repositories.Contracts.Administration;

namespace Velociti.ERP.Channel.Sql.Repositories.Administration
{
    public class TaxGroupRepository : ITaxGroupRepository
    {
        private readonly MarangoniERPContext _context;

        public TaxGroupRepository(MarangoniERPContext context)
        {
            _context = context;
        }

        public async Task<TaxGroup> FindByIdAsync(int id)
        {
            return await _context.TaxGroups.FindAsync(id);
        }

        public async Task<IEnumerable<TaxGroup>> GetAllAsync()
        {
            return await _context.TaxGroups.ToListAsync();
        }
    }
}
