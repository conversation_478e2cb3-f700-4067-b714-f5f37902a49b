﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("SalesServiceOrderLines", Schema = "sales")]
    public partial class SalesServiceOrderLine
    {
        public SalesServiceOrderLine()
        {
            SalesInvoiceLines = new HashSet<SalesInvoiceLine>();
            SalesServiceOrderJobs = new HashSet<SalesServiceOrderJob>();
        }

        [Key]
        public int SalesServiceOrderLineId { get; set; }
        public int SalesServiceOrderId { get; set; }
        public int? ProductId { get; set; }
        public int? BillOfMaterialId { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? Commission { get; set; }
        [Column(TypeName = "money")]
        public decimal? CommissionValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? Price { get; set; }
        public int? BrandId { get; set; }
        public int? TherapistId { get; set; }
        public int? TherapyRoomId { get; set; }
        public int? Quantity { get; set; }
        public int? Time { get; set; }
        [Column(TypeName = "decimal(18, 4)")]
        public decimal? DiscountPct { get; set; }
        [Column(TypeName = "money")]
        public decimal? DiscountValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? GrossValue { get; set; }
        public int? TaxGroupId { get; set; }
        [Column(TypeName = "money")]
        public decimal? TaxValue { get; set; }
        [Column(TypeName = "money")]
        public decimal? NetValue { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(BillOfMaterialId))]
        [InverseProperty("SalesServiceOrderLines")]
        public virtual BillOfMaterial BillOfMaterial { get; set; }
        [ForeignKey(nameof(BrandId))]
        [InverseProperty(nameof(SupportData.SalesServiceOrderLines))]
        public virtual SupportData Brand { get; set; }
        [ForeignKey(nameof(ProductId))]
        [InverseProperty("SalesServiceOrderLines")]
        public virtual Product Product { get; set; }
        [ForeignKey(nameof(SalesServiceOrderId))]
        [InverseProperty("SalesServiceOrderLines")]
        public virtual SalesServiceOrder SalesServiceOrder { get; set; }
        [ForeignKey(nameof(TaxGroupId))]
        [InverseProperty("SalesServiceOrderLines")]
        public virtual TaxGroup TaxGroup { get; set; }
        [ForeignKey(nameof(TherapistId))]
        [InverseProperty(nameof(Employee.SalesServiceOrderLines))]
        public virtual Employee Therapist { get; set; }
        [ForeignKey(nameof(TherapyRoomId))]
        [InverseProperty(nameof(Division.SalesServiceOrderLines))]
        public virtual Division TherapyRoom { get; set; }
        [InverseProperty(nameof(SalesInvoiceLine.SalesServiceOrderLine))]
        public virtual ICollection<SalesInvoiceLine> SalesInvoiceLines { get; set; }
        [InverseProperty(nameof(SalesServiceOrderJob.SalesServiceOrderLine))]
        public virtual ICollection<SalesServiceOrderJob> SalesServiceOrderJobs { get; set; }
    }
}
