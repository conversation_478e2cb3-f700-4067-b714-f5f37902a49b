﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement  
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class InternalServiceInvoiceLinesController : ControllerBase
    {
        private readonly IInternalServiceInvoiceLineService _internalServiceInvoiceLineService;  

        public InternalServiceInvoiceLinesController(IInternalServiceInvoiceLineService internalServiceInvoiceLineService)    
        {
            _internalServiceInvoiceLineService = internalServiceInvoiceLineService;
        }

        [HttpGet]
        [Route("{internalServiceInvoiceId}")]  
        public async Task<IActionResult> Get(int internalServiceInvoiceId)
        {
            return Ok(await _internalServiceInvoiceLineService.GetAsync(internalServiceInvoiceId));
        }

        [HttpPost]
        public async Task<IActionResult> GetInternalServiceInvoiceLine([FromBody]InternalServiceInvoiceLine internalServiceInvoiceLine)  
        {
            return Ok(await _internalServiceInvoiceLineService.AddInternalServiceInvoiceLineAsync(internalServiceInvoiceLine));
        }

        [HttpGet]
        [Route("Submitted/{companyId}")]
        public async Task<IActionResult> GetSubmitted(int companyId)
        {
            return Ok(await _internalServiceInvoiceLineService.GetSubmittedLinesAsync(companyId));
        }

        [HttpGet]
        [Route("Selected/{internalServiceInvoiceLineIds}/company/{companyId}")]
        public async Task<IActionResult> GetSelected(string internalServiceInvoiceLineIds, int companyId)  
        {
            string[] orderlineIdArray = internalServiceInvoiceLineIds.Split(',');
            var list = orderlineIdArray.Select(p => int.Parse(p)).ToList();

            return Ok(await _internalServiceInvoiceLineService.GetSelectedLinesAsync(list, companyId));
        }
    }
}