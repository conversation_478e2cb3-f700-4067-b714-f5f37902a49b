﻿using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.Inventory;

namespace Velociti.ERP.API.Controllers.Inventory
{
    [Route("api/Inventory/[controller]")]
    [Authorize]
    [ApiController]
    public class StockCountSheetLinesController : ControllerBase
    {
        private readonly IStockCountSheetLineService _stockCountSheetLinesService;

        public StockCountSheetLinesController(IStockCountSheetLineService stockCountSheetLinesService)
        {
            _stockCountSheetLinesService = stockCountSheetLinesService;
        }

        [HttpGet]
        [Route("All/{warehouseId}/company/{companyId}/priceList/{priceListId}/stockCountSheet/{stockCountSheetId}")]
        public async Task<IActionResult> GetLinesGridInformation(int warehouseId, int companyId, int priceListId, int stockCountSheetId)
        {
            return Ok(await _stockCountSheetLinesService.GetLinesGridInformation(warehouseId, companyId, priceListId, stockCountSheetId));
        }
    }
}