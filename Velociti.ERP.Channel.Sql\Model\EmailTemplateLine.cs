﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("EmailTemplateLines", Schema = "adm")]
    public partial class EmailTemplateLine
    {
        [Key]
        public int EmailTemplateLineId { get; set; }
        public int EmailTemplateId { get; set; }
        public byte? TypeEnum { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(EmailTemplateId))]
        [InverseProperty("EmailTemplateLines")]
        public virtual EmailTemplate EmailTemplate { get; set; }
    }
}
