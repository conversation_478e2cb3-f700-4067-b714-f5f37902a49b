﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("CustomerPaymentVouchers", Schema = "sales")]
    public partial class CustomerPaymentVoucher
    {
        [Key]
        public int CustomerPaymentVoucherId { get; set; }
        public int? CustomerPaymentId { get; set; }
        [StringLength(30)]
        public string SerialNumber { get; set; }
        [Column(TypeName = "money")]
        public decimal? Amount { get; set; }

        [ForeignKey(nameof(CustomerPaymentId))]
        [InverseProperty("CustomerPaymentVouchers")]
        public virtual CustomerPayment CustomerPayment { get; set; }
    }
}
