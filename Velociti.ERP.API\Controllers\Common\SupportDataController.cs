﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.Common;
using Velociti.ERP.Domain.Services.Common;
using static Velociti.ERP.Domain.Entities.Common.SupportData;

namespace Velociti.ERP.API.Controllers.Common
{
    [Route("api/Common/[controller]")]
    [Authorize]
    [ApiController]
    public class SupportDataController : ControllerBase
    {
        private readonly ISupportDataService _supportDataService;

        public SupportDataController(ISupportDataService supportDataService)
        {
            _supportDataService = supportDataService;
        }

        [HttpGet]
        [Route("{id}")]
        public async Task<ActionResult<Response<IEnumerable<SupportData>>>> Get(int id)
        {
            return Ok(await _supportDataService.FindByIdAsync(id));
        }

        [HttpGet]
        [Route("{typeEnum}/company/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<SupportData>>>> Get(int companyId, TypeEnum typeEnum)
        {
            return Ok(await _supportDataService.GetAllByTypeAsync(companyId, typeEnum));
        }

        [HttpGet]
        [AllowAnonymous]
        [Route("ShortList/{typeEnum}/company/{companyId}")]
        public async Task<IActionResult> GetShortList(TypeEnum typeEnum, int companyId)
        {
            var result = await _supportDataService.GetShortListAsync(typeEnum, companyId);

            return Ok(result.Data);
        }

        [HttpGet]
        [Route("ShortList/{typeEnum}/company/{companyId}/IsAll/{isAll}")]
        public async Task<IActionResult> GetShortList(TypeEnum typeEnum, int companyId, bool isAll)
        {
            var result = await _supportDataService.GetShortListAsync(typeEnum, companyId, isAll);

            return Ok(result.Data);
        }

        [HttpPost]
        [Route("{typeEnum}")]
        public async Task<IActionResult> Save([FromRoute]TypeEnum typeEnum, [FromBody]SupportData supportData)
        {
            if (supportData == null)
                return BadRequest();

            await _supportDataService.SaveAsync(typeEnum, supportData);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}/loggedInUser/{loggedInUserId}")]
        public async Task<IActionResult> ToggleActivation(int id, int loggedInUserId)
        {
            await _supportDataService.ToggleActivationAsync(id, loggedInUserId);

            return Ok();
        }

        [HttpGet]
        [Route("Reasons/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetReasons(int companyId)
        {
            return Ok(await _supportDataService.GetActiveAllReasonsAsync(companyId));
        }
        [HttpPost]
        [Route("Reasons")]
        public async Task<IActionResult> Save([FromBody]SupportData supportData)
        {
            if (supportData == null)
                return BadRequest();

            await _supportDataService.SaveAsync((TypeEnum)supportData.SupporDataType, supportData);

            return Ok();
        }

        [HttpGet]
        [Route("OverheadInformations/{companyId}")]
        public async Task<ActionResult<Response<IEnumerable<SupportData>>>> GetOverheadInformations(int companyId)
        {
            return Ok(await _supportDataService.GetActiveAllOverheadInformationsAsync(companyId));  
        }

        [HttpGet]
        [Route("ChargeGroups/{chargeInformationId}")]
        public async Task<ActionResult<Response<IEnumerable<ShortList>>>> GetChargeGroups(int chargeInformationId)      
        {
            return Ok(await _supportDataService.GetChargeGroupsByChargeInformationAsync(chargeInformationId));  
        }
    }
}