﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [Authorize]
    [ApiController]
    public class SupplierContactsController : ControllerBase  
    {
        private readonly ISupplierContactService _supplierContactService;  

        public SupplierContactsController(ISupplierContactService supplierContactService)  
        {
            _supplierContactService = supplierContactService;
        }

        [HttpGet]
        [Route("{supplierId}")]
        public async Task<IActionResult> Get(int supplierId)  
        {
            return Ok(await _supplierContactService.GetBySupplierIdAsync(supplierId));    
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]SupplierContact supplierContact)  
        {
            if (supplierContact == null)
                return BadRequest();

            await _supplierContactService.SaveAsync(supplierContact);

            return Ok();
        }

        [HttpDelete]
        [Route("{supplierId}/Contact/{contactId}/User/{userId}")]
        public async Task<IActionResult> ToggleActivation(int supplierId, int contactId, int userId)
        {
            await _supplierContactService.ToggleActivationAsync(supplierId, contactId, userId);

            return Ok();
        }
    }
}