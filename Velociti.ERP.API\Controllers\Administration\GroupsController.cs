﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Administration;
using Velociti.ERP.Domain.Services.Administration;

namespace Velociti.ERP.API.Controllers.Administration
{
    [Route("api/Administration/[controller]")]
    [Authorize]
    [ApiController]
    public class GroupsController : ControllerBase
    {
        private readonly IGroupService _groupService;

        public GroupsController(IGroupService groupService)
        {
            _groupService = groupService;
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            return Ok(await _groupService.GetAllAsync());
        }

        [HttpGet]
        [Route("ShortList")]
        public async Task<IActionResult> GetShortList()
        {
            return Ok(await _groupService.GetShortListAsync());
        }

        [HttpPost]
        public async Task<IActionResult> Save([FromBody]Group group)
        {
            if (group == null)
                return BadRequest();

            await _groupService.SaveAsync(group);

            return Ok();
        }

        [HttpDelete]
        [Route("{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            await _groupService.DeleteAsync(id);

            return Ok();
        }
    }
}