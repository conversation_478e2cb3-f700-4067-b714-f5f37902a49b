﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Finance;
using Velociti.ERP.Domain.Services.Finance;

namespace Velociti.ERP.API.Controllers.Finance
{
    [Route("api/Finance/[controller]")]
    [Authorize]
    [ApiController]
    public class AccountMappingLineDetailsController : ControllerBase
    {
        private readonly IAccountMappingLineDetailService _accountMappingLineDetailService;

        public AccountMappingLineDetailsController(IAccountMappingLineDetailService accountMappingLineDetailService)
        {
            _accountMappingLineDetailService = accountMappingLineDetailService;
        }

        [HttpGet]
        [Route("{accountMappingLineId}")]
        public async Task<IActionResult> Get(int accountMappingLineId)
        {
            var records = await _accountMappingLineDetailService.GetAsync(accountMappingLineId);

            return Ok(records);
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]AccountMappingLineDetail accountMappingLineDetail)
        {
            await _accountMappingLineDetailService.SaveAsync(accountMappingLineDetail);

            return Ok();
        }
        [HttpDelete]
        [Route("{accountMappingLineDetailId}/user/{userId}")]
        public async Task<IActionResult> RemoveAccountLine(int accountMappingLineDetailId, int userId)  
        {
            await _accountMappingLineDetailService.RemoveAccountLineAsync(accountMappingLineDetailId, userId);

            return Ok();
        }
    }
}