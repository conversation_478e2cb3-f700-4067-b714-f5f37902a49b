﻿<?xml version="1.0" encoding="utf-8"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <sectionGroup name="devExpress">
      <section name="themes" type="DevExpress.Web.ThemesConfigurationSection, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="compression" type="DevExpress.Web.CompressionConfigurationSection, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="settings" type="DevExpress.Web.SettingsConfigurationSection, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="errors" type="DevExpress.Web.ErrorsConfigurationSection, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
      <section name="resources" type="DevExpress.Web.ResourcesConfigurationSection, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" requirePermission="false" />
    </sectionGroup>

		  <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
	  
  </configSections>
	<log4net>
		<appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
			<file type="log4net.Util.PatternString" value="%property{LogPath}\app.log" />
			<appendToFile value="true" />
			<rollingStyle value="Date" />
			<datePattern value="yyyyMMdd'.log'" />
			<staticLogFileName value="false" />
			<layout type="log4net.Layout.PatternLayout">
				<conversionPattern value="%date [%thread] %-5level %logger - %message%newline" />
			</layout>
		</appender>

		<root>
			<level value="DEBUG" />
			<appender-ref ref="RollingFileAppender" />
		</root>
	</log4net>
	<connectionStrings>
		<add name="MarangoniERP" connectionString="XpoProvider=MSSqlServer;data source=DESKTOP-NRUK56G\SQLEXPRESS01;integrated security=SSPI;initial catalog=NemsuERPNew;Persist Security Info=true;User ID=***;Password=***;TrustServerCertificate=True" />
		<add name="MarangoniERPConnectionString" connectionString="Data Source=DESKTOP-NRUK56G\SQLEXPRESS01;Initial Catalog=NemsuERPNew;integrated security=SSPI;MultipleActiveResultSets=True;TrustServerCertificate=True;Encrypt=False" providerName="System.Data.SqlClient" />

		<add name="localhost_VelocitiSFAV2_Connection" connectionString="XpoProvider=MSSqlServer;data source=DESKTOP-NRUK56G\SQLEXPRESS01;integrated security=SSPI;initial catalog=VelocitiSFAV2" />
		<add name="localhost_DMS_Connection" connectionString="XpoProvider=MSSqlServer;data source=DESKTOP-NRUK56G\SQLEXPRESS01;integrated security=SSPI;initial catalog=DMS" />
		<add name="NemsuERPConnectionString" connectionString="Data Source=DESKTOP-NRUK56G\SQLEXPRESS01;Initial Catalog=NemsuERPNew;Persist Security Info=True;User ID=***;Password=***;TrustServerCertificate=True" providerName="System.Data.SqlClient" />
		<add name="NemsuERPConnectionString1" connectionString="Data Source=DESKTOP-NRUK56G\SQLEXPRESS01;Initial Catalog=NemsuERPNew;Persist Security Info=True;User ID=***;Password=***;TrustServerCertificate=True" providerName="System.Data.SqlClient" />
		<add name="NemsuERPConnectionString2" connectionString="Data Source=DESKTOP-NRUK56G\SQLEXPRESS01;Initial Catalog=NemsuERPNew;Persist Security Info=True;User ID=***;Password=***;TrustServerCertificate=True" providerName="System.Data.SqlClient" />
		<add name="NemsuERPConnectionString3" connectionString="Data Source=DESKTOP-NRUK56G\SQLEXPRESS01;Initial Catalog=NemsuERPNew;Persist Security Info=True;User ID=***;Password=***;TrustServerCertificate=True" providerName="System.Data.SqlClient" />
		<add name="NemsuERPConnectionString4" connectionString="Data Source=DESKTOP-NRUK56G\SQLEXPRESS01;Initial Catalog=NemsuERPNew;Persist Security Info=True;User ID=***;Password=***;TrustServerCertificate=True" providerName="System.Data.SqlClient" />
	    <add name="NemsuERPConnectionString5" connectionString="Data Source=DESKTOP-NRUK56G\SQLEXPRESS01;Initial Catalog=NemsuERPNew;Persist Security Info=True;User ID=***;Password=***;TrustServerCertificate=True" providerName="System.Data.SqlClient" />
 </connectionStrings>
  <appSettings>
    <add key="Env" value="2" />
    <add key="Client" value="Nemsui" />
    <add key="Supplier_SimahAsiaCode" value="SL-0002" />
    <add key="Supplier_SesdermaLaboratoriesCode" value="SL-0001" />
    <add key="BackDateEnabledUserIds" value="2" />
    <add key="ReportCredentialsUserName" value="reportuser" />
    <add key="ReportCredentialsPwd" value="4rfv$RFV" />
    <add key="ReportCredentialsDomain" value="win-bj5c8vsskv2" />
    <add key="ReportServerURL" value="http://win-bj5c8vsskv2/ReportServer" />
    <add key="ReportFolder" value="/MarangoniERPReports/" />
    <add key="ReportIFrameURL" value="../../MarangoniERP/ssrstemplates/ReportTemplate.aspx" />
    <add key="ReceiptIFrameURL" value="../../MarangoniERP/ssrstemplates/ReceiptTemplate.aspx" />
    <add key="webpages:Version" value="*******" />
    <add key="webpages:Enabled" value="false" />
    <add key="ClientValidationEnabled" value="true" />
    <add key="UnobtrusiveJavaScriptEnabled" value="true" />
    <!--<add key="ServiceUrl" value="http://localhost:2983/api/" />-->
    <add key="ServiceUrl" value="https://localhost:44382/api/" />
    <!--<add key="ServiceUrl" value="http://*************/marangoniERPAPI/api/" />-->
    <!--<add key="ServiceUrl" value="https://mitl***api.azurewebsites.net/api/" />-->
    <add key="vs:EnableBrowserLink" value="false" />
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.8" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.8">
      <buildProviders>
        <add extension=".rdlc" type="Microsoft.Reporting.RdlBuildProvider, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
      </buildProviders>
      <assemblies>
        <add assembly="DevExpress.Data.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Drawing.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Web.ASPxThemes.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.RichEdit.v25.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.RichEdit.v25.1.Export, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Printing.v25.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Web.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Web.Mvc5.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.XtraReports.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.XtraReports.v25.1.Web, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.XtraReports.v25.1.Web.WebForms, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Pdf.v25.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Web.Resources.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Charts.v25.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.CodeParser.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.DataAccess.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Office.v25.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.PivotGrid.v25.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Sparkline.v25.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Xpo.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.XtraCharts.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.XtraGauges.v25.1.Core, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Web.ASPxTreeList.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="DevExpress.Web.ASPxGantt.v25.1, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
        <add assembly="Microsoft.ReportViewer.Common, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
        <add assembly="Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
      </assemblies>
    </compilation>
    <httpRuntime targetFramework="4.7.2" maxRequestLength="**********" requestValidationMode="4.0" executionTimeout="240" />
    <httpHandlers>
      <add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" validate="false" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" validate="false" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRD.axd" validate="false" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" validate="false" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXQB.axd" validate="false" />
      <add path="Reserved.ReportViewerWebControl.axd" verb="*" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" validate="false" />
    </httpHandlers>
    <httpModules>
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
    </httpModules>
    <globalization culture="" uiCulture="" />
    <pages validateRequest="true" clientIDMode="Predictable">
      <namespaces>
        <add namespace="DevExpress.Utils" />
        <add namespace="DevExpress.Web.ASPxThemes" />
        <add namespace="DevExpress.Web" />
        <add namespace="DevExpress.Web.Mvc" />
        <add namespace="DevExpress.Web.Mvc.UI" />
        <add namespace="DevExpress.XtraReports" />
        <add namespace="DevExpress.XtraReports.UI" />
        <add namespace="DevExpress.XtraReports.Web" />
        <add namespace="DevExpress.XtraReports.Web.DocumentViewer" />
        <add namespace="DevExpress.Web.ASPxTreeList" />
      </namespaces>
    </pages>
  </system.web>
  <system.webServer>
    <handlers>
      <!--<add name="ReportViewerWebControlHandler" preCondition="integratedMode" verb="*" path="Reserved.ReportViewerWebControl.axd" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=14.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91" />-->
      <add type="DevExpress.Web.ASPxUploadProgressHttpHandler, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="ASPxUploadProgressHandlerPage.ashx" name="ASPxUploadProgressHandler" preCondition="integratedMode" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DX.ashx" name="ASPxHttpHandlerModule" preCondition="integratedMode" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRD.axd" name="ASPxReportDesignerHandlerModule" preCondition="integratedMode" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXXRDV.axd" name="ASPxWebDocumentViewerHandlerModule" preCondition="integratedMode" />
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" verb="GET,POST" path="DXQB.axd" name="ASPxQueryBuilderDesignerHandlerModule" preCondition="integratedMode" />
      <add name="ReportViewerWebControlHandler" verb="*" path="Reserved.ReportViewerWebControl.axd" preCondition="integratedMode" type="Microsoft.Reporting.WebForms.HttpHandler, Microsoft.ReportViewer.WebForms, Version=********, Culture=neutral, PublicKeyToken=89845DCD8080CC91" />
    <remove name="ExtensionlessUrlHandler-Integrated-4.0" /><remove name="OPTIONSVerbHandler" /><remove name="TRACEVerbHandler" /><add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0" /></handlers>
    <validation validateIntegratedModeConfiguration="false" />
    <modules runAllManagedModulesForAllRequests="true">
      <add type="DevExpress.Web.ASPxHttpHandlerModule, DevExpress.Web.v24.2, Version=********, Culture=neutral, PublicKeyToken=b88d1754d700e49a" name="ASPxHttpHandlerModule" />
    </modules>
    <security>
      <requestFiltering>
        <requestLimits maxAllowedContentLength="2147483648" />
      </requestFiltering>
    </security>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Antlr3.Runtime" publicKeyToken="eb42632606e9261f" />
        <bindingRedirect oldVersion="0.0.0.0-3.5.0.2" newVersion="3.5.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.1.0.0" newVersion="1.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-1.6.5135.21930" newVersion="1.6.5135.21930" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" />
        <bindingRedirect oldVersion="0.0.0.0-1*******" newVersion="1*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Buffers" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.3.0" newVersion="4.0.3.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Runtime.CompilerServices.Unsafe" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Numerics.Vectors" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.1.4.0" newVersion="4.1.4.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Memory" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.1.2" newVersion="4.0.1.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.ReportViewer.WebForms" publicKeyToken="89845dcd8080cc91" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-********" newVersion="********" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Json" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.10" newVersion="6.0.0.10" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Threading.Tasks.Extensions" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.0.1" newVersion="4.2.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.IdentityModel.Abstractions" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-7.5.0.0" newVersion="7.5.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Diagnostics.DiagnosticSource" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.1" newVersion="8.0.0.1" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Security.Cryptography.ProtectedData" publicKeyToken="b03f5f7f11d50a3a" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.0.5.0" newVersion="4.0.5.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Extensions.DependencyInjection.Abstractions" publicKeyToken="adb9793829ddae60" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.2" newVersion="8.0.0.2" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Bcl.AsyncInterfaces" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-8.0.0.0" newVersion="8.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Text.Encodings.Web" publicKeyToken="cc7b13ffcd2ddd51" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-6.0.0.0" newVersion="6.0.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-4.2.2.0" newVersion="4.2.2.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35" />
        <bindingRedirect oldVersion="0.0.0.0-5.2.7.0" newVersion="5.2.7.0" />
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <devExpress>
    <resources>
      <add type="ThirdParty" />
      <add type="DevExtreme" />
    </resources>
    <themes enableThemesAssembly="true" styleSheetTheme="" theme="MaterialCompact" customThemeAssemblies="" baseColor="#0072C6" font="" />
    <compression enableHtmlCompression="false" enableCallbackCompression="true" enableResourceCompression="true" enableResourceMerging="true" />
    <settings rightToLeft="false" checkReferencesToExternalScripts="true" doctypeMode="Html5" accessibilityCompliant="false" />
    <errors callbackErrorRedirectUrl="" />
  </devExpress>
  <system.web.extensions>
    <scripting>
      <webServices>
        <jsonSerialization maxJsonLength="**********" />
      </webServices>
    </scripting>
  </system.web.extensions>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:1659;1699;1701" />
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:default /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+" />
    </compilers>
  </system.codedom>
</configuration>