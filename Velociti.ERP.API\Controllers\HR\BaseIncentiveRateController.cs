﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Services.HR;
using Velociti.ERP.Domain.Entities;
using Velociti.ERP.Domain.Entities.HR;
using Velociti.ERP.Domain.Entities.Sales;

namespace Velociti.ERP.API.Controllers.HR
{
    [Route("api/HR/[controller]")]
    [Authorize]
    [ApiController]
    public class BaseIncentiveRateController : ControllerBase
    {
        private readonly IBaseIncentiveRateService _baseIncentiveRateService;

        public BaseIncentiveRateController(IBaseIncentiveRateService baseIncentiveRateService)
        {
            _baseIncentiveRateService = baseIncentiveRateService;
        }

        [HttpGet]
        [Route("Single/{baseIncentiveRateId}")]
        public async Task<IActionResult> FindById(int baseIncentiveRateId)
        {
            return Ok(await _baseIncentiveRateService.FindByIdAsync(baseIncentiveRateId));
        }

        [HttpGet]
        public async Task<IActionResult> GetAll()
        {
            return Ok(await _baseIncentiveRateService.GetAllAsync());
        }

        [HttpPost]
        public async Task<IActionResult> Post([FromBody]BaseIncentiveRate baseIncentiveRate)
        {
            switch (baseIncentiveRate.Action)
            {
                case "save": await _baseIncentiveRateService.SaveAsync(baseIncentiveRate); break;
                case "submit": await _baseIncentiveRateService.UpdateAsync(baseIncentiveRate); break;
            }

            return Ok();
        }
    }
}