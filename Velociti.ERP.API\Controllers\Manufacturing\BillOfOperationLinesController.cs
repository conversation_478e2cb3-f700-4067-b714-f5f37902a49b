﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Manufacturing;
using Velociti.ERP.Domain.Services.Manufacturing;

namespace Velociti.ERP.API.Controllers.Manufacturing  
{
    [Route("api/Manufacturing/[controller]")]
    [Authorize]
    [ApiController]  
    public class BillOfOperationLinesController : ControllerBase
    {
        private readonly IBillOfOperationLineService _billOfOperationLineService;  

        public BillOfOperationLinesController(IBillOfOperationLineService billOfOperationLineService)  
        {
            _billOfOperationLineService = billOfOperationLineService;
        }

        [HttpGet]
        [Route("{billOfOperationId}")]
        public async Task<IActionResult> Get(int billOfOperationId)
        {
            return Ok(await _billOfOperationLineService.GetAsync(billOfOperationId));
        }

        [HttpPost]
        public async Task<IActionResult> GetBillOfOperationLine([FromBody]BillOfOperationLine billOfOperationLine)
        {
            return Ok(await _billOfOperationLineService.AddBillOfOperationLineAsync(billOfOperationLine));
        }

        [HttpPut]
        public async Task<IActionResult> Update([FromBody]BillOfOperationLine billOfOperationLine)
        {
            switch(billOfOperationLine.Action)
            {
                case "Delete Secondary Machine": await _billOfOperationLineService.DeleteSecondaryMachineAsync(billOfOperationLine.BillOfOperationLineId); break;
            }

            return Ok();
        }
    }
}