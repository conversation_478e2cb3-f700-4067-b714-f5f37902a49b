﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Velociti.ERP.Domain.Entities.Procurement;
using Velociti.ERP.Domain.Services.Procurement;

namespace Velociti.ERP.API.Controllers.Procurement
{
    [Route("api/Procurement/[controller]")]
    [ApiController]
    public class PurchaseOrderSalesInvoicesController : ControllerBase
    {
        private readonly IPurchaseOrderSalesInvoiceService purchaseOrderSalesInvoiceService;  

        public PurchaseOrderSalesInvoicesController(IPurchaseOrderSalesInvoiceService purchaseOrderSalesInvoiceService)  
        {
            this.purchaseOrderSalesInvoiceService = purchaseOrderSalesInvoiceService;
        }
        [HttpPost]
        public async Task<IActionResult> Save([FromBody]IEnumerable<PurchaseOrderSalesInvoice> purchaseOrderSalesInvoice)  
        {
            await purchaseOrderSalesInvoiceService.SaveAsync(purchaseOrderSalesInvoice);

            return Ok();
        }
        [HttpGet]
        public async Task<IActionResult> GetAll(int purchaseOrderId)  
        {
            return Ok(await purchaseOrderSalesInvoiceService.GetAll(purchaseOrderId));
        }
    }
}