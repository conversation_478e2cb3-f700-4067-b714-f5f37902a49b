﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    public partial class PalletBarcode
    {
        [Key]
        public int Id { get; set; }
        [Column(TypeName = "date")]
        public DateTime? Date { get; set; }
        public int? SequenceNumber { get; set; }
        [StringLength(50)]
        public string Barcode { get; set; }
        public bool? IsPrinted { get; set; }
        public int? TempId { get; set; }
    }
}
