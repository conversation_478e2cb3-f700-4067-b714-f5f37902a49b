﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("MouldCavities", Schema = "man")]
    public partial class MouldCavity
    {
        public MouldCavity()
        {
            ProductionOrderLines = new HashSet<ProductionOrderLine>();
            ProductionPlanLines = new HashSet<ProductionPlanLine>();
        }

        [Key]
        public int CavityId { get; set; }
        public int MouldId { get; set; }
        [StringLength(50)]
        public string CavityCode { get; set; }
        [StringLength(255)]
        public string CavityDescription { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(MouldId))]
        [InverseProperty("MouldCavities")]
        public virtual Mould Mould { get; set; }
        [InverseProperty(nameof(ProductionOrderLine.Cavity))]
        public virtual ICollection<ProductionOrderLine> ProductionOrderLines { get; set; }
        [InverseProperty(nameof(ProductionPlanLine.Cavity))]
        public virtual ICollection<ProductionPlanLine> ProductionPlanLines { get; set; }
    }
}
