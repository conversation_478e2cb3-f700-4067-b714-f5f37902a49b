﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("HOReturnStockTransfers", Schema = "inv")]
    public partial class HoreturnStockTransfer
    {
        public HoreturnStockTransfer()
        {
            HoreturnStockTransferIntegrationLines = new HashSet<HoreturnStockTransferIntegrationLine>();
            HoreturnStockTransferLines = new HashSet<HoreturnStockTransferLine>();
        }

        [Key]
        [Column("HOReturnStockTransferId")]
        public int HoreturnStockTransferId { get; set; }
        public int? CompanyId { get; set; }
        public int? DepartmentId { get; set; }
        public int? CustomerId { get; set; }
        [Required]
        [StringLength(50)]
        public string DocNumber { get; set; }
        [StringLength(50)]
        public string RefDocNumber { get; set; }
        [Column("AODNumber")]
        [StringLength(50)]
        public string Aodnumber { get; set; }
        [Column(TypeName = "date")]
        public DateTime DocDate { get; set; }
        public byte? TypeEnum { get; set; }
        public byte? StatusEnum { get; set; }
        public int? FromWarehouseId { get; set; }
        public int? ToWarehouseId { get; set; }
        [StringLength(255)]
        public string VehicleDetails { get; set; }
        [StringLength(1000)]
        public string Remarks { get; set; }
        [Column("SFAStatusEnum")]
        public byte? SfastatusEnum { get; set; }
        [Column("SFAMessage")]
        [StringLength(1000)]
        public string Sfamessage { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? SubmittedDate { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? CreatedUserId { get; set; }
        public int? ModifiedUserId { get; set; }

        [ForeignKey(nameof(CompanyId))]
        [InverseProperty("HoreturnStockTransfers")]
        public virtual Company Company { get; set; }
        [ForeignKey(nameof(CustomerId))]
        [InverseProperty("HoreturnStockTransfers")]
        public virtual Customer Customer { get; set; }
        [ForeignKey(nameof(DepartmentId))]
        [InverseProperty("HoreturnStockTransfers")]
        public virtual Department Department { get; set; }
        [ForeignKey(nameof(FromWarehouseId))]
        [InverseProperty(nameof(Warehous.HoreturnStockTransferFromWarehouses))]
        public virtual Warehous FromWarehouse { get; set; }
        [ForeignKey(nameof(ToWarehouseId))]
        [InverseProperty(nameof(Warehous.HoreturnStockTransferToWarehouses))]
        public virtual Warehous ToWarehouse { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferIntegrationLine.HoreturnStockTransfer))]
        public virtual ICollection<HoreturnStockTransferIntegrationLine> HoreturnStockTransferIntegrationLines { get; set; }
        [InverseProperty(nameof(HoreturnStockTransferLine.HoreturnStockTransfer))]
        public virtual ICollection<HoreturnStockTransferLine> HoreturnStockTransferLines { get; set; }
    }
}
