﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Velociti.ERP.Channel.Sql.Model
{
    [Table("ChargeInfoChargeGroups", Schema = "fin")]
    public partial class ChargeInfoChargeGroup
    {
        [Key]
        public int ChargeInformationId { get; set; }
        [Key]
        public int ChargeGroupId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? CreationDate { get; set; }
        public int? ModifiedUserId { get; set; }
        [Column(TypeName = "datetime")]
        public DateTime? ExpiryDate { get; set; }
        public int? TempId { get; set; }

        [ForeignKey(nameof(ChargeGroupId))]
        [InverseProperty(nameof(SupportData.ChargeInfoChargeGroups))]
        public virtual SupportData ChargeGroup { get; set; }
        [ForeignKey(nameof(ChargeInformationId))]
        [InverseProperty("ChargeInfoChargeGroups")]
        public virtual ChargeInformation ChargeInformation { get; set; }
    }
}
